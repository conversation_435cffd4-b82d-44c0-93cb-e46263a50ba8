/**
 * Enhanced Deep Linking Configuration
 * Comprehensive URL routing and navigation with parameter support
 */

import { LinkingOptions   } from '@react-navigation/native';
import { Linking   } from 'react-native';
import type { RootStackParamList } from '../types/navigation';
import NavigationService from '../services/NavigationService';

// URL schemes for the app
export const URL_SCHEMES = {
  PRODUCTION: 'tailora',
  DEVELOPMENT: 'tailora-dev',
  UNIVERSAL: 'https://tailora.app'
};

// Deep linking configuration
export const LinkingConfiguration: LinkingOptions<RootStackParamList> = {
  prefixes: [
    URL_SCHEMES.PRODUCTION + '://',
    URL_SCHEMES.DEVELOPMENT + '://',
    URL_SCHEMES.UNIVERSAL,
    'https://app.tailora.com',
    'http://localhost:8081', // For development
    'http://localhost:8082',
    'http://localhost:8083',
    'http://localhost:8084'
  ],
  
  config: {
    screens: {
      Login: 'login',
      
      // Main app with nested tab navigation
      Main: {
        path: 'app',
        screens: {
          Dashboard: 'dashboard',
          Orders: 'orders',
          Products: {
            path: 'products',
            parse: {
              productId: (productId: string) => productId || undefined
            }
          },
          CRM: 'customers',
          Analytics: 'analytics',
        }
      },
      
      // Order management (handled through Main tab navigator)
      OrderDetails: {
        path: 'order/:orderId',
        parse: {
          orderId: (orderId: string) => orderId
        }
      },

      CreateOrder: {
        path: 'create-order',
        parse: {
          customerId: (customerId: string) => customerId || undefined,
          productId: (productId: string) => productId || undefined
        }
      },
      OrderSuccess: {
        path: 'order-success/:orderId',
        parse: {
          orderId: (orderId: string) => orderId
        }
      },
      
      // Customer management
      CustomerDetails: {
        path: 'customer/:customerId',
        parse: {
          customerId: (customerId: string) => customerId
        }
      },
      AddCustomer: 'add-customer',
      CustomerMeasurements: {
        path: 'customer/:customerId/measurements',
        parse: {
          customerId: (customerId: string) => customerId
        }
      },
      
      // Product management
      AddProduct: {
        path: 'add-product',
        parse: {
          category: (category: string) => category || undefined
        }
      },
      EditProduct: {
        path: 'products/:productId/edit',
        parse: {
          productId: (productId: string) => productId
        }
      },
      GarmentTemplates: 'templates',
      AddGarmentTemplate: 'add-template',
      EditGarmentTemplate: {
        path: 'edit-template/:templateId',
        parse: {
          templateId: (templateId: string) => templateId
        }
      },
      
      // Measurements
      Measurements: 'measurements',
      AddMeasurement: {
        path: 'add-measurement/:customerId',
        parse: {
          customerId: (customerId: string) => customerId,
          templateId: (templateId: string) => templateId || undefined
        }
      },
      EditMeasurement: {
        path: 'measurements/:measurementId/edit',
        parse: {
          measurementId: (measurementId: string) => measurementId
        }
      },
      
      // Fabric management
      AddFabric: 'add-fabric',
      
      // Profile and settings
      MyProfile: {
        path: 'profile/:userId?',
        parse: {
          userId: (userId: string) => userId || undefined
        }
      },
      // MyProfile route is already defined above with parameters
      EditProfile: 'edit-profile',
      UnifiedSettings: {
        path: 'settings',
        parse: {
          section: (section: string) => section || undefined
        }
      },
      PaymentMethods: 'payment-methods',
      NotificationSettings: 'notification-settings',
      
      // QR Scanner
      QRScanner: 'scan',
      
      // Reports & Analytics
      Reports: 'reports',
      FinancialReports: {
        path: 'financial-reports',
        parse: {
          reportType: (reportType: string) => reportType || undefined
        }
      },
      ProfitLoss: {
        path: 'profit-loss',
        parse: {
          dateRange: (dateRange: string) => dateRange || undefined
        }
      },
      TaxSummary: {
        path: 'tax-summary',
        parse: {
          year: (year: string) => year ? parseInt(year, 10) : undefined
        }
      },
      
      // Help and support
      HelpFAQ: 'help',
      ContactSupport: 'support',
      About: 'about',
      
      // Business management
      Sales: 'sales',
      EmployeeAttendance: 'attendance',
      BusinessHours: 'business-hours',
      
      // Inventory
      UnifiedInventory: 'inventory',
      
      // Financial
      Accounting: 'accounting',
      FinancialManagement: 'financial',
      ExpenseForm: {
        path: 'expense',
        parse: {
          expenseId: (expenseId: string) => expenseId || undefined
        }
      },
      CashReconciliation: 'cash-reconciliation',
      
      // Subscription
      Subscription: 'subscription',
      
      // Employee management
      EmployeeManagement: 'employees',
      AddEmployee: 'add-employee',
      
      // Data management
      DataManagement: 'data-management',
      ImportData: 'import-data',
      
      // Notifications
      Notifications: 'notifications',
      
      // Search
      UniversalSearch: {
        path: 'search',
        parse: {
          query: (query: string) => query || '',
          category: (category: string) => category || undefined
        }
      }
    }
  },
  
  // Custom URL handling
  async getInitialURL() {
    // Handle custom URL schemes
    const url = await Linking.getInitialURL();
    return url;
  },
  
  subscribe(listener) {
    // Handle URL changes when app is running
    const onReceiveURL = ({ url }: { url: string }) => {
      listener(url);
    };
    
    const subscription = Linking.addEventListener('url', onReceiveURL);
    
    return () => {
      subscription?.remove();
    };
  }
};

// Deep link handlers for specific actions
export const registerDeepLinkHandlers = () => {
  const navigationService = NavigationService;
  
  // Order-related deep links
  navigationService.registerDeepLinkHandler(
    '/orders/:orderId',
    (params: object) => {
      navigationService.navigate('OrderDetails', { orderId: params.orderId });
    }
  );
  
  // Customer-related deep links
  navigationService.registerDeepLinkHandler(
    '/customers/:customerId',
    (params: object) => {
      navigationService.navigate('CustomerDetails', { customerId: (params as any).customerId });
    }
  );
  
  // Product-related deep links
  navigationService.registerDeepLinkHandler(
    '/products/:productId',
    (params: object) => {
      navigationService.navigate('Main', { screen: 'Products' });
    }
  );
  
  // Profile deep links
  navigationService.registerDeepLinkHandler(
    '/profile/:userId?',
    (params: object) => {
      navigationService.navigate('MyProfile');
    }
  );
  
  // QR Scanner deep link
  navigationService.registerDeepLinkHandler(
    '/scan',
    () => {
      navigationService.navigate('QRScanner');
    }
  );
};

// Utility functions for generating deep links
export const generateDeepLink = {
  order: (orderId: string) => `${URL_SCHEMES.PRODUCTION} ://orders/${orderId} ,`
  customer: (customerId: string) => `${URL_SCHEMES.PRODUCTION} ://customers/${customerId} ,`
  product: (productId: string) => `${URL_SCHEMES.PRODUCTION} ://products/${productId} ,`
  profile: (userId?: string) => userId 
    ? `${URL_SCHEMES.PRODUCTION} ://profile/${userId}` 
    : `${URL_SCHEMES.PRODUCTION} ://profile`,
  scan: () => `${URL_SCHEMES.PRODUCTION} ://scan`,
  dashboard: () => `${URL_SCHEMES.PRODUCTION} ://dashboard`
};