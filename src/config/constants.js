// Application Constants
export const APP_CONFIG = {
  name: '<PERSON><PERSON>',
  version: '1.0.0',
  author: 'Tailora Solutions',
  supportEmail: '<EMAIL>'};

// API Configuration
export const API_CONFIG = {
  baseURL: process.env.EXPO_PUBLIC_API_URL || 'https://api.yourapp.com',
  timeout: 10000,
  retryAttempts: 3};

// Storage Keys
export const STORAGE_KEYS = {
  THEME: 'darkMode',
  USER_DATA: 'userData',
  TAILOR_DATA: 'tailorData',
  SETTINGS: 'appSettings',
  CACHE: 'appCache'};

// Measurement Units Configuration
export const MEASUREMENT_UNITS = {
  CENTIMETERS: {
    key: 'cm',
    label: 'Centimeters (cm)',
    shortLabel: 'cm',
    description: 'Metric system - commonly used worldwide',
    conversionFactor: 1, // Base unit for conversion
  },
  INCHES: {
    key: 'inches',
    label: 'Inches (in)',
    shortLabel: 'in',
    description: 'Imperial system - commonly used in US/UK',
    conversionFactor: 2.54, // 1 inch = 2.54 cm
  },
  FEET: {
    key: 'feet',
    label: 'Feet (ft)',
    shortLabel: 'ft',
    description: 'Imperial system - for larger measurements',
    conversionFactor: 30.48, // 1 foot = 30.48 cm
  }};

// Default measurement unit
export const DEFAULT_MEASUREMENT_UNIT = MEASUREMENT_UNITS.INCHES.key;

// Business Rules
export const BUSINESS_RULES = {
  MAX_PRODUCTS: 1000,
  MAX_ORDERS_PER_DAY: 500,
  MIN_STOCK_ALERT: 5,
  MAX_DISCOUNT_PERCENT: 50,
  ORDER_STATUSES: ['Pending', 'In Progress', 'Completed', 'Cancelled'],
  PAYMENT_METHODS: ['Cash', 'Card', 'Digital Wallet', 'Bank Transfer']};

// UI Constants
export const UI_CONSTANTS = {
  ANIMATION_DURATION: 300,
  DEBOUNCE_DELAY: 500,
  PAGINATION_SIZE: 20,
  MAX_SEARCH_RESULTS: 50,
  BOTTOM_SHEET_SNAP_POINTS: ['25%', '50%', '90%']};

// Validation Rules
export const VALIDATION_RULES = {
  PRODUCT_NAME: {
    minLength: 2,
    maxLength: 100,
    required: true},
  PRICE: {
    min: 0.01,
    max: 9999.99,
    required: true},
  STOCK: {
    min: 0,
    max: 10000,
    required: true},
  CUSTOMER_NAME: {
    minLength: 2,
    maxLength: 50,
    required: true},
  PHONE: {
    pattern: /^[\+]?[1-9][\d]{0,15}$/,
    required: false},
  EMAIL: {
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    required: false}};

// Financial Management
export const FINANCIAL_CONFIG = {
  EXPENSE_CATEGORIES: [
    'Fabrics & Materials',
    'Utilities',
    'Staff Wages',
    'Rent',
    'Sewing Equipment',
    'Marketing',
    'Insurance',
    'Supplies',
    'Maintenance',
    'Other'
  ],
  PAYMENT_METHODS: [
    'Cash',
    'Credit Card',
    'Debit Card',
    'Digital Wallet',
    'Bank Transfer',
    'Check',
    'Store Credit'
  ],
  TAX_RATES: {
    SALES_TAX: 0.08, // 8% default sales tax
    INCOME_TAX: 0.21, // 21% corporate tax rate
    PAYROLL_TAX: 0.153, // 15.3% payroll tax
  },
  CURRENCY: {
    SYMBOL: '৳',
    CODE: 'BDT',
    DECIMAL_PLACES: 2},
  FISCAL_YEAR_START: 1, // January (1-12)
  RECONCILIATION_TOLERANCE: 5.00, // ৳5 tolerance for cash reconciliation
};

// Feature Flags
export const FEATURE_FLAGS = {
  ENABLE_ANALYTICS: true,
  ENABLE_PUSH_NOTIFICATIONS: true,
  ENABLE_OFFLINE_MODE: true,
  ENABLE_MULTI_STORE: false,
  ENABLE_INVENTORY_TRACKING: true,
  ENABLE_CUSTOMER_LOYALTY: false,
  ENABLE_ADVANCED_REPORTS: true,
  ENABLE_FINANCIAL_MANAGEMENT: true};

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network connection failed. Please check your internet connection.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  SAVE_ERROR: 'Failed to save data. Please try again.',
  LOAD_ERROR: 'Failed to load data. Please refresh the app.',
  PERMISSION_ERROR: 'Permission denied. Please check app permissions.',
  GENERIC_ERROR: 'Something went wrong. Please try again.'};

// Success Messages
export const SUCCESS_MESSAGES = {
  SAVE_SUCCESS: 'Data saved successfully!',
  UPDATE_SUCCESS: 'Updated successfully!',
  DELETE_SUCCESS: 'Deleted successfully!',
  EXPORT_SUCCESS: 'Data exported successfully!',
  IMPORT_SUCCESS: 'Data imported successfully!'};
