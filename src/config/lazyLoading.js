/**
 * Lazy Loading Configuration
 * Centralized configuration for lazy loading behavior
 */

export const LAZY_LOADING_CONFIG = {
  // Global settings
  enabled: true,
  
  // Loading timeouts (in milliseconds)
  timeouts: {
    default: 10000,
    heavy: 15000,
    network: 30000
  },
  
  // Retry settings
  retries: {
    default: 3,
    critical: 5,
    optional: 1
  },
  
  // Preloading settings
  preload: {
    enabled: true,
    delay: 2000, // Preload after 2 seconds of idle time
    priority: ['ProfileScreen', 'SettingsScreen'] // High priority components to preload
  },
  
  // Component categories
  categories: {
    critical: [
      'HomeScreen',
      'LoginScreen',
      'DashboardScreen'
    ],
    heavy: [
      'ReportsScreen',
      'ChartsContainer',
      'DataVisualization'
    ],
    optional: [
      'HelpScreen',
      'AboutScreen',
      'FeedbackForm'
    },
  
  // Loading strategies
  strategies: {
    immediate: {
      delay: 0,
      timeout: 5000,
      retries: 1
    },
    lazy: {
      delay: 100,
      timeout: 10000,
      retries: 3
    },
    background: {
      delay: 2000,
      timeout: 30000,
      retries: 5
    }
  },
  
  // Error handling
  errorHandling: {
    showRetryButton: true,
    logErrors: true,
    fallbackComponent: 'ErrorBoundary'
  }
};

/**
 * Get configuration for a specific component
 * @param {string} componentName - Name of the component
 * @returns {Object] - Configuration object
 */
export const getComponentConfig = (componentName) => {
  const { categories, strategies  } = LAZY_LOADING_CONFIG;
  
  if (categories.critical.includes(componentName)) {
    return { ...strategies.immediate, priority: 'critical'  };
  }
  
  if (categories.heavy.includes(componentName)) {
    return { ...strategies.lazy, priority: 'heavy'  };
  }
  
  if (categories.optional.includes(componentName)) {
    return { ...strategies.background, priority: 'optional'  };
  }
  
  return { ...strategies.lazy, priority: 'default'  };
];

/**
 * Check if lazy loading is enabled for a component
 * @param {string} componentName - Name of the component
 * @returns {boolean] - Whether lazy loading is enabled
 */
export const isLazyLoadingEnabled = (componentName) => {
  return LAZY_LOADING_CONFIG.enabled && 
         !LAZY_LOADING_CONFIG.categories.critical.includes(componentName);
};

export default LAZY_LOADING_CONFIG;
