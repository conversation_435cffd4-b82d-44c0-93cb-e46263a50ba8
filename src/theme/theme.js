import { MD3LightTheme } from 'react-native-paper';
import { MD3DarkTheme } from 'react-native-paper';
import FontService from '../services/FontService';

// Get MD3 Typography with platform-optimized fonts
const MD3_TYPOGRAPHY = FontService.getTypographyStyles();

const lightTheme = {
  ...MD3LightTheme,
  fonts: MD3_TYPOGRAPHY,
  colors: {
    ...MD3LightTheme.colors,
    primary: '#1877F2', // Facebook Blue
    primaryContainer: '#E3F2FD',
    secondary: '#42A5F5', // Lighter Facebook Blue
    secondaryContainer: '#E1F5FE',
    tertiary: '#FF6B35', // Facebook Orange accent
    tertiaryContainer: '#FFE0B2',
    surface: '#FFFFFF',
    surfaceVariant: '#F0F2F5', // Facebook Surface Gray
    background: '#F8F9FA', // Facebook Light Gray
    error: '#F44336',
    errorContainer: '#FFEBEE',
    onPrimary: '#FFFFFF',
    onPrimaryContainer: '#0D47A1',
    onSecondary: '#FFFFFF',
    onSecondaryContainer: '#01579B',
    onTertiary: '#FFFFFF',
    onTertiaryContainer: '#E65100',
    onSurface: '#1C1E21', // Facebook Dark Text
    onSurfaceVariant: '#65676B', // Facebook Secondary Text
    onError: '#FFFFFF',
    onErrorContainer: '#B71C1C',
    onBackground: '#1C1E21',
    outline: '#CED0D4', // Facebook Border Gray
    outlineVariant: '#E4E6EA',
    inverseSurface: '#242526', // Facebook Dark Mode Surface
    inverseOnSurface: '#E4E6EA',
    inversePrimary: '#4FC3F7',
  },
};

const darkTheme = {
  ...MD3DarkTheme,
  fonts: MD3_TYPOGRAPHY,
  colors: {
    ...MD3DarkTheme.colors,
    primary: '#4FC3F7', // Light Facebook Blue for dark mode
    primaryContainer: '#0D47A1',
    secondary: '#81C784', // Light green accent
    secondaryContainer: '#2E7D32',
    tertiary: '#FFB74D', // Light orange accent
    tertiaryContainer: '#F57C00',
    surface: '#242526', // Facebook Dark Surface
    surfaceVariant: '#3A3B3C', // Facebook Dark Surface Variant
    background: '#18191A', // Facebook Dark Background
    error: '#EF5350',
    errorContainer: '#C62828',
    onPrimary: '#0D47A1',
    onPrimaryContainer: '#E3F2FD',
    onSecondary: '#2E7D32',
    onSecondaryContainer: '#E8F5E8',
    onTertiary: '#F57C00',
    onTertiaryContainer: '#FFF3E0',
    onSurface: '#E4E6EA', // Facebook Dark Text
    onSurfaceVariant: '#B0B3B8', // Facebook Dark Secondary Text
    onError: '#C62828',
    onErrorContainer: '#FFEBEE',
    onBackground: '#E4E6EA',
    outline: '#5A5C5E', // Facebook Dark Border
    outlineVariant: '#3A3B3C',
    inverseSurface: '#F8F9FA',
    inverseOnSurface: '#242526',
    inversePrimary: '#1877F2',
  },
};

export { lightTheme, darkTheme };
