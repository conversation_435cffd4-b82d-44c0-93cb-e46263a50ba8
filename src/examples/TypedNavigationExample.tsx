/**
 * Example demonstrating the new typed navigation system
 * This file shows how to use the improved navigation with TypeScript
 */

import React, { useCallback } from 'react';
import { View, StyleSheet   } from 'react-native';
import { Button   } from 'react-native-paper';
import { Text   } from 'react-native-paper';
import { Card   } from 'react-native-paper';
import { useTypedNavigation, useDeepLinkNavigation   } from '../hooks/useTypedNavigation';
import type { RootStackScreenProps } from '../types/navigation';
import { generateDeepLink   } from '../config/deepLinking';

// Example screen component with proper typing
type Props = RootStackScreenProps<'Main'>;

const TypedNavigationExample: React.FC<Props> = ({ navigation, route }) => {
  // Using the typed navigation hook
  const typedNavigation = useTypedNavigation();
  const deepLinkNav = useDeepLinkNavigation();

  // Example of type-safe navigation
  const handleNavigateToOrder = useCallback(() => {
    // TypeScript will ensure orderId is provided and is a string
    typedNavigation.navigate('OrderDetails', { orderId: '12345' }), []);
  };

  const handleNavigateToCustomer = useCallback(() => {
    // TypeScript will ensure customerId is provided
    deepLinkNav.navigateToCustomer('customer-123'), []);
  };

  const handleNavigateToProfile = useCallback(() => {
    // Optional parameters are properly typed
    deepLinkNav.navigateToProfile('user-456'), []);
  };

  const handleNavigateToSettings = useCallback(() => {
    typedNavigation.navigate('UnifiedSettings'), []);
  };

  const handleGenerateDeepLink = useCallback(() => {
    const link = generateDeepLink.order('order-123'), []);

  };

  // Example of generating deep links
  const shareOrderLink = () => {
    const deepLink = generateDeepLink.order('order-789');

    // In a real app, you'd use Share API or copy to clipboard
  };

  const shareCustomerLink = () => {
    const deepLink = generateDeepLink.customer('customer-456');

  };

  return (
    <View style={styles.style1}>
      <Text variant="headlineMedium" style={styles.style1}>
        Typed Navigation Examples
      </Text>
      
      <Card style={styles.style1}>
        <Card.Title title="Type-Safe Navigation" />
        <Card.Content>
          <Text variant="bodyMedium" style={styles.style1}>
            These buttons demonstrate type-safe navigation with proper parameter validation.
          </Text>
          
          <Button 
            mode="contained" 
            onPress={handleNavigateToOrder}
            style={styles.style1}>
            Navigate to Order Details
          </Button>
          
          <Button 
            mode="contained" 
            onPress={handleNavigateToCustomer}
            style={styles.style1}>
            Navigate to Customer
          </Button>
          
          <Button 
            mode="contained" 
            onPress={handleNavigateToProfile}
            style={styles.style1}>
            Navigate to Profile
          </Button>
          
          <Button 
            mode="contained" 
            onPress={handleNavigateToSettings}
            style={styles.style1}>
            Navigate to Settings
          </Button>
        </Card.Content>
      </Card>
      
      <Card style={styles.style1}>
        <Card.Title title="Deep Link Generation" />
        <Card.Content>
          <Text variant="bodyMedium" style={styles.style1}>
            Generate shareable deep links for app content.
          </Text>
          
          <Button 
            mode="outlined" 
            onPress={shareOrderLink}
            style={styles.style1}>
            Generate Order Deep Link
          </Button>
          
          <Button 
            mode="outlined" 
            onPress={shareCustomerLink}
            style={styles.style1}>
            Generate Customer Deep Link
          </Button>
        </Card.Content>
      </Card>
      
      <Card style={styles.style1}>
        <Card.Title title="Navigation Benefits" />
        <Card.Content>
          <Text variant="bodyMedium">✅ Type safety for all navigation calls</Text>
          <Text variant="bodyMedium">✅ Auto-completion for screen names</Text>
          <Text variant="bodyMedium">✅ Parameter validation at compile time</Text>
          <Text variant="bodyMedium">✅ Deep linking support</Text>
          <Text variant="bodyMedium">✅ Better developer experience</Text>
          <Text variant="bodyMedium">✅ Reduced runtime errors</Text>
        </Card.Content>
      </Card>
    </View>
  );
};






export default React.memo(TypedNavigationExample);

// Example of how to use in other components:

// 1. Basic navigation with parameters
// const navigation = useTypedNavigation<'Orders'>();
// navigation.navigate('OrderDetails', { orderId: 'abc123' });

// 2. Using deep link navigation
// const { navigateToOrder  } = useDeepLinkNavigation();
// navigateToOrder('order-456');

// 3. Generating shareable links
// const orderLink = generateDeepLink.order('order-789');
// Share.share({ message: orderLink });

// 4. Type-safe route parameters
// const route = useTypedRoute<'OrderDetails'>();
// const { orderId  } = route.params; // TypeScript knows this is a string
const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f5f5f5'
  },
  title: {
    textAlign: 'center',
    marginBottom: 24,
    fontWeight: 'bold'
  },
  card: {
    marginBottom: 16,
    elevation: 4
  },
  description: {
    marginBottom: 16,
    color: '#666'
  },
  button: {
    marginBottom: 8
  },
  style1: {
  }});