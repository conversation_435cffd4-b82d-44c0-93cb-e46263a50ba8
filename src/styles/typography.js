import { StyleSheet   } from 'react-native';
import { typography, colors   } from './theme';

/**
 * Typography utility styles
 * Auto-generated by stylesheet-optimization.js
 */

// Create a function that returns styles to avoid module-level theme access
export const createTextStyles = (themeColors = colors) => StyleSheet.create({
  // Headings
  h1: {
    fontSize: typography.fontSize.xxxl,
    fontWeight: typography.fontWeight.bold,
    lineHeight: typography.lineHeight.tight,
    color: themeColors.text},
  h2: {
    fontSize: typography.fontSize.xxl,
    fontWeight: typography.fontWeight.bold,
    lineHeight: typography.lineHeight.tight,
    color: themeColors.text},
  h3: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.semibold,
    lineHeight: typography.lineHeight.normal,
    color: themeColors.text},
  h4: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    lineHeight: typography.lineHeight.normal,
    color: themeColors.text},
  h5: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium,
    lineHeight: typography.lineHeight.normal,
    color: themeColors.text},
  h6: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    lineHeight: typography.lineHeight.normal,
    color: themeColors.text},
  
  // Body text
  body: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.normal,
    lineHeight: typography.lineHeight.normal,
    color: themeColors.text},
  bodySmall: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.normal,
    lineHeight: typography.lineHeight.normal,
    color: themeColors.text},
  bodyLarge: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.normal,
    lineHeight: typography.lineHeight.normal,
    color: themeColors.text},
  
  // Text variants
  caption: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.normal,
    lineHeight: typography.lineHeight.normal,
    color: themeColors.textSecondary},
  overline: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.medium,
    lineHeight: typography.lineHeight.normal,
    color: themeColors.textSecondary,
    textTransform: 'uppercase',
    letterSpacing: 1},
  
  // Text alignment
  textLeft: { textAlign: 'left' },
  textCenter: { textAlign: 'center' },
  textRight: { textAlign: 'right' },
  textJustify: { textAlign: 'justify' },
  
  // Text colors
  textPrimary: { color: themeColors.primary },
  textSecondary: { color: themeColors.textSecondary },
  textSuccess: { color: themeColors.success },
  textDanger: { color: themeColors.danger },
  textWarning: { color: themeColors.warning },
  textInfo: { color: themeColors.info },
  textLight: { color: themeColors.textLight },
  
  // Text decoration
  underline: { textDecorationLine: 'underline' },
  lineThrough: { textDecorationLine: 'line-through' },
  noDecoration: { textDecorationLine: 'none' },
  
  // Text transform
  uppercase: { textTransform: 'uppercase' },
  lowercase: { textTransform: 'lowercase' },
  capitalize: { textTransform: 'capitalize' }});

// Export default styles using fallback colors for backward compatibility
export const textStyles = createTextStyles();

export default textStyles;
