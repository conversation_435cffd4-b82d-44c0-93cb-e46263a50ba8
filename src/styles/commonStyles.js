import { StyleSheet   } from 'react-native';

/**
 * Common styles used across the application
 * Auto-generated by stylesheet-optimization.js
 */

export const commonStyles = StyleSheet.create({
  // Layout
  container: {
    flex: 1,
    backgroundColor: '#fff'},
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center'},
  row: {
    flexDirection: 'row',
    alignItems: 'center'},
  column: {
    flexDirection: 'column'},
  spaceBetween: {
    justifyContent: 'space-between'},
  spaceAround: {
    justifyContent: 'space-around'},
  
  // Spacing
  padding: {
    padding: 16},
  paddingHorizontal: {
    paddingHorizontal: 16},
  paddingVertical: {
    paddingVertical: 16},
  margin: {
    margin: 16},
  marginHorizontal: {
    marginHorizontal: 16},
  marginVertical: {
    marginVertical: 16},
  
  // Borders
  border: {
    borderWidth: 1,
    borderColor: '#ddd'},
  borderRadius: {
    borderRadius: 8},
  
  // Shadows
  shadow: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5},
  
  // Text
  textCenter: {
    textAlign: 'center'},
  textBold: {
    fontWeight: 'bold'},
  
  // Colors
  backgroundPrimary: {
    backgroundColor: '#007AFF'},
  backgroundSecondary: {
    backgroundColor: '#f8f9fa'},
  backgroundDanger: {
    backgroundColor: '#dc3545'},
  backgroundSuccess: {
    backgroundColor: '#28a745'}});

export default commonStyles;
