import { StyleSheet   } from 'react-native';
import { spacing   } from './theme';

/**
 * Layout utility styles
 * Auto-generated by stylesheet-optimization.js
 */

export const layout = StyleSheet.create({
  // Flex
  flex1: { flex: 1 },
  flex2: { flex: 2 },
  flex3: { flex: 3 },
  flexGrow: { flexGrow: 1 },
  flexShrink: { flexShrink: 1 },
  
  // Direction
  row: { flexDirection: 'row' },
  column: { flexDirection: 'column' },
  rowReverse: { flexDirection: 'row-reverse' },
  columnReverse: { flexDirection: 'column-reverse' },
  
  // Justify Content
  justifyStart: { justifyContent: 'flex-start' },
  justifyEnd: { justifyContent: 'flex-end' },
  justifyCenter: { justifyContent: 'center' },
  justifyBetween: { justifyContent: 'space-between' },
  justifyAround: { justifyContent: 'space-around' },
  justifyEvenly: { justifyContent: 'space-evenly' },
  
  // Align Items
  alignStart: { alignItems: 'flex-start' },
  alignEnd: { alignItems: 'flex-end' },
  alignCenter: { alignItems: 'center' },
  alignStretch: { alignItems: 'stretch' },
  alignBaseline: { alignItems: 'baseline' },
  
  // Align Self
  alignSelfStart: { alignSelf: 'flex-start' },
  alignSelfEnd: { alignSelf: 'flex-end' },
  alignSelfCenter: { alignSelf: 'center' },
  alignSelfStretch: { alignSelf: 'stretch' },
  alignSelfBaseline: { alignSelf: 'baseline' },
  
  // Position
  absolute: { position: 'absolute' },
  relative: { position: 'relative' },
  
  // Common layouts
  container: {
    flex: 1,
    padding: spacing.md},
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center'},
  fullScreen: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0}});

export default layout;
