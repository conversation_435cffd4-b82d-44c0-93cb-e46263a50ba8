/**
 * Build-Time Reset Handler
 * Detects and handles database resets triggered during build
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import MigrationService from '../services/MigrationService';
import SQLiteService from '../services/SQLiteService';

class BuildTimeResetHandler {
  constructor() {
    this.resetMarkerChecked = false;
  }

  /**
   * Check if a build-time reset was requested
   */
  async checkForBuildTimeReset() {
    if (this.resetMarkerChecked) return false;
    
    try {
      // Check for database reset marker (would be bundled with the app)
      const resetMarker = await this.getResetMarker();
      
      if(resetMarker && resetMarker.buildCleanup) {

        await this.performBuildTimeReset(resetMarker);
        this.resetMarkerChecked = true;
        return true;
      }
      
      this.resetMarkerChecked = true;
      return false;
    } catch (error) {
      console.warn('Error checking for build-time reset:', error);
      this.resetMarkerChecked = true;
      return false;
    }
  }

  /**
   * Get reset marker (this would be bundled with the app if created during build)
   */
  async getResetMarker() {
    try {
      // In a real implementation, this would check for a bundled reset marker file
      // For now, we'll check AsyncStorage for a reset flag
      const resetFlag = await AsyncStorage.getItem('build_time_reset_requested');
      
      if(resetFlag) {
        return { buildCleanup: true,
          resetTimestamp: new Date().toISOString(),
          clearSampleData: true,
          resetMigration: true
       };
      }
      
      return null;
    } catch (error) {
      console.warn('Error getting reset marker:', error);
      return null;
    }
  }

  /**
   * Perform the build-time reset
   */
  async performBuildTimeReset(resetMarker) {
    try {

      // Step 1: Clear all AsyncStorage data
      await this.clearAsyncStorage();
      
      // Step 2: Reset SQLite database if available
      await this.resetSQLiteDatabase();
      
      // Step 3: Reset migration status
      await this.resetMigrationStatus();
      
      // Step 4: Clear the reset flag
      await AsyncStorage.removeItem('build_time_reset_requested');

      return { success: true,
        message: 'Database reset completed successfully',
        timestamp: new Date().toISOString()
     };
      
    } catch (error) {
      console.error('❌ Build-time reset failed:', error);
      throw error;
    }
  }

  /**
   * Clear all AsyncStorage data
   */
  async clearAsyncStorage() {
    try {

      const keysToRemove = [
        'tailorData',
        'orders',
        'customers',
        'tailorMeasurements',
        'tailorFabrics',
        'tailorAppointments',
        'financial_expenses',
        'cash_reconciliations',
        'sqlite_migration_completed',
        'app_settings',
        'user_preferences'
      ];
      await AsyncStorage.multiRemove(keysToRemove);

    } catch (error) {
      console.error('Error clearing AsyncStorage:', error);
      throw error;
    }
  }

  /**
   * Reset SQLite database
   */
  async resetSQLiteDatabase() {
    try {

      if(SQLiteService.isInitialized) {
        await SQLiteService.resetDatabase();

      } else {

      }
      
    } catch (error) {
      console.error('Error resetting SQLite database:', error);
      // Don't throw - this is not critical
    }
  }

  /**
   * Reset migration status
   */
  async resetMigrationStatus() {
    try {

      // Reset migration service state
      if(MigrationService) {
        MigrationService.migrationCompleted = false;
        MigrationService.useSQLite = false;
      }

    } catch (error) {
      console.error('Error resetting migration status:', error);
      // Don't throw - this is not critical
    }
  }

  /**
   * Request a build-time reset (for testing purposes)
   */
  async requestBuildTimeReset() {
    try {
      await AsyncStorage.setItem('build_time_reset_requested', 'true');

      return true;
    } catch (error) {
      console.error('Error requesting build-time reset:', error);
      return false;
    }
  }

  /**
   * Check if app is in a clean state
   */
  async isCleanState() {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const dataKeys = keys.filter(key => 
        key.startsWith('tailor') || 
        key === 'orders' || 
        key === 'customers' ||
        key === 'sqlite_migration_completed'
      );
      
      return dataKeys.length === 0;
    } catch (error) {
      console.error('Error checking clean state:', error);
      return false;
    }
  }
}

export default new BuildTimeResetHandler();
