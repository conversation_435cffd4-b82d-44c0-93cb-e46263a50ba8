/**
 * FormValidation - Comprehensive form validation utilities
 * MD3-compliant validation with proper error handling
 */

export class FormValidator {
  constructor() {
    this.rules = {};
    this.errors = {};
  }

  /**
   * Add validation rule for a field
   */
  addRule(fieldName, rule) {
    if(!this.rules[fieldName]) {
      this.rules[fieldName] = [];
    }
    this.rules[fieldName].push(rule);
    return this;
  }

  /**
   * Validate all fields
   */
  validate(data) {
    this.errors = {};
    let isValid = true;

    for (const [fieldName, rules] of Object.entries(this.rules)) {
      const fieldValue = data[fieldName];
      const fieldErrors = [];

      for(const rule of rules) {
        const result = rule.validate(fieldValue, data);
        if(!result.isValid) {
          fieldErrors.push(result.message);
          isValid = false;
        }
      }

      if(fieldErrors.length > 0) {
        this.errors[fieldName] = fieldErrors;
      }
    }

    return { isValid,
      errors: this.errors,
   };
  }

  /**
   * Get errors for a specific field
   */
  getFieldErrors(fieldName) {
    return this.errors[fieldName] || [];
  }

  /**
   * Check if field has errors
   */
  hasFieldError(fieldName) {
    return this.errors[fieldName] && this.errors[fieldName].length > 0;
  }

  /**
   * Clear all errors
   */
  clearErrors() {
    this.errors = {};
  }

  /**
   * Clear errors for specific field
   */
  clearFieldErrors(fieldName) {
    delete this.errors[fieldName];
  }
}

// Validation Rules
export const ValidationRules = {
  required: (message = 'This field is required') => ({
    validate: (value) => ({
      isValid: value !== null && value !== undefined && String(value).trim() !== '',
      message,
    }),
  }),

  minLength: (min, message) => ({
    validate: (value) => ({
      isValid: !value || String(value).length >= min,
      message: message || `Minimum ${min} characters required`,
    }),
  }),

  maxLength: (max, message) => ({
    validate: (value) => ({
      isValid: !value || String(value).length <= max,
      message: message || `Maximum ${max} characters allowed`,
    }),
  }),

  email: (message = 'Please enter a valid email address') => ({
    validate: (value) => {
      if (!value) return { isValid: true, message  };
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return { isValid: emailRegex.test(value),
        message,
     };
    },
  }),

  phone: (message = 'Please enter a valid phone number') => ({
    validate: (value) => {
      if (!value) return { isValid: true, message  };
      const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
      return { isValid: phoneRegex.test(value.replace(/[\s\-\(\)]/g, '')),
        message,
     };
    },
  }),

  numeric: (message = 'Please enter a valid number') => ({
    validate: (value) => {
      if (!value) return { isValid: true, message  };
      return { isValid: !isNaN(value) && !isNaN(parseFloat(value)),
        message,
     };
    },
  }),

  positive: (message = 'Value must be positive') => ({
    validate: (value) => {
      if (!value) return { isValid: true, message  };
      const num = parseFloat(value);
      return { isValid: !isNaN(num) && num > 0,
        message,
     };
    },
  }),

  min: (min, message) => ({
    validate: (value) => {
      if (!value) return { isValid: true, message  };
      const num = parseFloat(value);
      return {
        isValid: !isNaN(num) && num >= min,
        message: message || `Value must be at least ${min}`,
      };
    },
  }),

  max: (max, message) => ({
    validate: (value) => {
      if (!value) return { isValid: true, message  };
      const num = parseFloat(value);
      return {
        isValid: !isNaN(num) && num <= max,
        message: message || `Value must be at most ${max}`,`
      };
    },
  }),

  pattern: (regex, message = 'Invalid format') => ({
    validate: (value) => {
      if (!value) return { isValid: true, message  };
      return { isValid: regex.test(value),
        message,
     };
    },
  }),

  custom: (validatorFn, message = 'Invalid value') => ({
    validate: (value, allData) => {
      try {
        const result = validatorFn(value, allData);
        return { isValid: result === true,
          message: typeof result === 'string' ? result : message,
       };
      } catch (error) {
        return { isValid: false,
          message: 'Validation error occurred',
       };
      }
    },
  }),
};

// Pre-built validators for common forms
export const FormValidators = {
  customer: () => {
    const validator = new FormValidator();
    return validator
      .addRule('name', ValidationRules.required('Customer name is required'))
      .addRule('name', ValidationRules.minLength(2, 'Name must be at least 2 characters'))
      .addRule('phone', ValidationRules.required('Phone number is required'))
      .addRule('phone', ValidationRules.phone())
      .addRule('email', ValidationRules.email());
  },

  order: () => {
    const validator = new FormValidator();
    return validator
      .addRule('customerName', ValidationRules.required('Customer name is required'))
      .addRule('items', ValidationRules.custom(
        (items) => items && items.length > 0,
        'At least one item is required'
      ))
      .addRule('dueDate', ValidationRules.required('Due date is required'))
      .addRule('total', ValidationRules.required('Total amount is required'))
      .addRule('total', ValidationRules.positive('Total must be positive'));
  },

  product: () => {
    const validator = new FormValidator();
    return validator
      .addRule('name', ValidationRules.required('Product name is required'))
      .addRule('name', ValidationRules.minLength(2, 'Name must be at least 2 characters'))
      .addRule('price', ValidationRules.required('Price is required'))
      .addRule('price', ValidationRules.positive('Price must be positive'))
      .addRule('category', ValidationRules.required('Category is required'));
  },

  fabric: () => {
    const validator = new FormValidator();
    return validator
      .addRule('name', ValidationRules.required('Fabric name is required'))
      .addRule('type', ValidationRules.required('Fabric type is required'))
      .addRule('pricePerMeter', ValidationRules.required('Price per meter is required'))
      .addRule('pricePerMeter', ValidationRules.positive('Price must be positive'))
      .addRule('stock', ValidationRules.numeric('Stock must be a number'))
      .addRule('stock', ValidationRules.min(0, 'Stock cannot be negative'));
  },

  measurement: () => {
    const validator = new FormValidator();
    return validator
      .addRule('customerName', ValidationRules.required('Customer name is required'))
      .addRule('chest', ValidationRules.numeric('Chest measurement must be a number'))
      .addRule('waist', ValidationRules.numeric('Waist measurement must be a number'))
      .addRule('hip', ValidationRules.numeric('Hip measurement must be a number'));
  },
};

// Utility functions
export const validateField = (value, rules) => {
  for(const rule of rules) {
    const result = rule.validate(value);
    if(!result.isValid) {
      return result;
    }
  }
  return { isValid: true, message: ''  };
};

export const getFieldError = (errors, fieldName) => {
  const fieldErrors = errors[fieldName];
  return fieldErrors && fieldErrors.length > 0 ? fieldErrors[0] : '';
};

export const hasFieldError = (errors, fieldName) => {
  return errors[fieldName] && errors[fieldName].length > 0;
};
