import React, { useState, useCallback, useEffect } from 'react';
import { VALIDATION_RULES } from '../config/constants';

/**
 * Validation utility class for form validation
 */
export class Validator {
  static validateRequired(value, fieldName) {
    if (!value || (typeof value === 'string' && value.trim() === '')) {
      return `${fieldName} is required`;
    }
    return null;
  }

  static validateLength(value, min, max, fieldName) {
    if (value && value.length < min) {
      return `${fieldName} must be at least ${min} characters`;
    }
    if (value && value.length > max) {
      return `${fieldName} must not exceed ${max} characters`;
    }
    return null;
  }

  static validatePattern(value, pattern, fieldName) {
    if (value && !pattern.test(value)) {
      return `${fieldName} format is invalid`;
    }
    return null;
  }

  static validateRange(value, min, max, fieldName) {
    const numValue = parseFloat(value);
    if (isNaN(numValue)) {
      return `${fieldName} must be a valid number`;
    }
    if (numValue < min) {
      return `${fieldName} must be at least ${min}`;
    }
    if (numValue > max) {
      return `${fieldName} must not exceed ${max}`;
    }
    return null;
  }

  static validateProduct(product) {
    const errors = {};

    // Name validation
    if (VALIDATION_RULES.PRODUCT_NAME.required) {
      const nameError = this.validateRequired(product.name, 'Product name');
      if (nameError) errors.name = nameError;
    }

    if (!errors.name) {
      const lengthError = this.validateLength(
        product.name,
        VALIDATION_RULES.PRODUCT_NAME.minLength,
        VALIDATION_RULES.PRODUCT_NAME.maxLength,
        'Product name'
      );
      if (lengthError) errors.name = lengthError;
    }

    // Price validation
    if (VALIDATION_RULES.PRICE.required) {
      const priceError = this.validateRequired(product.price, 'Price');
      if (priceError) errors.price = priceError;
    }

    if (!errors.price) {
      const rangeError = this.validateRange(
        product.price,
        VALIDATION_RULES.PRICE.min,
        VALIDATION_RULES.PRICE.max,
        'Price'
      );
      if (rangeError) errors.price = rangeError;
    }

    // Stock validation
    if (VALIDATION_RULES.STOCK.required) {
      const stockError = this.validateRequired(product.stock, 'Stock');
      if (stockError) errors.stock = stockError;
    }

    if (!errors.stock) {
      const rangeError = this.validateRange(
        product.stock,
        VALIDATION_RULES.STOCK.min,
        VALIDATION_RULES.STOCK.max,
        'Stock'
      );
      if (rangeError) errors.stock = rangeError;
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }

  static validateCustomer(customer) {
    const errors = {};

    // Name validation
    if (VALIDATION_RULES.CUSTOMER_NAME.required) {
      const nameError = this.validateRequired(customer.name, 'Customer name');
      if (nameError) errors.name = nameError;
    }

    if (!errors.name) {
      const lengthError = this.validateLength(
        customer.name,
        VALIDATION_RULES.CUSTOMER_NAME.minLength,
        VALIDATION_RULES.CUSTOMER_NAME.maxLength,
        'Customer name'
      );
      if (lengthError) errors.name = lengthError;
    }

    // Phone validation
    if (customer.phone && VALIDATION_RULES.PHONE.pattern) {
      const phoneError = this.validatePattern(
        customer.phone,
        VALIDATION_RULES.PHONE.pattern,
        'Phone number'
      );
      if (phoneError) errors.phone = phoneError;
    }

    // Email validation
    if (customer.email && VALIDATION_RULES.EMAIL.pattern) {
      const emailError = this.validatePattern(
        customer.email,
        VALIDATION_RULES.EMAIL.pattern,
        'Email address'
      );
      if (emailError) errors.email = emailError;
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }

  static validateOrder(order) {
    const errors = {};

    // Customer validation
    if (!order.customer || order.customer.trim() === '') {
      errors.customer = 'Customer is required';
    }

    // Items validation
    if (!order.items || order.items.length === 0) {
      errors.items = 'At least one item is required';
    }

    // Total validation
    if (!order.total || order.total <= 0) {
      errors.total = 'Order total must be greater than 0';
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }
}

/**
 * Form validation hook
 */
export const useFormValidation = (initialData, validationFunction) => {
  const [data, setData] = useState(initialData);
  const [errors, setErrors] = useState({});
  const [isValid, setIsValid] = useState(false);

  const validate = useCallback(() => {
    const result = validationFunction(data);
    setErrors(result.errors);
    setIsValid(result.isValid);
    return result;
  }, [data, validationFunction]);

  const updateField = useCallback((field, value) => {
    setData(prev => ({ ...prev, [field]: value }));
  }, []);

  const reset = useCallback(() => {
    setData(initialData);
    setErrors({});
    setIsValid(false);
  }, [initialData]);

  useEffect(() => {
    validate();
  }, [validate]);

  return {
    data,
    errors,
    isValid,
    updateField,
    validate,
    reset,
    setData
  };
};
