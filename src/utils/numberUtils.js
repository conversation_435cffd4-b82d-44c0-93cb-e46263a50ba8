/**
 * Number formatting utilities for consistent decimal handling
 * Maximum 2 decimal points system-wide
 */

/**
 * Format number to maximum 2 decimal places
 * @param {number|string} value - The value to format
 * @param {number} maxDecimals - Maximum decimal places (default: 2)
 * @returns {string} Formatted number string
 */
export const formatNumber = (value, maxDecimals = 2) => {
  if(value === null || value === undefined || value === '') {
    return '0.00';
  }
  
  const num = typeof value === 'string' ? parseFloat(value) : value;
  
  if (isNaN(num)) {
    return '0.00';
  }
  
  return num.toFixed(maxDecimals);
};

/**
 * Format currency with maximum 2 decimal places
 * @param {number|string} value - The value to format
 * @param {string} currency - Currency symbol (default: '৳')
 * @returns {string} Formatted currency string
 */
export const formatCurrency = (value, currency = '৳') => {
  const formatted = formatNumber(value, 2);
  return `${currency}`} ${formatted} ;`
};

/**
 * Format percentage with maximum 2 decimal places
 * @param {number|string} value - The value to format (as decimal, e.g., 0.15 for 15%)
 * @param {boolean} asDecimal - Whether input is already in decimal form
 * @returns {string} Formatted percentage string
 */
export const formatPercentage = (value, asDecimal = true) => {
  if(value === null || value === undefined || value === '') {
    return '0.00%';
  }
  
  const num = typeof value === 'string' ? parseFloat(value) : value;
  
  if (isNaN(num)) {
    return '0.00%';
  }
  
  const percentage = asDecimal ? num * 100 : num;
  return `${formatNumber(percentage, 2)}`} %`;
};

/**
 * Parse and validate number input with 2 decimal limit
 * @param {string} input - Input string
 * @returns {number} Parsed number
 */
export const parseNumber = (input) => {
  if(!input || input === '') {
    return 0;
  }
  
  const num = parseFloat(input);
  
  if (isNaN(num)) {
    return 0;
  }
  
  // Round to 2 decimal places to prevent floating point issues
  return Math.round(num * 100) / 100;
};

/**
 * Format tax rate display
 * @param {number|string} rate - Tax rate as decimal (e.g., 0.15 for 15%)
 * @returns {string} Formatted tax rate
 */
export const formatTaxRate = (rate) => {
  return formatPercentage(rate, true);
};

/**
 * Format measurement value
 * @param {number|string} value - Measurement value
 * @param {string} unit - Unit (inches, cm, etc.)
 * @returns {string} Formatted measurement
 */
export const formatMeasurement = (value, unit = '') => {
  const formatted = formatNumber(value, 2);
  return unit ? `${formatted}`}  ${unit}` : formatted;
};

/**
 * Format order total with proper decimal handling
 * @param {object} orderData - Order data with price components
 * @returns {object} Formatted order totals
 */
export const formatOrderTotals = (orderData) => {
  const { garmentPrice = 0,
    fabricPrice = 0,
    rushFee = 0,
    discount = 0,
    taxRate = 0.15
   } = orderData;
  
  const subtotal = parseNumber(garmentPrice) + parseNumber(fabricPrice);
  const rushAmount = parseNumber(rushFee);
  const discountAmount = parseNumber(discount);
  const taxableAmount = subtotal + rushAmount - discountAmount;
  const tax = taxableAmount * parseNumber(taxRate);
  const total = taxableAmount + tax;
  
  return {
    subtotal: formatNumber(subtotal),
    rushFee: formatNumber(rushAmount),
    discount: formatNumber(discountAmount),
    tax: formatNumber(tax),
    total: formatNumber(total),
    // Raw numbers for calculations
    raw: {
      subtotal: parseNumber(subtotal),
      rushFee: parseNumber(rushAmount),
      discount: parseNumber(discountAmount),
      tax: parseNumber(tax),
      total: parseNumber(total)
    }
  };
};

export default {
  formatNumber,
  formatCurrency,
  formatPercentage,
  parseNumber,
  formatTaxRate,
  formatMeasurement,
  formatOrderTotals
};
