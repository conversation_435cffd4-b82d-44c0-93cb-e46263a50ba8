/**
 * Data Reset Utility
 * Provides functions to completely reset all app data
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import MigrationService from '../services/MigrationService';
import SQLiteService from '../services/SQLiteService';

class DataResetUtility {
  /**
   * Complete application data reset
   * Clears all databases, caches, and storage
   */
  async resetAllAppData() {
    try {

      // Step 1: Reset all data through MigrationService
      await MigrationService.resetAllData();

      // Step 2: Clear any remaining AsyncStorage keys
      await this.clearAllAsyncStorage();

      // Step 3: Reset any cached data in memory
      await this.clearMemoryCaches();

      // Step 4: Reset order counter for clean start
      await this.resetOrderCounter();

      return { success: true,
        message: 'All application data has been reset successfully',
        timestamp: new Date().toISOString()
     };
    } catch (error) {
      console.error('❌ Error during complete data reset:', error);
      return { success: false,
        error: error.message,
        timestamp: new Date().toISOString()
     };
    }
  }

  /**
   * Clear all AsyncStorage data
   */
  async clearAllAsyncStorage() {
    try {

      // Get all keys
      const keys = await AsyncStorage.getAllKeys();

      if(keys.length > 0) {
        // Remove all keys
        await AsyncStorage.multiRemove(keys);

      } else {

      }
    } catch (error) {
      console.error('Error clearing AsyncStorage:', error);
      throw error;
    }
  }

  /**
   * Clear memory caches
   */
  async clearMemoryCaches() {
    try {

      // Clear any in-memory caches that might exist
      // This is a placeholder for any future cache implementations

    } catch (error) {
      console.error('Error clearing memory caches:', error);
      throw error;
    }
  }

  /**
   * Reset only database data (keep app settings)
   */
  async resetDatabaseOnly() {
    try {

      // Clear database-related AsyncStorage keys
      await AsyncStorage.multiRemove([
        'tailorData',
        'orders',
        'customers',
        'financial_expenses',
        'cash_reconciliations',
        'sqlite_migration_completed'
      ]);

      // Reset SQLite database if available
      if(MigrationService.useSQLite && SQLiteService.isInitialized) {
        await SQLiteService.resetDatabase();
      }

      return { success: true,
        message: 'Database data has been reset successfully',
        timestamp: new Date().toISOString()
     };
    } catch (error) {
      console.error('❌ Error during database reset:', error);
      return { success: false,
        error: error.message,
        timestamp: new Date().toISOString()
     };
    }
  }

  /**
   * Get current data status
   */
  async getDataStatus() {
    try {
      const status = {
        asyncStorageKeys: [],
        sqliteInitialized: false,
        migrationCompleted: false,
        dataExists: false
      };

      // Check AsyncStorage
      status.asyncStorageKeys = await AsyncStorage.getAllKeys();

      // Check SQLite status
      status.sqliteInitialized = SQLiteService.isInitialized;
      status.migrationCompleted = MigrationService.migrationCompleted;

      // Check if any data exists
      if(MigrationService.useSQLite) {
        const products = await MigrationService.getProducts();
        status.dataExists = products.length > 0;
      } else {
        const data = await AsyncStorage.getItem('tailorData');
        status.dataExists = data !== null;
      }

      return status;
    } catch (error) {
      console.error('Error getting data status:', error);
      return { error: error.message
   };
    }
  }

  /**
   * Verify reset completion
   */
  async verifyResetCompletion() {
    try {
      const status = await this.getDataStatus();

      const isCompletelyReset =
        status.asyncStorageKeys.length === 0 &&
        !status.migrationCompleted &&
        !status.dataExists;

      return { isCompletelyReset,
        status,
        message: isCompletelyReset
          ? 'Data reset verification passed - all data cleared'
          : 'Data reset verification failed - some data remains'
   };
    } catch (error) {
      return { isCompletelyReset: false,
        error: error.message,
        message: 'Error during reset verification'
     };
    }
  }

  /**
   * Reset order counter for clean start
   */
  async resetOrderCounter() {
    try {

      await AsyncStorage.removeItem('order_counter');
      return true;
    } catch (error) {
      console.error('Error resetting order counter:', error);
      return false;
    }
  }
}

export default new DataResetUtility();
