/**
 * Validation Utilities
 * Common validation functions used across the application
 */

/**
 * Validate email format
 * @param {string} email - Email to validate
 * @returns {boolean} - True if valid email format
 */
export const validateEmail = (email) => {
  const emailRegex = /^[^s@]+@[^s@]+.[^s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validate phone number
 * @param {string} phone - Phone number to validate
 * @returns {boolean} - True if valid phone format
 */
export const validatePhone = (phone) => {
  const phoneRegex = /^[+]?[1-9]?d{9,15}$/;
  return phoneRegex.test(phone.replace(/s/g, ''));
};

/**
 * Validate required field
 * @param {any} value - Value to validate
 * @returns {boolean} - True if value is not empty
 */
export const validateRequired = (value) => {
  if(typeof value === 'string') {
    return value.trim().length > 0;
  }
  return value !== null && value !== undefined;
};

/**
 * Validate number range
 * @param {number} value - Number to validate
 * @param {number} min - Minimum value
 * @param {number} max - Maximum value
 * @returns {boolean} - True if within range
 */
export const validateNumberRange = (value, min, max) => {
  const num = parseFloat(value);
  return !isNaN(num) && num >= min && num <= max;
};

/**
 * Check camera permissions (placeholder)
 * @returns {boolean} - Always returns true for now
 */
export const checkCameraPermissions = () => {
  // In a real app, you would check camera permissions here
  // For now, we'll just return true
  return true;
};

/**
 * Schedule periodic checks (placeholder)
 */
export const schedulePeriodicChecks = () => {
  // Disabled automatic notification generation for clean app experience
  // Only manual notifications will be created when actual events occur
  console.log('Periodic checks scheduled');
};

export default {
  validateEmail,
  validatePhone,
  validateRequired,
  validateNumberRange,
  checkCameraPermissions,
  schedulePeriodicChecks
};
