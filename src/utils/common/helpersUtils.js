/**
 * Helper Utilities
 * Common helper functions used across the application
 */

/**
 * Generate unique ID
 * @returns {string} - Unique identifier
 */
export const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

/**
 * Deep clone an object
 * @param {any} obj - Object to clone
 * @returns {any} - Cloned object
 */
export const deepClone = (obj) => {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj.getTime());
  if (obj instanceof Array) return obj.map(item => deepClone(item));
  if(typeof obj === 'object') {
    const clonedObj = {};
    for(const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }
};

/**
 * Debounce function calls
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {Function} - Debounced function
 */
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

/**
 * Check if running in browser environment
 * @returns {boolean} - True if in browser
 */
export const isBrowser = () => {
  return typeof window !== 'undefined' && typeof document !== 'undefined';
};

/**
 * Safe JSON parse
 * @param {string} jsonString - JSON string to parse
 * @param {any} defaultValue - Default value if parsing fails
 * @returns {any} - Parsed object or default value
 */
export const safeJsonParse = (jsonString, defaultValue = null) => {
  try {
    return JSON.parse(jsonString);
  } catch (error) {
    console.warn('Failed to parse JSON:', error);
    return defaultValue;
  }
};

/**
 * Sort array of objects by field
 * @param {Array} array - Array to sort
 * @param {string} field - Field to sort by
 * @param {string} direction - Sort direction ('asc' or 'desc')
 * @returns {Array} - Sorted array
 */
export const sortByField = (array, field, direction = 'asc') => {
  return [...array].sort((a, b) => {
    let aValue = a[field];
    let bValue = b[field];
    
    // Handle date fields
    if(field === 'createdAt' || field === 'dueDate') {
      aValue = new Date(aValue).getTime();
      bValue = new Date(bValue).getTime();
    }
    
    // Handle string fields
    if(typeof aValue === 'string') {
      aValue = aValue.toLowerCase();
      bValue = bValue.toLowerCase();
    }
    
    if(direction === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });
};

/**
 * Handle async errors safely
 * @param {Function} asyncFn - Async function to execute
 * @param {string} errorMessage - Error message prefix
 */
export const handleAsyncError = async (asyncFn, errorMessage = 'Operation failed') => {
  try {
    return await asyncFn();
  } catch (error) {
    console.error(`${errorMessage}`} :`, error);
    throw error;
  }
};

/**
 * Generate sample data if needed (placeholder)
 */
export const generateSampleDataIfNeeded = () => {
  console.log('Sample data generation skipped');
  return Promise.resolve();
};

/**
 * Network restore handler (placeholder)
 */
export const onNetworkRestore = async () => {
  console.log('Network restored - syncing data');
  // Sync offline data when network is restored
};

export default {
  generateId,
  deepClone,
  debounce,
  isBrowser,
  safeJsonParse,
  sortByField,
  handleAsyncError,
  generateSampleDataIfNeeded,
  onNetworkRestore
};
