/**
 * Icon Migration Utility
 * Maps common MaterialCommunityIcons to Feather equivalents
 */

export const materialToFeatherMap = {
  // Navigation & UI
  'magnify': 'search',
  'bell-outline': 'bell',
  'bell': 'bell',
  'home-outline': 'home',
  'home': 'home',
  'menu': 'menu',
  'cog-outline': 'settings',
  'cog': 'settings',
  'dots-horizontal': 'more-horizontal',
  'dots-vertical': 'more-vertical',
  'view-grid-outline': 'grid',
  'view-grid': 'grid',
  'format-list-bulleted': 'list',
  
  // Actions
  'plus': 'plus',
  'minus': 'minus',
  'pencil-outline': 'edit',
  'pencil': 'edit-2',
  'delete-outline': 'trash',
  'delete': 'trash-2',
  'content-save-outline': 'save',
  'content-save': 'save',
  'download-outline': 'download',
  'download': 'download',
  'upload-outline': 'upload',
  'upload': 'upload',
  'share-outline': 'share',
  'share': 'share',
  'content-copy': 'copy',
  'content-cut': 'cut',
  'content-paste': 'paste',
  
  // Navigation arrows
  'arrow-left': 'arrow-left',
  'arrow-right': 'arrow-right',
  'arrow-up': 'arrow-up',
  'arrow-down': 'arrow-down',
  'chevron-left': 'chevron-left',
  'chevron-right': 'chevron-right',
  'chevron-up': 'chevron-up',
  'chevron-down': 'chevron-down',
  
  // Status & feedback
  'check': 'check',
  'check-circle-outline': 'check-circle',
  'check-circle': 'check-circle',
  'close': 'x',
  'close-circle-outline': 'x-circle',
  'close-circle': 'x-circle',
  'alert-circle-outline': 'alert-circle',
  'alert-circle': 'alert-circle',
  'alert-outline': 'alert-triangle',
  'alert': 'alert-triangle',
  'information-outline': 'info',
  'information': 'info',
  'help-circle-outline': 'help-circle',
  'help-circle': 'help-circle',
  
  // Business & commerce
  'shopping-outline': 'shopping-bag',
  'shopping': 'shopping-bag',
  'cart-outline': 'shopping-cart',
  'cart': 'shopping-cart',
  'credit-card-outline': 'credit-card',
  'credit-card': 'credit-card',
  'currency-usd': 'dollar-sign',
  'trending-up': 'trending-up',
  'trending-down': 'trending-down',
  'chart-bar': 'bar-chart',
  'chart-pie': 'pie-chart',
  
  // People & communication
  'account-group-outline': 'users',
  'account-group': 'users',
  'account-outline': 'user',
  'account': 'user',
  'account-plus-outline': 'user-plus',
  'account-plus': 'user-plus',
  'account-minus-outline': 'user-minus',
  'account-minus': 'user-minus',
  'account-check-outline': 'user-check',
  'account-check': 'user-check',
  'account-remove-outline': 'user-x',
  'account-remove': 'user-x',
  'email-outline': 'mail',
  'email': 'mail',
  'phone-outline': 'phone',
  'phone': 'phone',
  'message-outline': 'message-circle',
  'message': 'message-circle',
  'comment-outline': 'message-square',
  'comment': 'message-square',
  
  // Media & files
  'camera-outline': 'camera',
  'camera': 'camera',
  'image-outline': 'image',
  'image': 'image',
  'file-outline': 'file',
  'file': 'file',
  'file-document-outline': 'file-text',
  'file-document': 'file-text',
  'folder-outline': 'folder',
  'folder': 'folder',
  'folder-plus-outline': 'folder-plus',
  'folder-plus': 'folder-plus',
  'attachment': 'paperclip',
  'printer-outline': 'printer',
  'printer': 'printer',
  'scanner': 'scanner',
  
  // Time & calendar
  'calendar-outline': 'calendar',
  'calendar': 'calendar',
  'clock-outline': 'clock',
  'clock': 'clock',
  'watch': 'watch',
  'timer-outline': 'timer',
  'timer': 'timer',
  'stopwatch-outline': 'stopwatch',
  'stopwatch': 'stopwatch',
  
  // Location & map
  'map-marker-outline': 'map-pin',
  'map-marker': 'map-pin',
  'map-outline': 'map',
  'map': 'map',
  'navigation-outline': 'navigation',
  'navigation': 'navigation',
  'compass-outline': 'compass',
  'compass': 'compass',
  'earth': 'globe',
  
  // Tools & utilities
  'wrench-outline': 'tool',
  'wrench': 'tool',
  'ruler': 'ruler',
  'calculator': 'calculator',
  'bookmark-outline': 'bookmark',
  'bookmark': 'bookmark',
  'tag-outline': 'tag',
  'tag': 'tag',
  'pound': 'hash',
  'at': 'at-sign',
  
  // Technology
  'cellphone': 'smartphone',
  'tablet': 'tablet',
  'monitor': 'monitor',
  'wifi': 'wifi',
  'bluetooth': 'bluetooth',
  'battery-outline': 'battery',
  'battery': 'battery',
  'flash-outline': 'zap',
  'flash': 'zap',
  
  // Weather & nature
  'weather-sunny': 'sun',
  'weather-night': 'moon',
  'weather-cloudy': 'cloud',
  'star-outline': 'star',
  'star': 'star',
  'heart-outline': 'heart',
  'heart': 'heart',
  'eye-outline': 'eye',
  'eye': 'eye',
  'eye-off-outline': 'eye-off',
  'eye-off': 'eye-off',
  
  // Shapes & symbols
  'circle-outline': 'circle',
  'circle': 'circle',
  'square-outline': 'square',
  'square': 'square',
  'triangle-outline': 'triangle',
  'triangle': 'triangle',
  
  // Specific business icons
  'package-variant-closed': 'package',
  'package-variant': 'package',
  'truck-outline': 'truck',
  'truck': 'truck',
  'cube-outline': 'box',
  'cube': 'box',
  'archive-outline': 'archive',
  'archive': 'archive',
  'layers-outline': 'layers',
  'layers': 'layers',
  'database-outline': 'database',
  'database': 'database',
  'server': 'server',
  'harddisk': 'hard-drive',
  
  // Social & sharing
  'thumb-up-outline': 'thumbs-up',
  'thumb-up': 'thumbs-up',
  'thumb-down-outline': 'thumbs-down',
  'thumb-down': 'thumbs-down',
  'flag-outline': 'flag',
  'flag': 'flag',
  'rss': 'rss',
  'link': 'link',
  'open-in-new': 'external-link'};

/**
 * Convert MaterialCommunityIcon name to Feather equivalent
 */
export const getLocalIcon = (materialIconName) => {
  return materialToFeatherMap[materialIconName] || materialIconName;
};

/**
 * Check if an icon has a Feather equivalent
 */
export const hasFeatherEquivalent = (materialIconName) => {
  return materialIconName in materialToFeatherMap;
};
