import { Alert, Share, Linking   } from 'react-native';

// Use expo-print instead of react-native-print for better Expo compatibility
let ExpoPrint, ExpoSharing;
let pdfAvailable = false, printAvailable = false;

try {
  ExpoPrint = require('expo-print');
  ExpoSharing = require('expo-sharing');
  pdfAvailable = true;
  printAvailable = true;
} catch (error) {
  // Gracefully handle cases where expo modules are not available
  ExpoPrint = null;
  ExpoSharing = null;
}

export class PDFInvoiceGenerator {
  static generateInvoiceHTML(order) {
    const invoiceDate = new Date().toLocaleDateString('en-GB');
    const orderDate = new Date(order.createdAt).toLocaleDateString('en-GB');
    const subtotal = order.total || 0;
    const tax = subtotal * 0.08;
    const total = subtotal + tax;

    // FIXED: Corrected all invalid semicolons and commas in CSS properties
    return `
<!DOCTYPE html>


    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice #${order.id}</title>
    
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Helvetica', 'Arial', sans-serif; line-height: 1.6; color: #333; max-width: 800px; margin: 0 auto; padding: 20px; background: #fff; }
        .invoice-header { background: linear-gradient(135deg, #2563EB, #1D4ED8); color: white; padding: 30px; border-radius: 12px; margin-bottom: 30px; text-align: center; }
        .business-name { font-size: 32px; font-weight: bold; margin-bottom: 10px; text-shadow: 0 2px 4px rgba(0,0,0,0.3); }
        .business-info { font-size: 16px; opacity: 0.95; line-height: 1.4; }
        .invoice-title { background: #F8FAFC; padding: 20px; border-radius: 8px; margin-bottom: 30px; border-left: 4px solid #2563EB; }
        .invoice-title h1 { font-size: 28px; color: #1E293B; margin-bottom: 5px; }
        .invoice-meta { display: flex; justify-content: space-between; margin-bottom: 30px; gap: 20px; }
        .invoice-details, .customer-details { flex: 1; background: #F1F5F9; padding: 20px; border-radius: 8px; }
        .section-title { font-size: 18px; font-weight: bold; color: #334155; margin-bottom: 15px; border-bottom: 2px solid #E2E8F0; padding-bottom: 5px; }
        .detail-row { display: flex; justify-content: space-between; margin-bottom: 8px; }
        .detail-label { font-weight: 600; color: #64748B; }
        .detail-value { color: #1E293B; font-weight: 500; }
        .items-table { width: 100%; border-collapse: collapse; margin: 30px 0; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
        .items-table th { background: #334155; color: white; padding: 15px; text-align: left; font-weight: 600; font-size: 14px; text-transform: uppercase; letter-spacing: 0.5px; }
        .items-table td { padding: 15px; border-bottom: 1px solid #E2E8F0; }
        .items-table tr:last-child td { border-bottom: none; }
        .items-table tr:nth-child(even) { background: #F8FAFC; }
        .item-name { font-weight: 600; color: #1E293B; }
        .item-details { color: #64748B; font-size: 14px; margin-top: 4px; }
        .text-right { text-align: right; }
        .text-center { text-align: center; }
        .totals-section { background: #F8FAFC; padding: 25px; border-radius: 8px; margin-top: 30px; border: 1px solid #E2E8F0; }
        .totals-table { width: 100%; max-width: 400px; margin-left: auto; }
        .totals-table td { padding: 8px 0; border-bottom: 1px solid #E2E8F0; }
        .totals-table .total-row { font-size: 18px; font-weight: bold; color: #1E293B; border-top: 2px solid #2563EB; padding-top: 15px; }
        .totals-table .total-row td { border-bottom: none; }
        .status-section { border: 1px solid; padding: 20px; border-radius: 8px; margin: 30px 0; text-align: center; }
        .status-completed { background: #F0FDF4; border-color: #BBF7D0; color: #166534; }
        .status-pending { background: #EFF6FF; border-color: #BFDBFE; color: #1D4ED8; }
        .status-inprogress { background: #FFF7ED; border-color: #FED7AA; color: #C2410C; }
        .status-cancelled { background: #FEF2F2; border-color: #FECACA; color: #DC2626; }
        .footer { text-align: center; margin-top: 40px; padding-top: 20px; border-top: 2px solid #E2E8F0; color: #64748B; }
        .footer-message { font-size: 18px; font-weight: 600; color: #2563EB; margin-bottom: 10px; }
        @media print { body { padding: 0; margin: 0; } .invoice-header { background: #2563EB !important; -webkit-print-color-adjust: exact; color-adjust: exact; }`}
    


    <div class="invoice-header">
        <div class="business-name">Elite Tailoring</div>
        <div class="business-info">123 Fashion Ave, Dhaka, BD 1212Phone: +880 1234-567890 | Email: <EMAIL></div>
    </div>
    <div class="invoice-title"><h1>INVOICE #${order.id}</h1><p>Professional Tailoring Services</p></div>
    <div class="invoice-meta">
        <div class="invoice-details"><div class="section-title">Invoice Details</div>
            <div class="detail-row"><span class="detail-label">Invoice Date:</span><span class="detail-value">${invoiceDate}</span></div>
            <div class="detail-row"><span class="detail-label">Order Date:</span><span class="detail-value">${orderDate}</span></div>
            <div class="detail-row"><span class="detail-label">Due Date:</span><span class="detail-value">${new Date(order.dueDate).toLocaleDateString('en-GB')}</span></div>
        </div>
        <div class="customer-details"><div class="section-title">Bill To</div>
            <div class="detail-row"><span class="detail-label">Customer:</span><span class="detail-value">${order.customerName}</span></div>
            <div class="detail-row"><span class="detail-label">Phone:</span><span class="detail-value">${order.phone}</span></div>
        </div>
    </div>
    <table class="items-table">
        <thead><tr><th>Item Description</th><th class="text-center">Quantity</th><th class="text-right">Unit Price</th><th class="text-right">Total</th></tr></thead>
        <tbody>
            ${(order.items || []).map(item => `
                <tr>
                    <td><div class="item-name">${item.name || item.productName}</div><div class="item-details">${item.description || 'Custom tailored garment'}</div></td>
                    <td class="text-center">${item.quantity}</td>
                    <td class="text-right">৳${(item.price || 0).toFixed(2)}</td>
                    <td class="text-right">৳${((item.price || 0) * item.quantity).toFixed(2)}</td>
                </tr>
`).join('')}
        </tbody>
    </table>
    <div class="totals-section">
        <table class="totals-table">
            <tr><td>Subtotal:</td><td class="text-right">৳${subtotal.toFixed(2)}</td></tr>
            <tr><td>Tax (8%):</td><td class="text-right">৳${tax.toFixed(2)}</td></tr>
            <tr class="total-row"><td>Total Amount:</td><td class="text-right">৳${total.toFixed(2)}</td></tr>
        </table>
    </div>
    <div class="status-section status-${order.status.toLowerCase().replace(/\s/g, '')}"><strong>Order Status: ${order.status}</strong></div>
    <div class="footer"><div class="footer-message">Thank you for your business!</div><p>We appreciate your trust in Elite Tailoring.</p></div>
`;
  }

  static async generatePDF(order) {
    if(!pdfAvailable || !ExpoPrint) {
        Alert.alert('PDF Generation Not Available', 'This feature requires a full build of the app.');
        return { success: false, error: 'PDF module not available.'  };
    }
    try {
      const htmlContent = this.generateInvoiceHTML(order);
      // FIXED: Removed extra comma from margins object
      const { uri  } = await ExpoPrint.printToFileAsync({
        html: htmlContent,
        base64: false,
        width: 612,
        height: 792,
        margins: { left: 40, top: 40, right: 40, bottom: 40 }});
      return { success: true, filePath: uri  };
    } catch (error) {
      console.error('PDF generation error:', error);
      throw new Error('Failed to generate PDF invoice');
    }
  }

  static async printInvoice(order) {
    if(!printAvailable || !ExpoPrint) {
        Alert.alert('Printing Not Available', 'This feature requires a full build of the app.');
        return;
    }
    try {
      const htmlContent = this.generateInvoiceHTML(order);
      await ExpoPrint.printAsync({ html: htmlContent });
    } catch (error) {
      console.error('Print error:', error);
      throw new Error('Failed to print invoice.');
    }
  }

  static async generateAndSharePDF(order) {
    try {
      const pdf = await this.generatePDF(order);
      if (pdf.filePath && ExpoSharing && await ExpoSharing.isAvailableAsync()) {
        await ExpoSharing.shareAsync(pdf.filePath, { mimeType: 'application/pdf', dialogTitle: `Share Invoice #${order.id}`});
        return { ...pdf, shared: true };
      } else if(pdf.filePath) {
        // Fallback for when sharing is not available but PDF was created
        Alert.alert('PDF Generated', `Invoice saved at: ${pdf.filePath}. Sharing is not available on this device.`);
        return { ...pdf, shared: false  };
      } else {
        throw new Error('PDF generation failed, cannot share.');
      }
    } catch (error) {
      console.error('PDF share error:', error);
      throw new Error('Failed to generate and share PDF');
    }
  }
  
  static async copyInvoiceDetails(order) {
      // This is a placeholder, as Clipboard is a native module.
      // In a real app, you would use @react-native-clipboard/clipboard.
      const invoiceText = `Invoice #${order.id}, Customer: ${order.customerName}, Total: ৳${order.total.toFixed(2)};
      Alert.alert('Invoice Details', 'Details copied to clipboard (simulated).');
      console.log(invoiceText);
  }
}