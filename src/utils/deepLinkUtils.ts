/**
 * Deep Link Generation Utilities
 * Helper functions for generating and handling deep links
 */

import type { RootStackParamList, TabParamList } from '../types/navigation';

// Base URL configuration
const BASE_URLS = {
  production: 'https://app.tailora.com',
  development: 'http://localhost:8084',
  scheme: 'tailora://',
} as const;

// Get the appropriate base URL based on environment
function getBaseUrl(): string {
  if(__DEV__) {
    return BASE_URLS.development;
  }
  return BASE_URLS.production;
}

// Generate deep link for tab screens
export function generateTabLink(screen: keyof TabParamList): string {
  const baseUrl = getBaseUrl();
  const paths: Record<keyof TabParamList, string> = {
    Dashboard: '/app/dashboard',
    Orders: '/app/orders',
    Products: '/app/products',
    CRM: '/app/customers',
    Analytics: '/app/reports',
  };
  
  return `${baseUrl} ${paths[screen]`;
}

// Generate deep link for stack screens with parameters
export function generateStackLink<T extends keyof RootStackParamList>(
  screen: T,
  params?: RootStackParamList[T]
): string {
  const baseUrl = getBaseUrl();
  
  // Define path templates for each screen
  const pathTemplates: Partial<Record<keyof RootStackParamList, string>> = {
    Login: '/login',
    CustomerDetails: '/customer/:customerId',
    AddCustomer: '/add-customer',
    CustomerMeasurements: '/customer/:customerId/measurements',
    OrderDetails: '/order/:orderId',
    CreateOrder: '/create-order',
    OrderSuccess: '/order-success/:orderId',
    AddProduct: '/add-product',
    GarmentTemplates: '/templates',
    AddGarmentTemplate: '/add-template',
    EditGarmentTemplate: '/edit-template/:templateId',
    AddMeasurement: '/add-measurement/:customerId',
    AddFabric: '/add-fabric',
    UniversalSearch: '/search',
    MyProfile: '/profile',
    EditProfile: '/edit-profile',
    UnifiedSettings: '/settings',
    PaymentMethods: '/payment-methods',
    NotificationSettings: '/notification-settings',
    BusinessHours: '/business-hours',
    Reports: '/reports',
    FinancialReports: '/financial-reports',
    ProfitLoss: '/profit-loss',
    TaxSummary: '/tax-summary',
    EmployeeManagement: '/employees',
    AddEmployee: '/add-employee',
    EmployeeAttendance: '/attendance',
    ImportData: '/import-data',
    DataManagement: '/data-management',
    HelpFAQ: '/help',
    ContactSupport: '/support',
    About: '/about',
    ExpenseForm: '/expense',
    CashReconciliation: '/cash-reconciliation',
    Subscription: '/subscription',
    QRScanner: '/scan',
    Notifications: '/notifications',
    Sales: '/sales',
    UnifiedInventory: '/inventory',
    Accounting: '/accounting',
 `};
  
  let path = pathTemplates[screen] || `/${screen.toLowerCase()}`;
  
  // Replace path parameters with actual values
  if(params && typeof params === 'object') {
    Object.entries(params).forEach(([key, value]) => {
      if(value !== undefined && value !== null) {
        path = path.replace(`:${key}`,` String(value));
      }
    });
  }
  
  // Add query parameters for remaining params
  const queryParams: string[] = [];
  if(params && typeof params === 'object') {
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && !path.includes(`:${key}`)) {
        queryParams.push(`${key} =${encodeURIComponent(String(value))}` );
      }
    });
  }
  
  const queryString = queryParams.length > 0 ? `?${queryParams.join('&')}` : '';
  return `${baseUrl} ${path}${queryString}`;`
}

// Generate scheme-based deep link
export function generateSchemeLink<T extends keyof RootStackParamList>(
  screen: T,
  params?: RootStackParamList[T]
): string {
  const webLink = generateStackLink(screen, params);
  return webLink.replace(getBaseUrl(), BASE_URLS.scheme);
}

// Parse deep link parameters
export function parseDeepLinkParams(url: string): Record<string, string> {
  try {
    const urlObj = new URL(url);
    const params: Record<string, string> = {};
    
    // Extract path parameters
    const pathSegments = urlObj.pathname.split('/').filter(Boolean);
    
    // Extract query parameters
    urlObj.searchParams.forEach((value, key) => {
      params[key] = value;
    });
    
    return params;
  } catch (error) {
    console.warn('Failed to parse deep link:', url, error);
    return {};
  }
}

// Validate deep link format
export function isValidDeepLink(url: string): boolean {
  try {
    const urlObj = new URL(url);
    const validPrefixes = [
      BASE_URLS.production,
      BASE_URLS.development,
      BASE_URLS.scheme,
      'http://localhost:8081',
      'http://localhost:8082',
      'http://localhost:8083',
    ];
    
    return validPrefixes.some(prefix => url.startsWith(prefix));
  } catch {
    return false;
  }
}

// Share deep link (for social sharing, etc.)
export function shareDeepLink<T extends keyof RootStackParamList>(
  screen: T,
  params?: RootStackParamList[T],
  options?: {
    title?: string;
    message?: string;
    useScheme?: boolean;
  }
): string {
  const link = options?.useScheme 
    ? generateSchemeLink(screen, params)
    : generateStackLink(screen, params);
    
  // In a real app, you might use React Native's Share API here
  // For now, just return the link
  return link;
}

// Common deep link generators for frequently used screens
export const commonLinks = {
  dashboard: () => generateTabLink('Dashboard'),
  orders: () => generateTabLink('Orders'),
  products: () => generateTabLink('Products'),
  customers: () => generateTabLink('CRM'),
  analytics: () => generateTabLink('Analytics'),
  
  customerDetails: (customerId: string) => 
    generateStackLink('CustomerDetails', { customerId }),
  
  orderDetails: (orderId: string) => 
    generateStackLink('OrderDetails', { orderId }),
  
  createOrder: (customerId?: string, productId?: string) => 
    generateStackLink('CreateOrder', { customerId, productId }),
  
  addCustomer: () => generateStackLink('AddCustomer'),
  
  search: (query?: string, category?: string) => 
    generateStackLink('UniversalSearch', { query, category }),
  
  settings: (section?: string) => 
    generateStackLink('UnifiedSettings', { section }),
};