/**
 * Icon Migration Helper for Tailora App
 * Provides utilities to systematically migrate from Feather/MaterialCommunity icons to Phosphor Icons
 */

import { getMigratedLocalIcon, MIGRATION_MAP   } from '../constants/phosphorIconMappings';

// Common icon patterns found in the codebase
export const ICON_USAGE_PATTERNS = {
  // Dashboard quick actions
  DASHBOARD_ACTIONS: {
    'package': 'Package',
    'plus-circle': 'PlusCircle', 
    'user-plus': 'UserPlus',
    'edit-3': 'PencilSimple',
    'layers': 'Stack',
    'grid': 'SquaresFour',
    'bar-chart-2': 'ChartBar',
    'archive': 'Package',
  },
  
  // Navigation icons
  NAVIGATION: {
    'home': 'House',
    'search': 'MagnifyingGlass',
    'bell': 'Bell',
    'user': 'User',
    'settings': 'Gear',
    'menu': 'List',
    'chevron-right': 'CaretRight',
    'chevron-left': 'CaretLeft',
    'chevron-up': 'CaretUp',
    'chevron-down': 'CaretDown',
  },
  
  // Business operations
  BUSINESS: {
    'clipboard-list-outline': 'ClipboardText',
    'tshirt-crew-outline': 'Shirt',
    'account-group-outline': 'Users',
    'ruler': 'Ruler',
    'credit-card': 'CreditCard',
    'receipt': 'Receipt',
    'dollar-sign': 'CurrencyDollar',
  },
  
  // Actions
  ACTIONS: {
    'plus': 'Plus',
    'edit': 'PencilSimple',
    'trash': 'Trash',
    'save': 'FloppyDisk',
    'download': 'Download',
    'upload': 'Upload',
    'share': 'ShareNetwork',
    'copy': 'Copy',
    'eye': 'Eye',
    'eye-off': 'EyeSlash',
  },
  
  // Status indicators
  STATUS: {
    'check': 'Check',
    'check-circle': 'CheckCircle',
    'x': 'X',
    'x-circle': 'XCircle',
    'alert-circle': 'Warning',
    'alert-triangle': 'Warning',
    'info': 'Info',
    'help-circle': 'Question',
  },
};

// Migration strategies
export const MIGRATION_STRATEGIES = {
  // Direct replacement - 1:1 mapping,
  DIRECT: 'direct',
  // Semantic replacement - choose best Phosphor equivalent
  SEMANTIC: 'semantic',
  // Custom replacement - manual selection needed
  CUSTOM: 'custom',
};

/**
 * Analyze icon usage in a component/file
 */
export const analyzeIconUsage = (iconList) => {
  const analysis = {
    total: iconList.length,
    migrated: 0,
    needsAttention: 0,
    categories: {
      navigation: 0,
      actions: 0,
      business: 0,
      status: 0,
      custom: 0,
    },
    details: [],
  };

  iconList.forEach(iconName => {
    const phosphorEquivalent = getMigratedLocalIcon(iconName);
    const hasDirectMapping = phosphorEquivalent !== 'Question';
    
    let category = 'custom';
    let strategy = MIGRATION_STRATEGIES.CUSTOM;
    
    // Categorize the icon
    if(ICON_USAGE_PATTERNS.NAVIGATION[iconName]) {
      category = 'navigation';
      strategy = MIGRATION_STRATEGIES.DIRECT;
    } else if(ICON_USAGE_PATTERNS.ACTIONS[iconName]) {
      category = 'actions';
      strategy = MIGRATION_STRATEGIES.DIRECT;
    } else if(ICON_USAGE_PATTERNS.BUSINESS[iconName]) {
      category = 'business';
      strategy = MIGRATION_STRATEGIES.SEMANTIC;
    } else if(ICON_USAGE_PATTERNS.STATUS[iconName]) {
      category = 'status';
      strategy = MIGRATION_STRATEGIES.DIRECT;
    }
    
    analysis.categories[category]++;
    
    if(hasDirectMapping) {
      analysis.migrated++;
    } else {
      analysis.needsAttention++;
    }
    
    analysis.details.push({
      original: iconName,
      phosphor: phosphorEquivalent,
      category,
      strategy,
      hasMapping: hasDirectMapping,
      priority: hasDirectMapping ? 'low' : 'high',
    });
  });

  return analysis;
};

/**
 * Generate migration code snippets
 */
export const generateMigrationCode = (iconName, context = 'general') => {
  const phosphorName = getMigratedLocalIcon(iconName);
  
  const codeSnippets = {
    // Using LocalIcon (recommended for migration)
    unified: `<LocalIcon name="${iconName}" size="large" colorContext="surface" /> ,
    
    // Using direct Phosphor icon
    direct: `<OptimizedLocalIcon name="${phosphorName}" size="large" colorContext="surface" />`,
    
    // Using category-specific component
    category: generateCategorySpecificCode(iconName, phosphorName, context),
    
    // Import statement
    import: `import LocalIcon, { NavigationLocalIcon, ActionLocalIcon, BusinessLocalIcon, SettingsLocalIcon, StatusLocalIcon } from './components/LocalIcon';
`,
  };
  
  return codeSnippets;
};

/**
 * Generate category-specific icon code
 */
const generateCategorySpecificCode = (iconName, phosphorName, context) => {
  if(ICON_USAGE_PATTERNS.NAVIGATION[iconName]) {
    return `<NavigationLocalIcon name="${iconName}" size="large" /> ;
  } else if(ICON_USAGE_PATTERNS.ACTIONS[iconName]) {
    return `<ActionLocalIcon name="${iconName}" size="large" />`;
  } else if(ICON_USAGE_PATTERNS.BUSINESS[iconName]) {
    return `<BusinessLocalIcon name="${iconName}" size="large" /> ;
  } else if(ICON_USAGE_PATTERNS.STATUS[iconName]) {
    return `<StatusLocalIcon name="${iconName}" size="large" />`;
  } else {
    return `<LocalIcon name="${iconName}" size="large" /> ;
  }
};

/**
 * Create migration checklist for a screen/component
 */
export const createMigrationChecklist = (componentName, iconList) => {
  const analysis = analyzeIconUsage(iconList);
  
  const checklist = {
    componentName,
    totalIcons: analysis.total,
    migrationSteps: [],
    estimatedTime: calculateMigrationTime(analysis),
    priority: calculatePriority(analysis),
  };
  
  // Group by category for organized migration
  Object.keys(analysis.categories).forEach(category => {
    if(analysis.categories[category] > 0) {
      const categoryIcons = analysis.details.filter(icon => icon.category === category);
      
      checklist.migrationSteps.push({
        step: `Migrate ${category} icons`,
        icons: categoryIcons.map(icon => icon.original),
        component: getCategoryComponent(category),
        estimatedTime: categoryIcons.length * 2, // 2 minutes per icon
        code: categoryIcons.map(icon => generateMigrationCode(icon.original, category)),
      });
    }
  });
  
  return checklist;
};

/**
 * Get recommended component for category
 */
const getCategoryComponent = (category) => {
  const componentMap = {
    navigation: 'NavigationLocalIcon',
    actions: 'ActionLocalIcon', 
    business: 'BusinessLocalIcon',
    status: 'StatusLocalIcon',
    custom: 'LocalIcon',
  };
  
  return componentMap[category] || 'LocalIcon';
};

/**
 * Calculate estimated migration time
 */
const calculateMigrationTime = (analysis) => {
  // Base time: 2 minutes per icon
  // Additional time for custom icons: +3 minutes
  const baseTime = analysis.total * 2;
  const customTime = analysis.categories.custom * 3;
  return baseTime + customTime;
};

/**
 * Calculate migration priority
 */
const calculatePriority = (analysis) => {
  const customRatio = analysis.categories.custom / analysis.total;
  const needsAttentionRatio = analysis.needsAttention / analysis.total;
  
  if (needsAttentionRatio > 0.5) return 'high';
  if (customRatio > 0.3) return 'medium';
  return 'low';
};

/**
 * Validate Phosphor icon exists
 */
export const validateLocalIcon = (iconName) => {
  // This would ideally check against the actual Phosphor icon list
  // For now, we check our mapping
  return Object.values(MIGRATION_MAP).includes(iconName) || 
         Object.values(ICON_USAGE_PATTERNS).some(pattern => 
           Object.values(pattern).includes(iconName)
         );
};

/**
 * Get migration recommendations for entire app
 */
export const getAppMigrationPlan = () => {
  const screens = [
    {
      name: 'DashboardScreen',
      icons: ['package', 'plus-circle', 'user-plus', 'edit-3', 'layers', 'grid', 'bar-chart-2', 'archive'],
      priority: 'high', // User-facing, frequently used
    },
    {
      name: 'ProfileScreen', 
      icons: ['credit-card', 'bell', 'chevron-right', 'user', 'settings'],
      priority: 'medium',
    },
    {
      name: 'Navigation',
      icons: ['home', 'search', 'bell', 'user', 'settings', 'menu'],
      priority: 'high', // Core navigation
    },
    {
      name: 'OrdersScreen',
      icons: ['clipboard-list-outline', 'plus', 'edit', 'trash', 'check-circle'],
      priority: 'medium',
    },
  ];
  
  return screens.map(screen => ({
    ...screen,
    checklist: createMigrationChecklist(screen.name, screen.icons),
    analysis: analyzeIconUsage(screen.icons),
  }));
};

/**
 * Generate migration report
 */
export const generateMigrationReport = () => {
  const migrationPlan = getAppMigrationPlan();
  
  const report = {
    summary: {
      totalScreens: migrationPlan.length,
      totalIcons: migrationPlan.reduce((sum, screen) => sum + screen.icons.length, 0),
      estimatedTime: migrationPlan.reduce((sum, screen) => sum + screen.checklist.estimatedTime, 0),
      highPriorityScreens: migrationPlan.filter(screen => screen.priority === 'high').length,
    },
    screens: migrationPlan,
    recommendations: [
      'Start with high-priority screens (Dashboard, Navigation)',
      'Use LocalIcon for automatic migration',
      'Test each screen after migration',
      'Update documentation with new icon usage',
    ],
  };
  
  return report;
};

export default {
  analyzeIconUsage,
  generateMigrationCode,
  createMigrationChecklist,
  getAppMigrationPlan,
  generateMigrationReport,
  validateLocalIcon,
  ICON_USAGE_PATTERNS,
  MIGRATION_STRATEGIES,
};
