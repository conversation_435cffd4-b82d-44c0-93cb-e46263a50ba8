// Core Entity Types
export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  stock: number;
  sku: string;
  barcode?: string;
  image?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  totalOrders: number;
  totalSpent: number;
  lastOrderDate?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  // Enhanced customer features
  birthday?: string;
  anniversary?: string;
  notes?: string;
  tags?: string[];
  isVIP?: boolean;
  visitHistory?: CustomerVisit[];
  measurements?: CustomerMeasurement[];
  preferences?: CustomerPreferences;
}

export interface CustomerVisit {
  id: string;
  customerId: string;
  visitDate: string;
  purpose: string; // 'measurement', 'fitting', 'delivery', 'consultation'
  notes?: string;
  orderId?: string;
  createdAt: string;
}

export interface CustomerMeasurement {
  id: string;
  customerId: string;
  garmentType: string;
  measurements: Record<string, number>;
  notes?: string;
  takenBy?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CustomerPreferences {
  preferredFabrics?: string[];
  preferredColors?: string[];
  specialInstructions?: string;
  communicationMethod?: 'phone' | 'email' | 'whatsapp' | 'sms';
  reminderPreferences?: {
    birthday: boolean;
    anniversary: boolean;

    payments: boolean;
  };
}

export interface Order {
  id: string;
  customerName: string;
  customer: string;
  customerId?: string;
  email: string;
  phone: string;
  date: string;
  time: string;
  status: OrderStatus;
  orderType: OrderType;
  subtotal: number;
  tax: number;
  discount: number;
  total: number;
  notes?: string;
  image?: string;
  items?: OrderItem[];
  createdAt: string;
  updatedAt: string;
  // Enhanced order features
  dueDate?: string;
  deliveryDate?: string;
  deliveryStatus?: 'pending' | 'ready' | 'delivered';
  tags?: OrderTag[];
  designReferences?: DesignReference[];
  measurements?: string[]; // Array of measurement IDs
  fittingScheduled?: boolean;
  fittingDate?: string;
  urgentOrder?: boolean;
  paymentStatus?: 'unpaid' | 'partial' | 'paid';
  paidAmount?: number;
  balanceAmount?: number;
  invoiceGenerated?: boolean;
  invoiceNumber?: string;
}

export interface OrderTag {
  id: string;
  name: string;
  color: string;
  priority?: 'low' | 'medium' | 'high';
}

export interface DesignReference {
  id: string;
  orderId: string;
  type: 'image' | 'sketch' | 'note';
  content: string; // URL for images, text for notes
  description?: string;
  createdAt: string;
}

export interface OrderItem {
  id: string;
  orderId: string;
  productId: string;
  productName: string;
  quantity: number;
  price: number;
  total: number;
}

// Enum Types
export type OrderStatus = 'Pending' | 'In Progress' | 'Fitting' | 'Ready' | 'Delivered' | 'Completed' | 'Cancelled';
export type OrderType = 'new_stitch' | 'alteration' | 'repair' | 'custom_design';
export type ProductCategory = 'Shirts' | 'Pants' | 'Suits' | 'Blazers' | 'Kurtas' | 'Sherwanis' | 'Traditional Wear' | 'Other';

// UI Component Types
export interface StatCard {
  id: string;
  title: string;
  value: string | number;
  icon: string;
  color?: string;
  iconColor?: string;
  elevation?: number;
  onPress?: () => void;
  props?: Record<string, any>;
}

export interface UnifiedInfoCardProps {
  type: 'stat' | 'info' | 'action';
  title: string;
  subtitle?: string;
  value?: string | number;
  icon?: string;
  iconColor?: string;
  color?: string;
  elevation?: number;
  onPress?: () => void;
  style?: Record<string, any>;
  children?: React.ReactNode;
}

// Service Response Types
export interface ServiceResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  counts?: {
    products: number;
    customers: number;
    orders: number;
  };
}

// Database Types
export interface DatabaseConfig {
  name: string;
  version: number;
  tables: string[];
}

export interface SQLiteResult {
  insertId?: number;
  rowsAffected: number;
  rows: {
    length: number;
    item: (index: number) => unknown;
    _array: unknown[];
  };
}

// Context Types
export interface DataContextType {
  products: Product[];
  orders: Order[];
  customers: Customer[];
  garmentTemplates: GarmentTemplate[];
  loading: boolean;
  error: string | null;
  refreshData: () => Promise<void>;
  addProduct: (product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateProduct: (id: string, product: Partial<Product>) => Promise<void>;
  deleteProduct: (id: string) => Promise<void>;
  addOrder: (order: Omit<Order, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateOrder: (id: string, order: Partial<Order>) => Promise<void>;
  deleteOrder: (id: string) => Promise<void>;
  addCustomer: (customer: Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateCustomer: (id: string, customer: Partial<Customer>) => Promise<void>;
  deleteCustomer: (id: string) => Promise<void>;
  addGarmentTemplate: (template: Omit<GarmentTemplate, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateGarmentTemplate: (template: Partial<GarmentTemplate>) => Promise<void>;
  deleteGarmentTemplate: (id: string) => Promise<void>;
}

// Analytics Types
export interface AnalyticsData {
  totalRevenue: number;
  totalOrders: number;
  totalCustomers: number;
  totalProducts: number;
  averageOrderValue: number;
  topProducts: Product[];
  recentOrders: Order[];
  monthlyRevenue: number[];
  dailyOrders: number[];
}

// Search Types
export interface SearchResult {
  type: 'product' | 'order' | 'customer';
  id: string;
  title: string;
  subtitle: string;
  data: Product | Order | Customer;
}

// Form Types
export interface ProductFormData {
  name: string;
  description: string;
  price: string;
  category: ProductCategory;
  stock: string;
  image?: string;
}

export interface OrderFormData {
  customerName: string;
  email: string;
  phone: string;
  orderType: OrderType;
  notes?: string;
  image?: string;
  items: OrderItem[];
}

export interface CustomerFormData {
  name: string;
  email: string;
  phone: string;
  address: string;
}

// Navigation Types
export type RootStackParamList = {
  Main: undefined;
  ProductForm: { product?: Product };
  OrderForm: { order?: Order };
  CustomerForm: { customer?: Customer };
  ProductDetails: { productId: string };
  OrderDetails: { orderId: string };
  CustomerDetails: { customerId: string };
  Search: { query?: string };
  Analytics: undefined;
  Reports: undefined;
  Settings: undefined;
  Profile: undefined;
};

// Error Types
export interface AppError {
  code: string;
  message: string;
  details?: object;
  timestamp: string;
}

// Performance Types
export interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  memoryUsage: number;
  cacheHitRate: number;
  errorRate: number;
  startupTime: number;
  fps: number;
  navigationTime: number;
}

// Cache Types
export interface CacheEntry<T = any> {
  key: string;
  data: T;
  timestamp: number;
  ttl: number;
}

export interface CacheConfig {
  maxSize: number;
  defaultTTL: number;
  cleanupInterval: number;
}

// ===== ENHANCED TAILOR MANAGEMENT TYPES =====

// Inventory Management Types
export interface InventoryItem {
  id: string;
  name: string;
  category: 'fabric' | 'notion' | 'accessory';
  type: string;
  color?: string;
  size?: string;
  stock: number;
  unit: string; // 'meters', 'pieces', 'yards'
  costPrice: number;
  sellingPrice: number;
  supplier?: string;
  reorderLevel: number;
  location?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface StockTransaction {
  id: string;
  itemId: string;
  type: 'in' | 'out' | 'adjustment';
  quantity: number;
  reason: string;
  orderId?: string;
  notes?: string;
  createdAt: string;
}

// Measurement Templates
export interface MeasurementTemplate {
  id: string;
  name: string;
  garmentType: string;
  fields: MeasurementField[];
  isDefault: boolean;
  createdBy?: string;
  createdAt: string;
  updatedAt: string;
}

export interface MeasurementField {
  id: string;
  name: string;
  label: string;
  unit: 'inches' | 'cm';
  required: boolean;
  category: 'chest' | 'waist' | 'length' | 'sleeve' | 'neck' | 'other';
  order: number;
  description?: string;
}

// Garment Templates
export interface GarmentTemplate {
  id: string;
  name: string;
  category: string;
  basePrice: number;
  description?: string;
  measurementFields: MeasurementField[];
  isActive: boolean;
  createdBy?: string;
  createdAt: string;
  updatedAt: string;
}

// Payment & Billing Types
export interface Invoice {
  id: string;
  invoiceNumber: string;
  orderId: string;
  customerId: string;
  customerName: string;
  items: InvoiceItem[];
  subtotal: number;
  tax: number;
  discount: number;
  total: number;
  paidAmount: number;
  balanceAmount: number;
  paymentStatus: 'unpaid' | 'partial' | 'paid';
  dueDate: string;
  generatedAt: string;
  paidAt?: string;
  notes?: string;
}

export interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  rate: number;
  amount: number;
}

export interface Payment {
  id: string;
  invoiceId: string;
  orderId: string;
  customerId: string;
  amount: number;
  method: 'cash' | 'card' | 'bank_transfer' | 'mobile_payment';
  reference?: string;
  notes?: string;
  receivedBy: string;
  receivedAt: string;
}

// Communication Types
export interface Reminder {
  id: string;
  type: 'birthday' | 'anniversary' | 'payment' | 'delivery';
  customerId?: string;
  orderId?: string;
  title: string;
  message: string;
  scheduledDate: string;
  sent: boolean;
  sentAt?: string;
  method: 'sms' | 'whatsapp' | 'email' | 'notification';
  createdAt: string;
}
