/**
 * Theme Types for TypeScript support
 */

export type ThemeMode = 'light' | 'dark' | 'system';

export interface ThemeColors {
  primary: string;
  primaryContainer: string;
  secondary: string;
  secondaryContainer: string;
  tertiary: string;
  tertiaryContainer: string;
  surface: string;
  surfaceVariant: string;
  background: string;
  error: string;
  errorContainer: string;
  onPrimary: string;
  onPrimaryContainer: string;
  onSecondary: string;
  onSecondaryContainer: string;
  onTertiary: string;
  onTertiaryContainer: string;
  onSurface: string;
  onSurfaceVariant: string;
  onError: string;
  onErrorContainer: string;
  onBackground: string;
  outline: string;
  outlineVariant: string;
  inverseSurface: string;
  inverseOnSurface: string;
  inversePrimary: string;
}

export interface ThemeContextType {
  isDarkMode: boolean;
  theme: object; // MD3 theme object
  themeMode: ThemeMode;
  systemColorScheme: 'light' | 'dark' | null;
  isLoading: boolean;
  setTheme: (mode: ThemeMode) => Promise<void>;
  toggleTheme: () => void;
  isSystemMode: boolean;
  isLightMode: boolean;
  isDarkModeSet: boolean;
  THEME_MODES: {
      LIGHT: 'light';
      DARK: 'dark';
      SYSTEM: 'system';
    };
}
