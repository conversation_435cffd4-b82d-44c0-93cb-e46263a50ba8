/**
 * Navigation Type Definitions
 * Provides type safety for React Navigation
 */

import type { NavigatorScreenParams } from '@react-navigation/native';
import type { StackScreenProps } from '@react-navigation/stack';
import type { BottomTabScreenProps } from '@react-navigation/bottom-tabs';

// Tab Navigator Param List - Core app sections
export type TabParamList = {
  Dashboard: undefined;
  Orders: undefined;
  Products: undefined;
  CRM: undefined;
  Analytics: undefined;
};

// Root Stack Navigator Param List - Clean organization
export type RootStackParamList = {
  // Auth Screens
  Login: undefined;
  
  // Main Tab Navigator
  Main: NavigatorScreenParams<TabParamList>;
  
  // Customer Management
  CustomerDetails: { customerId: string; customer?: object };
  AddCustomer: undefined;
  CustomerMeasurements: { customerId: string };
  
  // Order Management
  OrderDetails: { orderId: string; order?: object };
  CreateOrder: { customerId?: string; productId?: string };
  OrderSuccess: { orderId: string };
  
  // Product Management
  AddProduct: { category?: string };
  EditProduct: { productId: string };
  GarmentTemplates: undefined;
  AddGarmentTemplate: undefined;
  EditGarmentTemplate: { templateId: string };
  
  // Measurement Management
  Measurements: undefined;
  AddMeasurement: { customerId: string; templateId?: string };
  EditMeasurement: { measurementId: string };
  
  // Fabric Management
  AddFabric: undefined;
  
  // Search & Navigation
  UniversalSearch: { query?: string; category?: string };
  
  // Profile & Settings
  MyProfile: undefined;
  EditProfile: undefined;
  UnifiedSettings: { section?: string };
  PaymentMethods: undefined;
  NotificationSettings: undefined;
  BusinessHours: undefined;
  
  // Reports & Analytics
  Reports: undefined;
  FinancialReports: { reportType?: string };
  ProfitLoss: { dateRange?: string };
  TaxSummary: { year?: number };
  
  // Employee Management
  EmployeeManagement: undefined;
  AddEmployee: undefined;
  EmployeeAttendance: undefined;
  
  // Data Management
  ImportData: undefined;
  DataManagement: undefined;
  
  // Support & Help
  HelpFAQ: undefined;
  ContactSupport: undefined;
  About: undefined;
  
  // Financial
  FinancialManagement: undefined;
  ExpenseForm: { expenseId?: string };
  CashReconciliation: undefined;
  
  // Subscription
  Subscription: undefined;
  
  // QR Scanner
  QRScanner: undefined;
  
  // Notifications
  Notifications: undefined;
  
  // Additional Screens
  Sales: undefined;
  UnifiedInventory: undefined;
  Accounting: undefined;
};

// Screen Props Types
export type RootStackScreenProps<T extends keyof RootStackParamList> = StackScreenProps<
  RootStackParamList,
  T
>;

export type TabScreenProps<T extends keyof TabParamList> = BottomTabScreenProps<
  TabParamList,
  T
>;

// Navigation Prop Types
export type NavigationProp = RootStackScreenProps<keyof RootStackParamList>['navigation'];
export type RouteProp<T extends keyof RootStackParamList> = RootStackScreenProps<T>['route'];

// Deep linking configuration is now in src/config/deepLinking.ts

// Navigation Helper Types
export interface NavigationState {
  currentRoute: keyof RootStackParamList;
  currentTab: keyof TabParamList;
  isModalOpen: boolean;
  modalStack: string[];
  routeHistory: Array<{
    route: string;
    timestamp: number;
    params?: object;
  }>;
}

// Navigation Action Types
export interface NavigationActions {
  navigate: <T extends keyof RootStackParamList>(
    screen: T,
    params?: RootStackParamList[T]
  ) => void;
  goBack: () => void;
  reset: (state: object) => void;
  navigateToTab: <T extends keyof TabParamList>(tab: T) => void;
  openModal: (modalName: string, params?: object) => void;
  closeModal: () => void;
}

// Deep Link Handler Types
export interface DeepLinkHandler {
  handleDeepLink: (url: string) => void;
  buildDeepLink: <T extends keyof RootStackParamList>(
    screen: T,
    params?: RootStackParamList[T]
  ) => string;
}

declare global {
  namespace ReactNavigation {
    interface RootParamList extends RootStackParamList {}
  }
}