/**
 * Modern Order Types & Interfaces
 * Complete type definitions for the tailoring business
 */

export type OrderStatus = 
  | 'draft'
  | 'confirmed'
  | 'measurements_taken'
  | 'cutting'
  | 'stitching'
  | 'first_fitting'
  | 'adjustments'
  | 'final_fitting'
  | 'ready'
  | 'delivered'
  | 'completed'
  | 'cancelled'
  | 'on_hold';

export type OrderType = 'stitch' | 'repair' | 'alter' | 'design';

export type PaymentStatus = 'unpaid' | 'partial' | 'paid' | 'refunded';

export type Priority = 'low' | 'normal' | 'high' | 'urgent';

export interface OrderMeasurement {
  id: string;
  name: string;
  value: number;
  unit: 'cm' | 'inch' | 'feet';
  notes?: string;
}

export interface OrderItem {
  id: string;
  garmentType: string;
  category: string;
  quantity: number;
  price: number;
  measurements: OrderMeasurement[];
  fabric?: {
    type: string;
    color: string;
    quantity: number;
    source: 'customer' | 'shop';
  };
  notes?: string;
}

export interface OrderPayment {
  id: string;
  amount: number;
  method: 'cash' | 'card' | 'bank_transfer' | 'mobile_payment';
  date: string;
  reference?: string;
  notes?: string;
}

export interface OrderTimeline {
  id: string;
  status: OrderStatus;
  timestamp: string;
  notes?: string;
  user?: string;
}

export interface Order {
  // Core Information
  id: string;
  orderNumber: string;
  
  // Customer Information
  customerId: string;
  customerName: string;
  customerPhone: string;
  customerEmail?: string;
  
  // Order Details
  type: OrderType;
  status: OrderStatus;
  priority: Priority;
  
  // Items & Pricing
  items: OrderItem[];
  subtotal: number;
  discount: number;
  discountType: 'percentage' | 'fixed';
  tax: number;
  total: number;
  
  // Payment Information
  paymentStatus: PaymentStatus;
  payments: OrderPayment[];
  paidAmount: number;
  balanceAmount: number;
  
  // Dates
  createdAt: string;
  updatedAt: string;
  dueDate: string;
  deliveryDate?: string;
  
  // Additional Information
  notes?: string;
  internalNotes?: string;
  references: string[]; // Image URLs
  tags: string[];
  // Tracking
  timeline: OrderTimeline[];
  // Delivery
  deliveryAddress?: string;
  deliveryInstructions?: string;
  
  // Metadata
  isUrgent: boolean;
  isArchived: boolean;
  archivedAt?: string;
}

export interface OrderFilters {
  status?: OrderStatus[];
  type?: OrderType[];
  priority?: Priority[];
  paymentStatus?: PaymentStatus[];
  dateRange?: {
    start: string;
    end: string;
  };
  customer?: string;
  search?: string;
}

export interface OrderSortOptions {
  field: 'createdAt' | 'dueDate' | 'total' | 'customerName' | 'status';
  direction: 'asc' | 'desc';
}

export interface OrderStats {
  total: number;
  byStatus: Record<OrderStatus, number>;
  byType: Record<OrderType, number>;
  byPriority: Record<Priority, number>;
  revenue: {
    total: number;
    paid: number;
    pending: number;
  };
  averageOrderValue: number;
  completionRate: number;
}

// Form interfaces
export interface OrderFormData {
  customerId: string;
  customerName: string;
  customerPhone: string;
  customerEmail?: string;
  type: OrderType;
  priority: Priority;
  items: Omit<OrderItem, 'id'>[];
  discount: number;
  discountType: 'percentage' | 'fixed';
  dueDate: string;
  deliveryAddress?: string;
  notes?: string;
  references: string[];
  tags: string[];
  isUrgent: boolean;
  advancePayment?: number;
  paymentMethod?: string;
}

export interface OrderValidationError {
  field: string;
  message: string;
}

export interface OrderValidationResult {
  isValid: boolean;
  errors: OrderValidationError[];
}

// Constants
export const ORDER_STATUS_LABELS: Record<OrderStatus, string> = {
  draft: 'Draft',
  confirmed: 'Confirmed',
  measurements_taken: 'Measurements Taken',
  cutting: 'Cutting',
  stitching: 'Stitching',
  first_fitting: 'First Fitting',
  adjustments: 'Adjustments',
  final_fitting: 'Final Fitting',
  ready: 'Ready for Delivery',
  delivered: 'Delivered',
  completed: 'Completed',
  cancelled: 'Cancelled',
  on_hold: 'On Hold'};

export const ORDER_TYPE_LABELS: Record<OrderType, string> = {
  stitch: 'New Stitching',
  repair: 'Repair',
  alter: 'Alteration',
  design: 'Custom Design'};

export const PRIORITY_LABELS: Record<Priority, string> = {
  low: 'Low',
  normal: 'Normal',
  high: 'High',
  urgent: 'Urgent'};

export const PAYMENT_STATUS_LABELS: Record<PaymentStatus, string> = {
  unpaid: 'Unpaid',
  partial: 'Partially Paid',
  paid: 'Fully Paid',
  refunded: 'Refunded'};

// Status flow configuration
export const STATUS_FLOW: Record<OrderStatus, OrderStatus[]> = {
  draft: ['confirmed', 'cancelled'],
  confirmed: ['measurements_taken', 'cancelled', 'on_hold'],
  measurements_taken: ['cutting', 'on_hold'],
  cutting: ['stitching', 'on_hold'],
  stitching: ['first_fitting', 'on_hold'],
  first_fitting: ['adjustments', 'final_fitting', 'on_hold'],
  adjustments: ['final_fitting', 'on_hold'],
  final_fitting: ['ready', 'adjustments', 'on_hold'],
  ready: ['delivered', 'on_hold'],
  delivered: ['completed'],
  completed: [],
  cancelled: [],
  on_hold: ['confirmed', 'measurements_taken', 'cutting', 'stitching', 'cancelled']};
