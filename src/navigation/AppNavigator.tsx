import React from 'react';
import { View, ActivityIndicator, StyleSheet   } from 'react-native';
import { createStackNavigator   } from '@react-navigation/stack';
import { useTheme   } from '../context/ThemeContext';
import { useAuth   } from '../context/AuthContext';
import type { RootStackParamList } from '../types/navigation';

import LazyReportsScreen from '../components/lazy/LazyReportsScreen';
import LazyFinancialReportsScreen from '../components/lazy/LazyFinancialReportsScreen';
import LazyProfitLossScreen from '../components/lazy/LazyProfitLossScreen';
import LazyTaxSummaryScreen from '../components/lazy/LazyTaxSummaryScreen';
import LazyQRScannerScreen from '../components/lazy/LazyQRScannerScreen';
// LazyQRScanner and LazyImagePicker - not used in this navigator
import LazyCreateOrderScreen from '../components/lazy/LazyCreateOrderScreen';
import LazyUnifiedInventoryScreen from '../components/lazy/LazyUnifiedInventoryScreen';
import LazyDataManagementScreen from '../components/lazy/LazyDataManagementScreen';

// Import navigators and screens
import TabNavigator from './TabNavigator';
// ProductsScreen removed - using GarmentsScreen instead
// import CustomersScreen from '../screens/CustomersScreen'; // Removed - using CRM instead
// SearchScreen removed - using UniversalSearchScreen instead
import UniversalSearchScreen from '../screens/UniversalSearchScreen';
import ProfileScreen from '../screens/ProfileScreen';
import AddProductScreen from '../screens/AddProductScreen';
// AddOrderScreen removed - using CreateOrderScreen instead
// PDFInvoiceSettingsScreen and BusinessSettingsScreen consolidated into UnifiedSettingsScreen
import CustomerMeasurementsScreen from '../screens/CustomerMeasurementsScreen';
import GarmentTemplatesScreen from '../screens/GarmentTemplatesScreen';
import AddCustomerScreen from '../screens/AddCustomerScreen';
import AddMeasurementScreen from '../screens/AddMeasurementScreen';
import EditGarmentTemplateScreen from '../screens/EditGarmentTemplateScreen';
import AddFabricScreen from '../screens/AddFabricScreen';
import OrderSuccessScreen from '../screens/OrderSuccessScreen';

// QuickOrderScreen functionality now integrated into CreateOrderScreen
// CategoriesScreen removed - using ProductsScreen instead
import PaymentMethodsScreen from '../screens/PaymentMethodsScreen';
import EditProfileScreen from '../screens/EditProfileScreen';
// SecuritySettingsScreen consolidated into UnifiedSettingsScreen
import ImportDataScreen from '../screens/ImportDataScreen';

// ReportsScreen - using LazyReportsScreen instead
import HelpFAQScreen from '../screens/HelpFAQScreen';
import ContactSupportScreen from '../screens/ContactSupportScreen';
import AboutScreen from '../screens/AboutScreen';
import SalesScreen from '../screens/SalesScreen';
import EmployeeAttendanceScreen from '../screens/EmployeeAttendanceScreen';
import BusinessHoursScreen from '../screens/BusinessHoursScreen';
import NotificationSettingsScreen from '../screens/NotificationSettingsScreen';
// UnifiedInventoryScreen - using LazyUnifiedInventoryScreen instead
// StockTransactionsScreen removed - using UnifiedInventoryScreen instead

// IconShowcaseScreen removed - not needed
import AccountingScreen from '../screens/AccountingScreen';
import SubscriptionScreen from '../screens/SubscriptionScreen';
import LoginScreen from '../screens/LoginScreen';
import EmployeeManagementScreen from '../screens/EmployeeManagementScreen';
import AddEmployeeScreen from '../screens/AddEmployeeScreen';
// DataManagementScreen - using LazyDataManagementScreen instead
// BusinessAnalyticsScreen removed - was causing crashes
// ProfitLossScreen - using LazyProfitLossScreen instead
// PaymentAnalyticsScreen consolidated into FinancialReportsScreen
// TaxSummaryScreen - using LazyTaxSummaryScreen instead
import NotificationScreen from '../screens/NotificationScreen';
// QRScannerScreen - using LazyQRScannerScreen instead
// QROrderDetailsScreen removed - using OrderDetailsScreen instead
import OrderDetailsScreen from '../screens/OrderDetailsScreen';
import AddGarmentTemplateScreen from '../screens/AddGarmentTemplateScreen';

// CRMScreen removed - functionality integrated into CustomersScreen
import CustomerDetailsScreen from '../screens/CustomerDetailsScreen';


import ExpenseFormScreen from '../screens/ExpenseFormScreen';
import CashReconciliationScreen from '../screens/CashReconciliationScreen';
// ThemeSettingsScreen consolidated into UnifiedSettingsScreen
// AdminSettingsScreen consolidated into UnifiedSettingsScreen
import UnifiedSettingsScreen from '../screens/UnifiedSettingsScreen';
// FinancialReportsScreen - using LazyFinancialReportsScreen instead
import FinancialManagementScreen from '../screens/FinancialManagementScreen';


// ProtectedRoute - not used in this navigator

const Stack = createStackNavigator<RootStackParamList>();



const AppNavigator = () => {
  const { theme  } = useTheme();
  const { isAuthenticated, isLoading  } = useAuth();

  // For debugging: bypass auth check
  const BYPASS_AUTH = false;

  // Show loading screen while checking authentication
  if(!BYPASS_AUTH && isLoading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: theme.colors.background }>
        <ActivityIndicator size="large" color={theme.colors.primary} />
      </View>
    );
  }

  // Show login screen if not authenticated
  if(!BYPASS_AUTH && !isAuthenticated) {
    return (
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          cardStyle: { backgroundColor: theme.colors.background }}}
      >
        <Stack.Screen name="Login" component={LoginScreen} />
      </Stack.Navigator>
    );
  }

  // Show main app if authenticated
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        cardStyle: { backgroundColor: theme.colors.background },
        // Unified transition animations
        cardStyleInterpolator: ({ current, layouts }) => { 
          return {
            cardStyle: {
              transform: [
                {
                  translateX: current.progress.interpolate({
                    inputRange: [0, 1],
                    outputRange: [layouts.screen.width, 0]})}]}};
        }}}
      initialRouteName="Main"
    >
      {/* Main Tab Navigator */}
      <Stack.Screen
        name="Main"
        component={TabNavigator}
        options={{
          headerShown: false}
      />

      {/* Modal Screens */}
      {/* Products screen removed - using Garments instead */}

      {/* Customer Details Screen */}
      <Stack.Screen
        name="CustomerDetails"
        component={CustomerDetailsScreen}
        options={{
          headerShown: false}
      />

      {/* Order Details Screen */}
      <Stack.Screen
        name="OrderDetails"
        component={OrderDetailsScreen}
        options={{
          headerShown: false}
      />

      {/* Product Details Screen */}
      {/* ProductDetails removed - using GarmentDetails instead */}

      {/* Removed duplicate screens - these are now handled by tab navigation */}

      {/* Search Screen removed - using UniversalSearch instead */}

      {/* Universal Search Screen */}
      <Stack.Screen
        name="UniversalSearch"
        component={UniversalSearchScreen}
        options={{
          headerShown: false}
      />

      {/* Profile & Settings Screen */}
      <Stack.Screen
        name="MyProfile"
        component={ProfileScreen}
        options={{
          headerShown: false}
      />

      {/* Add Product Screen */}
      <Stack.Screen
        name="AddProduct"
        component={AddProductScreen}
        options={{
          headerShown: false}
      />

      {/* Order Creation - Consolidated */}
      <Stack.Screen
        name="CreateOrder"
        component={LazyCreateOrderScreen}
        options={{
          headerShown: false,
          presentation: 'modal'}
      />

      <Stack.Screen
        name="OrderSuccess"
        component={OrderSuccessScreen}
        options={{
          headerShown: false}
      />

      {/* PDFInvoiceSettings consolidated into UnifiedSettings */}

      {/* BusinessSettings consolidated into UnifiedSettings */}

      <Stack.Screen
        name="CustomerMeasurements"
        component={CustomerMeasurementsScreen}
        options={{
          headerShown: false}
      />

      <Stack.Screen
        name="GarmentTemplates"
        component={GarmentTemplatesScreen}
        options={{
          headerShown: false}
      />

      {/* Add Customer Screen */}
      <Stack.Screen
        name="AddCustomer"
        component={AddCustomerScreen}
        options={{
          headerShown: false}
      />

      {/* Add Measurement Screen */}
      <Stack.Screen
        name="AddMeasurement"
        component={AddMeasurementScreen}
        options={{
          headerShown: false}
      />

      {/* Edit Garment Template Screen */}
      <Stack.Screen
        name="EditGarmentTemplate"
        component={EditGarmentTemplateScreen}
        options={{
          headerShown: false}
      />

      {/* Add Fabric Screen */}
      <Stack.Screen
        name="AddFabric"
        component={AddFabricScreen}
        options={{
          headerShown: false}
      />



      {/* QuickOrder functionality now integrated into CreateOrder */}

      {/* Categories Screen removed - using Products instead */}

      {/* Payment Methods Screen */}
      <Stack.Screen
        name="PaymentMethods"
        component={PaymentMethodsScreen}
        options={{
          headerShown: false}
      />

      {/* Edit Profile Screen */}
      <Stack.Screen
        name="EditProfile"
        component={EditProfileScreen}
        options={{
          headerShown: false}
      />

      {/* Security Settings Screen */}
      {/* SecuritySettings consolidated into UnifiedSettings */}

      {/* Theme Settings Screen */}
      {/* ThemeSettings consolidated into UnifiedSettings */}

      {/* Unified Settings Screen - Consolidated settings */}
      <Stack.Screen
        name="UnifiedSettings"
        component={UnifiedSettingsScreen}
        options={{
          headerShown: false}
      />

      {/* Import Data Screen */}
      <Stack.Screen
        name="ImportData"
        component={ImportDataScreen}
        options={{
          headerShown: false}
      />



      {/* Reports Screen */}
      <Stack.Screen
        name="Reports"
        component={LazyReportsScreen}
        options={{
          headerShown: false}
      />

      {/* Help & FAQ Screen */}
      <Stack.Screen
        name="HelpFAQ"
        component={HelpFAQScreen}
        options={{
          headerShown: false}
      />

      {/* Contact Support Screen */}
      <Stack.Screen
        name="ContactSupport"
        component={ContactSupportScreen}
        options={{
          headerShown: false}
      />

      {/* About Screen */}
      <Stack.Screen
        name="About"
        component={AboutScreen}
        options={{
          headerShown: false}
      />

      {/* Sales Screen */}
      <Stack.Screen
        name="Sales"
        component={SalesScreen}
        options={{
          headerShown: false}
      />

      {/* Employee Attendance Screen */}
      <Stack.Screen
        name="EmployeeAttendance"
        component={EmployeeAttendanceScreen}
        options={{
          headerShown: false}
      />

      {/* Business Hours Screen */}
      <Stack.Screen
        name="BusinessHours"
        component={BusinessHoursScreen}
        options={{
          headerShown: false}
      />

      {/* Notification Settings Screen */}
      <Stack.Screen
        name="NotificationSettings"
        component={NotificationSettingsScreen}
        options={{
          headerShown: false}
      />

      {/* Notifications Screen */}
      <Stack.Screen
        name="Notifications"
        component={NotificationScreen}
        options={{
          headerShown: false}
      />

      {/* QR Scanner Screen */}
      <Stack.Screen
        name="QRScanner"
        component={LazyQRScannerScreen}
        options={{
          headerShown: false}
      />

      {/* Removed QROrderDetails - using OrderDetails instead */}

      {/* Add Garment Template Screen */}
      <Stack.Screen
        name="AddGarmentTemplate"
        component={AddGarmentTemplateScreen}
        options={{
          headerShown: false}
      />



      {/* Unified Inventory Screen */}
      <Stack.Screen
        name="UnifiedInventory"
        component={LazyUnifiedInventoryScreen}
        options={{
          headerShown: false}
      />

      {/* Stock Transactions Screen */}
      {/* StockTransactions consolidated into UnifiedInventory */}





      {/* Icon Showcase Screen removed - not needed */}

      {/* Accounting Screen */}
      <Stack.Screen
        name="Accounting"
        component={AccountingScreen}
        options={{
          headerShown: false}
      />

      {/* Subscription Management Screen */}
      <Stack.Screen
        name="Subscription"
        component={SubscriptionScreen}
        options={{
          headerShown: false}
      />

      {/* Employee Management Screen (Admin only) */}
      <Stack.Screen
        name="EmployeeManagement"
        component={EmployeeManagementScreen}
        options={{
          headerShown: false}
      />

      {/* Add Employee Screen (Admin only) */}
      <Stack.Screen
        name="AddEmployee"
        component={AddEmployeeScreen}
        options={{
          headerShown: false}
      />

      {/* Data Management Screen (Admin only) */}
      <Stack.Screen
        name="DataManagement"
        component={LazyDataManagementScreen}
        options={{
          headerShown: false}
      />

      {/* Analytics Screens */}
      {/* BusinessAnalytics screen removed - was causing crashes */}

      <Stack.Screen
        name="ProfitLoss"
        component={LazyProfitLossScreen}
        options={{
          headerShown: false}
      />

      {/* PaymentAnalytics consolidated into FinancialReports */}

      <Stack.Screen
        name="TaxSummary"
        component={LazyTaxSummaryScreen}
        options={{
          headerShown: false}
      />

      {/* Combined Financial Reports Screen */}
      <Stack.Screen
        name="FinancialReports"
        component={LazyFinancialReportsScreen}
        options={{
          headerShown: false}
      />

      {/* CRM functionality now integrated into CustomersScreen */}





      {/* Financial Management Screens */}
      <Stack.Screen
        name="FinancialManagement"
        component={FinancialManagementScreen}
        options={{
          headerShown: false}
      />

      <Stack.Screen
        name="ExpenseForm"
        component={ExpenseFormScreen}
        options={{
          headerShown: false}
      />

      <Stack.Screen
        name="CashReconciliation"
        component={CashReconciliationScreen}
        options={{
          headerShown: false}
      />

      {/* Admin Settings Screen */}
      {/* AdminSettings consolidated into UnifiedSettings */}



    </Stack.Navigator>
  );
};


const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center'}});

export default React.memo(AppNavigator);
