import React, { Suspense } from 'react';
import { createBottomTabNavigator   } from '@react-navigation/bottom-tabs';
import { StyleSheet   } from 'react-native';
import PhosphorIcon from '../components/PhosphorIcon';
import DashboardScreen from '../screens/DashboardScreen';
import OrdersScreen from '../screens/OrdersScreen';
import GarmentsScreen from '../screens/GarmentsScreen';
import CRMScreen from '../screens/CRMScreen';
import ReportsScreen from '../screens/ReportsScreen';
import { useTheme   } from '../context/ThemeContext';
import type { TabParamList } from '../types/navigation';

const Tab = createBottomTabNavigator<TabParamList>();

const TabNavigator = () => {
  const { theme, isDarkMode  } = useTheme();

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: string;

          switch(route.name) {
            case 'Dashboard':
              iconName = 'home';
              break;
            case 'Orders':
              iconName = 'shopping-bag';
              break;
            case 'Products':
              iconName = 'Package';
              break;
            case 'CRM':
              iconName = 'users';
              break;
            case 'Analytics':
              iconName = 'ChartBar';
              break;
            default:
              iconName = 'circle';
        }

          return (
            <PhosphorIcon
              name={iconName}
              size="20"
              color={color}
              weight={focused ? 'fill' : 'regular'}
              containerSize={32}
              backgroundColor="transparent"
              borderRadius={0}
              style={{}
              containerStyle={{}
              accessibilityLabel={`${route.name} tab icon />`
          );
        },
        tabBarActiveTintColor: theme.colors.primary,
        tabBarInactiveTintColor: theme.colors.onSurfaceVariant,
        tabBarStyle: {
          backgroundColor: theme.colors.surface,
          borderTopWidth: 0,
          height: 70,
          paddingBottom: 8,
          paddingTop: 8,
        },
        tabBarLabelStyle: {
          color: theme.colors.onSurface,
          fontSize: 12,
          marginTop: 2,
        },
        tabBarItemStyle: {
          paddingHorizontal: 4,
        },
        headerShown: false,
      })}
    >
      <Tab.Screen 
        name="Dashboard" 
        options={{ title: 'Dashboard' }>
        {(props) => <DashboardScreen {...props} navigateToTab={(tab: string) => props.navigation.navigate(tab)} />}
      </Tab.Screen>
      <Tab.Screen 
        name="Orders" 
        component={OrdersScreen}
        options={{ title: 'Orders' }
      />
      <Tab.Screen 
        name="Products" 
        component={GarmentsScreen}
        options={{ title: 'Products' }
      />
      <Tab.Screen 
        name="CRM" 
        component={CRMScreen}
        options={{ title: 'CRM' }
      />
      <Tab.Screen 
        name="Analytics" 
        component={ReportsScreen}
        options={{ title: 'Analytics' }
      />
    </Tab.Navigator>
  );
};


const styles = StyleSheet.create({});

export default React.memo(TabNavigator);
