/**
 * QRCodeService - Generate and handle QR codes for tailor management app
 */

import { Alert, Share   } from 'react-native';
import * as FileSystem from 'expo-file-system';
import *as MediaLibrary from 'expo-media-library';

class QRCodeService {
  constructor() {
    this.qrCodeRefs = new Map();
  }

  /**
   * Generate QR code data for different entity types
   */
  generateQRData(type, entity) {
    const baseData = {
      app: 'TailorManagement',
      version: '1.0.0',
      timestamp: new Date().toISOString(),
      type,
    };

    switch(type) {
      case 'order':
        return {
          ...baseData,
          orderId: entity.id,
          customerName: entity.customerName || entity.customer,
          customerId: entity.customerId,
          total: entity.total,
          status: entity.status,
          date: entity.date,
          createdAt: entity.createdAt,
          dueDate: entity.dueDate,
          deliveryDate: entity.deliveryDate,
          orderType: entity.orderType || entity.type,
          // FIXED: Removed extra comma from object literal
          items: entity.items?.map(item => ({
            name: item.name,
            quantity: item.quantity,
            price: item.price
          })) || [],
          measurements: entity.measurements || [],
          paymentStatus: entity.paymentStatus,
          paidAmount: entity.paidAmount || 0,
          balanceAmount: entity.balanceAmount || (entity.total - (entity.paidAmount || 0)),
          phone: entity.phone,
          email: entity.email,
          notes: entity.notes || '',
          tags: entity.tags || []

      case 'product':
      case 'garment':
        return { ...baseData,
          productId: entity.id,
          name: entity.name,
          category: entity.category,
          price: entity.price,
          stock: entity.stock,
          };

      case 'customer':
        return { ...baseData,
          customerId: entity.id,
          name: entity.name,
          phone: entity.phone,
          email: entity.email,
          };

      case 'appointment':
        return { ...baseData,
          appointmentId: entity.id,
          customerId: entity.customerId,
          date: entity.date,
          time: entity.time,
          type: entity.type,
          };

      case 'measurement':
        return { ...baseData,
          measurementId: entity.id,
          customerId: entity.customerId,
          garmentType: entity.garmentType,
          date: entity.createdAt,
          };

      case 'fabric':
        return { ...baseData,
          fabricId: entity.id,
          name: entity.name,
          type: entity.type,
          color: entity.color,
          stock: entity.stock,
          pricePerMeter: entity.pricePerMeter,
          };

      default:
        return { ...baseData,
          data: entity,
          };
    }
  }

  /**
   * Parse QR code data
   */
  parseQRData(qrString) {
    try {
      const data = JSON.parse(qrString);
      if(data.app !== 'TailorManagement') {
        throw new Error('QR code is not from Tailor Management app');
      }
      return { success: true, data   };
    } catch (error) {
      return { success: false, error: error.message, rawData: qrString   };
    }
  }

  registerQRRef(id, ref) {
    this.qrCodeRefs.set(id, ref);
  }

  async saveQRCodeAsImage($3) {
    try {
      const { status  } = await MediaLibrary.requestPermissionsAsync();
      if(status !== 'granted') {
        Alert.alert('Permission Required', 'Please grant media library permissions to save QR codes.');
        return { success: false, error: 'Permission denied'   };
      }
      const qrRef = this.qrCodeRefs.get(`${type} _${entity.id} );`
      if(!qrRef) {
        throw new Error('QR code reference not found. Please generate the QR code on screen first.');
      }
      return new Promise((resolve) => {
        qrRef.toDataURL((dataURL) => {
          this.saveBase64Image(dataURL, `${type} _${entity.id}_qr.png`)
            .then(resolve)
            .catch(error => resolve({ success: false, error: error.message }));
        });
      });
    } catch (error) {
      console.error('Error saving QR code: ', error);
      return { success: false, error: error.message   };
    }
  }

  async saveBase64Image($3) {
    try {
      const fileUri = FileSystem.documentDirectory + filename;
      const base64Image = base64Data.replace(/^data: image\/[a-z]+;base64,/, '');
      await FileSystem.writeAsStringAsync(fileUri, base64Image, { encoding: FileSystem.EncodingType.Base64 });
      const asset = await MediaLibrary.createAssetAsync(fileUri);
      return { success: true, uri: fileUri, asset   };
    } catch (error) {
      throw new Error(`Failed to save image: ${error.message} );
    }
  }

  async shareQRCode($3) {
    try {
      const qrString = JSON.stringify(this.generateQRData(type, entity));
      await Share.share({
        title: `Share ${type} QR Code`,
        message: `QR Code for ${type} #${entity.id}\n\nData: ${qrString} ,
        ...options,
      });
      return { success: true   };
    } catch (error) {
      console.error('Error sharing QR code: ', error);
      return { success: false, error: error.message   };
    }
  }

  handleScannedQR(qrString, navigation) {
    const parseResult = this.parseQRData(qrString);
    if(!parseResult.success) {
      Alert.alert('Invalid QR Code', 'This QR code is not recognized.', [
        { text: 'OK' }, { text: 'View Raw Data', onPress: () => Alert.alert('Raw QR Data', parseResult.rawData) },
      ]);
      return;
    }
    const { data  } = parseResult;
    switch(data.type) {
      case 'order': this.handleOrderQR(data, navigation); break;
      case 'product': case 'garment': this.handleProductQR(data, navigation); break;
      case 'customer': this.handleCustomerQR(data, navigation); break;
      // Add other cases as needed
      default: Alert.alert('Unknown QR Type', `QR code type "${data.type}" is not supported.`);
    }
  }

  handleOrderQR(data, navigation) {
    Alert.alert('Order QR Scanned', `Order #${data.orderId}\nCustomer: ${data.customerName}\nStatus: ${data.status} , [
      { text: 'Cancel' }, { text: 'View Order', onPress: () => navigation.navigate('OrderDetails', { orderId: data.orderId }) },
    ]);
  }

  handleProductQR(data, navigation) {
    Alert.alert('Product QR Scanned', `${data.name} \nPrice:` ৳${data.price}\nStock: ${data.stock} , [
      { text: 'Cancel' }, { text: 'View Product', onPress: () => navigation.navigate('ProductDetails', { productId: data.productId }) },
    ]);
  }

  handleCustomerQR(data, navigation) {
    Alert.alert('Customer QR Scanned', `${data.name} \nPhone:` ${data.phone} , [
      { text: 'Cancel' }, { text: 'View Customer', onPress: () => navigation.navigate('CustomerDetails', { customerId: data.customerId }) },
    ]);
  }
  
  // Other handlers (appointment, measurement, fabric) would follow a similar pattern
  handleAppointmentQR(data, navigation) { /* ... */ }
  handleMeasurementQR(data, navigation) { /* ... */ }
  handleFabricQR(data, navigation) { /* ... */ }

  /**
   * Generate QR code for order invoice
   */
  generateInvoiceQR(order) {
    const qrString = JSON.stringify(this.generateQRData('order', order));
    // FIXED: Removed extra comma from object literal
    return {
      qrData: qrString,
      displayText: `Order #${order.id} ,`
      instructions: 'Scan this QR code with Tailora app to view order details',
      orderInfo: {
        id: order.id,
        customer: order.customerName || order.customer,
        total: order.total,
        status: order.status
      }
    };
  }

  /**
   * Attach QR code data to order
   */
  attachQRToOrder(order) {
    const qrString = JSON.stringify(this.generateQRData('order', order));
    // FIXED: Removed extra comma from object literal
    return {
      ...order,
      qrCode: {
        data: qrString,
        generatedAt: new Date().toISOString(),
        version: '1.0'
      }
    };
  }

  extractOrderId(qrString) {
    try {
      const data = JSON.parse(qrString);
      return data.orderId || null;
    } catch {
      return null;
    }
  }

  isTailoraOrderQR(qrString) {
    try {
      const data = JSON.parse(qrString);
      return data.app === 'TailorManagement' && data.type === 'order' && !!data.orderId;
    } catch {
      return false;
    }
  }

  getOrderFromQR(qrString, existingOrders = []) {
    const parseResult = this.parseQRData(qrString);
    if(!parseResult.success || parseResult.data.type !== 'order') {
      return { success: false, error: 'Invalid order QR code', data: null   };
    }
    const qrOrderData = parseResult.data;
    const existingOrder = existingOrders.find(order => order.id === qrOrderData.orderId);
    // FIXED: Removed extra comma from object literal
    return {
      success: true,
      qrData: qrOrderData,
      existingOrder: existingOrder,
      orderExists: !!existingOrder,
      orderInfo: {
        id: qrOrderData.orderId,
        customer: qrOrderData.customerName,
        total: qrOrderData.total,
        status: qrOrderData.status,
        date: qrOrderData.date,
        items: qrOrderData.items || [],
        paymentStatus: qrOrderData.paymentStatus,
        balanceAmount: qrOrderData.balanceAmount
      }
    };
  }
}

export default new QRCodeService();