/**
 * Background Process Optimizer
 * Manages and optimizes all background processes in the Tailora app
 * Reduces battery drain and improves performance
 */

import { AppState   } from 'react-native';

class BackgroundProcessOptimizer {
  constructor() {
    this.processes = new Map();
    this.isAppActive = true;
    this.throttleRates = {
      active: 1.0,    // Normal speed when app is active
      background: 0.3, // 30% speed when app is in background
      inactive: 0.1    // 10% speed when app is inactive
    };
    
    this.setupAppStateListener();
  }

  /**
   * Register a background process for optimization
   */
  registerProcess(id, config) {
    const { interval = 30000,        // Default 30 seconds
      priority = 'medium',     // low, medium, high, critical
      canThrottle = true,      // Can this process be throttled?
      activeOnly = false,      // Only run when app is active?
      callback,
      description = ''
     } = config;

    const process = {
      id,
      interval,
      originalInterval: interval,
      priority,
      canThrottle,
      activeOnly,
      callback,
      description,
      intervalId: null,
      isRunning: false,
      lastRun: null,
      runCount: 0
    };

    this.processes.set(id, process);
    
    // Start the process if app is active or if it's critical
    if(this.isAppActive || priority === 'critical' || !activeOnly) {
      this.startProcess(id);
    }

    console.log(`📊 Registered background process: ${id} (${description})`);
    return process;
  }

  /**
   * Start a specific process
   */
  startProcess(id) {
    const process = this.processes.get(id);
    if (!process || process.isRunning) return;

    // Don't start activeOnly processes when app is not active
    if (process.activeOnly && !this.isAppActive) return;

    process.intervalId = setInterval(() => {
      try {
        process.callback();
        process.lastRun = Date.now();
        process.runCount++;
      } catch (error) {
        console.error(`Error in background process ${id}:`, error);
      }
    }, process.interval);

    process.isRunning = true;

  }

  /**
   * Stop a specific process
   */
  stopProcess(id) {
    const process = this.processes.get(id);
    if (!process || !process.isRunning) return;

    clearInterval(process.intervalId);
    process.intervalId = null;
    process.isRunning = false;

  }

  /**
   * Throttle process intervals based on app state
   */
  throttleProcesses() {
    const throttleRate = this.isAppActive ? 
      this.throttleRates.active : 
      this.throttleRates.background;

    this.processes.forEach((process, id) => {
      if (!process.canThrottle) return;

      const newInterval = Math.round(process.originalInterval / throttleRate);
      
      if(newInterval !== process.interval) {
        process.interval = newInterval;
        
        // Restart process with new interval
        if(process.isRunning) {
          this.stopProcess(id);
          this.startProcess(id);
        }
      }
    });

    console.log(`🎛️ Throttled processes (rate: ${throttleRate})`);
  }

  /**
   * Setup app state listener for optimization
   */
  setupAppStateListener() {
    AppState.addEventListener('change', (nextAppState) => {
      const wasActive = this.isAppActive;
      this.isAppActive = nextAppState === 'active';

      if(wasActive !== this.isAppActive) {

        this.handleAppStateChange(nextAppState);
      }
    });
  }

  /**
   * Handle app state changes
   */
  handleAppStateChange(appState) {
    switch(appState) {
      case 'active':
        this.resumeProcesses();
        break;
      case 'background':
        this.pauseNonCriticalProcesses();
        break;
      case 'inactive':
        this.pauseAllNonCriticalProcesses();
        break;
    }
    
    this.throttleProcesses();
  }

  /**
   * Resume processes when app becomes active
   */
  resumeProcesses() {
    this.processes.forEach((process, id) => {
      if (!process.isRunning && (process.priority === 'critical' || this.isAppActive)) {
        this.startProcess(id);
      }
    });
  }

  /**
   * Pause non-critical processes when app goes to background
   */
  pauseNonCriticalProcesses() {
    this.processes.forEach((process, id) => {
      if(process.activeOnly || process.priority === 'low') {
        this.stopProcess(id);
      }
    });
  }

  /**
   * Pause all non-critical processes when app becomes inactive
   */
  pauseAllNonCriticalProcesses() {
    this.processes.forEach((process, id) => {
      if(process.priority !== 'critical') {
        this.stopProcess(id);
      }
    });
  }

  /**
   * Get process statistics
   */
  getProcessStats() {
    const stats = {
      totalProcesses: this.processes.size,
      runningProcesses: 0,
      throttledProcesses: 0,
      processes: [];

    this.processes.forEach((process, id) => {
      if (process.isRunning) stats.runningProcesses++;
      if (process.interval !== process.originalInterval) stats.throttledProcesses++;
      
      stats.processes.push({
        id,
        description: process.description,
        priority: process.priority,
        isRunning: process.isRunning,
        interval: process.interval,
        originalInterval: process.originalInterval,
        runCount: process.runCount,
        lastRun: process.lastRun
      });
    });

    return stats;
  }

  /**
   * Optimize all processes
   */
  optimizeAll() {

    // Stop low priority processes if too many are running
    const runningCount = Array.from(this.processes.values())
      .filter(p => p.isRunning).length;
    
    if(runningCount > 5) {
      this.processes.forEach((process, id) => {
        if(process.priority === 'low' && process.isRunning) {
          this.stopProcess(id);
        }
      });
    }

    // Throttle based on current app state
    this.throttleProcesses();

    return this.getProcessStats();
  }

  /**
   * Clean up all processes
   */
  cleanup() {
    this.processes.forEach((process, id) => {
      this.stopProcess(id);
    });
    this.processes.clear();

  }
}

// Create singleton instance
const backgroundProcessOptimizer = new BackgroundProcessOptimizer();

// Register common Tailora background processes
backgroundProcessOptimizer.registerProcess('networkMonitoring', {
  interval: 60000, // Increased to 60s to reduce battery usage
  priority: 'low', // Reduced priority
  canThrottle: true,
  activeOnly: false,
  description: 'Network status monitoring',
  callback: () => {
    // Lightweight network check - only log when status changes
    if (Math.random() > 0.8) { // Only log 20% of the time to reduce noise

    }
  }
});

backgroundProcessOptimizer.registerProcess('dataSync', {
  interval: 300000, // 5 minutes
  priority: 'high',
  canThrottle: true,
  activeOnly: false,
  description: 'Offline data synchronization',
  callback: () => {
    // Data sync logic would go here

  }
});

backgroundProcessOptimizer.registerProcess('notificationCheck', {
  interval: 60000, // 1 minute
  priority: 'medium',
  canThrottle: true,
  activeOnly: true,
  description: 'Notification checking',
  callback: () => {
    // Notification check logic would go here

  }
});

export default backgroundProcessOptimizer;
