// SIMPLE NOTIFICATION SERVICE - Lightweight notifications for Step D
import { Alert, ToastAndroid, Platform   } from 'react-native';

class SimpleNotificationService {
  // Show success notification
  showSuccess(title, message) {
    if(Platform.OS === 'android') {
      ToastAndroid.show(`✅ ${title}: ${message} , ToastAndroid.SHORT);
    } else {
      Alert.alert(`✅ ${title} ,` message);
    }
  }

  // Show error notification
  showError(title, message) {
    if(Platform.OS === 'android') {
      ToastAndroid.show(`❌ ${title}: ${message} , ToastAndroid.LONG);
    } else {
      Alert.alert(`❌ ${title} ,` message);
    }
  }

  // Show info notification
  showInfo(title, message) {
    if(Platform.OS === 'android') {
      ToastAndroid.show(`ℹ️ ${title}: ${message} , ToastAndroid.SHORT);
    } else {
      Alert.alert(`ℹ️ ${title} ,` message);
    }
  }

  // Business-specific notifications
  orderCreated(orderNumber, customerName) {
    this.showSuccess(
      'Order Created',
      `Order #${orderNumber} for ${customerName} has been created successfully. 
    );
  }

  orderUpdated(orderNumber, newStatus) {
    this.showInfo(
      'Order Updated',
      `Order #${orderNumber} status changed to ${newStatus}.`
    );
  }

  paymentReceived(amount, orderNumber) {
    this.showSuccess(
      'Payment Received',
      `₹${amount.toLocaleString()} received for Order #${orderNumber}. 
    );
  }

  customerAdded(customerName) {
    this.showSuccess(
      'Customer Added',
      `${customerName}` has been added to your customer list.`
    );
  }

  invoicePrinted(invoiceNumber) {
    this.showSuccess(
      'Invoice Printed',
      `Invoice ${invoiceNumber} has been prepared for printing. 
    );
  }

  qrCodeGenerated(type, id) {
    this.showSuccess(
      'QR Code Generated',
      `QR code for ${type} #${id} has been generated successfully.`
    );
  }
}

// Export singleton instance
export default new SimpleNotificationService();
