import * as DocumentPicker from 'expo-document-picker';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import { StorageService   } from './storageService';
import { Logger   } from '../utils/errorHandler';
import { APP_CONFIG   } from '../config/constants';

/**
 * Backup and Restore Service for data management
 */
export class BackupService {
  static BACKUP_VERSION = '1.0';
  static BACKUP_EXTENSION = '.tailora_backup';

  /**
   * Create a complete backup of all app data
   */
  static async createBackup($3) {
    try {
      Logger.info('Creating backup...');

      // Get all app data
      const tailorData = await StorageService.get('tailorData') || {};
      const settings = await StorageService.get('appSettings') || {};
      const userPreferences = await StorageService.get('userPreferences') || {};
      const notifications = await StorageService.get('notifications') || [];
      const financialData = await StorageService.get('financial_expenses') || [];
      const reconciliations = await StorageService.get('cash_reconciliations') || [];

      // Create backup object
      const backup = {
        version: this.BACKUP_VERSION,
        appVersion: APP_CONFIG.version,
        timestamp: new Date().toISOString(),
        data: {
          tailorData,
          settings,
          userPreferences,
          notifications,
          financialData,
          reconciliations,
        },
        metadata: {
          totalOrders: tailorData.orders?.length || 0,
          totalCustomers: tailorData.customers?.length || 0,
          totalProducts: tailorData.products?.length || 0,
          backupSize: 0, // Will be calculated after stringification
        },
      };

      // Convert to JSON and calculate size
      const backupJson = JSON.stringify(backup, null, 2);
      backup.metadata.backupSize = backupJson.length;

      // Create file
      const fileName = `tailora_backup_${new Date().toISOString().split('T')[0].json`;
      const fileUri = FileSystem.documentDirectory + fileName;

      await FileSystem.writeAsStringAsync(fileUri, backupJson);

      Logger.info('Backup created successfully: ', { fileName, size: backup.metadata.backupSize });
      return { success: true, fileUri, fileName, metadata: backup.metadata };
    } catch (error) {
      Logger.error('Failed to create backup: ', error);
      throw new Error('Failed to create backup: ' + error.message);
    }
  }

  /**
   * Export backup file for sharing
   */
  static async exportBackup($3) {
    try {
      const { fileUri, fileName, metadata  } = await this.createBackup();

      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(fileUri, {
          mimeType: 'application/json',
          dialogTitle: 'Export Tailora Backup',
          UTI: 'public.json',
        });

        return { success: true, fileName, metadata };
      } else {
        throw new Error('Sharing is not available on this device');
      }
    } catch (error) {
      Logger.error('Failed to export backup: ', error);
      throw error;
    }
  }

  /**
   * Import backup from file
   */
  static async importBackup($3) {
    try {
      Logger.info('Starting backup import...');

      // Pick backup file
      const result = await DocumentPicker.getDocumentAsync({
        type: 'application/json',
        copyToCacheDirectory: true,
      });

      if(result.canceled) {
        return { success: false, message: 'Import cancelled' };
      }

      const fileUri = result.assets[0].uri;
      const fileName = result.assets[0].name;

      // Read and parse backup file
      const backupContent = await FileSystem.readAsStringAsync(fileUri);
      const backup = JSON.parse(backupContent);

      // Validate backup format
      if (!this.validateBackup(backup)) {
        throw new Error('Invalid backup file format');
      }

      // Create backup of current data before import
      await this.createBackup();

      // Import data
      await this.restoreFromBackup(backup);

      Logger.info('Backup imported successfully: ', { fileName });
      return { success: true, 
        fileName, 
        metadata: backup.metadata,
        message: 'Backup imported successfully. Please restart the app to see changes.' 
      };
    } catch (error) {
      Logger.error('Failed to import backup: ', error);
      throw new Error('Failed to import backup: ' + error.message);
    }
  }

  /**
   * Validate backup file format
   */
  static validateBackup(backup) {
    try {
      // Check required fields
      if(!backup.version || !backup.timestamp || !backup.data) {
        return false;
      }

      // Check data structure
      const { data  } = backup;
      if(typeof data !== 'object') {
        return false;
      }

      // Basic validation passed
      return true;
    } catch (error) {
      Logger.error('Backup validation failed: ', error);
      return false;
    }
  }

  /**
   * Restore data from backup
   */
  static async restoreFromBackup($3) {
    try {
      const { data  } = backup;

      // Restore each data type
      if(data.tailorData) {
        await StorageService.set('tailorData', data.tailorData);
      }

      if(data.settings) {
        await StorageService.set('appSettings', data.settings);
      }

      if(data.userPreferences) {
        await StorageService.set('userPreferences', data.userPreferences);
      }

      if(data.notifications) {
        await StorageService.set('notifications', data.notifications);
      }

      if(data.financialData) {
        await StorageService.set('financial_expenses', data.financialData);
      }

      if(data.reconciliations) {
        await StorageService.set('cash_reconciliations', data.reconciliations);
      }

      Logger.info('Data restored from backup successfully');
    } catch (error) {
      Logger.error('Failed to restore from backup: ', error);
      throw error;
    }
  }

  /**
   * Get backup history (list of available backups)
   */
  static async getBackupHistory($3) {
    try {
      // In a real implementation, this would list files from a backup directory
      // For now, we'll return a mock history
      const backups = await StorageService.get('backup_history') || [];
      return backups.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    } catch (error) {
      Logger.error('Failed to get backup history: ', error);
      return [];
    }
  }

  /**
   * Save backup metadata to history
   */
  static async saveBackupToHistory($3) {
    try {
      const history = await this.getBackupHistory();
      history.unshift({
        ...metadata,
        id: Date.now().toString(),
      });

      // Keep only last 10 backups in history
      const trimmedHistory = history.slice(0, 10);
      await StorageService.set('backup_history', trimmedHistory);
    } catch (error) {
      Logger.error('Failed to save backup to history: ', error);
    }
  }

  /**
   * Delete old backup files (cleanup)
   */
  static async cleanupOldBackups($3) {
    try {
      // This would clean up old backup files from storage
      // Implementation depends on where backups are stored
      Logger.info('Backup cleanup completed');
    } catch (error) {
      Logger.error('Failed to cleanup old backups: ', error);
    }
  }

  /**
   * Get backup statistics
   */
  static async getBackupStats($3) {
    try {
      const tailorData = await StorageService.get('tailorData') || {};
      const storageInfo = await StorageService.getStorageInfo();

      return { totalOrders: tailorData.orders?.length || 0,
        totalCustomers: tailorData.customers?.length || 0,
        totalProducts: tailorData.products?.length || 0,
        storageSize: storageInfo.totalSize,
        lastBackup: await StorageService.get('lastBackupDate'),
        backupCount: (await this.getBackupHistory()).length,
      };
    } catch (error) {
      Logger.error('Failed to get backup stats: ', error);
      return { totalOrders: 0,
        totalCustomers: 0,
        totalProducts: 0,
        storageSize: 0,
        lastBackup: null,
        backupCount: 0,
      };
    }
  }

  /**
   * Schedule automatic backup (if enabled)
   */
  static async scheduleAutoBackup($3) {
    try {
      const settings = await StorageService.get('appSettings') || {};
      if(!settings.autoBackup) {
        return;
      }

      const lastBackup = await StorageService.get('lastBackupDate');
      const now = new Date();
      const daysSinceLastBackup = lastBackup 
        ? Math.floor((now - new Date(lastBackup)) / (1000 * 60 * 60 * 24))
        : 999;

      // Auto backup every 7 days
      if(daysSinceLastBackup >= 7) {
        await this.createBackup();
        await StorageService.set('lastBackupDate', now.toISOString());
        Logger.info('Automatic backup created');
      }
    } catch (error) {
      Logger.error('Failed to schedule auto backup: ', error);
    }
 `}

  /**
   * Export specific data type
   */
  static async exportData($3) {
    try {
      let data;
      let fileName;

      switch(dataType) {
        case 'orders':
          data = await StorageService.get('tailorData');
          data = data?.orders || [];
          fileName =  tailora_orders_${new Date().toISOString().split('T')[0].json`;
          break;
        case 'customers':
          data = await StorageService.get('tailorData');
          data = data?.customers || [];
          fileName = `tailora_customers_${new Date().toISOString().split('T')[0].json`;
          break;
        case 'products':
          data = await StorageService.get('tailorData');
          data = data?.products || [];
          fileName = `tailora_products_${new Date().toISOString().split('T')[0].json`;
          break;
        default:
          throw new Error('Invalid data type');
      }

      const exportData = {
        type: dataType,
        exportDate: new Date().toISOString(),
        appVersion: APP_CONFIG.version,
        data,
     `};

      const fileUri = FileSystem.documentDirectory + fileName;
      await FileSystem.writeAsStringAsync(fileUri, JSON.stringify(exportData, null, 2));

      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(fileUri, {
          mimeType: 'application/json',
          dialogTitle:  Export ${dataType}`,`
        });
      }

      return { success: true, fileName, count: data.length };
    } catch (error) {
      Logger.error(`Failed to export ${dataType}:`, error);
      throw error;
    }
  }
}
