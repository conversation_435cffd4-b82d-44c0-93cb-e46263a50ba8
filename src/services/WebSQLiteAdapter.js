/**
 * Web SQLite Adapter for expo-sqlite
 * Provides web worker-based SQLite support for the web platform
 */

class WebSQLiteAdapter {
  constructor() {
    this.worker = null;
    this.isInitialized = false;
    this.messageId = 0;
    this.pendingMessages = new Map();
  }

  async initialize($3) {
    if (this.isInitialized && this.worker) return;

    try {
      // Create web worker
      this.worker = new Worker('/wa-sqlite-worker.js');
      
      // Set up message handler
      this.worker.onmessage = (e) => {
        const { id, type, result, error, success  } = e.data;
        const pending = this.pendingMessages.get(id);
        
        if(pending) {
          this.pendingMessages.delete(id);
          
          if(error) {
            pending.reject(new Error(error));
          } else {
            pending.resolve({ type, result, success });
          }
        }
      };
      
      this.worker.onerror = (error) => {
        console.error('Web worker error: ', error);
        // Reject all pending messages
        for(const [id, pending] of this.pendingMessages) {
          pending.reject(new Error('Worker error: ' + error.message));
        }
        this.pendingMessages.clear();
      };
      
      // Initialize the worker
      await this.sendMessage('init', {});
      this.isInitialized = true;

    } catch (error) {
      console.error('Failed to initialize Web SQLite adapter: ', error);
      this.isInitialized = false;
      this.worker = null;
      throw error;
    }
  }

  async sendMessage($3) {
    return new Promise((resolve, reject) => {
      const id = ++this.messageId;
      this.pendingMessages.set(id, { resolve, reject });
      
      this.worker.postMessage({ type, payload, id });
      
      // Set timeout for message
      setTimeout(() => {
        if (this.pendingMessages.has(id)) {
          this.pendingMessages.delete(id);
          reject(new Error('Message timeout'));
        }
      }, 30000); // 30 second timeout
    });
  }

  async execAsync($3) {
    if(!this.isInitialized) {
      await this.initialize();
    }
    
    const response = await this.sendMessage('exec', { sql, params });
    return response.result;
  }

  async getAllAsync($3) {
    const result = await this.execAsync(sql, params);
    return result.rows || [];
  }

  async getFirstAsync($3) {
    const result = await this.getAllAsync(sql, params);
    return result.length > 0 ? result[0] : null;
  }

  async runAsync($3) {
    const result = await this.execAsync(sql, params);
    return { changes: result.rowsAffected || 0,
      lastInsertRowId: result.insertId || 0
      };
  }

  async close($3) {
    if(this.worker) {
      try {
        await this.sendMessage('close', {});
      } catch (error) {
        console.warn('Error closing web worker: ', error);
      }
      
      this.worker.terminate();
      this.worker = null;
      this.isInitialized = false;
      this.pendingMessages.clear();
    }
  }
}

export default WebSQLiteAdapter;