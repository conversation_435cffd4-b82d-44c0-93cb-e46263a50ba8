/**
 * Native PDF Service - Lightweight PDF generation using expo-print
 * Replaces react-native-html-to-pdf to reduce dependencies
 */

import * as Print from 'expo-print';
import * as Sharing from 'expo-sharing';
import * as FileSystem from 'expo-file-system';

class NativePDFService {
  constructor() {
    this.isAvailable = true;
  }

  // Generate invoice PDF
  async generateInvoicePDF(orderData, customerData, settings = {}) {
    try {
      const html = this.generateInvoiceHTML(orderData, customerData, settings);
      
      const { uri } = await Print.printToFileAsync({
        html,
        base64: false,
        width: 612,
        height: 792,
        margins: {,
          left: 40,
          top: 40,
          right: 40,
          bottom: 40,
        },
      });

      return {
        success: true,
        uri,
        filename: `invoice_${orderData.id}_${Date.now()}.pdf`
      };
    } catch (error) {
      console.error('Error generating invoice PDF:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Generate report PDF
  async generateReportPDF(reportData, reportType = 'general') {
    try {
      const html = this.generateReportHTML(reportData, reportType);
      
      const { uri } = await Print.printToFileAsync({
        html,
        base64: false,
        width: 612,
        height: 792,
        margins: {,
          left: 40,
          top: 40,
          right: 40,
          bottom: 40,
        },
      });

      return {
        success: true,
        uri,
        filename: `${reportType}_report_${Date.now()}.pdf`
      };
    } catch (error) {
      console.error('Error generating report PDF:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Share PDF
  async sharePDF(uri, filename) {
    try {
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(uri, {
          mimeType: 'application/pdf',
          dialogTitle: `Share ${filename}`,
          UTI: 'com.adobe.pdf'
        });
        return { success: true };
      } else {
        return { success: false, error: 'Sharing not available' };
      }
    } catch (error) {
      console.error('Error sharing PDF:', error);
      return { success: false, error: error.message };
    }
  }

  // Print PDF
  async printPDF(uri) {
    try {
      await Print.printAsync({
        uri,
        printerUrl: undefined, // Use default printer
      });
      return { success: true };
    } catch (error) {
      console.error('Error printing PDF:', error);
      return { success: false, error: error.message };
    }
  }

  // Generate invoice HTML
  generateInvoiceHTML(orderData, customerData, settings) {
    const {
      shopName = 'Elite Tailoring',
      ownerName = 'Master Tailor',
      phone = '+880 1700-000000',
      email = '<EMAIL>',
      address = 'Dhaka, Bangladesh'
    } = settings;

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Invoice - ${orderData.id}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 0; padding: 20px; color: #333; }
          .header { text-align: center; border-bottom: 2px solid #2196F3; padding-bottom: 20px; margin-bottom: 30px; }
          .shop-name { font-size: 28px; font-weight: bold; color: #2196F3; margin-bottom: 5px; }
          .shop-details { font-size: 14px; color: #666; }
          .invoice-title { font-size: 24px; font-weight: bold; margin: 20px 0; }
          .invoice-info { display: flex; justify-content: space-between; margin-bottom: 30px; }
          .customer-info, .order-info { width: 48%; }
          .info-title { font-weight: bold; color: #2196F3; margin-bottom: 10px; }
          .table { width: 100%; border-collapse: collapse; margin: 20px 0; }
          .table th, .table td { border: 1px solid #ddd; padding: 12px; text-align: left; }
          .table th { background-color: #f5f5f5; font-weight: bold; }
          .total-section { margin-top: 30px; text-align: right; }
          .total-row { margin: 5px 0; }
          .grand-total { font-size: 18px; font-weight: bold; color: #2196F3; }
          .footer { margin-top: 50px; text-align: center; font-size: 12px; color: #666; }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="shop-name">${shopName}</div>
          <div class="shop-details">
            ${ownerName}<br>
            ${phone} | ${email}<br>
            ${address}
          </div>
        </div>

        <div class="invoice-title">INVOICE</div>

        <div class="invoice-info">
          <div class="customer-info">
            <div class="info-title">Bill To:</div>
            <div><strong>${customerData.name || orderData.customerName}</strong></div>
            <div>${customerData.phone || orderData.phone}</div>
            <div>${customerData.email || orderData.email || ''}</div>
            <div>${customerData.address || ''}</div>
          </div>
          <div class="order-info">
            <div class="info-title">Invoice Details:</div>
            <div><strong>Invoice #:</strong> ${orderData.id}</div>
            <div><strong>Order Date:</strong> ${orderData.date}</div>
            <div><strong>Due Date:</strong> ${orderData.dueDate || 'N/A'}</div>
            <div><strong>Status:</strong> ${orderData.status}</div>
          </div>
        </div>

        <table class="table">
          <thead>
            <tr>
              <th>Item</th>
              <th>Quantity</th>
              <th>Unit Price</th>
              <th>Total</th>
            </tr>
          </thead>
          <tbody>
            ${(orderData.items || []).map(item => `
              <tr>
                <td>${item.name || item.productName}</td>
                <td>${item.quantity}</td>
                <td>৳${item.price}</td>
                <td>৳${(item.quantity * item.price).toFixed(2)}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>

        <div class="total-section">
          <div class="total-row">Subtotal: ৳${orderData.subtotal || 0}</div>
          <div class="total-row">Tax: ৳${orderData.tax || 0}</div>
          <div class="total-row">Discount: ৳${orderData.discount || 0}</div>
          <div class="total-row grand-total">Total: ৳${orderData.total || 0}</div>
          <div class="total-row">Paid: ৳${orderData.paidAmount || 0}</div>
          <div class="total-row">Balance: ৳${orderData.balanceAmount || 0}</div>
        </div>

        <div class="footer">
          <p>Thank you for your business!</p>
          <p>This is a computer-generated invoice.</p>
        </div>
      </body>
      </html>
    `;
  }

  // Generate report HTML
  generateReportHTML(reportData, reportType) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>${reportType.toUpperCase()} Report</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 0; padding: 20px; color: #333; }
          .header { text-align: center; border-bottom: 2px solid #2196F3; padding-bottom: 20px; margin-bottom: 30px; }
          .report-title { font-size: 24px; font-weight: bold; color: #2196F3; }
          .report-date { font-size: 14px; color: #666; margin-top: 10px; }
          .section { margin: 30px 0; }
          .section-title { font-size: 18px; font-weight: bold; color: #2196F3; margin-bottom: 15px; }
          .table { width: 100%; border-collapse: collapse; margin: 20px 0; }
          .table th, .table td { border: 1px solid #ddd; padding: 12px; text-align: left; }
          .table th { background-color: #f5f5f5; font-weight: bold; }
          .summary-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }
          .summary-card { border: 1px solid #ddd; padding: 15px; border-radius: 5px; }
          .summary-value { font-size: 24px; font-weight: bold; color: #2196F3; }
          .summary-label { font-size: 14px; color: #666; }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="report-title">${reportType.toUpperCase()} REPORT</div>
          <div class="report-date">Generated on ${new Date().toLocaleDateString()}</div>
        </div>

        <div class="section">
          <div class="section-title">Summary</div>
          <div class="summary-grid">
            ${Object.entries(reportData.summary || {}).map(([key, value]) => `
              <div class="summary-card">
                <div class="summary-value">${value}</div>
                <div class="summary-label">${key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}</div>
              </div>
            `).join('')}
          </div>
        </div>

        ${reportData.details ? `
          <div class="section">
            <div class="section-title">Details</div>
            <table class="table">
              <thead>
                <tr>
                  ${Object.keys(reportData.details[0] || {}).map(key => `<th>${key}</th>`).join('')}
                </tr>
              </thead>
              <tbody>
                ${(reportData.details || []).map(row => `
                  <tr>
                    ${Object.values(row).map(value => `<td>${value}</td>`).join('')}
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>
        ` : ''}

        <div class="footer" style="margin-top: 50px; text-align: center; font-size: 12px; color: #666;">
          <p>Report generated by Elite Tailoring Management System</p>
        </div>
      </body>
      </html>
    `;
  }
}

export default new NativePDFService();
