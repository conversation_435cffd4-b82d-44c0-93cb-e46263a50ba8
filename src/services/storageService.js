import AsyncStorage from '@react-native-async-storage/async-storage';
import { STORAGE_KEYS   } from '../config/constants';
import { StorageError, Logger   } from '../utils/errorHandler';

/**
 * Enhanced storage service with error handling and caching
 */
export class StorageService {
  static cache = new Map();
  static cacheExpiry = new Map();
  static defaultTTL = 5 * 60 * 1000; // 5 minutes

  /**
   * Get data from storage with caching
   */
  static async get($3) {
    try {
      // Check cache first
      if (useCache && this.cache.has(key)) {
        const expiry = this.cacheExpiry.get(key);
        if (expiry && Date.now() < expiry) {
          Logger.debug(`Cache hit for key: ${key} );
          return this.cache.get(key);
        } else {
          // Cache expired
          this.cache.delete(key);
          this.cacheExpiry.delete(key);
        }
      }

      const value = await AsyncStorage.getItem(key);
      const parsedValue = value ? JSON.parse(value) : null;

      // Update cache
      if(useCache && parsedValue !== null) {
        this.cache.set(key, parsedValue);
        this.cacheExpiry.set(key, Date.now() + ttl);
      }

      // Only log if data is found to reduce noise
      if(parsedValue !== null) {
        Logger.debug(`Storage get for key: ${key} ,` { found: true });
      }
      return parsedValue;
    } catch (error) {
      Logger.error(`Failed to get data for key: ${key} , error);
      throw new StorageError(`Failed to retrieve data: ${key} ,` error);
    }
  }

  /**
   * Set data to storage with caching
   */
  static async set($3) {
    try {
      const serializedValue = JSON.stringify(value);
      await AsyncStorage.setItem(key, serializedValue);

      // Update cache
      if(updateCache) {
        this.cache.set(key, value);
        this.cacheExpiry.set(key, Date.now() + this.defaultTTL);
      }

      Logger.debug(`Storage set for key: ${key} );
      return true;
    } catch (error) {
      Logger.error(`Failed to set data for key: ${key} ,` error);
      throw new StorageError(`Failed to save data: ${key} , error);
    }
  }

  /**
   * Remove data from storage and cache
   */
  static async remove($3) {
    try {
      await AsyncStorage.removeItem(key);
      this.cache.delete(key);
      this.cacheExpiry.delete(key);

      Logger.debug(`Storage remove for key: ${key} );`
      return true;
    } catch (error) {
      Logger.error(`Failed to remove data for key: ${key} , error);
      throw new StorageError(`Failed to remove data: ${key} ,` error);
    }
  }

  /**
   * Clear all storage and cache
   */
  static async clear($3) {
    try {
      await AsyncStorage.clear();
      this.cache.clear();
      this.cacheExpiry.clear();

      Logger.info('Storage cleared');
      return true;
    } catch (error) {
      Logger.error('Failed to clear storage', error);
      throw new StorageError('Failed to clear storage', error);
    }
  }

  /**
   * Get multiple keys at once
   */
  static async getMultiple($3) {
    try {
      const results = await AsyncStorage.multiGet(keys);
      const data = {};

      results.forEach(([key, value]) => {
        data[key] = value ? JSON.parse(value) : null;
      });

      Logger.debug(`Storage getMultiple for keys: `, keys);
      return data;
    } catch (error) {
      Logger.error('Failed to get multiple keys', error);
      throw new StorageError('Failed to retrieve multiple data', error);
    }
  }

  /**
   * Set multiple key-value pairs at once
   */
  static async setMultiple($3) {
    try {
      const serializedPairs = keyValuePairs.map(([key, value]) => [
        key,
        JSON.stringify(value)
      ]);

      await AsyncStorage.multiSet(serializedPairs);

      // Update cache
      keyValuePairs.forEach(([key, value]) => {
        this.cache.set(key, value);
        this.cacheExpiry.set(key, Date.now() + this.defaultTTL);
      });

      Logger.debug('Storage setMultiple completed');
      return true;
    } catch (error) {
      Logger.error('Failed to set multiple keys', error);
      throw new StorageError('Failed to save multiple data', error);
    }
  }

  /**
   * Check if key exists
   */
  static async exists($3) {
    try {
      const value = await AsyncStorage.getItem(key);
      return value !== null;
    } catch (error) {
      Logger.error(`Failed to check existence for key: ${key} , error);
      return false;
    }
  }

  /**
   * Get storage size information
   */
  static async getStorageInfo($3) {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const values = await AsyncStorage.multiGet(keys);

      let totalSize = 0;
      const keyInfo = values.map(([key, value]) => {
        const size = value ? value.length : 0;
        totalSize += size;
        return { key, size   };
      });

      return { totalKeys: keys.length,
        totalSize,
        keyInfo,
        cacheSize: this.cache.size,
        };
    } catch (error) {
      Logger.error('Failed to get storage info', error);
      throw new StorageError('Failed to get storage information', error);
    }
  }

  /**
   * Clear expired cache entries
   */
  static clearExpiredCache() {
    const now = Date.now();
    const expiredKeys = [];

    this.cacheExpiry.forEach((expiry, key) => {
      if(expiry < now) {
        expiredKeys.push(key);
      }
    });

    expiredKeys.forEach(key => {
      this.cache.delete(key);
      this.cacheExpiry.delete(key);
    });

    if(expiredKeys.length > 0) {
      Logger.debug(`Cleared ${expiredKeys.length} expired cache entries`);
    }
  }

  /**
   * Backup data to a specific key
   */
  static async backup($3) {
    try {
      const data = await this.get(sourceKey, false);
      if(data) {
        await this.set(backupKey, {
          data,
          timestamp: Date.now(),
          version: '1.0'
        }, false);
        Logger.info(`Backup created: ${sourceKey} -> ${backupKey} );
        return true;
      }
      return false;
    } catch (error) {
      Logger.error(`Failed to backup ${sourceKey} ,` error);
      throw new StorageError(`Failed to create backup`, error);
    }
  }

  /**
   * Restore data from backup
   */
  static async restore($3) {
    try {
      const backup = await this.get(backupKey, false);
      if(backup && backup.data) {
        await this.set(targetKey, backup.data, false);
        Logger.info(`Data restored: ${backupKey} -> ${targetKey} );
        return true;
      }
      return false;
    } catch (error) {
      Logger.error(`Failed to restore from ${backupKey} ,` error);
      throw new StorageError(`Failed to restore backup`, error);
    }
  }
}

// Convenience methods for specific data types
export const TailorStorage = {
  async getTailorData($3) {
    return StorageService.get(STORAGE_KEYS.TAILOR_DATA);
  },

  async setTailorData($3) {
    return StorageService.set(STORAGE_KEYS.TAILOR_DATA, data);
  },

  async getSettings($3) {
    return StorageService.get(STORAGE_KEYS.SETTINGS);
  },

  async setSettings($3) {
    return StorageService.set(STORAGE_KEYS.SETTINGS, settings);
  },

  async getTheme($3) {
    return StorageService.get(STORAGE_KEYS.THEME);
  },

  async setTheme($3) {
    return StorageService.set(STORAGE_KEYS.THEME, isDarkMode);
  },
};
