// MINIMAL QR SERVICE - Lightweight QR generation and scanning
import { Alert } from 'react-native';

class QRService {
  constructor() {
    this.isInitialized = false;
  }

  // Generate QR code data for orders
  generateOrderQR(order) {
    try {
      const qrData = {
        type: 'order',
        id: order.id,
        customer: order.customer_name,
        garment: order.garment_type,
        amount: order.amount,
        status: order.status,
        timestamp: Date.now(),
      };
      
      return JSON.stringify(qrData);
    } catch (error) {
      console.error('Failed to generate QR data:', error);
      return null;
    }
  }

  // Generate QR code data for customers
  generateCustomerQR(customer) {
    try {
      const qrData = {
        type: 'customer',
        id: customer.id,
        name: customer.name,
        phone: customer.phone,
        email: customer.email,
        timestamp: Date.now(),
      };
      
      return JSON.stringify(qrData);
    } catch (error) {
      console.error('Failed to generate customer QR data:', error);
      return null;
    }
  }

  // Parse scanned QR code data
  parseQRData(qrString) {
    try {
      const data = JSON.parse(qrString);
      
      // Validate QR data structure
      if (!data.type || !data.id) {
        throw new Error('Invalid QR code format');
      }

      // Check if QR code is not too old (24 hours)
      const maxAge = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
      if (data.timestamp && (Date.now() - data.timestamp) > maxAge) {
        throw new Error('QR code has expired');
      }

      return {
        isValid: true,
        data: data,
        type: data.type,
      };
    } catch (error) {
      console.error('Failed to parse QR data:', error);
      return {
        isValid: false,
        error: error.message,
        data: null,
      };
    }
  }

  // Mock QR scanning (for development/fallback)
  async mockScan() {
    return new Promise((resolve) => {
      setTimeout(() => {
        // Simulate scanning an order QR code
        const mockOrderQR = this.generateOrderQR({
          id: 1,
          customer_name: 'John Doe',
          garment_type: 'Suit',
          amount: 15000,
          status: 'Ready',
        });

        resolve(mockOrderQR);
      }, 2000); // 2 second delay to simulate scanning
    });
  }

  // Check if real camera scanning is available
  async isCameraScanningAvailable() {
    try {
      // Check if expo-camera is available
      const { Camera } = require('expo-camera');
      return !!Camera;
    } catch (error) {

      return false;
    }
  }

  // Handle QR scan result
  async handleScanResult(qrData, navigation) {
    try {
      const parsed = this.parseQRData(qrData);
      
      if (!parsed.isValid) {
        Alert.alert('Invalid QR Code', parsed.error || 'Unable to read QR code');
        return false;
      }

      const { data, type } = parsed;

      switch (type) {
        case 'order':
          // Navigate to order details
          navigation.navigate('OrderDetails', { 
            order: {
              id: data.id,
              customer_name: data.customer,
              garment_type: data.garment,
              amount: data.amount,
              status: data.status,
            }
          });
          break;

        case 'customer':
          // Navigate to customer details
          navigation.navigate('CustomerDetails', {
            customer: {
              id: data.id,
              name: data.name,
              phone: data.phone,
              email: data.email,
            }
          });
          break;

        default:
          Alert.alert('Unknown QR Code', 'This QR code type is not supported');
          return false;
      }

      return true;
    } catch (error) {
      console.error('Failed to handle QR scan result:', error);
      Alert.alert('Error', 'Failed to process QR code');
      return false;
    }
  }

  // Generate QR code URL for display (using a free QR service)
  generateQRCodeURL(data, size = 200) {
    try {
      const encodedData = encodeURIComponent(data);
      return `https://api.qrserver.com/v1/create-qr-code/?size=${size}x${size}&data=${encodedData}`;
    } catch (error) {
      console.error('Failed to generate QR code URL:', error);
      return null;
    }
  }

  // Validate QR scanner permissions (mock for now)
  async checkCameraPermissions() {
    // In a real app, you would check camera permissions here
    // For now, we'll just return true
    return true;
  }

  // Request camera permissions (mock for now)
  async requestCameraPermissions() {
    // In a real app, you would request camera permissions here
    // For now, we'll just return true
    return true;
  }
}

// Export singleton instance
export default new QRService();
