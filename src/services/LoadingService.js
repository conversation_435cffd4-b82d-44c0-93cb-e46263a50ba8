// LOADING STATE SERVICE - Global loading state management

class LoadingService {
  constructor() {
    this.loadingStates = new Map();
    this.globalLoading = false;
    this.listeners = new Map();
  }

  // Start loading for a specific operation
  startLoading(operationId, message = 'Loading...') {
    this.loadingStates.set(operationId, {
      isLoading: true,
      message,
      startTime: Date.now()});
    
    this.updateGlobalLoading();
    this.notifyListeners('loadingStateChanged', {
      operationId,
      isLoading: true,
      message,
      allStates: this.getAllStates()});
  }

  // Stop loading for a specific operation
  stopLoading(operationId) {
    const state = this.loadingStates.get(operationId);
    if(state) {
      const duration = Date.now() - state.startTime;

    }
    
    this.loadingStates.delete(operationId);
    this.updateGlobalLoading();
    this.notifyListeners('loadingStateChanged', {
      operationId,
      isLoading: false,
      allStates: this.getAllStates()});
  }

  // Update loading message
  updateLoadingMessage(operationId, message) {
    const state = this.loadingStates.get(operationId);
    if(state) {
      state.message = message;
      this.notifyListeners('loadingStateChanged', {
        operationId,
        isLoading: true,
        message,
        allStates: this.getAllStates()});
    }
  }

  // Check if specific operation is loading
  isLoading(operationId) {
    return this.loadingStates.has(operationId);
  }

  // Get loading state for specific operation
  getLoadingState(operationId) {
    return this.loadingStates.get(operationId) || { isLoading: false };
  }

  // Get all loading states
  getAllStates() {
    const states = {};
    this.loadingStates.forEach((state, operationId) => {
      states[operationId] = state;
    });
    return states;
  }

  // Check if any operation is loading
  isGlobalLoading() {
    return this.globalLoading;
  }

  // Update global loading state
  updateGlobalLoading() {
    const wasLoading = this.globalLoading;
    this.globalLoading = this.loadingStates.size > 0;
    
    if(wasLoading !== this.globalLoading) {
      this.notifyListeners('globalLoadingChanged', this.globalLoading);
    }
  }

  // Predefined operation IDs for consistency
  static OPERATIONS = {
    // Data operations
    LOADING_CUSTOMERS: 'loading_customers',
    LOADING_ORDERS: 'loading_orders',
    LOADING_DASHBOARD: 'loading_dashboard',
    REFRESHING_DATA: 'refreshing_data',
    
    // CRUD operations
    SAVING_CUSTOMER: 'saving_customer',
    UPDATING_CUSTOMER: 'updating_customer',
    DELETING_CUSTOMER: 'deleting_customer',
    SAVING_ORDER: 'saving_order',
    UPDATING_ORDER: 'updating_order',
    DELETING_ORDER: 'deleting_order',
    
    // File operations
    GENERATING_PDF: 'generating_pdf',
    SHARING_PDF: 'sharing_pdf',
    PRINTING_INVOICE: 'printing_invoice',
    GENERATING_QR: 'generating_qr',
    
    // Authentication
    LOGGING_IN: 'logging_in',
    LOGGING_OUT: 'logging_out',
    
    // Database operations
    INITIALIZING_DB: 'initializing_db',
    BACKING_UP_DATA: 'backing_up_data',
    RESTORING_DATA: 'restoring_data',
    
    // Search operations
    SEARCHING: 'searching',
    FILTERING: 'filtering'};

  // Convenience methods for common operations
  startDataLoading(message = 'Loading data...') {
    this.startLoading(LoadingService.OPERATIONS.LOADING_CUSTOMERS, message);
  }

  stopDataLoading() {
    this.stopLoading(LoadingService.OPERATIONS.LOADING_CUSTOMERS);
  }

  startSaving(type = 'item', message) {
    const operationId = type === 'customer' 
      ? LoadingService.OPERATIONS.SAVING_CUSTOMER 
      : LoadingService.OPERATIONS.SAVING_ORDER;
    this.startLoading(operationId, message || `Saving ${type}...`);
  }

  stopSaving(type = 'item') {
    const operationId = type === 'customer' 
      ? LoadingService.OPERATIONS.SAVING_CUSTOMER 
      : LoadingService.OPERATIONS.SAVING_ORDER;
    this.stopLoading(operationId);
  }

  startPDFGeneration(message = 'Generating PDF...') {
    this.startLoading(LoadingService.OPERATIONS.GENERATING_PDF, message);
  }

  stopPDFGeneration() {
    this.stopLoading(LoadingService.OPERATIONS.GENERATING_PDF);
  }

  startRefresh(message = 'Refreshing data...') {
    this.startLoading(LoadingService.OPERATIONS.REFRESHING_DATA, message);
  }

  stopRefresh() {
    this.stopLoading(LoadingService.OPERATIONS.REFRESHING_DATA);
  }

  // Async wrapper for operations
  async withLoading($3) {
    try {
      this.startLoading(operationId, message);
      const result = await asyncOperation();
      return result;
    } catch (error) {
      console.error(`Operation ${operationId} failed: `, error);
      throw error;
    } finally {
      this.stopLoading(operationId);
    }
  }

  // Convenience wrapper for common operations
  async withDataLoading($3) {
    return this.withLoading(
      LoadingService.OPERATIONS.LOADING_CUSTOMERS,
      message,
      asyncOperation
    );
  }

  async withSaving($3) {
    const operationId = type === 'customer' 
      ? LoadingService.OPERATIONS.SAVING_CUSTOMER 
      : LoadingService.OPERATIONS.SAVING_ORDER;
    return this.withLoading(
      operationId,
      message || `Saving ${type}...`,
      asyncOperation
    );
  }

  async withPDFGeneration($3) {
    return this.withLoading(
      LoadingService.OPERATIONS.GENERATING_PDF,
      message,
      asyncOperation
    );
  }

  // Clear all loading states (useful for cleanup)
  clearAll() {
    this.loadingStates.clear();
    this.updateGlobalLoading();
    this.notifyListeners('allLoadingCleared');
  }

  // Simple event system replacement
  notifyListeners(event, data) {
    const eventListeners = this.listeners.get(event);
    if(eventListeners) {
      eventListeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in loading service listener for ${event}:`, error);
        }
      });
    }
  }

  // Add event listener
  addEventListener(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event).add(callback);
  }

  // Remove event listener
  removeEventListener(event, callback) {
    const eventListeners = this.listeners.get(event);
    if(eventListeners) {
      eventListeners.delete(callback);
    }
  }

  // Get loading statistics
  getStats() {
    const states = Array.from(this.loadingStates.values());
    const now = Date.now();
    
    return {
      totalOperations: states.length,
      longestRunning: states.reduce((max, state) => {
        const duration = now - state.startTime;
        return duration > max.duration ? { duration, message: state.message } : max;
      }, { duration: 0, message: '' }),
      averageDuration: states.length > 0 
        ? states.reduce((sum, state) => sum + (now - state.startTime), 0) / states.length 
        : 0};
  }

  // Debug method to log current state
  debugLog() {
    console.log('🔄 Loading Service State: ', {
      globalLoading: this.globalLoading,
      activeOperations: Array.from(this.loadingStates.keys()),
      stats: this.getStats()});
  }
}

// Export singleton instance
export default new LoadingService();
