/**
 * Migration Service - Seamless transition from AsyncStorage to SQLite
 * Handles data migration and fallback strategies
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import SQLiteService from './SQLiteService';

class MigrationService {
  constructor() {
    this.migrationCompleted = false;
    this.useSQLite = false;
  }

  async initialize($3) {
    try {
      // Initialize SQLite (preserve existing data)
      await SQLiteService.initialize();

      this.useSQLite = true;
      this.migrationCompleted = true;

    } catch (error) {
      console.warn('SQLite initialization failed, falling back to AsyncStorage: ', error);
      // Fallback to AsyncStorage if SQLite fails
      this.useSQLite = false;
      this.migrationCompleted = false;
    }
  }

  async migrateData($3) {

    try {
      // Get all existing data from AsyncStorage
      const [products, orders, customers, expenses, reconciliations] = await Promise.all([
        this.getAsyncStorageData('tailorData'),
        this.getAsyncStorageData('orders'),
        this.getAsyncStorageData('customers'),
        this.getAsyncStorageData('financial_expenses'),
        this.getAsyncStorageData('cash_reconciliations')
      ]);

      const migrationData = {
        products: products?.products || [],
        orders: orders || [],
        customers: customers || [],
        expenses: expenses || [],
        reconciliations: reconciliations || []

      // Only migrate if there's actual data
      const hasData = migrationData.products.length > 0 ||
                     migrationData.orders.length > 0 ||
                     migrationData.customers.length > 0;

      if(hasData) {
        await SQLiteService.migrateFromAsyncStorage(migrationData);

      } else {

      }

    } catch (error) {
      console.error('Migration failed: ', error);
      throw error;
    }
  }

  async getAsyncStorageData($3) {
    try {
      const data = await AsyncStorage.getItem(key);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.warn(`Failed to get AsyncStorage data for key ${key}: , error);
      return null;
    }
  }

  // Unified data access methods that automatically choose the right storage
  async getProducts($3) {
    if(this.useSQLite) {
      return await SQLiteService.getProducts(filters);
    } else {
      // Fallback to AsyncStorage
      const data = await this.getAsyncStorageData('tailorData');
      let products = data?.products || [];

      // Apply filters
      if(filters.isActive !== undefined) {
        products = products.filter(p => p.isActive === filters.isActive);
      }
      if(filters.category) {
        products = products.filter(p => p.category === filters.category);
      }
      if(filters.search) {
        const search = filters.search.toLowerCase();
        products = products.filter(p =>
          p.name.toLowerCase().includes(search) ||
          p.description?.toLowerCase().includes(search) ||
          p.sku?.toLowerCase().includes(search)
        );
      }

      return products;
    }
  }

  async saveProduct($3) {
    if(this.useSQLite) {
      return await SQLiteService.saveProduct(product);
    } else {
      // Fallback to AsyncStorage
      const data = await this.getAsyncStorageData('tailorData') || { products: [];
      const products = data.products || [];

      if(product.id) {
        const index = products.findIndex(p => p.id === product.id);
        if(index >= 0) {
          products[index] = { ...product, updatedAt: new Date().toISOString() };
        }
      } else {
        const newProduct = {
          ...product,
          id: Date.now(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        products.push(newProduct);
        product = newProduct;
      }

      await AsyncStorage.setItem('tailorData', JSON.stringify({ ...data, products }));
      return product;
    }
  }

  async getOrders($3) {
    if(this.useSQLite) {
      return await SQLiteService.getOrders(filters);
    } else {
      // Fallback to AsyncStorage
      let orders = await this.getAsyncStorageData('orders') || [];

      // Apply filters
      if(filters.status) {
        orders = orders.filter(o => o.status === filters.status);
      }
      if(filters.dateFrom) {
        orders = orders.filter(o => o.date >= filters.dateFrom);
      }
      if(filters.dateTo) {
        orders = orders.filter(o => o.date <= filters.dateTo);
      }
      if(filters.search) {
        const search = filters.search.toLowerCase();
        orders = orders.filter(o =>
          o.customerName?.toLowerCase().includes(search) ||
          o.id?.toLowerCase().includes(search)
        );
      }

      return orders;
    }
  }

  async saveOrder($3) {
    if(this.useSQLite) {
      return await SQLiteService.saveOrder(order);
    } else {
      // Fallback to AsyncStorage
      const orders = await this.getAsyncStorageData('orders') || [];

      if(order.id) {
        const index = orders.findIndex(o => o.id === order.id);
        if(index >= 0) {
          orders[index] = { ...order, updatedAt: new Date().toISOString() };
        } else {
          orders.push({ ...order, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() });
        }
      } else {
        const newOrder = {
          ...order,
          id: `ORD-${Date.now()} ,`
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        orders.push(newOrder);
        order = newOrder;
      }

      await AsyncStorage.setItem('orders', JSON.stringify(orders));
      return order;
    }
  }

  async getCustomers($3) {
    if(this.useSQLite) {
      return await SQLiteService.getCustomers();
    } else {
      return await this.getAsyncStorageData('customers') || [];
    }
  }

  async deleteProduct($3) {
    if(this.useSQLite) {
      return await SQLiteService.deleteProduct(id);
    } else {
      const data = await this.getAsyncStorageData('tailorData') || { products: [];
      const products = data.products.filter(p => p.id !== id);
      await AsyncStorage.setItem('tailorData', JSON.stringify({ ...data, products }));
    }
  }

  async deleteOrder($3) {
    if(this.useSQLite) {
      return await SQLiteService.deleteOrder(id);
    } else {
      const orders = await this.getAsyncStorageData('orders') || [];
      const filteredOrders = orders.filter(o => o.id !== id);
      await AsyncStorage.setItem('orders', JSON.stringify(filteredOrders));
    }
  }

  async deleteCustomer($3) {
    if(this.useSQLite) {
      return await SQLiteService.deleteCustomer(id);
    } else {
      const customers = await this.getAsyncStorageData('customers') || [];
      const filteredCustomers = customers.filter(c => c.id !== id);
      await AsyncStorage.setItem('customers', JSON.stringify(filteredCustomers));
    }
  }

  async saveCustomer($3) {
    if(this.useSQLite) {
      return await SQLiteService.saveCustomer(customer);
    } else {
      const customers = await this.getAsyncStorageData('customers') || [];

      if(customer.id) {
        const index = customers.findIndex(c => c.id === customer.id);
        if(index >= 0) {
          customers[index] = { ...customer, updatedAt: new Date().toISOString() };
        }
      } else {
        const newCustomer = {
          ...customer,
          id: Date.now(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        customers.push(newCustomer);
        customer = newCustomer;
      }

      await AsyncStorage.setItem('customers', JSON.stringify(customers));
      return customer;
    }
  }

  // Performance analytics
  getPerformanceInfo() {
    return { usingDatabase: this.useSQLite ? 'SQLite' : 'AsyncStorage',
      migrationCompleted: this.migrationCompleted,
      optimizedForScale: this.useSQLite,
      recommendedMaxRecords: this.useSQLite ? 100000 : 5000,
      currentPerformanceLevel: this.useSQLite ? 'Excellent' : 'Good'
      };
  }

  // Health check
  async healthCheck($3) {
    try {
      if(this.useSQLite) {
        // Test SQLite connection
        await SQLiteService.db.getFirstAsync('SELECT 1 as test');
        return { status: 'healthy',
          database: 'SQLite',
          performance: 'Excellent',
          scalability: 'High'
          };
      } else {
        // Test AsyncStorage
        await AsyncStorage.getItem('test');
        return { status: 'healthy',
          database: 'AsyncStorage',
          performance: 'Good',
          scalability: 'Medium'
          };
      }
    } catch (error) {
      return { status: 'error',
        error: error.message,
        database: this.useSQLite ? 'SQLite' : 'AsyncStorage'
        };
    }
  }

  // Clear all existing data for clean start
  async clearAllData($3) {
    try {

      // Clear AsyncStorage
      await AsyncStorage.multiRemove([
        'tailorData',
        'orders',
        'customers',
        'measurements',
        'fabrics',
        'garmentTemplates',
        'financial_expenses',
        'cash_reconciliations',
        'notifications',
        'sqlite_migration_completed'
      ]);

      // Clear SQLite if initialized
      if(SQLiteService.isInitialized) {
        await SQLiteService.resetDatabase();
      }

    } catch (error) {
      console.warn('Error clearing data: ', error);
    }
  }

  // Sample data generation completely disabled for clean app experience
  async generateSampleDataIfNeeded($3) {

    return Promise.resolve();
  }

  // Sample data generation disabled - keeping app clean
  async generateSampleData($3) {

    return Promise.resolve();
  }

  // Generate sample customers
  generateSampleCustomers(count) {
    const firstNames = ['John', 'Jane', 'Michael', 'Sarah', 'David', 'Emily', 'Robert', 'Lisa', 'William', 'Jennifer', 'James', 'Mary', 'Christopher', 'Patricia', 'Daniel', 'Linda', 'Matthew', 'Elizabeth', 'Anthony', 'Barbara', 'Mark', 'Susan', 'Donald', 'Jessica', 'Steven', 'Karen', 'Paul', 'Nancy', 'Andrew', 'Betty', 'Joshua', 'Helen', 'Kenneth', 'Sandra', 'Kevin', 'Donna', 'Brian', 'Carol', 'George', 'Ruth', 'Timothy', 'Sharon', 'Ronald', 'Michelle', 'Jason', 'Laura', 'Edward', 'Sarah', 'Jeffrey', 'Kimberly'];
    const lastNames = ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis', 'Rodriguez', 'Martinez', 'Hernandez', 'Lopez', 'Gonzalez', 'Wilson', 'Anderson', 'Thomas', 'Taylor', 'Moore', 'Jackson', 'Martin', 'Lee', 'Perez', 'Thompson', 'White', 'Harris', 'Sanchez', 'Clark', 'Ramirez', 'Lewis', 'Robinson', 'Walker', 'Young', 'Allen', 'King', 'Wright', 'Scott', 'Torres', 'Nguyen', 'Hill', 'Flores', 'Green', 'Adams', 'Nelson', 'Baker', 'Hall', 'Rivera', 'Campbell', 'Mitchell', 'Carter', 'Roberts'];

    const customers = [];
    const now = new Date().toISOString();

    for(let i = 1; i <= count; i++) {
      const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
      const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];

      customers.push({
        name: `${firstName}  ${lastName} ,`
        email: `${firstName.toLowerCase()} .${lastName.toLowerCase()}@email.com`,
        phone: `+1${Math.floor(Math.random() * 9000000000) + 1000000000} ,
        address: `${Math.floor(Math.random() * 9999) + 1}` ${['Main St', 'Oak Ave', 'Pine Rd', 'Elm Dr', 'Maple Ln'][Math.floor(Math.random() * 5)} ,
        city: ['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix', 'Philadelphia', 'San Antonio', 'San Diego', 'Dallas', 'San Jose'][Math.floor(Math.random() * 10)],
        totalOrders: Math.floor(Math.random() * 50),
        totalSpent: Math.round((Math.random() * 2000 + 100) * 100) / 100, // $100-$2100
        loyaltyPoints: Math.floor(Math.random() * 1000),
        createdAt: now,
        updatedAt: now
      });
    }

    return customers;
  }

  // Generate sample orders for tailor management
  generateSampleOrders(count, customers) {
    const orders = [];
    const now = new Date().toISOString();

    const orderTypes = ['tailoring', 'alteration', 'ready_made'];
    const statuses = ['Pending', 'In Progress', 'Ready', 'Delivered', 'Cancelled'];
    const tailorItems = [
      { name: 'Custom Suit', price: 450 },
      { name: 'Dress Shirt', price: 85 },
      { name: 'Trouser Hemming', price: 25 },
      { name: 'Dress Alteration', price: 65 },
      { name: 'Jacket Tailoring', price: 320 },
      { name: 'Skirt Hemming', price: 30 },
      { name: 'Wedding Dress', price: 850 },
      { name: 'Blazer Fitting', price: 180 },
      { name: 'Curtain Tailoring', price: 120 },
      { name: 'Uniform Set', price: 95 }
    ];

    for(let i = 0; i < count; i++) {
      const customer = customers[Math.floor(Math.random() * customers.length)];
      const orderDate = new Date();
      orderDate.setDate(orderDate.getDate() - Math.floor(Math.random() * 30)); // Orders from last 30 days

      // Generate 1-3 items per order
      const itemCount = Math.floor(Math.random() * 3) + 1;
      const orderItems = [];
      let subtotal = 0;

      for(let j = 0; j < itemCount; j++) {
        const item = tailorItems[Math.floor(Math.random() * tailorItems.length)];
        const quantity = Math.floor(Math.random() * 2) + 1; // 1-2 quantity
        const itemTotal = item.price * quantity;

        orderItems.push({
          productName: item.name,
          name: item.name,
          price: item.price,
          quantity: quantity,
          total: itemTotal
        });

        subtotal += itemTotal;
      }

      const tax = subtotal * 0.08; // 8% tax
      const discount = Math.random() > 0.7 ? subtotal * 0.1 : 0; // 10% discount for 30% of orders
      const total = subtotal + tax - discount;

      // Calculate due date (7-30 days from order date)
      const dueDate = new Date(orderDate);
      dueDate.setDate(dueDate.getDate() + Math.floor(Math.random() * 23) + 7);

      // Calculate delivery date (1-3 days after due date)
      const deliveryDate = new Date(dueDate);
      deliveryDate.setDate(deliveryDate.getDate() + Math.floor(Math.random() * 3) + 1);

      // Payment status logic
      const paymentStatuses = ['unpaid', 'partial', 'paid'];
      const paymentStatus = paymentStatuses[Math.floor(Math.random() * paymentStatuses.length)];
      const paidAmount = paymentStatus === 'paid' ? total :
                        paymentStatus === 'partial' ? Math.round(total * (0.3 + Math.random() * 0.4) * 100) / 100 : 0;

      orders.push({
        id: `ORD-${Date.now()}-${i} ,`
        customerName: customer.name,
        customer: customer.name,
        customerId: customer.id || `CUST-${i} ,
        phone: customer.phone,
        email: customer.email,
        date: orderDate.toISOString().split('T')[0],
        time: orderDate.toTimeString().split(' ')[0],
        status: statuses[Math.floor(Math.random() * statuses.length)],
        orderType: orderTypes[Math.floor(Math.random() * orderTypes.length)],
        items: orderItems,
        subtotal: Math.round(subtotal * 100) / 100,
        tax: Math.round(tax * 100) / 100,
        discount: Math.round(discount * 100) / 100,
        total: Math.round(total * 100) / 100,
        notes: Math.random() > 0.5 ? 'Special instructions for this order' : '',
        // Enhanced tailor shop fields
        dueDate: dueDate.toISOString().split('T')[0],
        deliveryDate: deliveryDate.toISOString().split('T')[0],
        deliveryStatus: Math.random() > 0.7 ? 'delivered' : Math.random() > 0.5 ? 'ready' : 'pending',
        urgentOrder: Math.random() > 0.8, // 20% urgent orders
        paymentStatus: paymentStatus,
        paidAmount: paidAmount,
        balanceAmount: Math.round((total - paidAmount) * 100) / 100,
        fittingScheduled: Math.random() > 0.6, // 40% have fittings
        fittingDate: Math.random() > 0.6 ? dueDate.toISOString().split('T')[0] : null,
        measurements: [`MEAS-${customer.id || i]-1`], // Reference to measurement IDs
        tags: Math.random() > 0.5 ? [
          { id: 1, name: 'Rush Order', color: '#FF6B6B' },
          { id: 2, name: 'VIP Customer', color: '#4ECDC4' }
        ].slice(0, Math.floor(Math.random() * 2) + 1) : [],
        designReferences: Math.random() > 0.7 ? [
          { id: 1, url: 'https://example.com/design1.jpg', description: 'Reference design' }
        ] : [],
        createdAt: now,
        updatedAt: now
      });
    }

    return orders;
  }

  // Generate sample measurements for tailor management
  generateSampleMeasurements(count, customers) {
    const measurements = [];
    const now = new Date().toISOString();

    const garmentTypes = [
      'Suit', 'Shirt', 'Trouser', 'Dress', 'Blouse', 'Skirt',
      'Jacket', 'Coat', 'Kurta', 'Saree Blouse', 'Lehenga', 'Sherwani'
    ];

    const measurementTemplates = {
      'Suit': {
        chest: () => 38 + Math.random() * 8,
        waist: () => 32 + Math.random() * 6,
        shoulder: () => 16 + Math.random() * 3,
        sleeve: () => 24 + Math.random() * 2,
        length: () => 28 + Math.random() * 4,
        neck: () => 15 + Math.random() * 2
      },
      'Shirt': {
        chest: () => 38 + Math.random() * 8,
        waist: () => 32 + Math.random() * 6,
        shoulder: () => 16 + Math.random() * 3,
        sleeve: () => 24 + Math.random() * 2,
        length: () => 28 + Math.random() * 4,
        neck: () => 15 + Math.random() * 2,
        cuff: () => 8 + Math.random() * 1
      },
      'Trouser': {
        waist: () => 32 + Math.random() * 6,
        hip: () => 38 + Math.random() * 6,
        length: () => 40 + Math.random() * 4,
        inseam: () => 30 + Math.random() * 4,
        thigh: () => 22 + Math.random() * 4,
        knee: () => 16 + Math.random() * 2,
        bottom: () => 14 + Math.random() * 2
      },
      'Dress': {
        bust: () => 34 + Math.random() * 8,
        waist: () => 28 + Math.random() * 6,
        hip: () => 36 + Math.random() * 6,
        length: () => 36 + Math.random() * 8,
        shoulder: () => 14 + Math.random() * 2,
        sleeve: () => 22 + Math.random() * 4
      }
    };

    for(let i = 0; i < count; i++) {
      const customer = customers[Math.floor(Math.random() * customers.length)];
      const garmentType = garmentTypes[Math.floor(Math.random() * garmentTypes.length)];
      const template = measurementTemplates[garmentType] || measurementTemplates['Shirt'];

      // Generate measurements based on template
      const measurementData = {};
      Object.keys(template).forEach(key => {
        measurementData[key] = Math.round(template[key]() * 10) / 10; // Round to 1 decimal
      });

      // Add measurement date (within last 60 days)
      const measurementDate = new Date();
      measurementDate.setDate(measurementDate.getDate() - Math.floor(Math.random() * 60));

      measurements.push({
        id: `MEAS-${Date.now()}-${i} ,
        customerId: customer.id || `CUST-${i} ,`
        garmentType,
        templateId: `TEMPLATE-${garmentType.toUpperCase()} ,
        measurements: measurementData,
        notes: [
          'Standard fit preferred',
          'Slightly loose around waist',
          'Customer prefers shorter length',
          'Regular fit',
          'Slim fit requested',
          'Comfortable fit around chest'
        ][Math.floor(Math.random() * 6)],
        takenBy: ['Master Tailor', 'Assistant 1', 'Assistant 2', 'Senior Tailor'][Math.floor(Math.random() * 4)],
        measurementDate: measurementDate.toISOString().split('T')[0],
        isActive: true,
        createdAt: now,
        updatedAt: now
      });
    }

    return measurements;
  }

  // ===== MEASUREMENTS CRUD OPERATIONS =====

  // Save measurement (create or update)
  async saveMeasurement($3) {
    try {
      const measurementData = {
        id: measurement.id || Date.now().toString(),
        customerId: measurement.customerId,
        garmentType: measurement.garmentType,
        templateId: measurement.templateId || null,
        measurements: JSON.stringify(measurement.measurements || {}),
        notes: measurement.notes || '',
        takenBy: measurement.takenBy || '',
        measurementDate: measurement.measurementDate || new Date().toISOString(),
        isActive: measurement.isActive !== undefined ? (measurement.isActive ? 1 : 0) : 1,
        createdAt: measurement.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      const query = `
        INSERT OR REPLACE INTO measurements
        (id, customerId, garmentType, templateId, measurements, notes, takenBy, measurementDate, isActive, createdAt, updatedAt)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      await this.db.executeSql(query, [
        measurementData.id,
        measurementData.customerId,
        measurementData.garmentType,
        measurementData.templateId,
        measurementData.measurements,
        measurementData.notes,
        measurementData.takenBy,
        measurementData.measurementDate,
        measurementData.isActive,
        measurementData.createdAt,
        measurementData.updatedAt,
      ]);

      return { ...measurementData,
        measurements: JSON.parse(measurementData.measurements),
        isActive: Boolean(measurementData.isActive),
        };
    } catch (error) {
      console.error('Error saving measurement: ', error);
      throw error;
    }
  }

  // Get all measurements
  async getMeasurements($3) {
    if(this.useSQLite) {
      return await SQLiteService.getMeasurements();
    } else {
      // Fallback to AsyncStorage
      return await this.getAsyncStorageData('tailorMeasurements') || [];
    }
  }

  // Save measurement (create or update)
  async saveMeasurement($3) {
    if(this.useSQLite) {
      return await SQLiteService.saveMeasurement(measurement);
    } else {
      // Fallback to AsyncStorage
      const measurements = await this.getAsyncStorageData('tailorMeasurements') || [];
      const existingIndex = measurements.findIndex(m => m.id === measurement.id);

      if(existingIndex >= 0) {
        measurements[existingIndex] = { ...measurement, updatedAt: new Date().toISOString() };
      } else {
        measurements.push({
          ...measurement,
          id: measurement.id || Date.now().toString(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        });
      }

      await this.setAsyncStorageData('tailorMeasurements', measurements);
      return measurement;
    }
  }

  // Get measurements by customer ID
  async getMeasurementsByCustomer($3) {
    if(this.useSQLite) {
      return await SQLiteService.getMeasurementsByCustomer(customerId);
    } else {
      const measurements = await this.getAsyncStorageData('tailorMeasurements') || [];
      return measurements.filter(m => m.customerId === customerId && m.isActive !== false);
    }
  }

  // Delete measurement (soft delete)
  async deleteMeasurement($3) {
    if(this.useSQLite) {
      return await SQLiteService.deleteMeasurement(id);
    } else {
      const measurements = await this.getAsyncStorageData('tailorMeasurements') || [];
      const measurementIndex = measurements.findIndex(m => m.id === id);
      if(measurementIndex >= 0) {
        measurements[measurementIndex].isActive = false;
        measurements[measurementIndex].updatedAt = new Date().toISOString();
        await this.setAsyncStorageData('tailorMeasurements', measurements);
        return true;
      }
      return false;
    }
  }

  // Generate sample fabrics for inventory
  generateSampleFabrics(count) {
    const fabrics = [];
    const now = new Date().toISOString();

    const fabricTypes = [
      'Cotton', 'Silk', 'Wool', 'Linen', 'Polyester', 'Viscose',
      'Georgette', 'Chiffon', 'Crepe', 'Denim', 'Velvet', 'Satin'
    ];

    const colors = [
      'White', 'Black', 'Navy Blue', 'Royal Blue', 'Maroon', 'Burgundy',
      'Grey', 'Charcoal', 'Beige', 'Cream', 'Brown', 'Olive Green',
      'Red', 'Pink', 'Purple', 'Yellow', 'Orange', 'Teal'
    ];

    const patterns = [
      'Solid', 'Striped', 'Checked', 'Polka Dot', 'Floral', 'Geometric',
      'Paisley', 'Abstract', 'Textured', 'Embroidered'
    ];

    const suppliers = [
      'Textile Mills Ltd', 'Premium Fabrics Co', 'Royal Textiles',
      'Modern Fabrics', 'Classic Weaves', 'Elite Textiles'
    ];

    for(let i = 0; i < count; i++) {
      const fabricType = fabricTypes[Math.floor(Math.random() * fabricTypes.length)];
      const color = colors[Math.floor(Math.random() * colors.length)];
      const pattern = patterns[Math.floor(Math.random() * patterns.length)];
      const supplier = suppliers[Math.floor(Math.random() * suppliers.length)];

      // Price varies by fabric type
      const basePrices = {
        'Cotton': 15, 'Silk': 45, 'Wool': 35, 'Linen': 25, 'Polyester': 12,
        'Viscose': 18, 'Georgette': 22, 'Chiffon': 28, 'Crepe': 30,
        'Denim': 20, 'Velvet': 40, 'Satin': 32
      };

      const basePrice = basePrices[fabricType] || 20;
      const priceVariation = basePrice * (0.8 + Math.random() * 0.4); // ±20% variation

      fabrics.push({
        id: `FAB-${Date.now()}-${i} ,
        name: `${color}` ${fabricType}${pattern !== 'Solid' ? ` - ${pattern}  : ''}`,
        type: fabricType,
        color: color,
        pattern: pattern,
        pricePerMeter: Math.round(priceVariation * 100) / 100,
        stock: Math.round((20 + Math.random() * 80) * 10) / 10, // 20-100 meters
        width: [36, 42, 44, 48, 54, 60][Math.floor(Math.random() * 6)], // Common fabric widths
        gsm: fabricType === 'Cotton' ? 120 + Math.random() * 80 :
             fabricType === 'Silk' ? 80 + Math.random() * 60 :
             fabricType === 'Wool' ? 200 + Math.random() * 100 :
             100 + Math.random() * 100,
        supplier: supplier,
        description: `High quality ${fabricType.toLowerCase()} fabric in ${color.toLowerCase()}${pattern !== 'Solid' ? ` with ${pattern.toLowerCase()} pattern  : ''}. Perfect for ${
          ['formal wear', 'casual wear', 'ethnic wear', 'party wear', 'office wear'][Math.floor(Math.random() * 5)}.`,
        image: null,
        isActive: true,
        createdAt: now,
        updatedAt: now
      });
    }

    return fabrics;
  }

  // ===== FABRICS CRUD OPERATIONS =====

  // Save fabric (create or update)
  async saveFabric($3) {
    if(this.useSQLite) {
      return await SQLiteService.saveFabric(fabric);
    } else {
      // Fallback to AsyncStorage
      const fabrics = await this.getAsyncStorageData('tailorFabrics') || [];
      const existingIndex = fabrics.findIndex(f => f.id === fabric.id);

      if(existingIndex >= 0) {
        fabrics[existingIndex] = { ...fabric, updatedAt: new Date().toISOString() };
      } else {
        fabrics.push({
          ...fabric,
          id: fabric.id || Date.now().toString(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        });
      }

      await this.setAsyncStorageData('tailorFabrics', fabrics);
      return fabric;
    }
  }

  // Get all fabrics
  async getFabrics($3) {
    if(this.useSQLite) {
      return await SQLiteService.getFabrics();
    } else {
      // Fallback to AsyncStorage
      return await this.getAsyncStorageData('tailorFabrics') || [];
    }
  }

  // Update fabric stock
  async updateFabricStock($3) {
    if(this.useSQLite) {
      return await SQLiteService.updateFabricStock(id, change);
    } else {
      const fabrics = await this.getAsyncStorageData('tailorFabrics') || [];
      const fabricIndex = fabrics.findIndex(f => f.id === id);
      if(fabricIndex >= 0) {
        fabrics[fabricIndex].stock = Math.max(0, (fabrics[fabricIndex].stock || 0) + change);
        fabrics[fabricIndex].updatedAt = new Date().toISOString();
        await this.setAsyncStorageData('tailorFabrics', fabrics);
        return true;
      }
      return false;
    }
  }

  // Delete fabric (soft delete)
  async deleteFabric($3) {
    if(this.useSQLite) {
      return await SQLiteService.deleteFabric(id);
    } else {
      const fabrics = await this.getAsyncStorageData('tailorFabrics') || [];
      const fabricIndex = fabrics.findIndex(f => f.id === id);
      if(fabricIndex >= 0) {
        fabrics[fabricIndex].isActive = false;
        fabrics[fabricIndex].updatedAt = new Date().toISOString();
        await this.setAsyncStorageData('tailorFabrics', fabrics);
        return true;
      }
      return false;
    }
  }

  // ===== GARMENT TEMPLATES CRUD OPERATIONS =====

  // Get all garment templates
  async getGarmentTemplates($3) {
    if(this.useSQLite) {
      try {
        return await SQLiteService.getGarmentTemplates();
      } catch (error) {
        console.warn('SQLite getGarmentTemplates not available, using fallback: ', error.message);
        return [];
      }
    } else {
      // Fallback to AsyncStorage
      return await this.getAsyncStorageData('garmentTemplates') || [];
    }
  }

  // Save garment template (create or update)
  async saveGarmentTemplate($3) {
    if(this.useSQLite) {
      return await SQLiteService.saveGarmentTemplate(template);
    } else {
      // Fallback to AsyncStorage
      const templates = await this.getAsyncStorageData('garmentTemplates') || [];
      const existingIndex = templates.findIndex(t => t.id === template.id);

      if(existingIndex >= 0) {
        templates[existingIndex] = { ...template, updatedAt: new Date().toISOString() };
      } else {
        templates.push({
          ...template,
          id: template.id || Date.now().toString(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        });
      }

      await this.setAsyncStorageData('garmentTemplates', templates);
      return template;
    }
  }

  // Delete garment template
  async deleteGarmentTemplate($3) {
    if(this.useSQLite) {
      return await SQLiteService.deleteGarmentTemplate(id);
    } else {
      // Fallback to AsyncStorage
      const templates = await this.getAsyncStorageData('garmentTemplates') || [];
      const filteredTemplates = templates.filter(t => t.id !== id);
      await this.setAsyncStorageData('garmentTemplates', filteredTemplates);
    }
  }

  // Clear all data - for tailoring management system
  async clearAllData($3) {
    try {

      // Clear AsyncStorage data (updated for tailoring system)
      await AsyncStorage.multiRemove([
        'tailorData', // New tailoring data
        'orders',
        'customers',
        'tailorMeasurements',
        'tailorFabrics',
        'garmentTemplates',
        'tailorAppointments',
        'financial_expenses',
        'cash_reconciliations',
        'sqlite_migration_completed',
        'app_settings',
        'user_preferences'
      ]);

      // Reset SQLite database if initialized
      if(this.useSQLite && SQLiteService.isInitialized) {
        await SQLiteService.resetDatabase();
      }

      // Reset migration status
      this.migrationCompleted = false;
      this.useSQLite = false;

      return true;
    } catch (error) {
      console.error('Failed to clear all data: ', error);
      return false;
    }
  }

  // Complete data reset - clears all data from both SQLite and AsyncStorage
  async resetAllData($3) {
    return await this.clearAllData();
  }
}

export default new MigrationService();
