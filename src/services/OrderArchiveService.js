/**
 * OrderArchiveService - Handles automatic archiving of completed orders
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { NotificationService } from './notificationService';

export class OrderArchiveService {
  static ARCHIVE_DELAY_DAYS = 30; // Archive completed orders after 30 days
  static STORAGE_KEY = 'archivedOrders';
  static LAST_ARCHIVE_CHECK_KEY = 'lastArchiveCheck';

  /**
   * Check and archive completed orders that are older than 30 days
   */
  static async checkAndArchiveOrders(orders = []) {
    try {
      const now = new Date();
      const archiveThreshold = new Date(now.getTime() - (this.ARCHIVE_DELAY_DAYS * 24 * 60 * 60 * 1000));
      
      const ordersToArchive = orders.filter(order => {
        if (order.status !== 'Completed') return false;
        
        // Check completion date or updated date
        const completionDate = new Date(order.completedAt || order.updatedAt || order.createdAt);
        return completionDate < archiveThreshold;
      });

      if (ordersToArchive.length > 0) {
        await this.archiveOrders(ordersToArchive);
        
        // Create notification about archived orders
        await NotificationService.createNotification({
          type: 'ORDER_ARCHIVE',
          title: 'Orders Archived',
          message: `${ordersToArchive.length} completed orders have been automatically archived`,
          priority: 'LOW',
          data: {
            archivedCount: ordersToArchive.length,
            archiveDate: now.toISOString()
          }
        });

        return ordersToArchive.map(order => order.id);
      }

      return [];
    } catch (error) {
      console.error('Error checking and archiving orders:', error);
      return [];
    }
  }

  /**
   * Archive specific orders
   */
  static async archiveOrders(orders) {
    try {
      const existingArchived = await this.getArchivedOrders();
      
      // Add archive metadata
      const ordersWithArchiveData = orders.map(order => ({
        ...order,
        archivedAt: new Date().toISOString(),
        archivedReason: 'auto_archive_completed',
        originalStatus: order.status,
        status: 'Archived'
      }));

      const updatedArchived = [...existingArchived, ...ordersWithArchiveData];
      await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(updatedArchived));

      return ordersWithArchiveData;
    } catch (error) {
      console.error('Error archiving orders:', error);
      throw error;
    }
  }

  /**
   * Get all archived orders
   */
  static async getArchivedOrders() {
    try {
      const archived = await AsyncStorage.getItem(this.STORAGE_KEY);
      return archived ? JSON.parse(archived) : [];
    } catch (error) {
      console.error('Error getting archived orders:', error);
      return [];
    }
  }

  /**
   * Search archived orders
   */
  static async searchArchivedOrders(query) {
    try {
      const archivedOrders = await this.getArchivedOrders();
      
      if (!query || query.trim() === '') {
        return archivedOrders;
      }

      const searchTerm = query.toLowerCase().trim();
      
      return archivedOrders.filter(order => {
        return (
          order.id.toLowerCase().includes(searchTerm) ||
          order.customerName?.toLowerCase().includes(searchTerm) ||
          order.phone?.toLowerCase().includes(searchTerm) ||
          order.email?.toLowerCase().includes(searchTerm) ||
          order.garmentType?.toLowerCase().includes(searchTerm) ||
          order.notes?.toLowerCase().includes(searchTerm)
        );
      });
    } catch (error) {
      console.error('Error searching archived orders:', error);
      return [];
    }
  }

  /**
   * Restore an archived order
   */
  static async restoreOrder(orderId) {
    try {
      const archivedOrders = await this.getArchivedOrders();
      const orderIndex = archivedOrders.findIndex(order => order.id === orderId);
      
      if (orderIndex === -1) {
        throw new Error('Archived order not found');
      }

      const orderToRestore = archivedOrders[orderIndex];
      
      // Remove from archived orders
      archivedOrders.splice(orderIndex, 1);
      await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(archivedOrders));

      // Restore original status and remove archive metadata
      const restoredOrder = {
        ...orderToRestore,
        status: orderToRestore.originalStatus || 'Completed',
        restoredAt: new Date().toISOString()
      };

      // Remove archive-specific fields
      delete restoredOrder.archivedAt;
      delete restoredOrder.archivedReason;
      delete restoredOrder.originalStatus;

      return restoredOrder;
    } catch (error) {
      console.error('Error restoring order:', error);
      throw error;
    }
  }

  /**
   * Permanently delete an archived order
   */
  static async deleteArchivedOrder(orderId) {
    try {
      const archivedOrders = await this.getArchivedOrders();
      const filteredOrders = archivedOrders.filter(order => order.id !== orderId);
      
      await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(filteredOrders));
      
      return true;
    } catch (error) {
      console.error('Error deleting archived order:', error);
      throw error;
    }
  }

  /**
   * Get archive statistics
   */
  static async getArchiveStats() {
    try {
      const archivedOrders = await this.getArchivedOrders();
      
      const stats = {
        totalArchived: archivedOrders.length,
        archivedThisMonth: 0,
        archivedThisYear: 0,
        oldestArchive: null,
        newestArchive: null,
        totalValue: 0
      };

      if (archivedOrders.length > 0) {
        const now = new Date();
        const thisMonth = now.getMonth();
        const thisYear = now.getFullYear();

        archivedOrders.forEach(order => {
          const archiveDate = new Date(order.archivedAt);
          
          // Count this month
          if (archiveDate.getMonth() === thisMonth && archiveDate.getFullYear() === thisYear) {
            stats.archivedThisMonth++;
          }
          
          // Count this year
          if (archiveDate.getFullYear() === thisYear) {
            stats.archivedThisYear++;
          }

          // Calculate total value
          stats.totalValue += order.total || 0;
        });

        // Find oldest and newest
        const sortedByDate = archivedOrders.sort((a, b) => 
          new Date(a.archivedAt) - new Date(b.archivedAt)
        );
        
        stats.oldestArchive = sortedByDate[0].archivedAt;
        stats.newestArchive = sortedByDate[sortedByDate.length - 1].archivedAt;
      }

      return stats;
    } catch (error) {
      console.error('Error getting archive stats:', error);
      return {
        totalArchived: 0,
        archivedThisMonth: 0,
        archivedThisYear: 0,
        oldestArchive: null,
        newestArchive: null,
        totalValue: 0
      };
    }
  }

  /**
   * Schedule periodic archive checks
   */
  static async scheduleArchiveCheck() {
    try {
      const lastCheck = await AsyncStorage.getItem(this.LAST_ARCHIVE_CHECK_KEY);
      const now = new Date();
      const today = now.toDateString();

      // Only check once per day
      if (lastCheck === today) {
        return;
      }

      // This would typically be called by the DataContext or a background service

      await AsyncStorage.setItem(this.LAST_ARCHIVE_CHECK_KEY, today);
    } catch (error) {
      console.error('Error scheduling archive check:', error);
    }
  }

  /**
   * Clear all archived orders (admin function)
   */
  static async clearAllArchived() {
    try {
      await AsyncStorage.removeItem(this.STORAGE_KEY);
      return true;
    } catch (error) {
      console.error('Error clearing archived orders:', error);
      throw error;
    }
  }
}

export default OrderArchiveService;
