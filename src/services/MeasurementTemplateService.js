/**
 * MeasurementTemplateService - Manage measurement templates for different garments
 */

import AsyncStorage from '@react-native-async-storage/async-storage';

class MeasurementTemplateService {
  constructor() {
    this.templates = [];
    this.init();
  }

  async init($3) {
    try {
      const stored = await AsyncStorage.getItem('measurementTemplates');
      if(stored) {
        this.templates = JSON.parse(stored);
      } else {
        // Initialize with default templates
        this.templates = this.getDefaultTemplates();
        await this.saveTemplates();
      }
    } catch (error) {
      console.error('Failed to load measurement templates: ', error);
      this.templates = this.getDefaultTemplates();
    }
  }

  async saveTemplates($3) {
    try {
      await AsyncStorage.setItem('measurementTemplates', JSON.stringify(this.templates));
    } catch (error) {
      console.error('Failed to save measurement templates: ', error);
    }
  }

  getDefaultTemplates() {
    return [
      // Men's Shirt Template
      {
        id: 'mens_shirt',
        name: "Men's Shirt",
        garmentType: 'Shirt',
        genderCategory: 'Men',
        isDefault: true,
        fields: [
          { id: 'chest', name: 'chest', label: 'Chest', unit: 'inches', required: true, category: 'chest', order: 1, description: 'Around the fullest part of chest' },
          { id: 'waist', name: 'waist', label: 'Waist', unit: 'inches', required: true, category: 'waist', order: 2, description: 'Around the natural waistline' },
          { id: 'shoulder', name: 'shoulder', label: 'Shoulder', unit: 'inches', required: true, category: 'chest', order: 3, description: 'From shoulder point to shoulder point' },
          { id: 'sleeve_length', name: 'sleeve_length', label: 'Sleeve Length', unit: 'inches', required: true, category: 'sleeve', order: 4, description: 'From shoulder to wrist' },
          { id: 'shirt_length', name: 'shirt_length', label: 'Shirt Length', unit: 'inches', required: true, category: 'length', order: 5, description: 'From shoulder to desired length' },
          { id: 'neck', name: 'neck', label: 'Neck', unit: 'inches', required: true, category: 'neck', order: 6, description: 'Around the neck base' },
          { id: 'bicep', name: 'bicep', label: 'Bicep', unit: 'inches', required: false, category: 'sleeve', order: 7, description: 'Around the fullest part of upper arm' },
          { id: 'wrist', name: 'wrist', label: 'Wrist', unit: 'inches', required: false, category: 'sleeve', order: 8, description: 'Around the wrist' },
        ],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },

      // Men's Pants Template
      {
        id: 'mens_pants',
        name: "Men's Pants",
        garmentType: 'Pants',
        genderCategory: 'Men',
        isDefault: true,
        fields: [
          { id: 'waist', name: 'waist', label: 'Waist', unit: 'inches', required: true, category: 'waist', order: 1, description: 'Around the natural waistline' },
          { id: 'hip', name: 'hip', label: 'Hip', unit: 'inches', required: true, category: 'waist', order: 2, description: 'Around the fullest part of hips' },
          { id: 'inseam', name: 'inseam', label: 'Inseam', unit: 'inches', required: true, category: 'length', order: 3, description: 'From crotch to ankle' },
          { id: 'outseam', name: 'outseam', label: 'Outseam', unit: 'inches', required: true, category: 'length', order: 4, description: 'From waist to ankle on the side' },
          { id: 'thigh', name: 'thigh', label: 'Thigh', unit: 'inches', required: true, category: 'other', order: 5, description: 'Around the fullest part of thigh' },
          { id: 'knee', name: 'knee', label: 'Knee', unit: 'inches', required: false, category: 'other', order: 6, description: 'Around the knee' },
          { id: 'calf', name: 'calf', label: 'Calf', unit: 'inches', required: false, category: 'other', order: 7, description: 'Around the fullest part of calf' },
          { id: 'ankle', name: 'ankle', label: 'Ankle', unit: 'inches', required: false, category: 'other', order: 8, description: 'Around the ankle' },
          { id: 'rise', name: 'rise', label: 'Rise', unit: 'inches', required: false, category: 'length', order: 9, description: 'From waist to crotch' },
        ],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },

      // Men's Suit Template
      {
        id: 'mens_suit',
        name: "Men's Suit",
        garmentType: 'Suit',
        genderCategory: 'Men',
        isDefault: true,
        fields: [
          // Jacket measurements
          { id: 'chest', name: 'chest', label: 'Chest', unit: 'inches', required: true, category: 'chest', order: 1, description: 'Around the fullest part of chest' },
          { id: 'waist', name: 'waist', label: 'Waist', unit: 'inches', required: true, category: 'waist', order: 2, description: 'Around the natural waistline' },
          { id: 'shoulder', name: 'shoulder', label: 'Shoulder', unit: 'inches', required: true, category: 'chest', order: 3, description: 'From shoulder point to shoulder point' },
          { id: 'sleeve_length', name: 'sleeve_length', label: 'Sleeve Length', unit: 'inches', required: true, category: 'sleeve', order: 4, description: 'From shoulder to wrist' },
          { id: 'jacket_length', name: 'jacket_length', label: 'Jacket Length', unit: 'inches', required: true, category: 'length', order: 5, description: 'From shoulder to desired length' },
          { id: 'neck', name: 'neck', label: 'Neck', unit: 'inches', required: true, category: 'neck', order: 6, description: 'Around the neck base' },
          // Pants measurements
          { id: 'pant_waist', name: 'pant_waist', label: 'Pant Waist', unit: 'inches', required: true, category: 'waist', order: 7, description: 'Pant waistline measurement' },
          { id: 'hip', name: 'hip', label: 'Hip', unit: 'inches', required: true, category: 'waist', order: 8, description: 'Around the fullest part of hips' },
          { id: 'inseam', name: 'inseam', label: 'Inseam', unit: 'inches', required: true, category: 'length', order: 9, description: 'From crotch to ankle' },
          { id: 'thigh', name: 'thigh', label: 'Thigh', unit: 'inches', required: true, category: 'other', order: 10, description: 'Around the fullest part of thigh' },
        ],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },

      // Women's Dress Template
      {
        id: 'womens_dress',
        name: "Women's Dress",
        garmentType: 'Dress',
        genderCategory: 'Women',
        isDefault: true,
        fields: [
          { id: 'bust', name: 'bust', label: 'Bust', unit: 'inches', required: true, category: 'chest', order: 1, description: 'Around the fullest part of bust' },
          { id: 'waist', name: 'waist', label: 'Waist', unit: 'inches', required: true, category: 'waist', order: 2, description: 'Around the natural waistline' },
          { id: 'hip', name: 'hip', label: 'Hip', unit: 'inches', required: true, category: 'waist', order: 3, description: 'Around the fullest part of hips' },
          { id: 'shoulder', name: 'shoulder', label: 'Shoulder', unit: 'inches', required: true, category: 'chest', order: 4, description: 'From shoulder point to shoulder point' },
          { id: 'dress_length', name: 'dress_length', label: 'Dress Length', unit: 'inches', required: true, category: 'length', order: 5, description: 'From shoulder to desired length' },
          { id: 'sleeve_length', name: 'sleeve_length', label: 'Sleeve Length', unit: 'inches', required: false, category: 'sleeve', order: 6, description: 'From shoulder to wrist (if applicable)' },
          { id: 'neck', name: 'neck', label: 'Neck', unit: 'inches', required: false, category: 'neck', order: 7, description: 'Around the neck base' },
          { id: 'armhole', name: 'armhole', label: 'Armhole', unit: 'inches', required: false, category: 'chest', order: 8, description: 'Around the armhole' },
        ],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },

      // Traditional Kurta Template
      {
        id: 'kurta',
        name: 'Kurta',
        garmentType: 'Traditional',
        genderCategory: 'Unisex',
        isDefault: true,
        fields: [
          { id: 'chest', name: 'chest', label: 'Chest', unit: 'inches', required: true, category: 'chest', order: 1, description: 'Around the fullest part of chest' },
          { id: 'waist', name: 'waist', label: 'Waist', unit: 'inches', required: true, category: 'waist', order: 2, description: 'Around the natural waistline' },
          { id: 'shoulder', name: 'shoulder', label: 'Shoulder', unit: 'inches', required: true, category: 'chest', order: 3, description: 'From shoulder point to shoulder point' },
          { id: 'sleeve_length', name: 'sleeve_length', label: 'Sleeve Length', unit: 'inches', required: true, category: 'sleeve', order: 4, description: 'From shoulder to wrist' },
          { id: 'kurta_length', name: 'kurta_length', label: 'Kurta Length', unit: 'inches', required: true, category: 'length', order: 5, description: 'From shoulder to desired length' },
          { id: 'neck', name: 'neck', label: 'Neck', unit: 'inches', required: true, category: 'neck', order: 6, description: 'Around the neck base' },
          { id: 'collar_height', name: 'collar_height', label: 'Collar Height', unit: 'inches', required: false, category: 'neck', order: 7, description: 'Height of the collar' },
          { id: 'sleeve_opening', name: 'sleeve_opening', label: 'Sleeve Opening', unit: 'inches', required: false, category: 'sleeve', order: 8, description: 'Around the sleeve opening' },
        ],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },

      // Saree Blouse Template
      {
        id: 'saree_blouse',
        name: 'Saree Blouse',
        garmentType: 'Blouse',
        genderCategory: 'Women',
        isDefault: true,
        fields: [
          { id: 'bust', name: 'bust', label: 'Bust', unit: 'inches', required: true, category: 'chest', order: 1, description: 'Around the fullest part of bust' },
          { id: 'under_bust', name: 'under_bust', label: 'Under Bust', unit: 'inches', required: true, category: 'chest', order: 2, description: 'Just under the bust line' },
          { id: 'waist', name: 'waist', label: 'Waist', unit: 'inches', required: true, category: 'waist', order: 3, description: 'Around the natural waistline' },
          { id: 'shoulder', name: 'shoulder', label: 'Shoulder', unit: 'inches', required: true, category: 'chest', order: 4, description: 'From shoulder point to shoulder point' },
          { id: 'blouse_length', name: 'blouse_length', label: 'Blouse Length', unit: 'inches', required: true, category: 'length', order: 5, description: 'From shoulder to desired length' },
          { id: 'sleeve_length', name: 'sleeve_length', label: 'Sleeve Length', unit: 'inches', required: true, category: 'sleeve', order: 6, description: 'From shoulder to desired sleeve length' },
          { id: 'armhole', name: 'armhole', label: 'Armhole', unit: 'inches', required: true, category: 'chest', order: 7, description: 'Around the armhole' },
          { id: 'neck_depth_front', name: 'neck_depth_front', label: 'Neck Depth (Front)', unit: 'inches', required: false, category: 'neck', order: 8, description: 'Depth of front neckline' },
          { id: 'neck_depth_back', name: 'neck_depth_back', label: 'Neck Depth (Back)', unit: 'inches', required: false, category: 'neck', order: 9, description: 'Depth of back neckline' },
        ],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },

      // Children's Shirt Template
      {
        id: 'children_shirt',
        name: "Children's Shirt",
        garmentType: 'Shirt',
        genderCategory: 'Children',
        isDefault: true,
        fields: [
          { id: 'chest', name: 'chest', label: 'Chest', unit: 'inches', required: true, category: 'chest', order: 1, description: 'Around the fullest part of chest' },
          { id: 'waist', name: 'waist', label: 'Waist', unit: 'inches', required: true, category: 'waist', order: 2, description: 'Around the natural waistline' },
          { id: 'shoulder', name: 'shoulder', label: 'Shoulder', unit: 'inches', required: true, category: 'chest', order: 3, description: 'From shoulder point to shoulder point' },
          { id: 'sleeve_length', name: 'sleeve_length', label: 'Sleeve Length', unit: 'inches', required: true, category: 'sleeve', order: 4, description: 'From shoulder to wrist' },
          { id: 'shirt_length', name: 'shirt_length', label: 'Shirt Length', unit: 'inches', required: true, category: 'length', order: 5, description: 'From shoulder to desired length' },
          { id: 'neck', name: 'neck', label: 'Neck', unit: 'inches', required: true, category: 'neck', order: 6, description: 'Around the neck base' },
        ],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },

      // Children's Dress Template
      {
        id: 'children_dress',
        name: "Children's Dress",
        garmentType: 'Dress',
        genderCategory: 'Children',
        isDefault: true,
        fields: [
          { id: 'chest', name: 'chest', label: 'Chest', unit: 'inches', required: true, category: 'chest', order: 1, description: 'Around the fullest part of chest' },
          { id: 'waist', name: 'waist', label: 'Waist', unit: 'inches', required: true, category: 'waist', order: 2, description: 'Around the natural waistline' },
          { id: 'shoulder', name: 'shoulder', label: 'Shoulder', unit: 'inches', required: true, category: 'chest', order: 3, description: 'From shoulder point to shoulder point' },
          { id: 'dress_length', name: 'dress_length', label: 'Dress Length', unit: 'inches', required: true, category: 'length', order: 4, description: 'From shoulder to desired length' },
          { id: 'sleeve_length', name: 'sleeve_length', label: 'Sleeve Length', unit: 'inches', required: false, category: 'sleeve', order: 5, description: 'From shoulder to wrist (if applicable)' },
        ],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    ];
  }

  // Get all templates
  getTemplates() {
    return this.templates;
  }

  // Get template by garment type
  getTemplateByGarmentType(garmentType) {
    return this.templates.find(template =>
      template.garmentType.toLowerCase() === garmentType.toLowerCase()
    );
  }

  // Get templates by gender category
  getTemplatesByGenderCategory(genderCategory) {
    return this.templates.filter(template =>
      template.genderCategory && template.genderCategory.toLowerCase() === genderCategory.toLowerCase()
    );
  }

  // Get all gender categories
  getGenderCategories() {
    const categories = [...new Set(this.templates.map(template => template.genderCategory).filter(Boolean))];
    return categories.sort();
  }

  // Get template by garment type and gender category
  getTemplateByGarmentTypeAndGender(garmentType, genderCategory) {
    return this.templates.find(template =>
      template.garmentType.toLowerCase() === garmentType.toLowerCase() &&
      template.genderCategory && template.genderCategory.toLowerCase() === genderCategory.toLowerCase()
    );
  }

  // Get template by ID
  getTemplateById(id) {
    return this.templates.find(template => template.id === id);
  }

  // Add custom template
  async addTemplate($3) {
    const newTemplate = {
      id: Date.now().toString(),
      ...template,
      isDefault: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    this.templates.push(newTemplate);
    await this.saveTemplates();
    return newTemplate;
  }

  // Update template
  async updateTemplate($3) {
    const templateIndex = this.templates.findIndex(t => t.id === id);
    if(templateIndex !== -1) {
      this.templates[templateIndex] = {
        ...this.templates[templateIndex],
        ...updates,
        updatedAt: new Date().toISOString(),
      };
      await this.saveTemplates();
      return this.templates[templateIndex];
    }
    return null;
  }

  // Delete template (only custom templates)
  async deleteTemplate($3) {
    const template = this.getTemplateById(id);
    if(template && !template.isDefault) {
      this.templates = this.templates.filter(t => t.id !== id);
      await this.saveTemplates();
      return true;
    }
    return false;
  }

  // Get measurement fields for a garment type
  getMeasurementFields(garmentType) {
    const template = this.getTemplateByGarmentType(garmentType);
    return template ? template.fields.sort((a, b) => a.order - b.order) : [];
  }

  // Get required fields for a garment type
  getRequiredFields(garmentType) {
    const fields = this.getMeasurementFields(garmentType);
    return fields.filter(field => field.required);
  }

  // Validate measurements against template
  validateMeasurements(garmentType, measurements) {
    const requiredFields = this.getRequiredFields(garmentType);
    const errors = [];

    requiredFields.forEach(field => {
      if(!measurements[field.name] || measurements[field.name] <= 0) {
        errors.push(`${field.label}  is required`);
      }
    });

    return { isValid: errors.length === 0,
      errors,
      };
  }
}

// Export singleton instance
export default new MeasurementTemplateService();
