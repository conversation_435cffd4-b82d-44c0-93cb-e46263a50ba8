/**
 * AlertService - Global modern alert service
 */

import React, { createContext, useContext, useState, useCallback } from 'react';
import ModernAlert from '../components/ModernAlert';

// Alert Context
const AlertContext = createContext();

// Alert Provider Component
export const AlertProvider = ({ children }) => {
  const [alerts, setAlerts] = useState([]);

  const showAlert = useCallback((config) => {
    const id = Date.now().toString();
    const alert = {
      id,
      ...config,
      visible: true,
    };

    setAlerts(prev => [...prev, alert]);
    return id;
  }, []);

  const hideAlert = useCallback((id) => {
    setAlerts(prev => prev.filter(alert => alert.id !== id));
  }, []);

  const hideAllAlerts = useCallback(() => {
    setAlerts([]);
  }, []);

  // Convenience methods
  const showSuccess = useCallback((title, message, buttons) => {
    return showAlert({
      type: 'success',
      title,
      message,
      buttons,
    });
  }, [showAlert]);

  const showError = useCallback((title, message, buttons) => {
    return showAlert({
      type: 'error',
      title,
      message,
      buttons,
    });
  }, [showAlert]);

  const showWarning = useCallback((title, message, buttons) => {
    return showAlert({
      type: 'warning',
      title,
      message,
      buttons,
    });
  }, [showAlert]);

  const showInfo = useCallback((title, message, buttons) => {
    return showAlert({
      type: 'info',
      title,
      message,
      buttons,
    });
  }, [showAlert]);

  const showConfirm = useCallback((title, message, onConfirm, onCancel) => {
    return showAlert({
      type: 'confirm',
      title,
      message,
      dismissable: false,
      buttons: [
        {
          text: 'Cancel',
          mode: 'outlined',
          onPress: () => {
            hideAllAlerts();
            if (onCancel) onCancel();
          },
        },
        {
          text: 'Confirm',
          mode: 'contained',
          onPress: () => {
            hideAllAlerts();
            if (onConfirm) onConfirm();
          },
        },
      ],
    });
  }, [showAlert, hideAllAlerts]);

  const value = {
    showAlert,
    hideAlert,
    hideAllAlerts,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showConfirm,
  };

  return (
    <AlertContext.Provider value={value}>
      {children}
      {alerts.map(alert => (
        <ModernAlert
          key={alert.id}
          {...alert}
          onDismiss={() => hideAlert(alert.id)}
        />
      ))}
    </AlertContext.Provider>
  );
};

// Hook to use alerts
export const useAlert = () => {
  const context = useContext(AlertContext);
  if(!context) {
    throw new Error('useAlert must be used within an AlertProvider');
  }
  return context;
};

// Static methods for use outside components
class AlertService {
  static instance = null;

  static setInstance(instance) {
    AlertService.instance = instance;
  }

  static show(config) {
    if(AlertService.instance) {
      return AlertService.instance.showAlert(config);
    }
    console.warn('AlertService not initialized');
  }

  static success(title, message, buttons) {
    if(AlertService.instance) {
      return AlertService.instance.showSuccess(title, message, buttons);
    }
    console.warn('AlertService not initialized');
  }

  static error(title, message, buttons) {
    if(AlertService.instance) {
      return AlertService.instance.showError(title, message, buttons);
    }
    console.warn('AlertService not initialized');
  }

  static warning(title, message, buttons) {
    if(AlertService.instance) {
      return AlertService.instance.showWarning(title, message, buttons);
    }
    console.warn('AlertService not initialized');
  }

  static info(title, message, buttons) {
    if(AlertService.instance) {
      return AlertService.instance.showInfo(title, message, buttons);
    }
    console.warn('AlertService not initialized');
  }

  static confirm(title, message, onConfirm, onCancel) {
    if(AlertService.instance) {
      return AlertService.instance.showConfirm(title, message, onConfirm, onCancel);
    }
    console.warn('AlertService not initialized');
  }

  static hide(id) {
    if(AlertService.instance) {
      return AlertService.instance.hideAlert(id);
    }
    console.warn('AlertService not initialized');
  }

  static hideAll() {
    if(AlertService.instance) {
      return AlertService.instance.hideAllAlerts();
    }
    console.warn('AlertService not initialized');
  }
}

export default AlertService;
