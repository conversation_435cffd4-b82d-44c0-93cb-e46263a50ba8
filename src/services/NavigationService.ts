/**
 * NavigationService - Centralized navigation management
 * Provides unified navigation methods and route management with performance tracking
 */

import { createNavigationContainerRef   } from '@react-navigation/native';
import { Linking   } from 'react-native';
import performanceService from './PerformanceService';
import type { RootStackParamList, NavigationActions, DeepLinkHandler } from '../types/navigation';

export const navigationRef = createNavigationContainerRef<RootStackParamList>();

interface RouteHistoryItem {
  name: string;
  params?: object;
  timestamp: number;
}

interface Analytics {
  screenViews: Record<string, number>;
  navigationTiming: Record<string, number>;
  userJourney: string[];
}

class NavigationService {
  private currentRoute: string | null;
  private routeHistory: RouteHistoryItem[];
  private navigationListeners: Array<(state: object) => void>;
  private analytics: Analytics;
  private deepLinkHandlers: Map<string, (params: object, searchParams?: URLSearchParams) => void>;

  constructor() {
    this.currentRoute = null;
    this.routeHistory = [];
    this.navigationListeners = [];
    this.analytics = {
      screenViews: {},
      navigationTiming: {},
      userJourney: [];
    this.deepLinkHandlers = new Map();
    this.initializeDeepLinking();
  }

  // Deep linking initialization
  initializeDeepLinking() {
    // Handle initial URL when app is opened from a deep link
    Linking.getInitialURL().then((url) => {
      if(url) {
        this.handleDeepLink(url);
      }
    });

    // Handle deep links when app is already running
    const linkingListener = Linking.addEventListener('url', (event) => {
      this.handleDeepLink(event.url);
    });

    return () => {
      linkingListener?.remove();
    };
  }

  // Register deep link handler
  registerDeepLinkHandler(pattern: string, handler: (params: object, searchParams?: URLSearchParams) => void) {
    this.deepLinkHandlers.set(pattern, handler);
  }

  // Handle deep link URL
  handleDeepLink(url: string) {
    try {
      const urlObj = new URL(url);
      const path = urlObj.pathname;
      
      // Find matching handler
      for(const [pattern, handler] of this.deepLinkHandlers) {
        const regex = new RegExp('^' + pattern.replace(/:[^/]+/g, '([^/]+)') + '$');
        const match = path.match(regex);
        
        if(match) {
          const params = this.extractParams(pattern, path);
          handler(params, urlObj.searchParams);
          return;
        }
      }
      
      // Default handling if no specific handler found
      this.handleDefaultDeepLink(path, urlObj.searchParams);
    } catch (error) {
      console.error('Error handling deep link:', error);
    }
  }

  // Extract parameters from URL path
  extractParams(pattern: string, path: string): Record<string, string> {
    const patternParts = pattern.split('/');
    const pathParts = path.split('/');
    const params: Record<string, string> = {};
    
    patternParts.forEach((part, index) => {
      if (part.startsWith(':')) {
        const paramName = part.slice(1);
        params[paramName] = pathParts[index];
      }
    });
    
    return params;
  }

  // Default deep link handling
  handleDefaultDeepLink(path: string, searchParams: URLSearchParams) {
    const routes: Record<string, keyof RootStackParamList> = {
      '/dashboard': 'Main',
      '/orders': 'Main',
      '/crm': 'Main',
      '/scan': 'QRScanner',
      '/profile': 'MyProfile',
      '/settings': 'UnifiedSettings'
    };
    
    const routeName = routes[path];
    if (routeName && navigationRef.isReady()) {
      this.navigate(routeName);
    }
  }

  // Core navigation methods with TypeScript support
  navigate<RouteName extends keyof RootStackParamList>(
    name: RouteName,
    params?: RootStackParamList[RouteName]
  ) {
    const timer = performanceService.startTimer(`navigate_${name}`, 'navigation');

    if (navigationRef.isReady()) {

      try {
        (navigationRef.navigate as any)(name, params);
        this.addToHistory(name, params);

        timer.end();
      } catch (error) {
        console.error('Navigation error:', error);
        // Track error using recordMetric since trackError doesn't exist
        performanceService.recordMetric('navigation_error', 1, 'error', {
          route: name,
          params,
          error: error instanceof Error ? error.message : String(error)
        });
        timer.end();
      }
    } else {
      console.error(`NavigationService: Navigation ref is not ready for ${name}`);`
      timer.end();
    }
  }

  goBack() {
    if (navigationRef.isReady() && navigationRef.canGoBack()) {
      navigationRef.goBack();
    }
  }

  reset(state: object) {
    if (navigationRef.isReady()) {
      navigationRef.reset(state);
      this.routeHistory = [];
    }
  }

  // Tab navigation methods
  navigateToTab(tabName: string) {
    this.navigate('Main', { screen: tabName } as any);
  }

  // Modal navigation methods
  openModal(modalName: keyof RootStackParamList, params?: object) {
    this.navigate(modalName, params);
  }

  closeModal() {
    this.goBack();
  }

  // Settings navigation methods
  openSettings(settingsScreen: string = 'MyProfile') {
    if(settingsScreen === 'Profile' || settingsScreen === 'MyProfile') {
      this.navigate('MyProfile');
    } else {
      this.navigate('UnifiedSettings');
    }
  }

  // Quick actions navigation
  openQuickAction(action: string, params?: object) {
    switch(action) {
      case 'add-product':
        this.navigate('AddProduct', params);
        break;
      case 'add-order':
        this.navigate('CreateOrder', params);
        break;
      case 'scan':
        this.navigate('QRScanner');
        break;
      case 'add-customer':
        this.navigate('AddCustomer');
        break;
      default:
        console.warn(`Unknown quick action: ${action}`);
    }
  }

  // Route management
  getCurrentRoute() {
    if (navigationRef.isReady()) {
      return navigationRef.getCurrentRoute();
    }
    return null;
  }

  getCurrentRouteName() {
    const route = this.getCurrentRoute();
    return route?.name || null;
  }

  addToHistory(name: string, params?: object) {
    this.routeHistory.push({ name, params, timestamp: Date.now() });
    // Keep only last 10 routes
    if(this.routeHistory.length > 10) {
      this.routeHistory.shift();
    }
  }

  getRouteHistory(): RouteHistoryItem[] {
    return this.routeHistory;
  }

  // Navigation state listeners
  addNavigationListener(listener: (state: object) => void) {
    this.navigationListeners.push(listener);
  }

  removeNavigationListener(listener: (state: object) => void) {
    this.navigationListeners = this.navigationListeners.filter(l => l !== listener);
  }

  notifyNavigationChange(state: object) {
    this.navigationListeners.forEach(listener => listener(state));
  }

  // Utility methods
  isTabRoute(routeName: string): boolean {
    const tabRoutes = ['Dashboard', 'Orders', 'Products', 'CRM', 'Analytics'];
    return tabRoutes.includes(routeName);
  }

  isModalRoute(routeName: string): boolean {
    const modalRoutes = ['AddProduct', 'CreateOrder', 'AddCustomer'];
    return modalRoutes.includes(routeName);
  }

  getTabFromRoute(routeName: string): string {
    if (this.isTabRoute(routeName)) {
      return routeName;
    }
    // Handle nested routes
    if(routeName === 'UnifiedSettings') {
      return 'Dashboard';
    }
    if(routeName === 'MyProfile') {
      return 'Dashboard';
    }
    if(routeName === 'AddProduct') {
      return 'Products';
    }
    if(routeName === 'CreateOrder') {
      return 'Orders';
    }
    if(routeName === 'ImportData') {
      return 'Dashboard';
    }
    return 'Dashboard'; // Default
  }

  // Deep linking support
  buildDeepLink(routeName: string, params?: object): string {
    let link = `tailora://`;

    if (this.isTabRoute(routeName)) {
      link += `tab/${routeName.toLowerCase()}`;
    } else if (this.isModalRoute(routeName)) {
      link += `modal/${routeName.toLowerCase()}`;
    } else {
      link += `screen/${routeName.toLowerCase()}`;
    }

    if(params) {
      const queryString = Object.keys(params)
        .map(key => `${key} =${encodeURIComponent(params[key])}` )
        .join('&');
      link += `?${queryString}`;
    }

    return link;
  }

  // Navigation analytics
  getNavigationAnalytics() {
    const routeCounts: Record<string, number> = {};
    this.routeHistory.forEach(route => {
      routeCounts[route.name] = (routeCounts[route.name] || 0) + 1;
    });

    return { totalNavigations: this.routeHistory.length,
      routeCounts,
      mostVisitedRoute: Object.keys(routeCounts).reduce((a, b) =>
        routeCounts[a] > routeCounts[b] ? a : b, 'Dashboard'
      ),
      currentRoute: this.getCurrentRouteName(),
      history: this.routeHistory.slice(-5) // Last 5 routes
    };
  }
}

// Create singleton instance
const navigationService = new NavigationService();

export default navigationService;

// Export convenience methods
export const navigate = <T extends keyof RootStackParamList>(name: T, params?: RootStackParamList[T]) => 
  navigationService.navigate(name, params);
export const goBack = () => navigationService.goBack();
export const navigateToTab = (tabName: string) => navigationService.navigateToTab(tabName);
export const openModal = (modalName: keyof RootStackParamList, params?: object) => 
  navigationService.openModal(modalName, params);
export const openSettings = (settingsScreen?: string) => navigationService.openSettings(settingsScreen);
export const openQuickAction = (action: string, params?: object) => navigationService.openQuickAction(action, params);
