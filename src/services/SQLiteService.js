/**
 * SQLite Database Service for Optimal Performance
 * Handles all database operations with proper indexing and optimization
 */

import * as SQLite from 'expo-sqlite';
import WebSQLiteAdapter from './WebSQLiteAdapter';

// Detect if running on web platform
const isWeb = typeof window !== 'undefined' && typeof window.document !== 'undefined';

class SQLiteService {
  constructor() {
    this.db = null;
    this.isInitialized = false;
    this.isWeb = isWeb;
  }

  async initialize() {
    if (this.isInitialized && this.db) return;

    try {
      if(this.isWeb) {
        // Use web adapter for web platform
        this.db = new WebSQLiteAdapter();
        await this.db.initialize();

      } else {
        // Use expo-sqlite for native platforms
        this.db = await SQLite.openDatabaseAsync('tailor_management.db');

      }

      // Check if we need to migrate from old schema
      await this.migrateIfNeeded();

      await this.createTables();
      await this.createIndexes();
      this.isInitialized = true;

    } catch (error) {
      console.error('Failed to initialize SQLite database:', error);
      this.isInitialized = false;
      this.db = null;
      throw error;
    }
  }

  async migrateIfNeeded() {
    try {
      if(!this.db) {

        return;
      }

      // Check if orders table exists and has the required columns
      const tableInfo = await this.db.getAllAsync("PRAGMA table_info(orders)");
      if(tableInfo && tableInfo.length > 0) {
        const hasIsActive = tableInfo.some(col => col.name === 'isActive');
        const hasDueDate = tableInfo.some(col => col.name === 'dueDate');
        const hasPaymentStatus = tableInfo.some(col => col.name === 'paymentStatus');
        const hasAssignedTo = tableInfo.some(col => col.name === 'assignedTo');

        if(!hasIsActive || !hasDueDate || !hasPaymentStatus || !hasAssignedTo) {

          // Drop all tables to recreate with new schema
          const tables = ['orders', 'order_items', 'customers', 'measurements', 'fabrics', 'products', 'expenses', 'cash_reconciliations', 'settings'];
          for(const table of tables) {
            try {
              await this.db.execAsync(`DROP TABLE IF EXISTS ${table}`);
            } catch (dropError) {

            }
          }

        }
      }
    } catch (error) {

    }
  }

  // Ensure database connection is available
  async ensureConnection() {
    if(!this.isInitialized || !this.db) {
      await this.initialize();
    }
    return this.db;
  }

  async createTables() {
    if(!this.db) {
      throw new Error('Database not initialized');
    }

    const tables = [
      // Products table
`CREATE TABLE IF NOT EXISTS products (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        price REAL NOT NULL,
        cost REAL NOT NULL,
        stock INTEGER DEFAULT 0,
        sku TEXT UNIQUE,
        barcode TEXT,
        category TEXT,
        image TEXT,
        isActive INTEGER DEFAULT 1,
        isFeatured INTEGER DEFAULT 0,
        tags TEXT, -- JSON string
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      )`,

      // Orders table - Enhanced for tailor management
`CREATE TABLE IF NOT EXISTS orders (
        id TEXT PRIMARY KEY,
        customerName TEXT NOT NULL,
        customer TEXT,
        customerId TEXT,
        email TEXT,
        phone TEXT,
        date TEXT NOT NULL,
        time TEXT NOT NULL,
        status TEXT NOT NULL,
        orderType TEXT DEFAULT 'tailoring',
        subtotal REAL NOT NULL,
        tax REAL DEFAULT 0,
        discount REAL DEFAULT 0,
        total REAL NOT NULL,
        notes TEXT,
        image TEXT,
        dueDate TEXT,
        deliveryDate TEXT,
        deliveryStatus TEXT DEFAULT 'pending',
        urgentOrder INTEGER DEFAULT 0,
        paymentStatus TEXT DEFAULT 'unpaid',
        paidAmount REAL DEFAULT 0,
        balanceAmount REAL DEFAULT 0,
        fittingScheduled INTEGER DEFAULT 0,
        fittingDate TEXT,
        measurements TEXT, -- JSON string
        tags TEXT, -- JSON string
        designReferences TEXT, -- JSON string
        assignedTo TEXT, -- Employee ID
        isActive INTEGER DEFAULT 1,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      )`,

      // Order items table
`CREATE TABLE IF NOT EXISTS order_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        orderId TEXT NOT NULL,
        productId INTEGER NOT NULL,
        productName TEXT NOT NULL,
        quantity INTEGER NOT NULL,
        price REAL NOT NULL,
        total REAL NOT NULL,
        FOREIGN KEY (orderId) REFERENCES orders (id) ON DELETE CASCADE,
        FOREIGN KEY (productId) REFERENCES products (id)
      )`,

      // Customers table
`CREATE TABLE IF NOT EXISTS customers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        email TEXT,
        phone TEXT,
        address TEXT,
        city TEXT,
        notes TEXT,
        isVIP INTEGER DEFAULT 0,
        totalOrders INTEGER DEFAULT 0,
        totalSpent REAL DEFAULT 0,
        loyaltyPoints INTEGER DEFAULT 0,
        tags TEXT, -- JSON string
        isActive INTEGER DEFAULT 1,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      )`,

      // Measurements table for tailor management
`CREATE TABLE IF NOT EXISTS measurements (
        id TEXT PRIMARY KEY,
        customerId TEXT NOT NULL,
        garmentType TEXT NOT NULL,
        templateId TEXT,
        measurements TEXT NOT NULL, -- JSON string
        notes TEXT,
        takenBy TEXT,
        measurementDate TEXT NOT NULL,
        isActive INTEGER DEFAULT 1,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL,
        FOREIGN KEY (customerId) REFERENCES customers (id)
      )`,

      // Fabrics table for inventory management
`CREATE TABLE IF NOT EXISTS fabrics (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        type TEXT NOT NULL,
        color TEXT,
        pattern TEXT DEFAULT 'Solid',
        pricePerMeter REAL DEFAULT 0,
        stock REAL DEFAULT 0,
        width REAL,
        gsm REAL,
        supplier TEXT,
        description TEXT,
        image TEXT,
        isActive INTEGER DEFAULT 1,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      )`,

      // Financial expenses table
`CREATE TABLE IF NOT EXISTS expenses (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        category TEXT NOT NULL,
        amount REAL NOT NULL,
        description TEXT,
        date TEXT NOT NULL,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      )`,

      // Cash reconciliations table
`CREATE TABLE IF NOT EXISTS cash_reconciliations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        date TEXT NOT NULL,
        expectedCash REAL NOT NULL,
        actualCash REAL NOT NULL,
        difference REAL NOT NULL,
        notes TEXT,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      )`,

      // Settings table
`CREATE TABLE IF NOT EXISTS settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      )`];
    for(const table of tables) {
      try {
        await this.db.execAsync(table);
      } catch (error) {
        console.error('Error creating table:', error.message);
        throw error;
      }
    }
  }

  async createIndexes() {
    if(!this.db) {
      throw new Error('Database not initialized');
    }

    const indexes = [
      // Product indexes
      'CREATE INDEX IF NOT EXISTS idx_products_name ON products (name)',
      'CREATE INDEX IF NOT EXISTS idx_products_category ON products (category)',
      'CREATE INDEX IF NOT EXISTS idx_products_sku ON products (sku)',
      'CREATE INDEX IF NOT EXISTS idx_products_barcode ON products (barcode)',
      'CREATE INDEX IF NOT EXISTS idx_products_active ON products (isActive)',
      'CREATE INDEX IF NOT EXISTS idx_products_featured ON products (isFeatured)',

      // Order indexes
      'CREATE INDEX IF NOT EXISTS idx_orders_date ON orders (date)',
      'CREATE INDEX IF NOT EXISTS idx_orders_status ON orders (status)',
      'CREATE INDEX IF NOT EXISTS idx_orders_customer ON orders (customerName)',
      'CREATE INDEX IF NOT EXISTS idx_orders_total ON orders (total)',
      'CREATE INDEX IF NOT EXISTS idx_orders_created ON orders (createdAt)',
      'CREATE INDEX IF NOT EXISTS idx_orders_due_date ON orders (dueDate)',
      'CREATE INDEX IF NOT EXISTS idx_orders_payment_status ON orders (paymentStatus)',
      'CREATE INDEX IF NOT EXISTS idx_orders_urgent ON orders (urgentOrder)',
      'CREATE INDEX IF NOT EXISTS idx_orders_active ON orders (isActive)',

      // Order items indexes
      'CREATE INDEX IF NOT EXISTS idx_order_items_order ON order_items (orderId)',
      'CREATE INDEX IF NOT EXISTS idx_order_items_product ON order_items (productId)',

      // Customer indexes
      'CREATE INDEX IF NOT EXISTS idx_customers_name ON customers (name)',
      'CREATE INDEX IF NOT EXISTS idx_customers_email ON customers (email)',
      'CREATE INDEX IF NOT EXISTS idx_customers_phone ON customers (phone)',
      'CREATE INDEX IF NOT EXISTS idx_customers_vip ON customers (isVIP)',
      'CREATE INDEX IF NOT EXISTS idx_customers_active ON customers (isActive)',

      // Measurements indexes
      'CREATE INDEX IF NOT EXISTS idx_measurements_customer ON measurements (customerId)',
      'CREATE INDEX IF NOT EXISTS idx_measurements_garment ON measurements (garmentType)',
      'CREATE INDEX IF NOT EXISTS idx_measurements_date ON measurements (measurementDate)',
      'CREATE INDEX IF NOT EXISTS idx_measurements_active ON measurements (isActive)',

      // Fabrics indexes
      'CREATE INDEX IF NOT EXISTS idx_fabrics_name ON fabrics (name)',
      'CREATE INDEX IF NOT EXISTS idx_fabrics_type ON fabrics (type)',
      'CREATE INDEX IF NOT EXISTS idx_fabrics_color ON fabrics (color)',
      'CREATE INDEX IF NOT EXISTS idx_fabrics_active ON fabrics (isActive)',

      // Expense indexes
      'CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses (category)',
      'CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses (date)',

      // Cash reconciliation indexes
      'CREATE INDEX IF NOT EXISTS idx_cash_date ON cash_reconciliations (date)'
    ];
    for(const index of indexes) {
      try {
        await this.db.execAsync(index);
      } catch (error) {
        console.warn('Error creating index: ', error.message);
        // Continue with other indexes even if one fails
      }
    }
  }

  // Product operations
  async getProducts(filters = {}) {
    // Web fallback - return empty array
    if(typeof window !== 'undefined' && typeof document !== 'undefined') {
      return [];
    }

    try {
      const db = await this.ensureConnection();

      let query = 'SELECT * FROM products WHERE 1=1';
      const params = [];
      if(filters.isActive !== undefined) {
        query += ' AND isActive = ?';
        params.push(filters.isActive ? 1 : 0);
      }

      if(filters.category) {
        query += ' AND category = ?';
        params.push(filters.category);
      }

      if(filters.search) {
        query += ' AND (name LIKE ? OR description LIKE ? OR sku LIKE ?)';
        const searchTerm = `%${filters.search}%`;
        params.push(searchTerm, searchTerm, searchTerm);
      }

      query += ' ORDER BY name ASC';

      const result = await db.getAllAsync(query, params);
      return result.map(this.parseProduct);
    } catch (error) {
      console.error('Error getting products:', error);
      return [];
    }
  }

  async getProductById(id) {
    if(typeof window !== 'undefined' && typeof document !== 'undefined') {
      return null;
    }
    const result = await this.db.getFirstAsync('SELECT * FROM products WHERE id = ?', [id]);
    return result ? this.parseProduct(result) : null;
  }

  async saveProduct(product) {
    const now = new Date().toISOString();

    if(product.id) {
      // Update existing product
      await this.db.runAsync(`
        UPDATE products SET
          name = ?, description = ?, price = ?, cost = ?, stock = ?,
          sku = ?, barcode = ?, category = ?, image = ?, isActive = ?,
          isFeatured = ?, tags = ?, updatedAt = ?
        WHERE id = ?
`, [
        product.name, product.description, product.price, product.cost, product.stock,
        product.sku, product.barcode, product.category, product.image,
        product.isActive ? 1 : 0, product.isFeatured ? 1 : 0,
        JSON.stringify(product.tags || []), now, product.id
      ]);
      return { ...product, updatedAt: now };
    } else {
      // Insert new product
      const result = await this.db.runAsync(`
        INSERT INTO products (
          name, description, price, cost, stock, sku, barcode, category,
          image, isActive, isFeatured, tags, createdAt, updatedAt
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
`, [
        product.name, product.description, product.price, product.cost, product.stock,
        product.sku, product.barcode, product.category, product.image,
        product.isActive ? 1 : 0, product.isFeatured ? 1 : 0,
        JSON.stringify(product.tags || []), now, now
      ]);

      return { ...product, id: result.lastInsertRowId, createdAt: now, updatedAt: now };
    }
  }

  async deleteProduct(id) {
    await this.db.runAsync('DELETE FROM products WHERE id = ?', [id]);
  }

  // Order operations
  async getOrders(filters = {}) {
    if(typeof window !== 'undefined' && typeof document !== 'undefined') {
      return [];
    }

    let query = `
      SELECT o.*,
        GROUP_CONCAT(
          json_object(
            'productId', oi.productId,
            'productName', oi.productName,
            'quantity', oi.quantity,
            'price', oi.price,
            'total', oi.total
          )
        ) as items
      FROM orders o
      LEFT JOIN order_items oi ON o.id = oi.orderId
      WHERE 1=1
`;
    const params = [];
    if(filters.status) {
      query += ' AND o.status = ?';
      params.push(filters.status);
    }

    if(filters.dateFrom) {
      query += ' AND o.date >= ?';
      params.push(filters.dateFrom);
    }

    if(filters.dateTo) {
      query += ' AND o.date <= ?';
      params.push(filters.dateTo);
    }

    if(filters.search) {
      query += ' AND (o.customerName LIKE ? OR o.id LIKE ?)';
      const searchTerm = `%${filters.search}%`;
      params.push(searchTerm, searchTerm);
    }

    query += ' GROUP BY o.id ORDER BY o.createdAt DESC';

    const result = await this.db.getAllAsync(query, params);
    return result.map(this.parseOrder);
  }

  async getOrderById(id) {
    const query = `
      SELECT o.*,
        GROUP_CONCAT(
          json_object(
            'productId', oi.productId,
            'productName', oi.productName,
            'quantity', oi.quantity,
            'price', oi.price,
            'total', oi.total
          )
        ) as items
      FROM orders o
      LEFT JOIN order_items oi ON o.id = oi.orderId
      WHERE o.id = ?
      GROUP BY o.id
`;

    const result = await this.db.getFirstAsync(query, [id]);
    return result ? this.parseOrder(result) : null;
  }

  async saveOrder(order) {
    const now = new Date().toISOString();

    try {
      const db = await this.ensureConnection();

      // Ensure all required fields have default values to prevent null pointer exceptions
      const safeOrder = {
        id: order.id || `ORD-${Date.now()}`,
        customerName: order.customerName || '',
        customer: order.customer || order.customerName || '',
        customerId: order.customerId || '',
        email: order.email || '',
        phone: order.phone || '',
        date: order.date || new Date().toISOString().split('T')[0],
        time: order.time || new Date().toTimeString().split(' ')[0],
        status: order.status || 'pending',
        orderType: order.orderType || 'tailoring',
        subtotal: order.subtotal || 0,
        tax: order.tax || 0,
        discount: order.discount || 0,
        total: order.total || 0,
        notes: order.notes || '',
        image: order.image || null,
        // Enhanced tailor fields
        dueDate: order.dueDate || null,
        deliveryDate: order.deliveryDate || null,
        deliveryStatus: order.deliveryStatus || 'pending',
        urgentOrder: order.urgentOrder || false,
        paymentStatus: order.paymentStatus || 'unpaid',
        paidAmount: order.paidAmount || 0,
        balanceAmount: order.balanceAmount || 0,
        fittingScheduled: order.fittingScheduled || false,
        fittingDate: order.fittingDate || null,
        measurements: order.measurements || [],
        tags: order.tags || [],
        designReferences: order.designReferences || [],
        isActive: order.isActive !== undefined ? order.isActive : true,
        items: order.items || []
      };

      await db.withTransactionAsync(async () => {
        if (safeOrder.id && await this.getOrderById(safeOrder.id)) {
          // Update existing order
          await db.runAsync(`
            UPDATE orders SET
              customerName = ?, customer = ?, customerId = ?, email = ?, phone = ?, date = ?, time = ?,
              status = ?, orderType = ?, subtotal = ?, tax = ?, discount = ?, total = ?,
              notes = ?, image = ?, dueDate = ?, deliveryDate = ?, deliveryStatus = ?,
              urgentOrder = ?, paymentStatus = ?, paidAmount = ?, balanceAmount = ?,
              fittingScheduled = ?, fittingDate = ?, measurements = ?, tags = ?,
              designReferences = ?, isActive = ?, updatedAt = ?
            WHERE id = ?
`, [
            safeOrder.customerName, safeOrder.customer, safeOrder.customerId, safeOrder.email, safeOrder.phone,
            safeOrder.date, safeOrder.time, safeOrder.status, safeOrder.orderType,
            safeOrder.subtotal, safeOrder.tax, safeOrder.discount, safeOrder.total,
            safeOrder.notes, safeOrder.image, safeOrder.dueDate, safeOrder.deliveryDate, safeOrder.deliveryStatus,
            safeOrder.urgentOrder ? 1 : 0, safeOrder.paymentStatus, safeOrder.paidAmount, safeOrder.balanceAmount,
            safeOrder.fittingScheduled ? 1 : 0, safeOrder.fittingDate, JSON.stringify(safeOrder.measurements),
            JSON.stringify(safeOrder.tags), JSON.stringify(safeOrder.designReferences),
            safeOrder.isActive ? 1 : 0, now, safeOrder.id
          ]);

          // Delete existing order items
          await db.runAsync('DELETE FROM order_items WHERE orderId = ?', [safeOrder.id]);
        } else {
          // Insert new order
          await db.runAsync(`
            INSERT INTO orders (
              id, customerName, customer, customerId, email, phone, date, time, status,
              orderType, subtotal, tax, discount, total, notes, image, dueDate, deliveryDate,
              deliveryStatus, urgentOrder, paymentStatus, paidAmount, balanceAmount,
              fittingScheduled, fittingDate, measurements, tags, designReferences,
              assignedTo, isActive, createdAt, updatedAt
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
`, [
            safeOrder.id, safeOrder.customerName, safeOrder.customer, safeOrder.customerId, safeOrder.email, safeOrder.phone,
            safeOrder.date, safeOrder.time, safeOrder.status, safeOrder.orderType,
            safeOrder.subtotal, safeOrder.tax, safeOrder.discount, safeOrder.total,
            safeOrder.notes, safeOrder.image, safeOrder.dueDate, safeOrder.deliveryDate, safeOrder.deliveryStatus,
            safeOrder.urgentOrder ? 1 : 0, safeOrder.paymentStatus, safeOrder.paidAmount, safeOrder.balanceAmount,
            safeOrder.fittingScheduled ? 1 : 0, safeOrder.fittingDate, JSON.stringify(safeOrder.measurements),
            JSON.stringify(safeOrder.tags), JSON.stringify(safeOrder.designReferences),
            safeOrder.assignedTo || null, safeOrder.isActive ? 1 : 0, now, now
          ]);
        }

        // Insert order items
        if(safeOrder.items && safeOrder.items.length > 0) {
          for(const item of safeOrder.items) {
            await db.runAsync(`
              INSERT INTO order_items (orderId, productId, productName, quantity, price, total)
              VALUES (?, ?, ?, ?, ?, ?)
`, [
              safeOrder.id,
              item.productId || item.id || '',
              item.productName || item.name || '',
              item.quantity || 1,
              item.price || 0,
              item.total || (item.quantity || 1) * (item.price || 0)
            ]);
          }
        }
      });

      return { ...safeOrder, updatedAt: now };
    } catch (error) {
      console.error('Error saving order:', error);
      throw error;
    }
  }

  async deleteOrder(id) {
    await this.db.withTransactionAsync(async () => {
      // Delete order items first (foreign key constraint)
      await this.db.runAsync('DELETE FROM order_items WHERE orderId = ?', [id]);
      // Delete the order
      await this.db.runAsync('DELETE FROM orders WHERE id = ?', [id]);
    });
  }

  async deleteCustomer(id) {
    await this.db.runAsync('DELETE FROM customers WHERE id = ?', [id]);
  }

  // Helper methods
  parseProduct(row) {
    return {
      ...row,
      isActive: Boolean(row.isActive),
      isFeatured: Boolean(row.isFeatured),
      tags: row.tags ? JSON.parse(row.tags) : []
    };
  }

  parseOrder(row) {
    try {
      let items = [];
      if(row.items && row.items !== null) {
        // Handle GROUP_CONCAT result from SQLite
        if(typeof row.items === 'string') {
          if (row.items.startsWith('[')) {
            // Already a JSON array string
            items = JSON.parse(row.items);
          } else {
            // GROUP_CONCAT result - wrap in array and parse
            const wrappedItems = `[${row.items];
            items = JSON.parse(wrappedItems);
          }
        } else if (Array.isArray(row.items)) {
          items = row.items;
        }
      }

      return {
        ...row,
        items: items || [],
        urgentOrder: Boolean(row.urgentOrder),
        fittingScheduled: Boolean(row.fittingScheduled),
        isActive: Boolean(row.isActive),
        measurements: row.measurements ? JSON.parse(row.measurements) : [],
        tags: row.tags ? JSON.parse(row.tags) : [],
        designReferences: row.designReferences ? JSON.parse(row.designReferences) : []
      };
    } catch (error) {
      console.warn('Error parsing order items: ', error, 'Raw items: ', row.items);
      // Fallback: try to extract items manually if JSON parsing fails
      try {
        if(row.items && typeof row.items === 'string') {
          // Try to split by JSON object boundaries and parse individually
          const jsonObjects = row.items.match(/\{[^]*\}/g);
          if(jsonObjects) {
            items = jsonObjects.map(obj => JSON.parse(obj));
            return {
              ...row,
              items: items,
              urgentOrder: Boolean(row.urgentOrder),
              fittingScheduled: Boolean(row.fittingScheduled),
              isActive: Boolean(row.isActive),
              measurements: row.measurements ? JSON.parse(row.measurements) : [],
              tags: row.tags ? JSON.parse(row.tags) : [],
              designReferences: row.designReferences ? JSON.parse(row.designReferences) : []
          }
        }
      } catch (fallbackError) {
        console.warn('Fallback parsing also failed: ', fallbackError);
      }

      return {
        ...row,
        items: [],
        urgentOrder: Boolean(row.urgentOrder),
        fittingScheduled: Boolean(row.fittingScheduled),
        isActive: Boolean(row.isActive),
        measurements: [],
        tags: [],
        designReferences: []
      };
    }
  }

  // Migration method to import existing data
  async migrateFromAsyncStorage(data) {

    await this.db.withTransactionAsync(async () => {
      // Migrate products
      if(data.products) {
        for(const product of data.products) {
          await this.saveProduct(product);
        }

      }

      // Migrate orders
      if(data.orders) {
        for(const order of data.orders) {
          await this.saveOrder(order);
        }

      }

      // Migrate customers
      if(data.customers) {
        for(const customer of data.customers) {
          await this.saveCustomer(customer);
        }

      }
    });

  }

  async saveCustomer(customer) {
    const now = new Date().toISOString();

    // Ensure all required fields have default values
    const safeCustomer = {
      name: customer.name || '',
      email: customer.email || '',
      phone: customer.phone || '',
      address: customer.address || '',
      notes: customer.notes || '',
      isVIP: customer.isVIP || false,
      totalOrders: customer.totalOrders || 0,
      totalSpent: customer.totalSpent || 0,
      tags: customer.tags || [] // Keep tags for backward compatibility but default to empty array
    };

    if(customer.id) {
      await this.db.runAsync(`
        UPDATE customers SET
          name = ?, email = ?, phone = ?, address = ?, notes = ?,
          isVIP = ?, totalOrders = ?, totalSpent = ?, tags = ?, updatedAt = ?
        WHERE id = ?
`, [
        safeCustomer.name, safeCustomer.email, safeCustomer.phone, safeCustomer.address, safeCustomer.notes,
        safeCustomer.isVIP ? 1 : 0, safeCustomer.totalOrders, safeCustomer.totalSpent,
        JSON.stringify(safeCustomer.tags), now, customer.id
      ]);

      return { ...safeCustomer, id: customer.id, updatedAt: now };
    } else {
      const result = await this.db.runAsync(`
        INSERT INTO customers (
          name, email, phone, address, notes, isVIP, totalOrders, totalSpent, tags, createdAt, updatedAt
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
`, [
        safeCustomer.name, safeCustomer.email, safeCustomer.phone, safeCustomer.address, safeCustomer.notes,
        safeCustomer.isVIP ? 1 : 0, safeCustomer.totalOrders, safeCustomer.totalSpent,
        JSON.stringify(safeCustomer.tags), now, now
      ]);

      return { ...safeCustomer, id: result.lastInsertRowId, createdAt: now, updatedAt: now };
    }
  }

  async getCustomers() {
    try {
      const db = await this.ensureConnection();
      const result = await db.getAllAsync('SELECT * FROM customers ORDER BY name ASC');
      return result.map(row => ({
        ...row,
        isVIP: Boolean(row.isVIP),
        tags: row.tags ? JSON.parse(row.tags) : []));
    } catch (error) {
      console.error('Error getting customers:', error);
      return [];
    }
  }

  // Measurements operations
  async getMeasurements() {
    try {
      const db = await this.ensureConnection();
      const result = await db.getAllAsync('SELECT * FROM measurements WHERE isActive = 1 ORDER BY createdAt DESC');
      return result.map(row => ({
        ...row,
        measurements: row.measurements ? JSON.parse(row.measurements) : {},
        isActive: Boolean(row.isActive)
      }));
    } catch (error) {
      console.error('Error getting measurements:', error);
      return [];
    }
  }

  async getMeasurementsByCustomer(customerId) {
    try {
      const db = await this.ensureConnection();
      const result = await db.getAllAsync(
        'SELECT * FROM measurements WHERE customerId = ? AND isActive = 1 ORDER BY createdAt DESC',
        [customerId]
      );
      return result.map(row => ({
        ...row,
        measurements: row.measurements ? JSON.parse(row.measurements) : {},
        isActive: Boolean(row.isActive)
      }));
    } catch (error) {
      console.error('Error getting measurements by customer:', error);
      return [];
    }
  }

  async saveMeasurement(measurement) {
    const now = new Date().toISOString();
    const db = await this.ensureConnection();

    const safeMeasurement = {
      id: measurement.id || `MEAS-${Date.now()}`
      customerId: measurement.customerId || '',
      garmentType: measurement.garmentType || '',
      templateId: measurement.templateId || '',
      measurements: measurement.measurements || {},
      notes: measurement.notes || '',
      takenBy: measurement.takenBy || '',
      measurementDate: measurement.measurementDate || new Date().toISOString().split('T')[0],
      isActive: measurement.isActive !== undefined ? measurement.isActive : true
    };

    if (await this.getMeasurementById(safeMeasurement.id)) {
      // Update existing measurement
      await db.runAsync(`
        UPDATE measurements SET
          customerId = ?, garmentType = ?, templateId = ?, measurements = ?,
          notes = ?, takenBy = ?, measurementDate = ?, isActive = ?, updatedAt = ?
        WHERE id = ?
`, [
        safeMeasurement.customerId, safeMeasurement.garmentType, safeMeasurement.templateId,
        JSON.stringify(safeMeasurement.measurements), safeMeasurement.notes, safeMeasurement.takenBy,
        safeMeasurement.measurementDate, safeMeasurement.isActive ? 1 : 0, now, safeMeasurement.id
      ]);
      return { ...safeMeasurement, updatedAt: now };
    } else {
      // Insert new measurement
      await db.runAsync(`
        INSERT INTO measurements (
          id, customerId, garmentType, templateId, measurements, notes, takenBy,
          measurementDate, isActive, createdAt, updatedAt
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
`, [
        safeMeasurement.id, safeMeasurement.customerId, safeMeasurement.garmentType, safeMeasurement.templateId,
        JSON.stringify(safeMeasurement.measurements), safeMeasurement.notes, safeMeasurement.takenBy,
        safeMeasurement.measurementDate, safeMeasurement.isActive ? 1 : 0, now, now
      ]);
      return { ...safeMeasurement, createdAt: now, updatedAt: now };
    }
  }

  async getMeasurementById(id) {
    try {
      const db = await this.ensureConnection();
      const result = await db.getFirstAsync('SELECT * FROM measurements WHERE id = ?', [id]);
      if(result) {
        return {
          ...result,
          measurements: result.measurements ? JSON.parse(result.measurements) : {},
          isActive: Boolean(result.isActive)
        };
      }
      return null;
    } catch (error) {
      console.error('Error getting measurement by ID:', error);
      return null;
    }
  }

  async deleteMeasurement(id) {
    try {
      const db = await this.ensureConnection();
      await db.runAsync('UPDATE measurements SET isActive = 0, updatedAt = ? WHERE id = ?', [
        new Date().toISOString(), id
      ]);
      return true;
    } catch (error) {
      console.error('Error deleting measurement:', error);
      return false;
    }
  }

  // Fabrics operations
  async getFabrics() {
    try {
      const db = await this.ensureConnection();
      const result = await db.getAllAsync('SELECT * FROM fabrics WHERE isActive = 1 ORDER BY createdAt DESC');
      return result.map(row => ({
        ...row,
        isActive: Boolean(row.isActive)
      }));
    } catch (error) {
      console.error('Error getting fabrics:', error);
      return [];
    }
  }

  async saveFabric(fabric) {
    const now = new Date().toISOString();
    const db = await this.ensureConnection();

    const safeFabric = {
      id: fabric.id || `FAB-${Date.now()}`
      name: fabric.name || '',
      type: fabric.type || '',
      color: fabric.color || '',
      pattern: fabric.pattern || 'Solid',
      pricePerMeter: fabric.pricePerMeter || 0,
      stock: fabric.stock || 0,
      width: fabric.width || null,
      gsm: fabric.gsm || null,
      supplier: fabric.supplier || '',
      description: fabric.description || '',
      image: fabric.image || null,
      isActive: fabric.isActive !== undefined ? fabric.isActive : true
    };

    if (await this.getFabricById(safeFabric.id)) {
      // Update existing fabric
      await db.runAsync(`
        UPDATE fabrics SET
          name = ?, type = ?, color = ?, pattern = ?, pricePerMeter = ?, stock = ?,
          width = ?, gsm = ?, supplier = ?, description = ?, image = ?, isActive = ?, updatedAt = ?
        WHERE id = ?
`, [
        safeFabric.name, safeFabric.type, safeFabric.color, safeFabric.pattern,
        safeFabric.pricePerMeter, safeFabric.stock, safeFabric.width, safeFabric.gsm,
        safeFabric.supplier, safeFabric.description, safeFabric.image,
        safeFabric.isActive ? 1 : 0, now, safeFabric.id
      ]);
      return { ...safeFabric, updatedAt: now };
    } else {
      // Insert new fabric
      await db.runAsync(`
        INSERT INTO fabrics (
          id, name, type, color, pattern, pricePerMeter, stock, width, gsm,
          supplier, description, image, isActive, createdAt, updatedAt
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
`, [
        safeFabric.id, safeFabric.name, safeFabric.type, safeFabric.color, safeFabric.pattern,
        safeFabric.pricePerMeter, safeFabric.stock, safeFabric.width, safeFabric.gsm,
        safeFabric.supplier, safeFabric.description, safeFabric.image,
        safeFabric.isActive ? 1 : 0, now, now
      ]);
      return { ...safeFabric, createdAt: now, updatedAt: now };
    }
  }

  async getFabricById(id) {
    try {
      const db = await this.ensureConnection();
      const result = await db.getFirstAsync('SELECT * FROM fabrics WHERE id = ?', [id]);
      if(result) {
        return { ...result,
          isActive: Boolean(result.isActive)
  };
      }
      return null;
    } catch (error) {
      console.error('Error getting fabric by ID:', error);
      return null;
    }
  }

  async updateFabricStock(id, change) {
    try {
      const db = await this.ensureConnection();
      await db.runAsync(
        'UPDATE fabrics SET stock = MAX(0, stock + ?), updatedAt = ? WHERE id = ?',
        [change, new Date().toISOString(), id]
      );
      return true;
    } catch (error) {
      console.error('Error updating fabric stock:', error);
      return false;
    }
  }

  async deleteFabric(id) {
    try {
      const db = await this.ensureConnection();
      await db.runAsync('UPDATE fabrics SET isActive = 0, updatedAt = ? WHERE id = ?', [
        new Date().toISOString(), id
      ]);
      return true;
    } catch (error) {
      console.error('Error deleting fabric:', error);
      return false;
    }
  }

  // Clear all data from all tables
  async clearAllData() {
    await this.db.withTransactionAsync(async () => {
      // Clear all tables in the correct order (respecting foreign key constraints)
      await this.db.runAsync('DELETE FROM order_items');
      await this.db.runAsync('DELETE FROM orders');
      await this.db.runAsync('DELETE FROM products');
      await this.db.runAsync('DELETE FROM measurements');
      await this.db.runAsync('DELETE FROM fabrics');
      await this.db.runAsync('DELETE FROM customers');
      await this.db.runAsync('DELETE FROM expenses');
      await this.db.runAsync('DELETE FROM cash_reconciliations');
      await this.db.runAsync('DELETE FROM settings');

      // Reset auto-increment counters
      await this.db.runAsync('DELETE FROM sqlite_sequence');
    });

  }

  // Complete database reset - drops and recreates all tables
  async resetDatabase() {
    try {
      // Drop all tables
      const tables = [
        'order_items',
        'orders',
        'products',
        'measurements',
        'fabrics',
        'customers',
        'expenses',
        'cash_reconciliations',
        'settings'
      ];
      for(const table of tables) {
        await this.db.runAsync(`DROP TABLE IF EXISTS ${table}`}

      // Recreate all tables and indexes
      await this.createTables();
      await this.createIndexes();

    } catch (error) {
      console.error('Error resetting database:', error);
      throw error;
    }
  }

  // Garment templates operations
  async getGarmentTemplates() {
    try {
      // For now, return empty array since garment templates table might not exist
      // This can be implemented when the garment templates feature is fully developed
      return [];
    } catch (error) {
      console.error('Error getting garment templates:', error);
      return [];
    }
  }

  async saveGarmentTemplate(template) {
    try {
      // For now, just return the template since garment templates table might not exist
      // This can be implemented when the garment templates feature is fully developed

      return template;
    } catch (error) {
      console.error('Error saving garment template:', error);
      throw error;
    }
  }

  async deleteGarmentTemplate(id) {
    try {
      // For now, just return success since garment templates table might not exist
      // This can be implemented when the garment templates feature is fully developed

      return true;
    } catch (error) {
      console.error('Error deleting garment template:', error);
      throw error;
    }
  }
}

export default new SQLiteService();
