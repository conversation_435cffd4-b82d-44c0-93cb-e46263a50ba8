/**
 * Enhanced Offline Service
 * Provides comprehensive offline capabilities for Elite Tailoring Management
 */

// Import NetInfo with fallback for Expo Go
let NetInfo;
try {
  NetInfo = require('@react-native-community/netinfo').default;
} catch (error) {
  // Fallback for Expo Go - assume online
  NetInfo = {
    fetch: () => Promise.resolve({ isConnected: true, type: 'wifi' }),
    addEventListener: () => () => {},
  };
  console.warn('NetInfo not available, using fallback (always online)');
}
import AsyncStorage from '@react-native-async-storage/async-storage';
import SQLiteService from './SQLiteService';

class OfflineService {
  constructor() {
    this.isOnline = true;
    this.offlineQueue = [];
    this.syncInProgress = false;
    this.listeners = [];
    this.lastSyncTime = null;
    
    this.initializeOfflineCapabilities();
  }

  async initializeOfflineCapabilities() {
    // Monitor network status
    this.setupNetworkMonitoring();
    
    // Load offline queue from storage
    await this.loadOfflineQueue();
    
    // Setup periodic sync
    this.setupPeriodicSync();

  }

  setupNetworkMonitoring() {
    // Set up NetInfo listener for real-time network monitoring
    this.networkUnsubscribe = NetInfo.addEventListener(state => {
      const wasOnline = this.isOnline;
      this.isOnline = state.isConnected && state.isInternetReachable;

      if (wasOnline !== this.isOnline) {
        if (this.isOnline) {
          this.onNetworkRestore();
        } else {
          this.onNetworkLost();
        }
        this.notifyListeners();
      }
    });
  }

  // Removed old checkNetworkStatus - using NetInfo listener instead

  async onNetworkRestore() {
    // Sync offline data when network is restored
    await this.syncOfflineData();
  }

  onNetworkLost() {
    // Handle network loss

  }

  // Queue operations for offline sync
  async queueOperation(operation) {
    const queueItem = {
      id: Date.now() + Math.random(),
      timestamp: new Date().toISOString(),
      operation,
      retryCount: 0,
      maxRetries: 3
    };
    
    this.offlineQueue.push(queueItem);
    await this.saveOfflineQueue();

    // Try to sync immediately if online
    if (this.isOnline) {
      await this.syncOfflineData();
    }
  }

  async syncOfflineData() {
    if (this.syncInProgress || !this.isOnline || this.offlineQueue.length === 0) {
      return;
    }

    this.syncInProgress = true;

    const successfulSyncs = [];
    const failedSyncs = [];

    for (const queueItem of this.offlineQueue) {
      try {
        await this.executeOperation(queueItem.operation);
        successfulSyncs.push(queueItem.id);

      } catch (error) {
        queueItem.retryCount++;
        
        if (queueItem.retryCount >= queueItem.maxRetries) {
          failedSyncs.push(queueItem.id);
          console.error(`❌ Failed to sync after ${queueItem.maxRetries} retries:`, queueItem.operation.type);
        } else {
          console.warn(`⚠️ Retry ${queueItem.retryCount}/${queueItem.maxRetries}:`, queueItem.operation.type);
        }
      }
    }

    // Remove successfully synced and permanently failed items
    this.offlineQueue = this.offlineQueue.filter(item => 
      !successfulSyncs.includes(item.id) && !failedSyncs.includes(item.id)
    );

    await this.saveOfflineQueue();
    this.lastSyncTime = new Date().toISOString();

    this.syncInProgress = false;
  }

  async executeOperation(operation) {
    switch (operation.type) {
      case 'CREATE_ORDER':
        return await this.syncCreateOrder(operation.data);
      case 'UPDATE_ORDER':
        return await this.syncUpdateOrder(operation.data);
      case 'CREATE_CUSTOMER':
        return await this.syncCreateCustomer(operation.data);
      case 'UPDATE_CUSTOMER':
        return await this.syncUpdateCustomer(operation.data);
      case 'CREATE_PRODUCT':
        return await this.syncCreateProduct(operation.data);
      case 'UPLOAD_IMAGE':
        return await this.syncUploadImage(operation.data);
      default:
        throw new Error(`Unknown operation type: ${operation.type}`);
    }
  }

  // Specific sync methods for different data types
  async syncCreateOrder(orderData) {
    // Implement order creation sync
    if (SQLiteService.isInitialized) {
      return await SQLiteService.addOrder(orderData);
    }
    return false;
  }

  async syncUpdateOrder(orderData) {
    // Implement order update sync
    if (SQLiteService.isInitialized) {
      return await SQLiteService.updateOrder(orderData);
    }
    return false;
  }

  async syncCreateCustomer(customerData) {
    // Implement customer creation sync
    if (SQLiteService.isInitialized) {
      return await SQLiteService.addCustomer(customerData);
    }
    return false;
  }

  async syncUpdateCustomer(customerData) {
    // Implement customer update sync
    if (SQLiteService.isInitialized) {
      return await SQLiteService.updateCustomer(customerData);
    }
    return false;
  }

  async syncCreateProduct(productData) {
    // Implement product creation sync
    if (SQLiteService.isInitialized) {
      return await SQLiteService.addProduct(productData);
    }
    return false;
  }

  async syncUploadImage(imageData) {
    // Implement image upload sync
    // This would typically upload to a cloud service

    return true;
  }

  // Offline queue management
  async saveOfflineQueue() {
    try {
      await AsyncStorage.setItem('offlineQueue', JSON.stringify(this.offlineQueue));
    } catch (error) {
      console.error('Failed to save offline queue:', error);
    }
  }

  async loadOfflineQueue() {
    try {
      const queueData = await AsyncStorage.getItem('offlineQueue');
      this.offlineQueue = queueData ? JSON.parse(queueData) : [];

    } catch (error) {
      console.error('Failed to load offline queue:', error);
      this.offlineQueue = [];
    }
  }

  // Periodic sync setup
  setupPeriodicSync() {
    // Sync every 5 minutes when online
    setInterval(async () => {
      if (this.isOnline && this.offlineQueue.length > 0) {
        await this.syncOfflineData();
      }
    }, 5 * 60 * 1000);
  }

  // Event listeners for network status
  addNetworkListener(callback) {
    this.listeners.push(callback);
  }

  removeNetworkListener(callback) {
    this.listeners = this.listeners.filter(listener => listener !== callback);
  }

  notifyListeners() {
    this.listeners.forEach(callback => {
      try {
        callback({
          isOnline: this.isOnline,
          queueLength: this.offlineQueue.length,
          lastSyncTime: this.lastSyncTime,
          syncInProgress: this.syncInProgress
        });
      } catch (error) {
        console.error('Error in network listener:', error);
      }
    });
  }

  // Public API methods
  getNetworkStatus() {
    return {
      isOnline: this.isOnline,
      queueLength: this.offlineQueue.length,
      lastSyncTime: this.lastSyncTime,
      syncInProgress: this.syncInProgress
    };
  }

  async forceSync() {
    if (this.isOnline) {
      await this.syncOfflineData();
    } else {
      throw new Error('Cannot sync while offline');
    }
  }

  async clearOfflineQueue() {
    this.offlineQueue = [];
    await this.saveOfflineQueue();

  }

  // Offline data operations
  async getOfflineData(type, id = null) {
    const sqliteService = new SQLiteService();
    
    switch (type) {
      case 'orders':
        return id ? await sqliteService.getOrder(id) : await sqliteService.getAllOrders();
      case 'customers':
        return id ? await sqliteService.getCustomer(id) : await sqliteService.getAllCustomers();
      case 'products':
        return id ? await sqliteService.getProduct(id) : await sqliteService.getAllProducts();
      default:
        throw new Error(`Unknown data type: ${type}`);
    }
  }

  async saveOfflineData(type, data) {
    // Queue the operation for sync when online
    await this.queueOperation({
      type: `CREATE_${type.toUpperCase().slice(0, -1)}`, // Remove 's' and add CREATE_
      data
    });

    // Save locally immediately
    const sqliteService = new SQLiteService();
    
    switch (type) {
      case 'orders':
        return await sqliteService.createOrder(data);
      case 'customers':
        return await sqliteService.createCustomer(data);
      case 'products':
        return await sqliteService.createProduct(data);
      default:
        throw new Error(`Unknown data type: ${type}`);
    }
  }
}

// Export singleton instance
export default new OfflineService();
