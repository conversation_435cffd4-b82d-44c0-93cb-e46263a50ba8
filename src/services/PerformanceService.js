/**
 * Performance Monitoring Service
 * Tracks app performance, memory usage, and optimization metrics
 */

import cacheService from './CacheService';

class PerformanceService {
  constructor() {
    this.metrics = new Map();
    this.timers = new Map();
    this.memorySnapshots = [];
    this.renderMetrics = new Map();
    this.networkMetrics = [];
    this.maxMetricsHistory = 100;
    this.isMonitoring = false;
    
    this.initializeMonitoring();
  }

  /**
   * Initialize performance monitoring
   */
  initializeMonitoring() {
    // Start memory monitoring
    this.startMemoryMonitoring();
    
    // Monitor JavaScript heap if available
    if(global.performance && global.performance.memory) {
      this.startHeapMonitoring();
    }
  }

  /**
   * Start timing a performance metric
   */
  startTimer(name, category = 'general') {
    const startTime = Date.now();
    this.timers.set(name, {
      startTime,
      category
    });
    
    return { end: () => this.endTimer(name)
  };
  }

  /**
   * End timing a performance metric
   */
  endTimer(name) {
    const timer = this.timers.get(name);
    if(!timer) {
      // FIXED: Corrected template literal syntax
      console.warn(`Timer ${name} not found`);
      return 0;
    }

    const duration = Date.now() - timer.startTime;
    this.timers.delete(name);

    // Record metric
    this.recordMetric(name, duration, timer.category);
    
    return duration;
  }

  /**
   * Record a performance metric
   */
  recordMetric(name, value, category = 'general', metadata = {}) {
    const metric = {
      name,
      value,
      category,
      timestamp: Date.now(),
      metadata
    };

    if (!this.metrics.has(category)) {
      this.metrics.set(category, []);
    }

    const categoryMetrics = this.metrics.get(category);
    categoryMetrics.push(metric);

    // Limit history size
    if(categoryMetrics.length > this.maxMetricsHistory) {
      categoryMetrics.shift();
    }

    // Log significant performance issues
    this.checkPerformanceThresholds(metric);
  }

  /**
   * Check performance thresholds and log warnings
   */
  checkPerformanceThresholds(metric) {
    const thresholds = {
      'data_fetch': 2000, // 2 seconds
      'search': 1000, // 1 second
      'render': 100, // 100ms
      'navigation': 500, // 500ms
      'cache_operation': 50 // 50ms
    };

    const threshold = thresholds[metric.name] || thresholds[metric.category];
    if(threshold && metric.value > threshold) {
      // FIXED: Corrected template literal syntax
      console.warn(`Performance warning: ${metric.name} took ${metric.value}ms (threshold: ${threshold}ms)`);
    }
  }

  /**
   * Measure component render time
   */
  measureRender(componentName, renderFunction) {
    const startTime = Date.now();
    
    try {
      const result = renderFunction();
      const duration = Date.now() - startTime;
      
      this.recordRenderMetric(componentName, duration, true);
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.recordRenderMetric(componentName, duration, false, error);
      throw error;
    }
  }

  /**
   * Record render metric
   */
  recordRenderMetric(componentName, duration, success, error = null) {
    if (!this.renderMetrics.has(componentName)) {
      this.renderMetrics.set(componentName, {
        totalRenders: 0,
        totalTime: 0,
        averageTime: 0,
        maxTime: 0,
        minTime: Infinity,
        errors: 0,
        lastRender: null
      });
    }

    const metrics = this.renderMetrics.get(componentName);
    metrics.totalRenders++;
    metrics.totalTime += duration;
    metrics.averageTime = metrics.totalTime / metrics.totalRenders;
    metrics.maxTime = Math.max(metrics.maxTime, duration);
    metrics.minTime = Math.min(metrics.minTime, duration);
    metrics.lastRender = Date.now();

    if(!success) {
      metrics.errors++;
    }

    // Record in general metrics
    this.recordMetric(`render_${componentName}`, duration, 'render', {
      success,
      error: error?.message
    });
  }

  /**
   * Measure network request performance
   */
  measureNetwork(url, requestFunction) {
    const startTime = Date.now();
    
    return requestFunction()
      .then(response => {
        const duration = Date.now() - startTime;
        this.recordNetworkMetric(url, duration, true, response);
        return response;
      })
      .catch(error => {
        const duration = Date.now() - startTime;
        this.recordNetworkMetric(url, duration, false, error);
        throw error;
      });
  }

  /**
   * Record network metric
   */
  recordNetworkMetric(url, duration, success, responseOrError) {
    const metric = {
      url,
      duration,
      success,
      timestamp: Date.now(),
      responseSize: success ? this.getResponseSize(responseOrError) : 0,
      error: success ? null : responseOrError?.message
    };

    this.networkMetrics.push(metric);

    // Limit history
    if(this.networkMetrics.length > this.maxMetricsHistory) {
      this.networkMetrics.shift();
    }

    // Record in general metrics
    this.recordMetric('network_request', duration, 'network', metric);
  }

  /**
   * Get response size estimate
   */
  getResponseSize(response) {
    try {
      if(response && typeof response === 'object') {
        return JSON.stringify(response).length;
      }
      return String(response).length;
    } catch {
      return 0;
    }
  }

  /**
   * Start memory monitoring
   */
  startMemoryMonitoring() {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    
    const monitorMemory = () => {
      if (!this.isMonitoring) return;
      
      const snapshot = this.takeMemorySnapshot();
      this.memorySnapshots.push(snapshot);
      
      // Limit snapshots
      if(this.memorySnapshots.length > 50) {
        this.memorySnapshots.shift();
      }
      
      // Schedule next snapshot
      setTimeout(monitorMemory, 30000); // Every 30 seconds
    };
    
    monitorMemory();
  }

  /**
   * Stop memory monitoring
   */
  stopMemoryMonitoring() {
    this.isMonitoring = false;
  }

  /**
   * Take memory snapshot
   */
  takeMemorySnapshot() {
    const snapshot = {
      timestamp: Date.now(),
      jsHeap: null,
      native: null
    };

    // JavaScript heap (if available)
    if(global.performance && global.performance.memory) {
      snapshot.jsHeap = {
        used: global.performance.memory.usedJSHeapSize,
        total: global.performance.memory.totalJSHeapSize,
        limit: global.performance.memory.jsHeapSizeLimit
      };
    }

    return snapshot;
  }

  /**
   * Start heap monitoring
   */
  startHeapMonitoring() {
    const checkHeap = () => {
      if (!this.isMonitoring) return;
      
      const memory = global.performance.memory;
      const usedRatio = memory.usedJSHeapSize / memory.jsHeapSizeLimit;
      
      // Warn if memory usage is high
      if(usedRatio > 0.8) {
        // FIXED: Corrected template literal syntax
        console.warn(`High memory usage: ${Math.round(usedRatio * 100)}%`);
      }
      
      this.recordMetric('heap_usage', usedRatio * 100, 'memory');
      
      setTimeout(checkHeap, 60000); // Every minute
    };
    
    checkHeap();
  }

  /**
   * Get performance summary
   */
  getPerformanceSummary() {
    const summary = {
      categories: {},
      renders: {},
      network: {},
      memory: {}
    };

    // Summarize metrics by category
    this.metrics.forEach((metrics, category) => {
      const values = metrics.map(m => m.value);
      summary.categories[category] = {
        count: metrics.length,
        average: values.length > 0 ? values.reduce((a, b) => a + b, 0) / values.length : 0,
        min: values.length > 0 ? Math.min(...values) : 0,
        max: values.length > 0 ? Math.max(...values) : 0,
        // FIXED: Removed extra comma from object literal
        recent: metrics.slice(-10).map(m => ({
          name: m.name,
          value: m.value,
          timestamp: m.timestamp
        }))
      };
    });

    // Summarize render metrics
    this.renderMetrics.forEach((metrics, component) => {
      summary.renders[component] = { ...metrics };
    });

    // Summarize network metrics
    const networkSuccess = this.networkMetrics.filter(m => m.success);
    const networkFailures = this.networkMetrics.filter(m => !m.success);
    
    summary.network = {
      totalRequests: this.networkMetrics.length,
      successRate: this.networkMetrics.length > 0 ? networkSuccess.length / this.networkMetrics.length : 0,
      averageResponseTime: networkSuccess.length > 0 ? networkSuccess.reduce((sum, m) => sum + m.duration, 0) / networkSuccess.length : 0,
      failures: networkFailures.length,
      recentRequests: this.networkMetrics.slice(-10)
    };

    // Summarize memory
    if(this.memorySnapshots.length > 0) {
      const latest = this.memorySnapshots[this.memorySnapshots.length - 1];
      summary.memory = {
        current: latest,
        trend: this.getMemoryTrend(),
        snapshots: this.memorySnapshots.length
      };
    }

    return summary;
  }

  /**
   * Get memory trend
   */
  getMemoryTrend() {
    if (this.memorySnapshots.length < 2) return 'stable';
    
    const recent = this.memorySnapshots.slice(-5);
    const first = recent[0];
    const last = recent[recent.length - 1];
    if (!first.jsHeap || !last.jsHeap) return 'unknown';
    
    const change = (last.jsHeap.used - first.jsHeap.used) / first.jsHeap.used;
    
    if (change > 0.1) return 'increasing';
    if (change < -0.1) return 'decreasing';
    return 'stable';
  }

  /**
   * Get slow operations
   */
  getSlowOperations(threshold = 1000) {
    const slowOps = [];
    this.metrics.forEach((metrics) => {
      metrics.forEach(metric => {
        if(metric.value > threshold) {
          slowOps.push(metric);
        }
      });
    });
    
    return slowOps.sort((a, b) => b.value - a.value);
  }

  /**
   * Get performance recommendations
   */
  getRecommendations() {
    const recommendations = [];
    const summary = this.getPerformanceSummary();
    
    // Check render performance
    Object.entries(summary.renders).forEach(([component, metrics]) => {
      if(metrics.averageTime > 100) {
        recommendations.push({
          type: 'render',
          severity: 'warning',
          message: `Component ${component} has slow average render time: ${Math.round(metrics.averageTime)}ms`,
          suggestion: 'Consider memoization or component optimization'
        });
      }
    });
    
    // Check network performance
    if(summary.network.averageResponseTime > 2000) {
      recommendations.push({
        type: 'network',
        severity: 'warning',
        message: `Slow network requests: ${Math.round(summary.network.averageResponseTime)}ms average`,
        suggestion: 'Consider caching, request optimization, or loading states'
      });
    }
    
    // Check memory usage
    if(summary.memory.trend === 'increasing') {
      recommendations.push({
        type: 'memory',
        severity: 'warning',
        message: 'Memory usage is increasing over time',
        suggestion: 'Check for memory leaks, optimize data structures, or implement cleanup'
      });
    }
    
    return recommendations;
  }

  /**
   * Export performance data
   */
  async exportData($3) {
    const data = {
      summary: this.getPerformanceSummary(),
      recommendations: this.getRecommendations(),
      slowOperations: this.getSlowOperations(),
      exportTime: Date.now()
    };
    
    // Cache the export
    await cacheService.set('performance', 'export', data, 60 * 60 * 1000); // 1 hour
    
    return data;
  }

  /**
   * Clear all performance data
   */
  clearData() {
    this.metrics.clear();
    this.timers.clear();
    this.renderMetrics.clear();
    this.networkMetrics = [];
    this.memorySnapshots = [];
  }

  /**
   * Get real-time stats
   */
  getRealTimeStats() {
    return { activeTimers: this.timers.size,
      totalMetrics: Array.from(this.metrics.values()).reduce((sum, arr) => sum + arr.length, 0),
      memorySnapshots: this.memorySnapshots.length,
      isMonitoring: this.isMonitoring,
      lastSnapshot: this.memorySnapshots.length > 0 ? 
        this.memorySnapshots[this.memorySnapshots.length - 1] : null
  };
  }
}

// Create singleton instance
const performanceService = new PerformanceService();

export default performanceService;