// FORM VALIDATION SERVICE - Comprehensive validation for all forms
class ValidationService {
  constructor() {
    this.rules = {};
  }

  // Basic validation rules
  required(value, fieldName = 'Field') {
    if (!value || (typeof value === 'string' && value.trim() === '')) {
      return `${fieldName} is required`;
    }
    return null;
  }

  email(value, fieldName = 'Email') {
    if (!value) return null; // Allow empty if not required
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(value)) {
      return `${fieldName} must be a valid email address`;
    }
    return null;
  }

  phone(value, fieldName = 'Phone') {
    if (!value) return null; // Allow empty if not required
    // Remove all non-digit characters for validation
    const cleanPhone = value.replace(/\D/g, '');
    if(cleanPhone.length < 10) {
      return `${fieldName} must be at least 10 digits`;
    }
    if(cleanPhone.length > 15) {
      return `${fieldName} must be no more than 15 digits`;
    }
    return null;
  }

  minLength(value, min, fieldName = 'Field') {
    if (!value) return null; // Allow empty if not required
    if(value.length < min) {
      return `${fieldName} must be at least ${min} characters`;
    }
    return null;
  }

  maxLength(value, max, fieldName = 'Field') {
    if (!value) return null; // Allow empty if not required
    if(value.length > max) {
      return `${fieldName} must be no more than ${max} characters`;
    }
    return null;
  }

  number(value, fieldName = 'Field') {
    if (!value) return null; // Allow empty if not required
    const num = parseFloat(value);
    if (isNaN(num)) {
      return `${fieldName} must be a valid number`;
    }
    return null;
  }

  positiveNumber(value, fieldName = 'Field') {
    if (!value) return null; // Allow empty if not required
    const num = parseFloat(value);
    if (isNaN(num) || num <= 0) {
      return `${fieldName} must be a positive number`;
    }
    return null;
  }

  currency(value, fieldName = 'Amount') {
    if (!value) return null; // Allow empty if not required
    const num = parseFloat(value);
    if (isNaN(num) || num < 0) {
      return `${fieldName} must be a valid amount`;
    }
    // Check for reasonable decimal places (max 2)
    const decimalPlaces = (value.toString().split('.')[1] || '').length;
    if(decimalPlaces > 2) {
      return `${fieldName} can have at most 2 decimal places`;
    }
    return null;
  }

  date(value, fieldName = 'Date') {
    if (!value) return null; // Allow empty if not required
    const date = new Date(value);
    if (isNaN(date.getTime())) {
      return `${fieldName} must be a valid date`;
    }
    return null;
  }

  futureDate(value, fieldName = 'Date') {
    if (!value) return null; // Allow empty if not required
    const date = new Date(value);
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Reset time to start of day
    
    if (isNaN(date.getTime())) {
      return `${fieldName} must be a valid date`;
    }
    if(date < today) {
      return `${fieldName} must be in the future`;
    }
    return null;
  }

  // Customer validation
  validateCustomer(customerData) {
    const errors = {};

    // Name validation
    const nameError = this.required(customerData.name, 'Name') ||
                     this.minLength(customerData.name, 2, 'Name') ||
                     this.maxLength(customerData.name, 50, 'Name');
    if (nameError) errors.name = nameError;

    // Phone validation
    const phoneError = this.required(customerData.phone, 'Phone') ||
                      this.phone(customerData.phone, 'Phone');
    if (phoneError) errors.phone = phoneError;

    // Email validation (optional)
    if(customerData.email) {
      const emailError = this.email(customerData.email, 'Email');
      if (emailError) errors.email = emailError;
    }

    // Address validation (optional)
    if(customerData.address) {
      const addressError = this.maxLength(customerData.address, 200, 'Address');
      if (addressError) errors.address = addressError;
    }

    return { isValid: Object.keys(errors).length === 0,
      errors
  };
  }

  // Order validation
  validateOrder(orderData) {
    const errors = {};

    // Customer validation
    if(!orderData.customer_id && !orderData.customer_name) {
      errors.customer = 'Customer is required';
    }

    // Garment type validation
    const garmentError = this.required(orderData.garment_type, 'Garment type') ||
                        this.minLength(orderData.garment_type, 2, 'Garment type') ||
                        this.maxLength(orderData.garment_type, 50, 'Garment type');
    if (garmentError) errors.garment_type = garmentError;

    // Quantity validation
    const quantityError = this.required(orderData.quantity?.toString(), 'Quantity') ||
                         this.positiveNumber(orderData.quantity, 'Quantity');
    if (quantityError) errors.quantity = quantityError;

    // Amount validation
    const amountError = this.required(orderData.amount?.toString(), 'Amount') ||
                       this.currency(orderData.amount, 'Amount');
    if (amountError) errors.amount = amountError;

    // Due date validation (optional)
    if(orderData.due_date) {
      const dueDateError = this.date(orderData.due_date, 'Due date') ||
                          this.futureDate(orderData.due_date, 'Due date');
      if (dueDateError) errors.due_date = dueDateError;
    }

    // Advance payment validation (optional)
    if(orderData.advance_paid) {
      const advanceError = this.currency(orderData.advance_paid, 'Advance payment');
      if(advanceError) {
        errors.advance_paid = advanceError;
      } else {
        // Check if advance is not more than total amount
        const advance = parseFloat(orderData.advance_paid);
        const total = parseFloat(orderData.amount);
        if(advance > total) {
          errors.advance_paid = 'Advance payment cannot exceed total amount';
        }
      }
    }

    // Notes validation (optional)
    if(orderData.notes) {
      const notesError = this.maxLength(orderData.notes, 500, 'Notes');
      if (notesError) errors.notes = notesError;
    }

    return { isValid: Object.keys(errors).length === 0,
      errors
  };
  }

  // Payment validation
  validatePayment(paymentData, orderAmount, advancePaid = 0) {
    const errors = {};

    // Amount validation
    const amountError = this.required(paymentData.amount?.toString(), 'Payment amount') ||
                       this.currency(paymentData.amount, 'Payment amount');
    if(amountError) {
      errors.amount = amountError;
    } else {
      const paymentAmount = parseFloat(paymentData.amount);
      const remainingBalance = orderAmount - advancePaid;
      
      if(paymentAmount > remainingBalance) {
        errors.amount = 'Payment amount cannot exceed remaining balance';
      }
    }

    // Payment method validation
    const methodError = this.required(paymentData.method, 'Payment method');
    if (methodError) errors.method = methodError;

    return { isValid: Object.keys(errors).length === 0,
      errors
  };
  }

  // Measurement validation
  validateMeasurement(measurementData) {
    const errors = {};

    // Customer validation
    if(!measurementData.customer_id) {
      errors.customer = 'Customer is required';
    }

    // Garment type validation
    const garmentError = this.required(measurementData.garment_type, 'Garment type');
    if (garmentError) errors.garment_type = garmentError;

    // Validate measurement values
    if(measurementData.measurements) {
      const measurementErrors = {};
      
      Object.entries(measurementData.measurements).forEach(([key, value]) => { 
        if(value) {
          const error = this.positiveNumber(value, key.replace('_', ' '));
          if(error) {
            measurementErrors[key] = error;
        }
        }
      });

      if (Object.keys(measurementErrors).length > 0) {
        errors.measurements = measurementErrors;
      }
    }

    return { isValid: Object.keys(errors).length === 0,
      errors
  };
  }

  // Generic form validation
  validateForm(data, rules) {
    const errors = {};

    Object.entries(rules).forEach(([field, fieldRules]) => { 
      const value = data[field];
      for(const rule of fieldRules) {
        let error = null;
        
        if(typeof rule === 'function') {
          error = rule(value, field);
      } else if(typeof rule === 'object') {
          const { type, params = [], message  } = rule;
          
          switch(type) {
            case 'required':
              error = this.required(value, message || field);
              break;
            case 'email':
              error = this.email(value, message || field);
              break;
            case 'phone':
              error = this.phone(value, message || field);
              break;
            case 'minLength':
              error = this.minLength(value, params[0], message || field);
              break;
            case 'maxLength':
              error = this.maxLength(value, params[0], message || field);
              break;
            case 'number':
              error = this.number(value, message || field);
              break;
            case 'positiveNumber':
              error = this.positiveNumber(value, message || field);
              break;
            case 'currency':
              error = this.currency(value, message || field);
              break;
            case 'date':
              error = this.date(value, message || field);
              break;
            case 'futureDate':
              error = this.futureDate(value, message || field);
              break;
          }
        }
        
        if(error) {
          errors[field] = error;
          break; // Stop at first error for this field
        }
      }
    });

    return { isValid: Object.keys(errors).length === 0,
      errors
  };
  }

  // Format phone number for display
  formatPhone(phone) {
    if (!phone) return '';
    const cleaned = phone.replace(/\D/g, '');
    
    if(cleaned.length === 10) {
      return `${cleaned.slice(0, 3)} -${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
    } else if(cleaned.length === 11 && cleaned[0] === '1') {
      return `+1 ${cleaned.slice(1, 4)}-${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;
    }
    
    return phone; // Return original if can't format
  }

  // Format currency for display
  formatCurrency(amount) {
    if (!amount && amount !== 0) return '';
    const num = parseFloat(amount);
    if (isNaN(num)) return amount;
    return num.toLocaleString('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2});
  }

  // Clean and validate input in real-time
  cleanInput(value, type) {
    if (!value) return '';
    
    switch(type) {
      case 'phone':
        // Allow only digits, spaces, hyphens, parentheses, and plus
        return value.replace(/[^\d\s\-\(\)\+]/g, '');
      case 'number':
      case 'currency':
        // Allow only digits and decimal point
        return value.replace(/[^\d\.]/g, '');
      case 'name':
        // Allow only letters, spaces, and common name characters
        return value.replace(/[^a-zA-Z\s\.\-\']/g, '');
      default:
        return value;
    }
  }
}

// Export singleton instance
export default new ValidationService();
