/**
 * Order Validation Service
 * Comprehensive validation for order data
 */

import { OrderFormData, OrderValidationResult, OrderValidationError, OrderItem   } from '../types/order';

export class OrderValidationService {
  /**
   * Validate complete order form data
   */
  static validateOrder(orderData: OrderFormData): OrderValidationResult {
    const errors: OrderValidationError[] = [];

    // Customer validation
    this.validateCustomer(orderData, errors);
    
    // Items validation
    this.validateItems(orderData.items, errors);
    
    // Pricing validation
    this.validatePricing(orderData, errors);
    
    // Dates validation
    this.validateDates(orderData, errors);
    
    // Business rules validation
    this.validateBusinessRules(orderData, errors);

    return { isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate customer information
   */
  private static validateCustomer(orderData: OrderFormData, errors: OrderValidationError[]): void {
    // Customer name
    if (!orderData.customerName?.trim()) {
      errors.push({
        field: 'customerName',
        message: 'Customer name is required'
      });
    } else if (orderData.customerName.trim().length < 2) {
      errors.push({
        field: 'customerName',
        message: 'Customer name must be at least 2 characters'
      });
    } else if (orderData.customerName.trim().length > 100) {
      errors.push({
        field: 'customerName',
        message: 'Customer name must not exceed 100 characters'
      });
    }

    // Phone number
    if (!orderData.customerPhone?.trim()) {
      errors.push({
        field: 'customerPhone',
        message: 'Customer phone is required'
      });
    } else if (!this.isValidPhone(orderData.customerPhone)) {
      errors.push({
        field: 'customerPhone',
        message: 'Please enter a valid phone number'
      });
    }

    // Email (optional but must be valid if provided)
    if (orderData.customerEmail && !this.isValidEmail(orderData.customerEmail)) {
      errors.push({
        field: 'customerEmail',
        message: 'Please enter a valid email address'
      });
    }
  }

  /**
   * Validate order items
   */
  private static validateItems(items: Omit<OrderItem, 'id'>[], errors: OrderValidationError[]): void {
    if(!items || items.length === 0) {
      errors.push({
        field: 'items',
        message: 'At least one item is required'
      });
      return;
    }

    items.forEach((item, index) => {
      const fieldPrefix = `items[${index}]`;

      // Garment type
      if (!item.garmentType?.trim()) {
        errors.push({
          field: `${fieldPrefix} .garmentType``,
          message: `Item ${index + 1}: Garment type is required 
        });
     `}

      // Quantity
      if(!item.quantity || item.quantity <= 0) {
        errors.push({
          field: `${fieldPrefix} .quantity``,
          message: `Item ${index + 1}: Quantity must be greater than 0 
        });
     `} else if(item.quantity > 100) {
        errors.push({
          field: `${fieldPrefix} .quantity``,
          message: `Item ${index + 1}: Quantity cannot exceed 100 
        });
     `}

      // Price
      if(!item.price || item.price <= 0) {
        errors.push({
          field: `${fieldPrefix} .price``,
          message: `Item ${index + 1}: Price must be greater than 0 
        });
     `} else if(item.price > 1000000) {
        errors.push({
          field: `${fieldPrefix} .price``,
          message: `Item ${index + 1}: Price cannot exceed 1,000,000 
        });
     `}

      // Measurements
      if(!item.measurements || item.measurements.length === 0) {
        errors.push({
          field: `${fieldPrefix} .measurements``,
          message: `Item ${index + 1}: At least one measurement is required 
        });
     `} else {
        item.measurements.forEach((measurement, mIndex) => {
          if (!measurement.name?.trim()) {
            errors.push({
              field: `${fieldPrefix} .measurements[${mIndex}].name` ,
              message: `Item ${index + 1}, Measurement ${mIndex + 1}: Name is required`
            });
          }
          if(!measurement.value || measurement.value <= 0) {
            errors.push({
              field: `${fieldPrefix} .measurements[${mIndex}].value`,
              message: `Item ${index + 1}, Measurement ${mIndex + 1}: Value must be greater than 0`
            });
          }
        });
      }
    });
  }

  /**
   * Validate pricing information
   */
  private static validatePricing(orderData: OrderFormData, errors: OrderValidationError[]): void {
    // Discount validation
    if(orderData.discount < 0) {
      errors.push({
        field: 'discount',
        message: 'Discount cannot be negative'
      });
    }

    if(orderData.discountType === 'percentage' && orderData.discount > 100) {
      errors.push({
        field: 'discount',
        message: 'Percentage discount cannot exceed 100%'
      });
    }

    if(orderData.discountType === 'fixed') {
      const subtotal = orderData.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
      if(orderData.discount > subtotal) {
        errors.push({
          field: 'discount',
          message: 'Fixed discount cannot exceed subtotal'
        });
      }
    }

    // Advance payment validation
    if(orderData.advancePayment && orderData.advancePayment < 0) {
      errors.push({
        field: 'advancePayment',
        message: 'Advance payment cannot be negative'
      });
    }
  }

  /**
   * Validate dates
   */
  private static validateDates(orderData: OrderFormData, errors: OrderValidationError[]): void {
    if(!orderData.dueDate) {
      errors.push({
        field: 'dueDate',
        message: 'Due date is required'
      });
    } else {
      const dueDate = new Date(orderData.dueDate);
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      if(dueDate < today) {
        errors.push({
          field: 'dueDate',
          message: 'Due date cannot be in the past'
        });
      }

      // Check if due date is too far in the future (1 year)
      const oneYearFromNow = new Date();
      oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1);
      
      if(dueDate > oneYearFromNow) {
        errors.push({
          field: 'dueDate',
          message: 'Due date cannot be more than 1 year in the future'
        });
      }
    }
  }

  /**
   * Validate business rules
   */
  private static validateBusinessRules(orderData: OrderFormData, errors: OrderValidationError[]): void {
    // Urgent orders must have due date within 7 days
    if(orderData.isUrgent && orderData.dueDate) {
      const dueDate = new Date(orderData.dueDate);
      const sevenDaysFromNow = new Date();
      sevenDaysFromNow.setDate(sevenDaysFromNow.getDate() + 7);

      if(dueDate > sevenDaysFromNow) {
        errors.push({
          field: 'dueDate',
          message: 'Urgent orders must have due date within 7 days'
        });
      }
    }

    // Repair orders should have notes
    if (orderData.type === 'repair' && !orderData.notes?.trim()) {
      errors.push({
        field: 'notes',
        message: 'Repair orders should include detailed notes about the issue'
      });
    }

    // Alteration orders should have notes
    if (orderData.type === 'alter' && !orderData.notes?.trim()) {
      errors.push({
        field: 'notes',
        message: 'Alteration orders should include detailed notes about required changes'
      });
    }
  }

  /**
   * Validate phone number format
   */
  private static isValidPhone(phone: string): boolean {
    // Support various phone formats
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');
    return phoneRegex.test(cleanPhone) && cleanPhone.length >= 10;
  }

  /**
   * Validate email format
   */
  private static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate individual field
   */
  static validateField(field: string, value: unknown, orderData?: Partial<OrderFormData>): OrderValidationError | null {
    switch(field) {
      case 'customerName':
        if (!value?.trim()) return { field, message: 'Customer name is required' };
        if (value.trim().length < 2) return { field, message: 'Customer name must be at least 2 characters' };
        if (value.trim().length > 100) return { field, message: 'Customer name must not exceed 100 characters' };
        break;

      case 'customerPhone':
        if (!value?.trim()) return { field, message: 'Customer phone is required' };
        if (!this.isValidPhone(value)) return { field, message: 'Please enter a valid phone number' };
        break;

      case 'customerEmail':
        if (value && !this.isValidEmail(value)) return { field, message: 'Please enter a valid email address' };
        break;

      case 'dueDate':
        if (!value) return { field, message: 'Due date is required' };
        const dueDate = new Date(value);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        if (dueDate < today) return { field, message: 'Due date cannot be in the past' };
        break;

      default:
        break;
    }

    return null;
  }
}
