import { StorageService } from './storageService';
import { FINANCIAL_CONFIG } from '../config/constants';
import { Logger, AppError } from '../utils/errorHandler';

/**
 * Financial Service for managing all financial operations
 */
export class FinancialService {
  static STORAGE_KEYS = {
    EXPENSES: 'financial_expenses',
    RECONCILIATIONS: 'cash_reconciliations',
    TAX_SETTINGS: 'tax_settings',
    FINANCIAL_SETTINGS: 'financial_settings',
  };

  /**
   * Expense Management
   */
  static async addExpense($3) {
    try {
      const expenses = await this.getExpenses();
      const newExpense = {
        id: Date.now().toString(),
        ...expense,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      expenses.push(newExpense);
      await StorageService.set(this.STORAGE_KEYS.EXPENSES, expenses);

      Logger.info('Expense added: ', newExpense);
      return newExpense;
    } catch (error) {
      Logger.error('Failed to add expense: ', error);
      throw new AppError('Failed to add expense');
    }
  }

  static async getExpenses($3) {
    try {
      const expenses = await StorageService.get(this.STORAGE_KEYS.EXPENSES) || [];

      let filtered = expenses;

      if(filters.category) {
        filtered = filtered.filter(e => e.category === filters.category);
      }

      if(filters.startDate) {
        filtered = filtered.filter(e => new Date(e.date) >= new Date(filters.startDate));
      }

      if(filters.endDate) {
        filtered = filtered.filter(e => new Date(e.date) <= new Date(filters.endDate));
      }

      if(filters.paymentMethod) {
        filtered = filtered.filter(e => e.paymentMethod === filters.paymentMethod);
      }

      return filtered.sort((a, b) => new Date(b.date) - new Date(a.date));
    } catch (error) {
      Logger.error('Failed to get expenses: ', error);
      return [];
    }
  }

  static async updateExpense($3) {
    try {
      const expenses = await this.getExpenses();
      const index = expenses.findIndex(e => e.id === id);

      if(index === -1) {
        throw new AppError('Expense not found');
      }

      expenses[index] = {
        ...expenses[index],
        ...updates,
        updatedAt: new Date().toISOString(),
      };

      await StorageService.set(this.STORAGE_KEYS.EXPENSES, expenses);
      return expenses[index];
    } catch (error) {
      Logger.error('Failed to update expense: ', error);
      throw new AppError('Failed to update expense');
    }
  }

  static async deleteExpense($3) {
    try {
      const expenses = await this.getExpenses();
      const filtered = expenses.filter(e => e.id !== id);
      await StorageService.set(this.STORAGE_KEYS.EXPENSES, filtered);
      return true;
    } catch (error) {
      Logger.error('Failed to delete expense: ', error);
      throw new AppError('Failed to delete expense');
    }
  }

  /**
   * Cash Reconciliation
   */
  static async performCashReconciliation($3) {
    try {
      const reconciliations = await this.getCashReconciliations();

      const newReconciliation = {
        id: Date.now().toString(),
        date: reconciliationData.date || new Date().toISOString().split('T')[0],
        expectedCash: reconciliationData.expectedCash,
        actualCash: reconciliationData.actualCash,
        difference: reconciliationData.actualCash - reconciliationData.expectedCash,
        notes: reconciliationData.notes || '',
        performedBy: reconciliationData.performedBy || 'System',
        createdAt: new Date().toISOString(),
      };

      // Check if difference is within tolerance
      newReconciliation.status = Math.abs(newReconciliation.difference) <= FINANCIAL_CONFIG.RECONCILIATION_TOLERANCE
        ? 'balanced'
        : 'discrepancy';

      reconciliations.push(newReconciliation);
      await StorageService.set(this.STORAGE_KEYS.RECONCILIATIONS, reconciliations);

      Logger.info('Cash reconciliation performed: ', newReconciliation);
      return newReconciliation;
    } catch (error) {
      Logger.error('Failed to perform cash reconciliation: ', error);
      throw new AppError('Failed to perform cash reconciliation');
    }
  }

  static async getCashReconciliations($3) {
    try {
      const reconciliations = await StorageService.get(this.STORAGE_KEYS.RECONCILIATIONS) || [];

      let filtered = reconciliations;

      if(filters.startDate) {
        filtered = filtered.filter(r => r.date >= filters.startDate);
      }

      if(filters.endDate) {
        filtered = filtered.filter(r => r.date <= filters.endDate);
      }

      if(filters.status) {
        filtered = filtered.filter(r => r.status === filters.status);
      }

      return filtered.sort((a, b) => new Date(b.date) - new Date(a.date));
    } catch (error) {
      Logger.error('Failed to get cash reconciliations: ', error);
      return [];
    }
  }

  /**
   * Financial Analytics
   */
  static async calculateDailyCashExpected($3) {
    try {
      // Get orders for the date
      const tailorData = await StorageService.get('tailorData') || {};
      const orders = tailorData.orders || [];

      const dateStr = new Date(date).toLocaleDateString();
      const dayOrders = orders.filter(order =>
        order.date === dateStr && order.paymentMethod === 'Cash'
      );

      const totalCashSales = dayOrders.reduce((sum, order) => sum + order.total, 0);

      // Get cash expenses for the day
      const expenses = await this.getExpenses({
        startDate: dateStr,
        endDate: dateStr,
        paymentMethod: 'Cash'
      });

      const totalCashExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0);

      // Get previous day's closing cash (if available)
      const previousDay = new Date(date);
      previousDay.setDate(previousDay.getDate() - 1);
      const previousReconciliations = await this.getCashReconciliations({
        startDate: previousDay.toISOString().split('T')[0],
        endDate: previousDay.toISOString().split('T')[0]);

      const previousClosingCash = previousReconciliations.length > 0
        ? previousReconciliations[0].actualCash
        : 0;

      return { openingCash: previousClosingCash,
        cashSales: totalCashSales,
        cashExpenses: totalCashExpenses,
        expectedClosingCash: previousClosingCash + totalCashSales - totalCashExpenses,
        orderCount: dayOrders.length,
       };
    } catch (error) {
      Logger.error('Failed to calculate daily cash expected: ', error);
      throw new AppError('Failed to calculate expected cash');
    }
  }

  /**
   * Profit & Loss Calculations
   */
  static async generateProfitLossStatement($3) {
    try {
      // Get revenue from orders
      const tailorData = await StorageService.get('tailorData') || {};
      const orders = tailorData.orders || [];

      const periodOrders = orders.filter(order => {
        const orderDate = new Date(order.date);
        return orderDate >= new Date(startDate) && orderDate <= new Date(endDate);
      });

      const revenue = {
        totalSales: periodOrders.reduce((sum, order) => sum + order.total, 0),
        orderCount: periodOrders.length,
        averageOrderValue: periodOrders.length > 0
          ? periodOrders.reduce((sum, order) => sum + order.total, 0) / periodOrders.length
          : 0,
      };

      // Get expenses
      const expenses = await this.getExpenses({ startDate, endDate });
      const expensesByCategory = {};
      let totalExpenses = 0;

      expenses.forEach(expense => {
        if (!expensesByCategory[expense.category]) {
          expensesByCategory[expense.category] = 0;
        }
        expensesByCategory[expense.category] += expense.amount;
        totalExpenses += expense.amount;
      });

      // Calculate taxes
      const grossProfit = revenue.totalSales - totalExpenses;
      const salesTax = revenue.totalSales * FINANCIAL_CONFIG.TAX_RATES.SALES_TAX;
      const incomeTax = Math.max(0, grossProfit * FINANCIAL_CONFIG.TAX_RATES.INCOME_TAX);

      const netProfit = grossProfit - incomeTax;

      return {
        period: { startDate, endDate },
        revenue,
        expenses: {
          byCategory: expensesByCategory,
          total: totalExpenses,
        },
        taxes: {
          salesTax,
          incomeTax,
          total: salesTax + incomeTax,
        },
        profit: {
          gross: grossProfit,
          net: netProfit,
          margin: revenue.totalSales > 0 ? (netProfit / revenue.totalSales) * 100 : 0,
        },
        generatedAt: new Date().toISOString(),
      };
    } catch (error) {
      Logger.error('Failed to generate P&L statement: ', error);
      throw new AppError('Failed to generate profit & loss statement');
    }
  }

  /**
   * Payment Method Analytics
   */
  static async getPaymentMethodAnalytics($3) {
    try {
      const tailorData = await StorageService.get('tailorData') || {};
      const orders = tailorData.orders || [];

      const periodOrders = orders.filter(order => {
        const orderDate = new Date(order.date);
        return orderDate >= new Date(startDate) && orderDate <= new Date(endDate);
      });

      const analytics = {};
      let totalRevenue = 0;
      let totalOrders = 0;

      // Initialize analytics for all payment methods
      FINANCIAL_CONFIG.PAYMENT_METHODS.forEach(method => {
        analytics[method] = {
          orderCount: 0,
          totalAmount: 0,
          percentage: 0,
          averageOrderValue: 0,
        };
      });

      // Calculate analytics
      periodOrders.forEach(order => {
        const method = order.paymentMethod || 'Cash';
        if (analytics[method]) {
          analytics[method].orderCount++;
          analytics[method].totalAmount += order.total;
          totalRevenue += order.total;
          totalOrders++;
        }
      });

      // Calculate percentages and averages
      Object.keys(analytics).forEach(method => {
        const data = analytics[method];
        data.percentage = totalRevenue > 0 ? (data.totalAmount / totalRevenue) * 100 : 0;
        data.averageOrderValue = data.orderCount > 0 ? data.totalAmount / data.orderCount : 0;
      });

      return {
        period: { startDate, endDate },
        summary: {
          totalRevenue,
          totalOrders,
          averageOrderValue: totalOrders > 0 ? totalRevenue / totalOrders : 0,
        },
        byPaymentMethod: analytics,
        generatedAt: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Failed to get payment method analytics: ', error);
      throw new Error('Failed to generate payment analytics');
    }
  }

  /**
   * Tax Calculations
   */
  static calculateTaxes(amount, taxType = 'SALES_TAX') {
    const rate = FINANCIAL_CONFIG.TAX_RATES[taxType] || 0;
    return { amount,
      taxRate: rate,
      taxAmount: amount * rate,
      totalWithTax: amount + (amount * rate),
     };
  }

  static async getTaxSummary($3) {
    try {
      const profitLoss = await this.generateProfitLossStatement(startDate, endDate);

      return {
        period: { startDate, endDate },
        salesTax: {
          taxableAmount: profitLoss.revenue.totalSales,
          rate: FINANCIAL_CONFIG.TAX_RATES.SALES_TAX,
          amount: profitLoss.taxes.salesTax,
        },
        incomeTax: {
          taxableAmount: profitLoss.profit.gross,
          rate: FINANCIAL_CONFIG.TAX_RATES.INCOME_TAX,
          amount: profitLoss.taxes.incomeTax,
        },
        totalTaxLiability: profitLoss.taxes.total,
        generatedAt: new Date().toISOString(),
      };
    } catch (error) {
      Logger.error('Failed to get tax summary: ', error);
      throw new AppError('Failed to generate tax summary');
    }
  }
}
