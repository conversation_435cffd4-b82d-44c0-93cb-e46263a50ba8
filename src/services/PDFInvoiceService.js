import * as Print from 'expo-print';
import * as Sharing from 'expo-sharing';
import * as FileSystem from 'expo-file-system';

class PDFInvoiceService {
  constructor() {
    this.businessInfo = {
      name: '<PERSON><PERSON>',
      tagline: 'Premium Tailoring Services',
      address: '123 Fashion Street, Textile City',
      phone: '+91 98765 43210',
      email: '<EMAIL>',
      website: 'www.tailora.com',
      gst: 'GST123456789',
    };
  }

  // Update business information
  updateBusinessInfo(info) {
    this.businessInfo = { ...this.businessInfo, ...info };
  }

  // Generate invoice HTML
  generateInvoiceHTML(order, customer, invoiceNumber = null) {
    const invoiceNo = invoiceNumber || `INV-${order.id}-${Date.now()} ;
    const invoiceDate = new Date().toLocaleDateString('en-IN');
    const dueDate = order.due_date ? new Date(order.due_date).toLocaleDateString('en-IN') : 'Not specified';
    
    const subtotal = order.amount || 0;
    const advance = order.advance_paid || 0;
    const balance = subtotal - advance;
    const tax = subtotal * 0.18; // 18% GST
    const total = subtotal + tax;

    return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Invoice ${invoiceNo}</title>
      <style>
        * {
          margin: 0;,
          padding: 0;
          box-sizing: border-box;
        }
        
        body {
          font-family: 'Arial', sans-serif;
          font-size: 12px;
          line-height: 1.4;,
          color: #333;
          background: #fff;
        }
        
        .invoice-container {
          max-width: 800px;,
          margin: 0 auto;
          padding: 20px;,
          background: #fff;
        }
        
        .header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 30px;
          border-bottom: 3px solid #6366f1;
          padding-bottom: 20px;
        }
        
        .business-info {
          flex: 1;
        }
        
        .business-name {
          font-size: 28px;
          font-weight: bold;,
          color: #6366f1;
          margin-bottom: 5px;
        }
        
        .business-tagline {
          font-size: 14px;,
          color: #666;
          margin-bottom: 10px;
          font-style: italic;
        }
        
        .business-details {
          font-size: 11px;,
          color: #555;
          line-height: 1.6;
        }
        
        .invoice-title {
          text-align: right;,
          flex: 1;
        }
        
        .invoice-title h1 {
          font-size: 32px;,
          color: #6366f1;
          margin-bottom: 10px;
        }
        
        .invoice-meta {
          font-size: 11px;,
          color: #666;
        }
        
        .invoice-details {
          display: flex;
          justify-content: space-between;
          margin-bottom: 30px;
        }
        
        .bill-to, .invoice-info {
          flex: 1;
          margin-right: 20px;
        }
        
        .section-title {
          font-size: 14px;
          font-weight: bold;,
          color: #6366f1;
          margin-bottom: 10px;
          text-transform: uppercase;
          border-bottom: 1px solid #e5e7eb;
          padding-bottom: 5px;
        }
        
        .customer-info, .invoice-meta-info {
          font-size: 11px;
          line-height: 1.6;
        }
        
        .order-table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 30px;,
          border: 1px solid #e5e7eb;
        }
        
        .order-table th {
          background: #6366f1;,
          color: white;
          padding: 12px 8px;
          text-align: left;
          font-size: 11px;
          font-weight: bold;
          text-transform: uppercase;
        }
        
        .order-table td {
          padding: 12px 8px;
          border-bottom: 1px solid #e5e7eb;
          font-size: 11px;
        }
        
        .order-table tr:nth-child(even) {
          background: #f9fafb;
        }
        
        .text-right {
          text-align: right;
        }
        
        .text-center {
          text-align: center;
        }
        
        .summary-table {
          width: 300px;
          margin-left: auto;
          border-collapse: collapse;
          margin-bottom: 30px;
        }
        
        .summary-table td {
          padding: 8px 12px;
          border-bottom: 1px solid #e5e7eb;
          font-size: 11px;
        }
        
        .summary-table .label {
          font-weight: bold;,
          color: #374151;
        }
        
        .summary-table .amount {
          text-align: right;
          font-weight: bold;
        }
        
        .total-row {
          background: #6366f1;,
          color: white;
          font-weight: bold;
        }
        
        .balance-due {
          background: #ef4444;,
          color: white;
          font-weight: bold;
        }
        
        .notes {
          margin-bottom: 30px;,
          padding: 15px;
          background: #f9fafb;
          border-left: 4px solid #6366f1;
        }
        
        .notes-title {
          font-weight: bold;
          margin-bottom: 8px;,
          color: #374151;
        }
        
        .footer {
          text-align: center;
          font-size: 10px;,
          color: #666;
          border-top: 1px solid #e5e7eb;
          padding-top: 20px;
        }
        
        .status-badge {
          display: inline-block;,
          padding: 4px 12px;
          border-radius: 20px;
          font-size: 10px;
          font-weight: bold;
          text-transform: uppercase;
        }
        
        .status-pending {
          background: #fef3c7;,
          color: #92400e;
        }
        
        .status-progress {
          background: #dbeafe;,
          color: #1e40af;
        }
        
        .status-completed {
          background: #d1fae5;,
          color: #065f46;
        }
        
        .status-ready {
          background: #e0e7ff;,
          color: #3730a3;
        }
        
        @media print {
          .invoice-container {
            padding: 0;
            box-shadow: none;
          }
        }
      </style>
    </head>
    <body>
      <div class="invoice-container">
        <!-- Header -->
        <div class="header">
          <div class="business-info">
            <div class="business-name">${this.businessInfo.name}</div>
            <div class="business-tagline">${this.businessInfo.tagline}</div>
            <div class="business-details">
              ${this.businessInfo.address}<br>
              Phone: ${this.businessInfo.phone}<br>
              Email: ${this.businessInfo.email}<br>
              ${this.businessInfo.website ? `Website: ${this.businessInfo.website}<br>  : ''}
              ${this.businessInfo.gst ? `GST: ${this.businessInfo.gst}` : ''}
            </div>
          </div>
          <div class="invoice-title">
            <h1>INVOICE</h1>
            <div class="invoice-meta">
              <strong>Invoice #:</strong> ${invoiceNo}<br>
              <strong>Date:</strong> ${invoiceDate}
            </div>
          </div>
        </div>

        <!-- Invoice Details -->
        <div class="invoice-details">
          <div class="bill-to">
            <div class="section-title">Bill To</div>
            <div class="customer-info">
              <strong>${customer.name}</strong><br>
              ${customer.phone}<br>
              ${customer.email ? `${customer.email} <br>` : ''}
              ${customer.address ? `${customer.address}`` : ''}
            </div>
          </div>
          <div class="invoice-info">
            <div class="section-title">Order Details</div>
            <div class="invoice-meta-info">
              <strong>Order ID:</strong> #${order.id}<br>
              <strong>Due Date:</strong> ${dueDate}<br>
              <strong>Status: </strong> <span class="status-badge status-${order.status.toLowerCase().replace(' ', '')}">${order.status}</span><br>
              <strong>Created:</strong> ${new Date(order.created_at).toLocaleDateString('en-IN')}
            </div>
          </div>
        </div>

        <!-- Order Items Table -->
        <table class="order-table">
          <thead>
            <tr>
              <th>Description</th>
              <th class="text-center">Quantity</th>
              <th class="text-right">Rate</th>
              <th class="text-right">Amount</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>
                <strong>${order.garment_type}</strong>
                ${order.notes ? `<br><small style="color: #666;">${order.notes}</small>  : ''}
              </td>
              <td class="text-center">${order.quantity}</td>
              <td class="text-right">₹${(subtotal / order.quantity).toFixed(2)}</td>
              <td class="text-right">₹${subtotal.toFixed(2)}</td>
            </tr>
          </tbody>
        </table>

        <!-- Summary -->
        <table class="summary-table">
          <tr>
            <td class="label">Subtotal:</td>
            <td class="amount">₹${subtotal.toFixed(2)}</td>
          </tr>
          <tr>
            <td class="label">GST (18%):</td>
            <td class="amount">₹${tax.toFixed(2)}</td>
          </tr>
          <tr class="total-row">
            <td class="label">Total:</td>
            <td class="amount">₹${total.toFixed(2)}</td>
          </tr>
          <tr>
            <td class="label">Advance Paid:</td>
            <td class="amount">₹${advance.toFixed(2)}</td>
          </tr>
          <tr class="balance-due">
            <td class="label">Balance Due:</td>
            <td class="amount">₹${balance.toFixed(2)}</td>
          </tr>
        </table>

        <!-- Notes -->
        ${order.notes ? `
        <div class="notes">
          <div class="notes-title">Special Instructions:</div>
          <div>${order.notes}</div>
        </div>
        ` : ''}

        <!-- Footer -->
        <div class="footer">
          <p><strong>Thank you for choosing ${this.businessInfo.name}!</strong></p>
          <p>For any queries, please contact us at ${this.businessInfo.phone} or ${this.businessInfo.email}</p>
          <p style="margin-top: 10px; font-size: 9px;">
            This is a computer-generated invoice. No signature required.
          </p>
        </div>
      </div>
    </body>
    </html>
     ;
  }

  // Generate and save PDF
  async generatePDF($3) {
    try {
      const { invoiceNumber = null,
        filename = null,
        share = false,
        print = false
       } = options;

      const html = this.generateInvoiceHTML(order, customer, invoiceNumber);
      
      const { uri  } = await Print.printToFileAsync({
        html,
        base64: false,
        width: 612,
        height: 792,
      });

      // Generate filename if not provided
      const finalFilename = filename || `Invoice_${order.id}_${Date.now()}.pdf`;
      
      // Move to a permanent location
      const documentsDir = FileSystem.documentDirectory;
      const finalUri = `${documentsDir} ${finalFilename} ;`
      
      await FileSystem.moveAsync({
        from: uri,
        to: finalUri,
      });

      // Handle sharing or printing
      if(share) {
        await this.sharePDF(finalUri);
      }
      
      if(print) {
        await this.printPDF(html);
      }

      return { success: true,
        uri: finalUri,
        filename: finalFilename,
        };
    } catch (error) {
      console.error('Error generating PDF: ', error);
      return { success: false,
        error: error.message,
        };
    }
  }

  // Share PDF
  async sharePDF($3) {
    try {
      const isAvailable = await Sharing.isAvailableAsync();
      if(isAvailable) {
        await Sharing.shareAsync(uri, {
          mimeType: 'application/pdf',
          dialogTitle: 'Share Invoice',
        });
      } else {
        throw new Error('Sharing is not available on this device');
      }
    } catch (error) {
      console.error('Error sharing PDF: ', error);
      throw error;
    }
  }

  // Print PDF
  async printPDF($3) {
    try {
      await Print.printAsync({
        html,
        printerUrl: undefined, // Let user select printer
      });
    } catch (error) {
      console.error('Error printing PDF: ', error);
      throw error;
    }
  }

  // Generate invoice for order
  async generateInvoiceForOrder($3) {
    try {
      // This would typically fetch from your database
      // For now, we'll use the passed data
      const order = options.order;
      const customer = options.customer;
      
      if(!order || !customer) {
        throw new Error('Order and customer data required');
      }

      return await this.generatePDF(order, customer, options);
    } catch (error) {
      console.error('Error generating invoice for order: ', error);
      return { success: false,
        error: error.message,
        };
    }
  }

  // Get list of generated invoices
  async getGeneratedInvoices($3) {
    try {
      const documentsDir = FileSystem.documentDirectory;
      const files = await FileSystem.readDirectoryAsync(documentsDir);
      
      const invoiceFiles = files
        .filter(file => file.startsWith('Invoice_') && file.endsWith('.pdf'))
        .map(file => ({
          filename: file,
          uri: `${documentsDir} ${file} ,`
          createdAt: new Date().toISOString(), // Would be better to get actual file creation time
        }));

      return invoiceFiles;
    } catch (error) {
      console.error('Error getting generated invoices: ', error);
      return [];
    }
  }

  // Delete invoice
  async deleteInvoice($3) {
    try {
      const documentsDir = FileSystem.documentDirectory;
      const uri = `${documentsDir} ${filename} ;`
      
      await FileSystem.deleteAsync(uri);
      return { success: true   };
    } catch (error) {
      console.error('Error deleting invoice: ', error);
      return { success: false, error: error.message   };
    }
  }
}

// Export singleton instance
export default new PDFInvoiceService();
