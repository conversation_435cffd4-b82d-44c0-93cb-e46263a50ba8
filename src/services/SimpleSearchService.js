// SIMPLE SEARCH SERVICE - Lightweight search and filtering for Step D
class SimpleSearchService {
  // Search customers
  searchCustomers(customers, query) {
    if (!query || query.trim() === '') {
      return customers;
    }

    const searchTerm = query.toLowerCase().trim();
    
    return customers.filter(customer => 
      customer.name.toLowerCase().includes(searchTerm) ||
      customer.phone.includes(searchTerm) ||
      (customer.email && customer.email.toLowerCase().includes(searchTerm))
    );
  }

  // Search orders
  searchOrders(orders, query) {
    if (!query || query.trim() === '') {
      return orders;
    }

    const searchTerm = query.toLowerCase().trim();
    
    return orders.filter(order => 
      order.id.toString().includes(searchTerm) ||
      order.customer_name.toLowerCase().includes(searchTerm) ||
      order.garment_type.toLowerCase().includes(searchTerm) ||
      order.status.toLowerCase().includes(searchTerm)
    );
  }

  // Filter orders by status
  filterOrdersByStatus(orders, status) {
    if(status === 'All' || !status) {
      return orders;
    }
    return orders.filter(order => order.status === status);
  }

  // Sort orders
  sortOrders(orders, field = 'id', direction = 'desc') {
    return [...orders].sort((a, b) => {
      let aValue = a[field];
      let bValue = b[field];
      // Handle different data types
      if(typeof aValue === 'string' && typeof bValue === 'string') {
        const comparison = aValue.toLowerCase().localeCompare(bValue.toLowerCase());
        return direction === 'asc' ? comparison : -comparison;
      }

      if(typeof aValue === 'number' && typeof bValue === 'number') {
        return direction === 'asc' ? aValue - bValue : bValue - aValue;
      }

      // Convert to string for comparison
      const aStr = aValue.toString().toLowerCase();
      const bStr = bValue.toString().toLowerCase();
      const comparison = aStr.localeCompare(bStr);
      return direction === 'asc' ? comparison : -comparison;
    });
  }

  // Sort customers
  sortCustomers(customers, field = 'name', direction = 'asc') {
    return [...customers].sort((a, b) => {
      let aValue = a[field];
      let bValue = b[field];
      // Handle null/undefined values
      if (aValue == null && bValue == null) return 0;
      if (aValue == null) return direction === 'asc' ? 1 : -1;
      if (bValue == null) return direction === 'asc' ? -1 : 1;

      // Handle different data types
      if(typeof aValue === 'string' && typeof bValue === 'string') {
        const comparison = aValue.toLowerCase().localeCompare(bValue.toLowerCase());
        return direction === 'asc' ? comparison : -comparison;
      }

      if(typeof aValue === 'number' && typeof bValue === 'number') {
        return direction === 'asc' ? aValue - bValue : bValue - aValue;
      }

      // Convert to string for comparison
      const aStr = aValue.toString().toLowerCase();
      const bStr = bValue.toString().toLowerCase();
      const comparison = aStr.localeCompare(bStr);
      return direction === 'asc' ? comparison : -comparison;
    });
  }

  // Get unique order statuses
  getUniqueOrderStatuses(orders) {
    const statuses = orders.map(order => order.status).filter(status => status);
    return [...new Set(statuses)].sort();
  }

  // Get unique garment types
  getUniqueGarmentTypes(orders) {
    const types = orders.map(order => order.garment_type).filter(type => type);
    return [...new Set(types)].sort();
  }
}

// Export singleton instance
export default new SimpleSearchService();
