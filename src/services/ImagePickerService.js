/**
 * ImagePickerService - Standardized image picking functionality
 * Based on the working implementation from QuickOrderScreen
 * Provides consistent image picking across the entire app
 */

import { Alert } from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import * as ImageManipulator from 'expo-image-manipulator';

class ImagePickerService {
  /**
   * Request camera permissions
   */
  static async requestCameraPermissions() {
    try {
      // Check current permission status first
      const currentStatus = await ImagePicker.getCameraPermissionsAsync();

      if (currentStatus.status === 'granted') {
        return true;
      }

      if (currentStatus.status === 'denied' && !currentStatus.canAskAgain) {
        Alert.alert(
          'Camera Permission Denied',
          'Camera permission has been permanently denied. Please enable it in your device settings.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Open Settings', onPress: () => ImagePicker.requestCameraPermissionsAsync() }
          ]
        );
        return false;
      }

      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Camera Permission Required',
          'This app needs camera permission to take photos. Please grant permission in the next dialog.',
          [{ text: 'OK' }]
        );
        return false;
      }
      return true;
    } catch (error) {
      console.error('Error requesting camera permissions:', error);
      Alert.alert('Error', 'Failed to request camera permission. Please try again.');
      return false;
    }
  }

  /**
   * Request media library permissions
   */
  static async requestMediaLibraryPermissions() {
    try {
      // Check current permission status first
      const currentStatus = await ImagePicker.getMediaLibraryPermissionsAsync();

      if (currentStatus.status === 'granted') {
        return true;
      }

      if (currentStatus.status === 'denied' && !currentStatus.canAskAgain) {
        Alert.alert(
          'Gallery Permission Denied',
          'Gallery permission has been permanently denied. Please enable it in your device settings.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Open Settings', onPress: () => ImagePicker.requestMediaLibraryPermissionsAsync() }
          ]
        );
        return false;
      }

      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Gallery Permission Required',
          'This app needs gallery permission to select photos. Please grant permission in the next dialog.',
          [{ text: 'OK' }]
        );
        return false;
      }
      return true;
    } catch (error) {
      console.error('Error requesting media library permissions:', error);
      Alert.alert('Error', 'Failed to request gallery permission. Please try again.');
      return false;
    }
  }

  /**
   * Optimize image for better performance and storage
   */
  static async optimizeImage(imageUri, options = {}) {
    try {
      const {
        resize = { width: 800, height: 800 },
        compress = 0.8,
        format = ImageManipulator.SaveFormat.JPEG
      } = options;

      const manipulatedImage = await ImageManipulator.manipulateAsync(
        imageUri,
        [{ resize }],
        {
          compress,
          format,
          base64: false,
        }
      );

      return manipulatedImage.uri;
    } catch (error) {
      console.error('Error optimizing image:', error);
      // Return original URI if optimization fails
      return imageUri;
    }
  }

  /**
   * Take photo with camera
   */
  static async takePhoto(options = {}) {
    try {
      const hasPermission = await this.requestCameraPermissions();
      if (!hasPermission) {
        return null;
      }

      const {
        allowsEditing = true,
        aspect = [1, 1],
        quality = 0.8,
        exif = false,
        optimize = true
      } = options;

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: 'images',
        allowsEditing,
        aspect,
        quality,
        exif,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const imageUri = result.assets[0].uri;
        
        if (optimize) {
          return await this.optimizeImage(imageUri, options);
        }
        
        return imageUri;
      }

      return null;
    } catch (error) {
      console.error('Error taking photo:', error);
      Alert.alert('Error', 'Failed to take photo. Please try again.');
      return null;
    }
  }

  /**
   * Select photo from gallery
   */
  static async selectFromGallery(options = {}) {
    try {
      const hasPermission = await this.requestMediaLibraryPermissions();
      if (!hasPermission) {
        return null;
      }

      const {
        allowsEditing = true,
        aspect = [1, 1],
        quality = 0.8,
        exif = false,
        optimize = true,
        allowsMultipleSelection = false
      } = options;

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: 'images',
        allowsEditing,
        aspect,
        quality,
        exif,
        allowsMultipleSelection,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        if (allowsMultipleSelection) {
          // Return array of optimized images
          const optimizedImages = [];
          for (const asset of result.assets) {
            const optimizedUri = optimize ? 
              await this.optimizeImage(asset.uri, options) : 
              asset.uri;
            optimizedImages.push(optimizedUri);
          }
          return optimizedImages;
        } else {
          // Return single optimized image
          const imageUri = result.assets[0].uri;
          
          if (optimize) {
            return await this.optimizeImage(imageUri, options);
          }
          
          return imageUri;
        }
      }

      return null;
    } catch (error) {
      console.error('Error selecting from gallery:', error);
      Alert.alert('Error', 'Failed to select photo. Please try again.');
      return null;
    }
  }

  /**
   * Show image source selection dialog
   */
  static showImageSourceOptions(onImageSelected, options = {}) {
    const {
      title = 'Select Image Source',
      message = 'Choose how you want to add an image',
      cameraText = 'Camera',
      galleryText = 'Gallery',
      cancelText = 'Cancel'
    } = options;

    Alert.alert(
      title,
      message,
      [
        {
          text: cameraText,
          onPress: async () => {
            const imageUri = await this.takePhoto(options);
            if (imageUri && onImageSelected) {
              onImageSelected(imageUri);
            }
          },
          style: 'default',
        },
        {
          text: galleryText,
          onPress: async () => {
            const imageUri = await this.selectFromGallery(options);
            if (imageUri && onImageSelected) {
              onImageSelected(imageUri);
            }
          },
          style: 'default',
        },
        {
          text: cancelText,
          style: 'cancel',
        },
      ],
      { cancelable: true }
    );
  }

  /**
   * Create a photo object for order/product use
   */
  static createPhotoObject(uri, type = 'reference') {
    return {
      id: Date.now().toString(),
      uri,
      type,
      timestamp: new Date().toISOString()
    };
  }
}

export default ImagePickerService;
