import React, { useState, useCallback, useEffect } from 'react';
import { Al<PERSON>, Linking   } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { StorageService   } from './storageService';
import { Logger   } from '../utils/errorHandler';
import { FEATURE_FLAGS   } from '../config/constants';

/**
 * Notification types
 */
export const NotificationTypes = {
  LOW_STOCK: 'LOW_STOCK',
  ORDER_UPDATE: 'ORDER_UPDATE',
  DAILY_SUMMARY: 'DAILY_SUMMARY',
  SYSTEM: 'SYSTEM',
  REMINDER: 'REMINDER',
  ERROR: 'ERROR',
  SUCCESS: 'SUCCESS',
  // Tailor-specific types
  MEASUREMENT_NEEDED: 'MEASUREMENT_NEEDED',
  FABRIC_LOW_STOCK: 'FABRIC_LOW_STOCK',
  DELIVERY_DUE: 'DELIVERY_DUE',
  PAYMENT_DUE: 'PAYMENT_DUE',
  ORDER_STATUS_CHANGE: 'ORDER_STATUS_CHANGE',
};

/**
 * Notification priorities
 */
export const NotificationPriority = {
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH',
  URGENT: 'URGENT',
};

/**
 * Notification service for managing app notifications
 */
export class NotificationService {
  static notifications = [];
  static listeners = [];
  static isEnabled = FEATURE_FLAGS.ENABLE_PUSH_NOTIFICATIONS;
  static smsSettings = {
    enabled: true,
    whatsappEnabled: true,
    templates: {
      orderReady: 'Dear {customerName}, your order #{orderId} is ready for pickup. Please visit our shop at your convenience. Thank you!',
      orderDue: 'Dear {customerName}, your order #{orderId} is due on {dueDate}. Please visit for fitting/pickup. Contact us for any queries.',
      paymentDue: 'Dear {customerName}, payment of ৳{amount} for order #{orderId} is due. Please make payment at your earliest convenience.',
      appointmentReminder: 'Dear {customerName}, you have an appointment scheduled for {appointmentDate} at {appointmentTime}. See you soon!',
      birthdayWish: 'Happy Birthday {customerName}! 🎉 Wishing you a wonderful year ahead. Visit us for special birthday discounts!',
      measurementReminder: 'Dear {customerName}, please visit us for measurement taking for your order #{orderId}. Our shop is open from 9 AM to 6 PM.',
    },
  };

  /**
   * Initialize notification service
   */
  static async initialize($3) {
    try {
      await this.loadNotifications();
      this.notificationsEnabled = await this.getNotificationsEnabled();
      if(this.notificationsEnabled) {
        this.schedulePeriodicChecks();
      }
      Logger.info('Notification service initialized');
    } catch (error) {
      Logger.error('Failed to initialize notification service', error);
    }
  }

  /**
   * Enable/disable notifications
   */
  static async setNotificationsEnabled($3) {
    try {
      this.notificationsEnabled = enabled;
      await AsyncStorage.setItem('notificationsEnabled', JSON.stringify(enabled));

      if(enabled) {
        this.schedulePeriodicChecks();
      } else {
        this.stopPeriodicChecks();
      }

      Logger.info(`Notifications ${enabled ? 'enabled' : 'disabled'}`);
    } catch (error) {
      Logger.error('Failed to set notifications enabled: ', error);
    }
  }

  /**
   * Get notifications enabled status
   */
  static async getNotificationsEnabled($3) {
    try {
      const enabled = await AsyncStorage.getItem('notificationsEnabled');
      return enabled ? JSON.parse(enabled) : true; // Default to enabled
    } catch (error) {
      Logger.error('Failed to get notifications enabled status: ', error);
      return true;
    }
  }

  /**
   * Stop periodic checks
   */
  static stopPeriodicChecks() {
    if(this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
  }

  /**
   * Create a new notification
   */
  static async createNotification({
    id = Date.now().toString(),
    type,
    title,
    message,
    priority = NotificationPriority.MEDIUM,
    data = {},
    timestamp = new Date().toISOString(),
    read = false,
    persistent = false,
  }) {
    const notification = {
      id,
      type,
      title,
      message,
      priority,
      data,
      timestamp,
      read,
      persistent,
    };

    this.notifications.unshift(notification);
    await this.saveNotifications();
    this.notifyListeners('notification_created', notification);

    // Show immediate alert for high priority notifications
    if(priority === NotificationPriority.HIGH || priority === NotificationPriority.URGENT) {
      this.showAlert(notification);
    }

    Logger.info(`Notification created: ${title}`);
    return notification;
  }

  /**
   * Get all notifications
   */
  static getNotifications(filter = {}) {
    let filtered = [...this.notifications];

    if(filter.type) {
      filtered = filtered.filter(n => n.type === filter.type);
    }

    if(filter.unreadOnly) {
      filtered = filtered.filter(n => !n.read);
    }

    if(filter.priority) {
      filtered = filtered.filter(n => n.priority === filter.priority);
    }

    return filtered.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
  }

  /**
   * Mark notification as read
   */
  static async markAsRead($3) {
    const notification = this.notifications.find(n => n.id === notificationId);
    if(notification) {
      notification.read = true;
      await this.saveNotifications();
      this.notifyListeners('notification_read', notification);
    }
  }

  /**
   * Mark all notifications as read
   */
  static async markAllAsRead($3) {
    this.notifications.forEach(n => n.read = true);
    await this.saveNotifications();
    this.notifyListeners('all_notifications_read');
  }

  /**
   * Delete notification
   */
  static async deleteNotification($3) {
    const index = this.notifications.findIndex(n => n.id === notificationId);
    if(index !== -1) {
      const notification = this.notifications[index];
      this.notifications.splice(index, 1);
      await this.saveNotifications();
      this.notifyListeners('notification_deleted', notification);
    }
  }

  /**
   * Clear all notifications
   */
  static async clearAll($3) {
    this.notifications = [];
    await this.saveNotifications();
    this.notifyListeners('all_notifications_cleared');
  }

  /**
   * Get unread count
   */
  static getUnreadCount() {
    return this.notifications.filter(n => !n.read).length;
  }

  /**
   * Add event listener
   */
  static addListener(callback) {
    this.listeners.push(callback);
    return () => {
      const index = this.listeners.indexOf(callback);
      if(index !== -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  /**
   * Notify all listeners
   */
  static notifyListeners(event, data = null) {
    this.listeners.forEach(callback => {
      try {
        callback(event, data);
      } catch (error) {
        Logger.error('Error in notification listener', error);
      }
    });
  }

  /**
   * Show alert for notification
   */
  static showAlert(notification) {
    if (!this.isEnabled) return;

    Alert.alert(
      notification.title,
      notification.message,
      [
        {
          text: 'Dismiss',
          style: 'cancel',
          onPress: () => this.markAsRead(notification.id),
        },
        ...(notification.data.action ? [{
          text: notification.data.actionText || 'View',
          onPress: () => {
            this.markAsRead(notification.id);
            notification.data.action();
          },
        }] : []),
      ]
    );
  }

  /**
   * Save notifications to storage
   */
  static async saveNotifications($3) {
    try {
      // Keep only last 100 notifications and all persistent ones
      const toKeep = this.notifications
        .filter(n => n.persistent)
        .concat(
          this.notifications
            .filter(n => !n.persistent)
            .slice(0, 100)
        );

      this.notifications = toKeep;
      await StorageService.set('notifications', this.notifications);
    } catch (error) {
      Logger.error('Failed to save notifications', error);
    }
  }

  /**
   * Load notifications from storage
   */
  static async loadNotifications($3) {
    try {
      const saved = await StorageService.get('notifications') || [];
      this.notifications = saved;
    } catch (error) {
      Logger.error('Failed to load notifications', error);
      this.notifications = [];
    }
  }

  /**
   * Schedule periodic checks for business logic (disabled for clean experience)
   */
  static schedulePeriodicChecks() {
    // Disabled automatic notification generation for clean app experience
    // Only manual notifications will be created when actual events occur
  }

  /**
   * Clear periodic checks
   */
  static clearPeriodicChecks() {
    if(this.periodicCheckInterval) {
      clearInterval(this.periodicCheckInterval);
      this.periodicCheckInterval = null;
    }
  }

  /**
   * Check for low stock items
   */
  static async checkLowStock($3) {
    try {
      const tailorData = await StorageService.get('tailorData');
      if (!tailorData?.products) return;

      const lowStockItems = tailorData.products.filter(product => product.stock <= 5);

      if(lowStockItems.length > 0) {
        const lastCheck = await StorageService.get('lastLowStockCheck');
        const now = new Date().toDateString();

        // Only notify once per day
        if(lastCheck !== now) {
          await this.createNotification({
            type: NotificationTypes.LOW_STOCK,
            title: 'Low Stock Alert',
            message: `${lowStockItems.length}`} item(s) are running low on stock`,
            priority: NotificationPriority.HIGH,
            data: {
              items: lowStockItems,
              action: () => {
                // Navigate to products screen
              },
              actionText: 'View Products',
            },
          });

          await StorageService.set('lastLowStockCheck', now);
        }
      }
    } catch (error) {
      Logger.error('Failed to check low stock', error);
    }
  }

  /**
   * Check for daily summary
   */
  static async checkDailySummary($3) {
    try {
      const lastSummary = await StorageService.get('lastDailySummary');
      const today = new Date().toDateString();

      // Send daily summary at 6 PM
      const now = new Date();
      const isAfter6PM = now.getHours() >= 18;

      if(lastSummary !== today && isAfter6PM) {
        const tailorData = await StorageService.get('tailorData');
        if (!tailorData?.orders) return;

        const todayOrders = tailorData.orders.filter(order =>
          order.date === new Date().toLocaleDateString()
        );

        const totalSales = todayOrders.reduce((sum, order) => sum + order.total, 0);

        await this.createNotification({
          type: NotificationTypes.DAILY_SUMMARY,
          title: 'Daily Summary',
          message: `Today: ${todayOrders.length} orders, ${totalSales.toFixed(2)} in sales`,
          priority: NotificationPriority.MEDIUM,
          data: {
            orders: todayOrders.length,
            sales: totalSales,
            action: () => {
              // Navigate to reports
            },
            actionText: 'View Report',
          },
        });

        await StorageService.set('lastDailySummary', today);
      }
    } catch (error) {
      Logger.error('Failed to check daily summary', error);
    }
  }

  // ===== SMS/WHATSAPP NOTIFICATION METHODS =====

  /**
   * Send SMS notification (native implementation)
   */
  static async sendSMS($3) {
    try {
      if(!this.smsSettings.enabled) {
        throw new Error('SMS notifications are disabled');
      }

      // Clean phone number
      const cleanPhone = phoneNumber.replace(/[^\d+]/g, '');

      // For native SMS, we use the device's SMS app (Android format)
      const smsUrl = `sms:${cleanPhone}?body=${encodeURIComponent(message)}`;

      const canOpen = await Linking.canOpenURL(smsUrl);
      if(canOpen) {
        await Linking.openURL(smsUrl);
        Logger.info(`SMS sent to ${cleanPhone}`);
        return { success: true, method: 'sms' };
      } else {
        throw new Error('SMS app not available');
      }
    } catch (error) {
      Logger.error('SMS sending failed: ', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Send WhatsApp notification (native implementation)
   */
  static async sendWhatsApp($3) {
    try {
      if(!this.smsSettings.whatsappEnabled) {
        throw new Error('WhatsApp notifications are disabled');
      }

      // Clean phone number and ensure it has country code
      let cleanPhone = phoneNumber.replace(/[^\d+]/g, '');
      if (!cleanPhone.startsWith('+')) {
        cleanPhone = '+880' + cleanPhone; // Default to Bangladesh country code
      }

      // WhatsApp URL scheme
      const whatsappUrl = `whatsapp://send?phone=${cleanPhone}&text=${encodeURIComponent(message)}`;

      const canOpen = await Linking.canOpenURL(whatsappUrl);
      if(canOpen) {
        await Linking.openURL(whatsappUrl);
        Logger.info(`WhatsApp message sent to ${cleanPhone}`);
        return { success: true, method: 'whatsapp' };
      } else {
        // Fallback to web WhatsApp
        const webWhatsappUrl = `https://wa.me/${cleanPhone}?text=${encodeURIComponent(message)}`;
        await Linking.openURL(webWhatsappUrl);
        Logger.info(`WhatsApp web message sent to ${cleanPhone}`);
        return { success: true, method: 'whatsapp_web' };
      }
    } catch (error) {
      Logger.error('WhatsApp sending failed: ', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Generate message from template
   */
  static generateMessage(templateKey, variables) {
    const template = this.smsSettings.templates[templateKey];
    if(!template) {
      throw new Error(`Template ${templateKey} not found`);
    }

    let message = template;
    Object.keys(variables).forEach(key => {
      const placeholder = `{${key}}`;
      message = message.replace(new RegExp(placeholder, 'g'), variables[key]);
    });

    return message;
  }

  /**
   * Send notification with fallback methods
   */
  static async sendExternalNotification($3) {
    let result = { success: false };

    // Try preferred method first
    if(preferredMethod === 'whatsapp') {
      result = await this.sendWhatsApp(phoneNumber, message);
      if(!result.success) {
        // Fallback to SMS
        result = await this.sendSMS(phoneNumber, message);
      }
    } else {
      result = await this.sendSMS(phoneNumber, message);
      if(!result.success) {
        // Fallback to WhatsApp
        result = await this.sendWhatsApp(phoneNumber, message);
      }
    }

    return result;
  }

  /**
   * Send order ready notification
   */
  static async sendOrderReadyNotification($3) {
    if (!customer.phone) return { success: false, error: 'No phone number'   };

    const message = this.generateMessage('orderReady', {
      customerName: customer.name,
      orderId: order.id,
    });

    const result = await this.sendExternalNotification(
      customer.phone,
      message,
      customer.preferences?.communicationMethod || 'whatsapp'
    );

    // Also create in-app notification
    if(result.success) {
      await this.createNotification({
        type: NotificationTypes.ORDER_UPDATE,
        title: 'Order Ready Notification Sent',
        message: `Notified ${customer.name} that order #${order.id} is ready`,
        priority: NotificationPriority.MEDIUM,
      });
    }

    return result;
  }

  /**
   * Send payment due notification
   */
  static async sendPaymentDueNotification($3) {
    if (!customer.phone) return { success: false, error: 'No phone number'   };

    const amount = (order.total - (order.paidAmount || 0)).toFixed(2);
    const message = this.generateMessage('paymentDue', {
      customerName: customer.name,
      orderId: order.id,
      amount: amount,
    });

    const result = await this.sendExternalNotification(
      customer.phone,
      message,
      customer.preferences?.communicationMethod || 'whatsapp'
    );

    // Also create in-app notification
    if(result.success) {
      await this.createNotification({
        type: NotificationTypes.PAYMENT_DUE,
        title: 'Payment Reminder Sent',
        message: `Sent payment reminder to ${customer.name} for ৳${amount}`,
        priority: NotificationPriority.MEDIUM,
      });
    }

    return result;
  }

  /**
   * Send birthday wish
   */
  static async sendBirthdayWish($3) {
    if (!customer.phone) return { success: false, error: 'No phone number'   };

    const message = this.generateMessage('birthdayWish', {
      customerName: customer.name,
    });

    const result = await this.sendExternalNotification(
      customer.phone,
      message,
      customer.preferences?.communicationMethod || 'whatsapp'
    );

    return result;
  }

  /**
   * Update SMS settings
   */
  static async updateSMSSettings($3) {
    this.smsSettings = { ...this.smsSettings, ...newSettings };
    await StorageService.set('smsSettings', this.smsSettings);
  }

  /**
   * Get SMS settings
   */
  static getSMSSettings() {
    return this.smsSettings;
  }

  // ===== TAILOR-SPECIFIC NOTIFICATION METHODS =====

  /**
   * Create appointment reminder notification
   */
  static async createAppointmentReminder($3) {
    const appointmentDate = new Date(appointment.date + 'T' + appointment.time);
    const now = new Date();
    const timeDiff = appointmentDate.getTime() - now.getTime();
    const hoursDiff = timeDiff / (1000 * 60 * 60);

    // If appointment is within 24 hours, create reminder
    if(hoursDiff > 0 && hoursDiff <= 24) {
      await this.createNotification({
        type: NotificationTypes.APPOINTMENT_REMINDER,
        title: 'Upcoming Appointment',
        message: `Appointment with ${customer.name} tomorrow at ${appointment.time}`,
        priority: NotificationPriority.HIGH,
        data: {
          appointmentId: appointment.id,
          customerId: customer.id,
          action: () => {
            // Navigate to appointments
          },
          actionText: 'View Appointment',
        },
      });
    }
  }

  /**
   * Create order status change notification
   */
  static async createOrderStatusNotification(order, oldStatus, newStatus) {
    await this.createNotification({
      type: NotificationTypes.ORDER_STATUS_CHANGE,
      title: 'Order Status Updated',
      message: `Order #${order.id} changed from ${oldStatus} to ${newStatus}`,
      priority: NotificationPriority.MEDIUM,
      data: {
        orderId: order.id,
        oldStatus,
        newStatus,
        action: () => {
          // Navigate to order details
        },
        actionText: 'View Order',
      },
    });
  }

  /**
   * Create low fabric stock notification
   */
  static async createFabricLowStockNotification(fabric) {
    await this.createNotification({
      type: NotificationTypes.FABRIC_LOW_STOCK,
      title: 'Low Fabric Stock',
      message: `${fabric.name}`} is running low (${fabric.stock} meters remaining)`,
      priority: NotificationPriority.MEDIUM,
      data: {
        fabricId: fabric.id,
        action: () => {
          // Navigate to fabrics
        },
        actionText: 'View Fabrics',
      },
    });
  }

  /**
   * Create measurement needed notification
   */
  static async createMeasurementNeededNotification(customer, orderType) {
    await this.createNotification({
      type: NotificationTypes.MEASUREMENT_NEEDED,
      title: 'Measurements Required',
      message: `${customer.name}`} needs measurements for ${orderType}`,
      priority: NotificationPriority.HIGH,
      data: {
        customerId: customer.id,
        orderType,
        action: () => {
          // Navigate to measurements
        },
        actionText: 'Add Measurements',
      },
    });
  }

  /**
   * Create payment due notification
   */
  static async createPaymentDueNotification(order, customer) {
    await this.createNotification({
      type: NotificationTypes.PAYMENT_DUE,
      title: 'Payment Due',
      message: `Payment pending for Order #${order.id} from ${customer.name}`,
      priority: NotificationPriority.HIGH,
      // FIXED: Removed extra comma from object literal
      data: {
        orderId: order.id,
        customerId: customer.id,
        action: () => {
          // Navigate to order details
        },
        actionText: 'View Order',
      },
    });
  }

  /**
   * Create delivery due notification
   */
  static async createDeliveryDueNotification(order, customer) {
    await this.createNotification({
      type: NotificationTypes.DELIVERY_DUE,
      title: 'Delivery Due',
      message: `Order #${order.id} ready for delivery to ${customer.name}`,
      priority: NotificationPriority.HIGH,
      // FIXED: Removed extra comma from object literal
      data: {
        orderId: order.id,
        customerId: customer.id,
        action: () => {
          // Navigate to order details
        },
        actionText: 'View Order',
      },
    });
  }

  /**
   * Check for tailor-specific notifications (disabled for clean experience)
   */
  static async checkTailorNotifications($3) {
    try {
      // Automatic notification generation disabled for clean app experience
      // Only manual notifications will be created when actual events occur
      return;

      // Check for new orders
      if(state.orders && state.customers) {
        const today = new Date().toISOString().split('T')[0];
        const todayOrders = state.orders.filter(order =>
          order.createdAt && order.createdAt.startsWith(today)
        );

        if (todayOrders.length > 0 && !this.hasRecentNotification('ORDER_SUMMARY', today)) {
          await this.createNotification({
            type: 'ORDER_UPDATE',
            title: 'New Orders Today',
            message: `${todayOrders.length}`} new order(s) received today`,
            priority: NotificationPriority.MEDIUM,
            data: {
              orderId: todayOrders[0]?.id,
              count: todayOrders.length,
              action: () => {
                // Navigate to orders
              },
              actionText: 'View Orders',
            },
          });
        }
      }

      // Check for orders due today
      if(state.orders && state.customers) {
        const today = new Date().toISOString().split('T')[0];
        const dueToday = state.orders.filter(order =>
          order.dueDate === today && order.status !== 'completed'
        );

        if (dueToday.length > 0 && !this.hasRecentNotification('ORDER_DUE_TODAY', today)) {
          await this.createNotification({
            type: 'ORDER_UPDATE',
            title: 'Orders Due Today',
            message: `${dueToday.length}`} order(s) are due for delivery today`,
            priority: NotificationPriority.HIGH,
            data: {
              orderId: dueToday[0]?.id,
              count: dueToday.length,
              action: () => {
                // Navigate to orders
              },
              actionText: 'View Orders',
            },
          });
        }
      }

      // Check for overdue orders
      if(state.orders && state.customers) {
        const now = new Date();
        const overdueOrders = state.orders.filter(order => {
          if (!order.dueDate || order.status === 'completed') return false;
          const dueDate = new Date(order.dueDate);
          return dueDate < now;
        });

        if (overdueOrders.length > 0 && !this.hasRecentNotification('OVERDUE_ORDERS', 'daily')) {
          await this.createNotification({
            type: 'ORDER_UPDATE',
            title: 'Overdue Orders',
            message: `${overdueOrders.length}`} order(s) are overdue`,
            priority: NotificationPriority.URGENT,
            data: {
              orderId: overdueOrders[0]?.id,
              count: overdueOrders.length,
              action: () => {
                // Navigate to orders
              },
              actionText: 'View Orders',
            },
          });
        }
      }

      // Check for low stock items (general inventory)
      if(state.inventory) {
        const lowStockItems = state.inventory.filter(item =>
          item.quantity < 5
        );

        if (lowStockItems.length > 0 && !this.hasRecentNotification('LOW_STOCK_GENERAL', 'daily')) {
          await this.createNotification({
            type: 'LOW_STOCK',
            title: 'Low Stock Alert',
            message: `${lowStockItems.length}`} item(s) are running low on stock`,
            priority: NotificationPriority.HIGH,
            // FIXED: Removed extra comma from object literal
            data: {
              items: lowStockItems,
              action: () => {
                // Navigate to inventory
              },
              actionText: 'View Inventory',
            },
          });
        }
      }
    } catch (error) {
      Logger.error('Failed to check tailor notifications', error);
    }
  }

  /**
   * Check if there's a recent notification of the same type for the same item
   */
  static hasRecentNotification(type, itemId) {
    const oneDayAgo = new Date();
    oneDayAgo.setDate(oneDayAgo.getDate() - 1);

    return this.notifications.some(n =>
      n.type === type &&
      n.data &&
      (n.data.fabricId === itemId ||
       n.data.appointmentId === itemId ||
       n.data.orderId === itemId ||
       n.data.customerId === itemId) &&
      new Date(n.timestamp) > oneDayAgo
    );
  }
}

/**
 * React hook for notifications
 */
export const useNotifications = () => {
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);

  useEffect(() => {
    const updateNotifications = () => {
      setNotifications(NotificationService.getNotifications());
      setUnreadCount(NotificationService.getUnreadCount());
    };

    updateNotifications();

    const unsubscribe = NotificationService.addListener(() => {
      updateNotifications();
    });

    return unsubscribe;
  }, []);

  const markAsRead = useCallback((id) => {
    NotificationService.markAsRead(id);
  }, []);

  const markAllAsRead = useCallback(() => {
    NotificationService.markAllAsRead();
  }, []);

  const deleteNotification = useCallback((id) => {
    NotificationService.deleteNotification(id);
  }, []);

  const clearAll = useCallback(() => {
    NotificationService.clearAll();
  }, []);

  return { notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    clearAll,
    };
};