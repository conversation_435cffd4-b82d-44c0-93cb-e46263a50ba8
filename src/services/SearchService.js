/**
 * Advanced Search Service
 * Implements fuzzy search, indexing, caching, and real-time search
 */

import cacheService from './CacheService';

class SearchService {
  constructor() {
    this.searchIndex = new Map();
    this.searchHistory = [];
    this.maxHistorySize = 50;
    this.searchCache = new Map();
    this.debounceTimers = new Map();
    this.searchProviders = new Map();
    
    this.initializeSearchProviders();
  }

  /**
   * Initialize search providers for different data types
   */
  initializeSearchProviders() {
    // Customer search provider
    this.registerSearchProvider('customers', {
      searchFields: ['name', 'email', 'phone', 'address'],
      weightedFields: {
        name: 3,
        email: 2,
        phone: 2,
        address: 1
      },
      dataFetcher: async () => {
        // This will be injected by the data context
        return [];
      }
    });

    // Order search provider
    this.registerSearchProvider('orders', {
      searchFields: ['orderNumber', 'customerName', 'status', 'garmentType'],
      weightedFields: {
        orderNumber: 3,
        customerName: 2,
        status: 1,
        garmentType: 1
      },
      dataFetcher: async () => {
        return [];
      }
    });

    // Product search provider
    this.registerSearchProvider('products', {
      searchFields: ['name', 'category', 'description', 'sku'],
      weightedFields: {
        name: 3,
        sku: 2,
        category: 1,
        description: 1
      },
      dataFetcher: async () => {
        return [];
      }
    });

    // Garment template search provider
    this.registerSearchProvider('garments', {
      searchFields: ['name', 'category', 'measurements'],
      weightedFields: {
        name: 3,
        category: 2,
        measurements: 1
      },
      dataFetcher: async () => {
        return [];
      }
    });

    // Fabric search provider
    this.registerSearchProvider('fabrics', {
      searchFields: ['name', 'type', 'color', 'supplier'],
      weightedFields: {
        name: 3,
        type: 2,
        color: 1,
        supplier: 1
      },
      dataFetcher: async () => {
        return [];
      }
    });
  }

  /**
   * Register a search provider
   */
  registerSearchProvider(type, config) {
    this.searchProviders.set(type, {
      ...config,
      lastIndexed: null,
      index: new Map()
    });
  }

  /**
   * Update search provider data fetcher
   */
  updateSearchProvider(type, dataFetcher) {
    const provider = this.searchProviders.get(type);
    if(provider) {
      provider.dataFetcher = dataFetcher;
      // Invalidate index to force rebuild
      provider.lastIndexed = null;
      provider.index.clear();
    }
  }

  /**
   * Build search index for a provider
   */
  async buildIndex($3) {
    const provider = this.searchProviders.get(type);
    if (!provider) return;

    try {
      const data = await provider.dataFetcher();
      const index = new Map();

      data.forEach((item, itemIndex) => {
        const searchableText = this.extractSearchableText(item, provider.searchFields);
        const tokens = this.tokenize(searchableText);
        
        tokens.forEach(token => {
          if (!index.has(token)) {
            index.set(token, []);
          }
          
          index.get(token).push({
            item,
            itemIndex,
            relevance: this.calculateRelevance(token, item, provider.weightedFields)
          });
        });
      });

      provider.index = index;
      provider.lastIndexed = Date.now();
      
      // Cache the index
      await cacheService.set('search_index', type, {
        index: Array.from(index.entries()),
        lastIndexed: provider.lastIndexed
      }, 10 * 60 * 1000); // 10 minutes

    } catch (error) {
      console.error(`Failed to build search index for ${type}:`, error);
    }
  }

  /**
   * Extract searchable text from item
   */
  extractSearchableText(item, searchFields) {
    return searchFields
      .map(field => {
        const value = this.getNestedValue(item, field);
        return value ? String(value).toLowerCase() : '';
      })
      .filter(Boolean)
      .join(' ');
  }

  /**
   * Get nested value from object
   */
  getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  /**
   * Tokenize text for search
   */
  tokenize(text) {
    return text
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(token => token.length > 1)
      .concat(this.generateNGrams(text, 2))
      .concat(this.generateNGrams(text, 3));
  }

  /**
   * Generate n-grams for partial matching
   */
  generateNGrams(text, n) {
    const words = text.toLowerCase().replace(/[^\w\s]/g, ' ').split(/\s+/);
    const ngrams = [];
    words.forEach(word => {
      if(word.length >= n) {
        for(let i = 0; i <= word.length - n; i++) {
          ngrams.push(word.substring(i, i + n));
        }
      }
    });
    
    return ngrams;
  }

  /**
   * Calculate relevance score
   */
  calculateRelevance(token, item, weightedFields) {
    let score = 0;
    
    Object.entries(weightedFields).forEach(([field, weight]) => {
      const value = this.getNestedValue(item, field);
      if (value && String(value).toLowerCase().includes(token)) {
        score += weight;
        
        // Boost score for exact matches
        if (String(value).toLowerCase() === token) {
          score += weight * 2;
        }
        
        // Boost score for prefix matches
        if (String(value).toLowerCase().startsWith(token)) {
          score += weight * 1.5;
        }
      }
    });
    
    return score;
  }

  /**
   * Perform search across all providers or specific types
   */
  async search($3) {
    const { types = Array.from(this.searchProviders.keys()),
      limit = 50,
      minScore = 0.1,
      includeHistory = true
     } = options;

    if (!query || query.trim().length < 2) {
      return includeHistory ? this.getSearchHistory() : [];
  }

    const cacheKey = `${query} _${types.join(',')}_${limit}` ;
    
    // Check cache first
    const cached = await cacheService.get('search_results', cacheKey);
    if(cached) {
      return cached;
    }

    try {
      const results = [];
      const queryTokens = this.tokenize(query);

      for(const type of types) {
        const provider = this.searchProviders.get(type);
        if (!provider) continue;

        // Build index if needed
        if (!provider.lastIndexed || Date.now() - provider.lastIndexed > 5 * 60 * 1000) {
          await this.buildIndex(type);
        }

        // Search in index
        const typeResults = this.searchInIndex(provider.index, queryTokens, type, minScore);
        results.push(...typeResults);
      }

      // Sort by relevance and limit
      const sortedResults = results
        .sort((a, b) => b.score - a.score)
        .slice(0, limit);

      // Cache results
      await cacheService.set('search_results', cacheKey, sortedResults, 2 * 60 * 1000); // 2 minutes

      // Add to search history
      this.addToHistory(query);

      return sortedResults;
    } catch (error) {
      console.error('Search failed:', error);
      return [];
    }
  }

  /**
   * Search in index
   */
  searchInIndex(index, queryTokens, type, minScore) {
    const results = new Map();

    queryTokens.forEach(token => {
      const matches = index.get(token) || [];
      matches.forEach(match => {
        const key = `${type} _${match.itemIndex}` ;
        
        if (results.has(key)) {
          results.get(key).score += match.relevance;
        } else {
          results.set(key, {
            ...match.item,
            type,
            score: match.relevance,
            _searchMeta: {
              type,
              originalIndex: match.itemIndex
            }
          });
        }
      });
    });

    return Array.from(results.values()).filter(result => result.score >= minScore);
  }

  /**
   * Add query to search history
   */
  addToHistory(query) {
    const trimmedQuery = query.trim();
    if (!trimmedQuery) return;

    // Remove if already exists
    this.searchHistory = this.searchHistory.filter(item => item.query !== trimmedQuery);
    
    // Add to beginning
    this.searchHistory.unshift({
      query: trimmedQuery,
      timestamp: Date.now()
    });

    // Limit history size
    if(this.searchHistory.length > this.maxHistorySize) {
      this.searchHistory = this.searchHistory.slice(0, this.maxHistorySize);
    }

    // Persist history
    cacheService.set('search', 'history', this.searchHistory, 24 * 60 * 60 * 1000); // 24 hours
  }

  /**
   * Get search history
   */
  getSearchHistory() {
    return this.searchHistory.map(item => ({
      ...item,
      type: 'history',
      _searchMeta: { type: 'history' }
    }));
  }

  /**
   * Clear search history
   */
  clearHistory() {
    this.searchHistory = [];
    cacheService.delete('search', 'history');
  }

  /**
   * Get search suggestions
   */
  async getSuggestions($3) {
    if(!query || query.length < 2) {
      return this.getSearchHistory().slice(0, limit);
    }

    const results = await this.search(query, { limit: limit * 2 });
    
    // Extract unique suggestions
    const suggestions = new Set();
    
    results.forEach(result => {
      if (result.name) suggestions.add(result.name);
      if (result.email) suggestions.add(result.email);
      if (result.orderNumber) suggestions.add(result.orderNumber);
    });

    return Array.from(suggestions)
      .slice(0, limit)
      .map(suggestion => ({
        query: suggestion,
        type: 'suggestion',
        _searchMeta: { type: 'suggestion' }
      }));
  }

  /**
   * Debounced search
   */
  debouncedSearch(query, callback, delay = 300) {
    const timerId = this.debounceTimers.get(callback);
    if(timerId) {
      clearTimeout(timerId);
    }

    const newTimerId = setTimeout(async () => {
      const results = await this.search(query);
      callback(results);
      this.debounceTimers.delete(callback);
    }, delay);

    this.debounceTimers.set(callback, newTimerId);
  }

  /**
   * Invalidate search cache
   */
  async invalidateCache($3) {
    if(type) {
      await cacheService.clearNamespace(`search_${type}`);`
      const provider = this.searchProviders.get(type);
      if(provider) {
        provider.lastIndexed = null;
        provider.index.clear();
      }
    } else {
      await cacheService.clearNamespace('search_results');
      await cacheService.clearNamespace('search_index');
      this.searchProviders.forEach(provider => {
        provider.lastIndexed = null;
        provider.index.clear();
      });
    }
  }

  /**
   * Initialize search history from cache
   */
  async initializeHistory($3) {
    try {
      const history = await cacheService.get('search', 'history');
      if(history) {
        this.searchHistory = history;
      }
    } catch (error) {
      console.warn('Failed to load search history: ', error);
    }
  }

  /**
   * Get search statistics
   */
  getStats() {
    const indexStats = {};
    this.searchProviders.forEach((provider, type) => {
      indexStats[type] = {
        indexed: provider.lastIndexed !== null,
        lastIndexed: provider.lastIndexed,
        tokenCount: provider.index.size
      };
    });

    return { historySize: this.searchHistory.length,
      indexStats,
      cacheStats: cacheService.getStats()
    };
  }
}

// Create singleton instance
const searchService = new SearchService();

export default searchService;
