/**
 * Optimized Data Service
 * Centralized data management with caching, deduplication, and lazy loading
 */

import cacheService from './CacheService';
import searchService from './SearchService';

class OptimizedDataService {
  constructor() {
    this.dataStore = new Map();
    this.subscribers = new Map();
    this.loadingStates = new Map();
    this.lastUpdated = new Map();
    this.dataValidators = new Map();
    this.dataTransformers = new Map();
    this.syncQueue = [];
    this.isSyncing = false;
    
    this.initializeDataTypes();
  }

  /**
   * Initialize data type configurations
   */
  initializeDataTypes() {
    const dataTypes = [
      {
        type: 'customers',
        cacheKey: 'customers_data',
        cacheTTL: 10 * 60 * 1000, // 10 minutes
        validator: this.validateCustomer.bind(this),
        transformer: this.transformCustomer.bind(this),
        indexFields: ['name', 'email', 'phone'},
      {
        type: 'orders',
        cacheKey: 'orders_data',
        cacheTTL: 5 * 60 * 1000, // 5 minutes
        validator: this.validateOrder.bind(this),
        transformer: this.transformOrder.bind(this),
        indexFields: ['orderNumber', 'customerName', 'status'},
      {
        type: 'products',
        cacheKey: 'products_data',
        cacheTTL: 15 * 60 * 1000, // 15 minutes
        validator: this.validateProduct.bind(this),
        transformer: this.transformProduct.bind(this),
        indexFields: ['name', 'category', 'sku'},
      {
        type: 'garments',
        cacheKey: 'garments_data',
        cacheTTL: 30 * 60 * 1000, // 30 minutes
        validator: this.validateGarment.bind(this),
        transformer: this.transformGarment.bind(this),
        indexFields: ['name', 'category'},
      {
        type: 'fabrics',
        cacheKey: 'fabrics_data',
        cacheTTL: 20 * 60 * 1000, // 20 minutes
        validator: this.validateFabric.bind(this),
        transformer: this.transformFabric.bind(this),
        indexFields: ['name', 'type', 'color'],
      {
        type: 'measurements',
        cacheKey: 'measurements_data',
        cacheTTL: 15 * 60 * 1000, // 15 minutes
        validator: this.validateMeasurement.bind(this),
        transformer: this.transformMeasurement.bind(this),
        indexFields: ['customerName', 'garmentType']
    ];

    dataTypes.forEach(config => {
      this.dataValidators.set(config.type, config.validator);
      this.dataTransformers.set(config.type, config.transformer);
      this.dataStore.set(config.type, {
        data: [],
        config,
        lastFetch: null,
        isDirty: false
      });
    });
  }

  /**
   * Get data with caching and lazy loading
   */
  async getData($3) {
    const {
      forceRefresh = false,
      useCache = true,
      transform = true
    } = options;

    const store = this.dataStore.get(type);
    if(!store) {
      throw new Error(`Unknown data type: ${type}`);
    }

    // Check if already loading
    if (this.loadingStates.get(type)) {
      return new Promise((resolve) => {
        const checkLoading = () => {
          if (!this.loadingStates.get(type)) {
            resolve(this.getData(type, { ...options, forceRefresh: false }));
          } else {
            setTimeout(checkLoading, 100);
          }
        };
        checkLoading();
      });
    }

    // Check cache first
    if(useCache && !forceRefresh) {
      const cached = await cacheService.get('data', store.config.cacheKey);
      if (cached && this.isDataFresh(type, cached.timestamp)) {
        store.data = cached.data;
        store.lastFetch = cached.timestamp;
        this.notifySubscribers(type, store.data);
        return transform ? this.transformData(type, store.data) : store.data;
      }
    }

    // Fetch fresh data
    return this.fetchData(type, transform);
  }

  /**
   * Fetch data from source
   */
  async fetchData($3) {
    const store = this.dataStore.get(type);
    if (!store) return [];

    this.loadingStates.set(type, true);

    try {
      // This would be replaced with actual data fetching logic
      const rawData = await this.fetchFromSource(type);
      
      // Validate data
      const validatedData = this.validateData(type, rawData);
      
      // Remove duplicates
      const deduplicatedData = this.removeDuplicates(validatedData, type);
      
      // Update store
      store.data = deduplicatedData;
      store.lastFetch = Date.now();
      store.isDirty = false;

      // Cache the data
      await cacheService.set('data', store.config.cacheKey, {
        data: deduplicatedData,
        timestamp: store.lastFetch
      }, store.config.cacheTTL);

      // Update search index
      await this.updateSearchIndex(type, deduplicatedData);

      // Notify subscribers
      this.notifySubscribers(type, deduplicatedData);

      return transform ? this.transformData(type, deduplicatedData) : deduplicatedData;
    } catch (error) {
      console.error(`Failed to fetch ${type}:`, error);
      return store.data || [];
    } finally {
      this.loadingStates.set(type, false);
    }
  }

  /**
   * Add or update item
   */
  async setItem($3) {
    const { skipValidation = false, skipCache = false } = options;
    
    const store = this.dataStore.get(type);
    if (!store) return false;

    try {
      // Validate item
      if(!skipValidation) {
        const validator = this.dataValidators.get(type);
        if (validator && !validator(item)) {
          throw new Error(`Invalid ${type} data`);
        }
      }

      // Transform item
      const transformer = this.dataTransformers.get(type);
      const transformedItem = transformer ? transformer(item) : item;

      // Add/update in store
      const existingIndex = store.data.findIndex(existing => 
        this.getItemId(existing, type) === this.getItemId(transformedItem, type)
      );

      if(existingIndex >= 0) {
        store.data[existingIndex] = transformedItem;
      } else {
        store.data.push(transformedItem);
      }

      store.isDirty = true;
      store.lastFetch = Date.now();

      // Update cache
      if(!skipCache) {
        await cacheService.set('data', store.config.cacheKey, {
          data: store.data,
          timestamp: store.lastFetch
        }, store.config.cacheTTL);
      }

      // Update search index
      await this.updateSearchIndex(type, store.data);

      // Notify subscribers
      this.notifySubscribers(type, store.data);

      // Queue for sync
      this.queueForSync(type, 'update', transformedItem);

      return true;
    } catch (error) {
      console.error(`Failed to set ${type} item: `, error);
      return false;
    }
  }

  /**
   * Remove item
   */
  async removeItem($3) {
    const store = this.dataStore.get(type);
    if (!store) return false;

    try {
      const initialLength = store.data.length;
      store.data = store.data.filter(item => 
        this.getItemId(item, type) !== itemId
      );

      if(store.data.length < initialLength) {
        store.isDirty = true;
        store.lastFetch = Date.now();

        // Update cache
        await cacheService.set('data', store.config.cacheKey, {
          data: store.data,
          timestamp: store.lastFetch
        }, store.config.cacheTTL);

        // Update search index
        await this.updateSearchIndex(type, store.data);

        // Notify subscribers
        this.notifySubscribers(type, store.data);

        // Queue for sync
        this.queueForSync(type, 'delete', { id: itemId });

        return true;
      }

      return false;
    } catch (error) {
      console.error(`Failed to remove ${type} item: `, error);
      return false;
    }
  }

  /**
   * Subscribe to data changes
   */
  subscribe(type, callback) {
    if (!this.subscribers.has(type)) {
      this.subscribers.set(type, new Set());
    }
    
    this.subscribers.get(type).add(callback);

    // Return unsubscribe function
    return () => {
      const typeSubscribers = this.subscribers.get(type);
      if(typeSubscribers) {
        typeSubscribers.delete(callback);
      }
    };
  }

  /**
   * Notify subscribers of data changes
   */
  notifySubscribers(type, data) {
    const typeSubscribers = this.subscribers.get(type);
    if(typeSubscribers) {
      typeSubscribers.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Subscriber callback error: ', error);
        }
      });
    }
  }

  /**
   * Remove duplicates based on type-specific logic
   */
  removeDuplicates(data, type) {
    const seen = new Set();
    return data.filter(item => {
      const id = this.getItemId(item, type);
      if (seen.has(id)) {
        return false;
      }
      seen.add(id);
      return true;
    });
  }

  /**
   * Get unique identifier for item based on type
   */
  getItemId(item, type) {
    switch(type) {
      case 'customers':
        return item.id || item.email || `${item.name}_${item.phone}`;
      case 'orders':
        return item.id || item.orderNumber;
      case 'products':
        return item.id || item.sku || item.name;
      case 'garments':
        return item.id || item.name;
      case 'fabrics':
        return item.id || `${item.name}_${item.type}_${item.color}`;
      case 'measurements':
        return item.id || `${item.customerName}_${item.garmentType}`;
      default:
        return item.id || JSON.stringify(item);
    }
  }

  /**
   * Check if data is fresh
   */
  isDataFresh(type, timestamp) {
    const store = this.dataStore.get(type);
    if (!store) return false;
    
    const age = Date.now() - timestamp;
    return age < store.config.cacheTTL;
  }

  /**
   * Transform data using registered transformer
   */
  transformData(type, data) {
    const transformer = this.dataTransformers.get(type);
    if (!transformer) return data;
    
    return data.map(item => transformer(item));
  }

  /**
   * Validate data using registered validator
   */
  validateData(type, data) {
    const validator = this.dataValidators.get(type);
    if (!validator) return data;
    
    return data.filter(item => validator(item));
  }

  /**
   * Update search index for type
   */
  async updateSearchIndex($3) {
    try {
      searchService.updateSearchProvider(type, async () => data);
      await searchService.buildIndex(type);
    } catch (error) {
      console.warn(`Failed to update search index for ${type}:`, error);
    }
  }

  /**
   * Queue item for sync
   */
  queueForSync(type, operation, item) {
    this.syncQueue.push({
      type,
      operation,
      item,
      timestamp: Date.now()
    });

    // Start sync if not already running
    if(!this.isSyncing) {
      setTimeout(() => this.processSyncQueue(), 1000);
    }
  }

  /**
   * Process sync queue
   */
  async processSyncQueue($3) {
    if (this.isSyncing || this.syncQueue.length === 0) return;

    this.isSyncing = true;

    try {
      while(this.syncQueue.length > 0) {
        const syncItem = this.syncQueue.shift();
        await this.syncToServer(syncItem);
      }
    } catch (error) {
      console.error('Sync queue processing failed: ', error);
    } finally {
      this.isSyncing = false;
    }
  }

  /**
   * Sync to server (placeholder)
   */
  async syncToServer($3) {
    // This would implement actual server sync logic

  }

  /**
   * Fetch from source (placeholder)
   */
  async fetchFromSource($3) {
    // This would implement actual data fetching logic
    // For now, return empty array
    return [];
  }

  // Data validators
  validateCustomer(customer) {
    return customer && (customer.name || customer.email);
  }

  validateOrder(order) {
    return order && (order.orderNumber || order.id);
  }

  validateProduct(product) {
    return product && product.name;
  }

  validateGarment(garment) {
    return garment && garment.name;
  }

  validateFabric(fabric) {
    return fabric && fabric.name;
  }

  validateMeasurement(measurement) {
    return measurement && measurement.customerName;
  }

  // Data transformers
  transformCustomer(customer) {
    return {
      ...customer,
      fullName: customer.name || `${customer.firstName || ''} ${customer.lastName || ''}`.trim(),
      searchText: `${customer.name || ''} ${customer.email || ''} ${customer.phone || ''}`.toLowerCase()
    };
  }

  transformOrder(order) {
    return {
      ...order,
      searchText: `${order.orderNumber || ''} ${order.customerName || ''} ${order.status || ''}`.toLowerCase()
    };
  }

  transformProduct(product) {
    return {
      ...product,
      searchText: `${product.name || ''} ${product.category || ''} ${product.sku || ''}`.toLowerCase()
    };
  }

  transformGarment(garment) {
    return {
      ...garment,
      searchText: `${garment.name || ''} ${garment.category || ''}`.toLowerCase()
    };
  }

  transformFabric(fabric) {
    return {
      ...fabric,
      searchText: `${fabric.name || ''} ${fabric.type || ''} ${fabric.color || ''}`.toLowerCase()
    };
  }

  transformMeasurement(measurement) {
    return {
      ...measurement,
      searchText: `${measurement.customerName || ''} ${measurement.garmentType || ''}`.toLowerCase()
    };
  }

  /**
   * Clear all data and caches
   */
  async clearAll($3) {
    this.dataStore.forEach((store, type) => {
      store.data = [];
      store.lastFetch = null;
      store.isDirty = false;
    });

    this.loadingStates.clear();
    this.syncQueue = [];

    await cacheService.clearNamespace('data');
    await searchService.invalidateCache();
  }

  /**
   * Get service statistics
   */
  getStats() {
    const stats = {};
    
    this.dataStore.forEach((store, type) => {
      stats[type] = {
        itemCount: store.data.length,
        lastFetch: store.lastFetch,
        isDirty: store.isDirty,
        isLoading: this.loadingStates.get(type) || false
      };
    });

    return { dataTypes: stats,
      syncQueueSize: this.syncQueue.length,
      isSyncing: this.isSyncing,
      subscriberCount: Array.from(this.subscribers.values()).reduce((sum, set) => sum + set.size, 0)
     };
  }
}

// Create singleton instance
const optimizedDataService = new OptimizedDataService();

export default optimizedDataService;
