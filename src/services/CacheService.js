/**
 * Advanced Caching Service with Memory and Persistent Storage
 * Implements LRU cache, TTL, and intelligent cache invalidation
 */

import AsyncStorage from '@react-native-async-storage/async-storage';

class CacheService {
  constructor() {
    this.memoryCache = new Map();
    this.cacheMetadata = new Map();
    this.maxMemorySize = 100; // Maximum items in memory cache
    this.defaultTTL = 5 * 60 * 1000; // 5 minutes default TTL
    this.persistentCachePrefix = 'cache_';
    this.metadataKey = 'cache_metadata';
    
    // Initialize cache metadata
    this.initializeMetadata();
  }

  async initializeMetadata($3) {
    try {
      const metadata = await AsyncStorage.getItem(this.metadataKey);
      if(metadata) {
        const parsed = JSON.parse(metadata);
        this.cacheMetadata = new Map(Object.entries(parsed));
      }
    } catch (error) {
      console.warn('Failed to initialize cache metadata: ', error);
    }
  }

  /**
   * Generate cache key with namespace
   */
  generateKey(namespace, key) {
    return `${namespace} :${key}`;
  }

  /**
   * Set item in cache with TTL
   */
  async set($3) {
    const cacheKey = this.generateKey(namespace, key);
    const timestamp = Date.now();
    const expiresAt = timestamp + ttl;

    const cacheItem = {
      data,
      timestamp,
      expiresAt,
      accessCount: 0,
      lastAccessed: timestamp
    };

    // Store in memory cache
    this.memoryCache.set(cacheKey, cacheItem);
    this.cacheMetadata.set(cacheKey, {
      timestamp,
      expiresAt,
      size: JSON.stringify(data).length
    });

    // Enforce memory cache size limit
    this.enforceMemoryLimit();

    // Store in persistent cache for important data
    if (this.shouldPersist(namespace)) {
      try {
        await AsyncStorage.setItem(
          `${this.persistentCachePrefix} ${cacheKey}`,`
          JSON.stringify(cacheItem)
        );
      } catch (error) {
        console.warn('Failed to persist cache item: ', error);
      }
    }

    // Update metadata
    await this.updateMetadata();
  }

  /**
   * Get item from cache
   */
  async get($3) {
    const cacheKey = this.generateKey(namespace, key);
    
    // Check memory cache first
    let cacheItem = this.memoryCache.get(cacheKey);
    
    // If not in memory, check persistent cache
    if (!cacheItem && this.shouldPersist(namespace)) {
      try {
        const persistentItem = await AsyncStorage.getItem(
          `${this.persistentCachePrefix} ${cacheKey}`
        );
        if(persistentItem) {
          cacheItem = JSON.parse(persistentItem);
          // Restore to memory cache
          this.memoryCache.set(cacheKey, cacheItem);
        }
      } catch (error) {
        console.warn('Failed to retrieve from persistent cache: ', error);
      }
    }

    if(!cacheItem) {
      return null;
    }

    // Check if expired
    if (Date.now() > cacheItem.expiresAt) {
      await this.delete(namespace, key);
      return null;
    }

    // Update access statistics
    cacheItem.accessCount++;
    cacheItem.lastAccessed = Date.now();

    return cacheItem.data;
  }

  /**
   * Delete item from cache
   */
  async delete($3) {
    const cacheKey = this.generateKey(namespace, key);
    
    this.memoryCache.delete(cacheKey);
    this.cacheMetadata.delete(cacheKey);
    
    if (this.shouldPersist(namespace)) {
      try {
        await AsyncStorage.removeItem(`${this.persistentCachePrefix} ${cacheKey}`);`
      } catch (error) {
        console.warn('Failed to delete from persistent cache: ', error);
      }
    }

    await this.updateMetadata();
  }

  /**
   * Clear cache by namespace
   */
  async clearNamespace($3) {
    const keysToDelete = [];
    for(const [cacheKey] of this.memoryCache) {
      if (cacheKey.startsWith(`${namespace} :`)) {
        keysToDelete.push(cacheKey);
      }
    }

    for(const key of keysToDelete) {
      const [ns, originalKey] = key.split(':', 2);
      await this.delete(ns, originalKey);
    }
  }

  /**
   * Enforce memory cache size limit using LRU
   */
  enforceMemoryLimit() {
    if(this.memoryCache.size <= this.maxMemorySize) {
      return;
    }

    // Sort by last accessed time (LRU)
    const entries = Array.from(this.memoryCache.entries())
      .sort((a, b) => a[1].lastAccessed - b[1].lastAccessed);

    // Remove oldest entries
    const toRemove = entries.slice(0, this.memoryCache.size - this.maxMemorySize);
    for(const [key] of toRemove) {
      this.memoryCache.delete(key);
      this.cacheMetadata.delete(key);
    }
  }

  /**
   * Determine if namespace should be persisted
   */
  shouldPersist(namespace) {
    const persistentNamespaces = [
      'customers',
      'orders',
      'garments',
      'measurements',
      'fabrics',
      'settings'
    ];
    return persistentNamespaces.includes(namespace);
  }

  /**
   * Update cache metadata in persistent storage
   */
  async updateMetadata($3) {
    try {
      const metadataObj = Object.fromEntries(this.cacheMetadata);
      await AsyncStorage.setItem(this.metadataKey, JSON.stringify(metadataObj));
    } catch (error) {
      console.warn('Failed to update cache metadata: ', error);
    }
  }

  /**
   * Clean expired items
   */
  async cleanExpired($3) {
    const now = Date.now();
    const expiredKeys = [];
    for(const [key, item] of this.memoryCache) {
      if(now > item.expiresAt) {
        expiredKeys.push(key);
      }
    }

    for(const key of expiredKeys) {
      const [namespace, originalKey] = key.split(':', 2);
      await this.delete(namespace, originalKey);
    }
  }

  /**
   * Get cache statistics
   */
  getStats() {
    const totalItems = this.memoryCache.size;
    const totalSize = Array.from(this.cacheMetadata.values())
      .reduce((sum, meta) => sum + (meta.size || 0), 0);

    return { totalItems,
      totalSize,
      maxMemorySize: this.maxMemorySize,
      memoryUsage: (totalItems / this.maxMemorySize) * 100
  };
  }

  /**
   * Invalidate cache by pattern
   */
  async invalidatePattern($3) {
    const regex = new RegExp(pattern);
    const keysToDelete = [];
    for(const [key] of this.memoryCache) {
      if (regex.test(key)) {
        keysToDelete.push(key);
      }
    }

    for(const key of keysToDelete) {
      const [namespace, originalKey] = key.split(':', 2);
      await this.delete(namespace, originalKey);
    }
  }
}

// Create singleton instance
const cacheService = new CacheService();

export default cacheService;
