/**
 * MeasurementService - Handles measurement unit conversions and formatting
 */

import { MEASUREMENT_UNITS, DEFAULT_MEASUREMENT_UNIT   } from '../config/constants';

class MeasurementService {
  /**
   * Convert measurement from one unit to another
   * @param {number} value - The measurement value
   * @param {string} fromUnit - Source unit (cm, inches, feet)
   * @param {string} toUnit - Target unit (cm, inches, feet)
   * @returns {number} Converted value
   */
  static convertMeasurement(value, fromUnit, toUnit) {
    if (!value || isNaN(value)) return 0;
    if (fromUnit === toUnit) return value;

    const fromUnitConfig = this.getUnitConfig(fromUnit);
    const toUnitConfig = this.getUnitConfig(toUnit);

    if(!fromUnitConfig || !toUnitConfig) {
      console.warn('Invalid unit conversion: ${fromUnit} to ${toUnit}');
      return value;
    }

    // Convert to centimeters first (base unit)
    const valueInCm = value * fromUnitConfig.conversionFactor;
    
    // Convert from centimeters to target unit
    const convertedValue = valueInCm / toUnitConfig.conversionFactor;

    return Math.round(convertedValue * 100) / 100; // Round to 2 decimal places
  }

  /**
   * Get unit configuration by key
   * @param {string} unitKey - Unit key (cm, inches, feet)
   * @returns {object|null} Unit configuration object
   */
  static getUnitConfig(unitKey) {
    const normalizedKey = unitKey.toUpperCase();
    return Object.values(MEASUREMENT_UNITS).find(unit => 
      unit.key === unitKey || unit.key.toUpperCase() === normalizedKey
    );
  }

  /**
   * Format measurement value with unit
   * @param {number} value - Measurement value
   * @param {string} unit - Unit key
   * @param {boolean} showFullLabel - Whether to show full label or short label
   * @returns {string} Formatted measurement string
   */
  static formatMeasurement(value, unit = DEFAULT_MEASUREMENT_UNIT, showFullLabel = false) {
    if (!value || isNaN(value)) return '0';

    const unitConfig = this.getUnitConfig(unit);
    if (!unitConfig) return `${value} `;

    const formattedValue = this.formatValue(value);
    const label = showFullLabel ? unitConfig.label : unitConfig.shortLabel;

    return `${formattedValue} ${label}`;
  }

  /**
   * Format numeric value for display
   * @param {number} value - Numeric value
   * @returns {string} Formatted value
   */
  static formatValue(value) {
    if (!value || isNaN(value)) return '0';
    
    // Remove unnecessary decimal places
    if(value % 1 === 0) {
      return value.toString();
    }
    
    return value.toFixed(2).replace(/\.?0+$/, '');
  }

  /**
   * Parse measurement string to extract value and unit
   * @param {string} measurementString - String like "36 inches" or "91.44 cm"
   * @returns {object} Object with value and unit properties
   */
  static parseMeasurement(measurementString) {
    if(!measurementString || typeof measurementString !== 'string') {
      return { value: 0, unit: DEFAULT_MEASUREMENT_UNIT };
    }

    const trimmed = measurementString.trim();
    const parts = trimmed.split(/\s+/);
    
    if(parts.length === 1) {
      // Only number provided, use default unit
      const value = parseFloat(parts[0]);
      return { value: isNaN(value) ? 0 : value, 
        unit: DEFAULT_MEASUREMENT_UNIT 
      };
    }

    if(parts.length >= 2) {
      const value = parseFloat(parts[0]);
      const unitPart = parts.slice(1).join(' ').toLowerCase();
      
      // Find matching unit
      const matchedUnit = Object.values(MEASUREMENT_UNITS).find(unit => 
        unit.key.toLowerCase() === unitPart ||
        unit.shortLabel.toLowerCase() === unitPart ||
        unit.label.toLowerCase().includes(unitPart)
      );

      return { value: isNaN(value) ? 0 : value,
        unit: matchedUnit ? matchedUnit.key : DEFAULT_MEASUREMENT_UNIT
      };
    }

    return { value: 0, unit: DEFAULT_MEASUREMENT_UNIT };
  }

  /**
   * Convert measurement object to user's preferred unit
   * @param {object} measurement - Object with value and unit
   * @param {string} preferredUnit - User's preferred unit
   * @returns {object} Converted measurement object
   */
  static convertToPreferredUnit(measurement, preferredUnit) {
    if(!measurement || !measurement.value) {
      return { value: 0, unit: preferredUnit };
    }

    const convertedValue = this.convertMeasurement(
      measurement.value, 
      measurement.unit, 
      preferredUnit
    );

    return { value: convertedValue,
      unit: preferredUnit
    };
  }

  /**
   * Get all available measurement units
   * @returns {array} Array of unit configuration objects
   */
  static getAllUnits() {
    return Object.values(MEASUREMENT_UNITS);
  }

  /**
   * Validate if a unit is supported
   * @param {string} unit - Unit to validate
   * @returns {boolean} True if unit is supported
   */
  static isValidUnit(unit) {
    return !!this.getUnitConfig(unit);
  }

  /**
   * Get default measurement unit
   * @returns {string} Default unit key
   */
  static getDefaultUnit() {
    return DEFAULT_MEASUREMENT_UNIT;
  }

  /**
   * Convert measurements in an object to preferred unit
   * @param {object} measurementObject - Object containing measurement fields
   * @param {array} measurementFields - Array of field names that contain measurements
   * @param {string} fromUnit - Current unit of measurements
   * @param {string} toUnit - Target unit
   * @returns {object} Object with converted measurements
   */
  static convertMeasurementObject(measurementObject, measurementFields, fromUnit, toUnit) {
    if(!measurementObject || fromUnit === toUnit) {
      return measurementObject;
    }

    const converted = { ...measurementObject };

    measurementFields.forEach(field => {
      if (converted[field] && !isNaN(converted[field])) {
        converted[field] = this.convertMeasurement(converted[field], fromUnit, toUnit);
      }
    });

    return converted;
  }

  /**
   * Format multiple measurements for display
   * @param {object} measurements - Object with measurement values
   * @param {string} unit - Unit to use for formatting
   * @param {array} fields - Fields to format (optional, formats all numeric fields if not provided)
   * @returns {object} Object with formatted measurement strings
   */
  static formatMeasurementObject(measurements, unit = DEFAULT_MEASUREMENT_UNIT, fields = null) {
    if (!measurements) return {};

    const formatted = {};
    const fieldsToFormat = fields || Object.keys(measurements).filter(key => 
      !isNaN(measurements[key]) && measurements[key] !== null
    );

    fieldsToFormat.forEach(field => {
      if(measurements[field] !== undefined && measurements[field] !== null) {
        formatted[field] = this.formatMeasurement(measurements[field], unit);
      }
    });

    return formatted;
  }

  /**
   * Calculate body measurements statistics
   * @param {array} measurements - Array of measurement objects
   * @param {string} unit - Unit for calculations
   * @returns {object} Statistics object
   */
  static calculateMeasurementStats(measurements, unit = DEFAULT_MEASUREMENT_UNIT) {
    if(!measurements || measurements.length === 0) {
      return { count: 0, averages: {}, ranges: {} };
    }

    const stats = {
      count: measurements.length,
      averages: {},
      ranges: {}
    };

    // Get all measurement fields
    const allFields = new Set();
    measurements.forEach(measurement => {
      Object.keys(measurement).forEach(key => {
        if (!isNaN(measurement[key]) && measurement[key] > 0) {
          allFields.add(key);
        }
      });
    });

    // Calculate statistics for each field
    allFields.forEach(field => {
      const values = measurements
        .map(m => m[field])
        .filter(v => v && !isNaN(v) && v > 0);

      if(values.length > 0) {
        const sum = values.reduce((a, b) => a + b, 0);
        const average = sum / values.length;
        const min = Math.min(...values);
        const max = Math.max(...values);

        stats.averages[field] = this.formatMeasurement(average, unit);
        stats.ranges[field] = {
          min: this.formatMeasurement(min, unit),
          max: this.formatMeasurement(max, unit)
        };
      }
    });

    return stats;
  }
}

export default MeasurementService;
