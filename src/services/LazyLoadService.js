/**
 * Advanced Lazy Loading Service
 * Implements intelligent component and data lazy loading with preloading strategies
 */

import React, { lazy, Suspense, useState, useEffect, useRef } from 'react';
import { View, ActivityIndicator, StyleSheet   } from 'react-native';
import { useTheme   } from '../context/ThemeContext';
import cacheService from './CacheService';

class LazyLoadService {
  constructor() {
    this.componentCache = new Map();
    this.loadingStates = new Map();
    this.preloadQueue = [];
    this.isPreloading = false;
    this.intersectionObserver = null;
    this.preloadThreshold = 0.1; // Preload when 10% visible
  }

  /**
   * Create lazy loaded component with caching
   */
  createLazyComponent(importFunction, componentName, fallback = null) {
    if (this.componentCache.has(componentName)) {
      return this.componentCache.get(componentName);
    }

    const LazyComponent = lazy(async () => {
      try {
        this.setLoadingState(componentName, true);
        
        const cached = await cacheService.get('components', componentName);
        if(cached) {
          this.setLoadingState(componentName, false);
          return cached;
        }

        const component = await importFunction();
        
        await cacheService.set('components', componentName, component, 30 * 60 * 1000); // 30 minutes
        
        this.setLoadingState(componentName, false);
        return component;
      } catch (error) {
        console.error(`Failed to load component ${componentName}:`, error);
        this.setLoadingState(componentName, false);
        throw error;
      }
    });

    const WrappedComponent = (props) => (
      <Suspense fallback={fallback || <LazyLoadFallback componentName={componentName} />}>
        <LazyComponent {...props} />
      </Suspense>
    );

    this.componentCache.set(componentName, WrappedComponent);
    return WrappedComponent;
  }

  /**
   * Preload component
   */
  async preloadComponent($3) {
    if (this.componentCache.has(componentName)) return;
    try {
      const component = await importFunction();
      await cacheService.set('components', componentName, component, 30 * 60 * 1000);
    } catch (error) {
      console.warn(`Failed to preload component ${componentName}:`, error);
    }
  }

  /**
   * Batch preload components
   */
  async batchPreload($3) {
    const preloadPromises = components.map(({ importFunction, componentName }) =>
      this.preloadComponent(importFunction, componentName)
    );
    try {
      await Promise.allSettled(preloadPromises);
    } catch (error) {
      console.warn('Batch preload failed: ', error);
    }
  }

  setLoadingState(componentName, isLoading) {
    this.loadingStates.set(componentName, isLoading);
  }

  getLoadingState(componentName) {
    return this.loadingStates.get(componentName) || false;
  }

  /**
   * Create lazy data loader with pagination
   */
  createLazyDataLoader(dataFetcher, options = {}) {
    const { pageSize = 20, cacheKey, cacheTTL = 5 * 60 * 1000, preloadNext = true  } = options;

    return {
      async loadPage($3) {
        const cacheKeyWithPage = `${cacheKey} _page_${page}_${JSON.stringify(filters)}`;
        if(cacheKey) {
          const cached = await cacheService.get('data', cacheKeyWithPage);
          if (cached) return cached;
        }
        try {
          const data = await dataFetcher({ page, pageSize, filters });
          if (cacheKey) await cacheService.set('data', cacheKeyWithPage, data, cacheTTL);
          if(preloadNext && data.hasMore) {
            setTimeout(() => this.loadPage(page + 1, filters), 1000);
          }
          return data;
        } catch (error) {
          console.error('Failed to load data page:', error);
          throw error;
        }
      },
      async invalidateCache($3) {
        if (cacheKey) await cacheService.invalidatePattern(`data:${cacheKey}_page_.*`);
      }
    };
  }

  async clearAllCaches($3) {
    this.componentCache.clear();
    this.loadingStates.clear();
    await cacheService.clearNamespace('components');
    await cacheService.clearNamespace('data');
  }

  getStats() {
    return { cachedComponents: this.componentCache.size,
      loadingComponents: Array.from(this.loadingStates.values()).filter(Boolean).length,
      cacheStats: cacheService.getStats()
  };
  }
}

/**
 * Default fallback component for lazy loading
 */
const LazyLoadFallback = ({ componentName }) => {
  const { theme  } = useTheme();
  
  // FIXED: Stylesheet moved inside component to access theme
  const styles = StyleSheet.create({
    fallbackContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20,
      backgroundColor: theme.colors.background}});

  // FIXED: Corrected style prop
  return (
    <View style={styles.fallbackContainer}>
      <ActivityIndicator size="large" color={theme.colors.primary} />
    </View>
  );
};

/**
 * Hook for lazy loading data with pagination
 */
export const useLazyData = (dataFetcher, options = {}) => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(0);
  const [error, setError] = useState(null);

  const lazyLoader = useRef(
    lazyLoadService.createLazyDataLoader(dataFetcher, options)
  ).current;

  const loadMore = async (reset = false) => {
    if (loading || (!hasMore && !reset)) return;
    setLoading(true);
    setError(null);
    try {
      const currentPage = reset ? 0 : page;
      const result = await lazyLoader.loadPage(currentPage, options.filters);
      setData(prev => (reset ? result.data : [...prev, ...result.data]));
      setPage(currentPage + 1);
      setHasMore(result.hasMore);
    } catch (err) {
      setError(err);
      console.error('Failed to load more data:', err);
    } finally {
      setLoading(false);
    }
  };

  const refresh = () => loadMore(true);
  const invalidateCache = () => lazyLoader.invalidateCache();

  useEffect(() => {
    refresh();
  }, [JSON.stringify(options.filters)]); // Use stringify to deep compare filters

  return { data, loading, hasMore, error, loadMore, refresh, invalidateCache };
};

/**
 * Hook for component preloading
 */
export const usePreloadComponents = (components) => {
  useEffect(() => {
    lazyLoadService.batchPreload(components);
  }, [components]);
};

// Create singleton instance
const lazyLoadService = new LazyLoadService();

export default lazyLoadService;