/**
 * Order Service
 * Core business logic for order management
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { Order, OrderFormData, OrderStatus, OrderStats, OrderFilters, OrderSortOptions   } from '../types/order';
import { OrderValidationService   } from './OrderValidationService';

export class OrderService {
  private static readonly STORAGE_KEY = 'orders_v2';
  private static readonly COUNTER_KEY = 'order_counter_v2';

  /**
   * Generate unique order number
   */
  static async generateOrderNumber(): Promise<string> {
    try {
      const counterStr = await AsyncStorage.getItem(this.COUNTER_KEY);
      const counter = counterStr ? parseInt(counterStr, 10) : 1000;
      const newCounter = counter + 1;
      
      await AsyncStorage.setItem(this.COUNTER_KEY, newCounter.toString());
      
      // Format: ORD-YYYY-NNNN
      const year = new Date().getFullYear();
      const orderNumber = `ORD-${year}-${newCounter.toString().padStart(4, '0')}`;
      return orderNumber;
    } catch (error) {
      console.error('Error generating order number:', error);
      // Fallback to timestamp-based number
      return `ORD-${Date.now()}`;
    }
  }

  /**
   * Create new order
   */
  static async createOrder(formData: OrderFormData): Promise<Order> {
    // Validate order data
    const validation = OrderValidationService.validateOrder(formData);
    if(!validation.isValid) {
      throw new Error(`Validation failed: ${validation.errors.map(e => e.message).join(', ')}`);
    }

    try {
      const orderNumber = await this.generateOrderNumber();
      const now = new Date().toISOString();

      // Calculate pricing
      const subtotal = formData.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
      const discountAmount = formData.discountType === 'percentage' 
        ? (subtotal * formData.discount / 100)
        : formData.discount;
      const total = subtotal - discountAmount;

      // Create order object
      const order: Order = {
        id: `order_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,`
        orderNumber,
        
        // Customer information
        customerId: formData.customerId,
        customerName: formData.customerName,
        customerPhone: formData.customerPhone,
        customerEmail: formData.customerEmail,
        
        // Order details
        type: formData.type,
        status: 'draft',
        priority: formData.priority,
        
        // Items with generated IDs
        items: formData.items.map(item => ({
          ...item,
          id: `item_${Date.now()}_${Math.random().toString(36).substr(2, 9)} 
       `})),
        
        // Pricing
        subtotal,
        discount: formData.discount,
        discountType: formData.discountType,
        tax: 0, // Can be configured later
        total,
        
        // Payment
        paymentStatus: 'unpaid',
        payments: [],
        paidAmount: 0,
        balanceAmount: total,
        
        // Dates
        createdAt: now,
        updatedAt: now,
        dueDate: formData.dueDate,
        
        // Additional information
        notes: formData.notes,
        references: formData.references || [],
        tags: formData.tags || [],
        deliveryAddress: formData.deliveryAddress,
        
        // Timeline
        timeline: [{
          id: `timeline_${Date.now()}`,`
          status: 'draft',
          timestamp: now,
          notes: 'Order created'
        }],
        
        // Flags
        isUrgent: formData.isUrgent || false,
        isArchived: false
      };

      // Handle advance payment if provided
      if(formData.advancePayment && formData.advancePayment > 0) {
        const payment = {
          id: `payment_${Date.now()}`,
          amount: formData.advancePayment,
          method: formData.paymentMethod || 'cash',
          date: now,
          notes: 'Advance payment'
        };

        order.payments.push(payment);
        order.paidAmount = formData.advancePayment;
        order.balanceAmount = total - formData.advancePayment;
        order.paymentStatus = order.balanceAmount <= 0 ? 'paid' : 'partial';
      }

      // Save order
      await this.saveOrder(order);

      return order;
    } catch (error) {
      console.error('Error creating order:', error);
      throw error;
    }
  }

  /**
   * Update order
   */
  static async updateOrder(orderId: string, updates: Partial<Order>): Promise<Order> {
    try {
      const orders = await this.getAllOrders();
      const orderIndex = orders.findIndex(order => order.id === orderId);
      
      if(orderIndex === -1) {
        throw new Error('Order not found');
      }

      const updatedOrder = {
        ...orders[orderIndex],
        ...updates,
        updatedAt: new Date().toISOString()
      };

      orders[orderIndex] = updatedOrder;
      await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(orders));

      return updatedOrder;
    } catch (error) {
      console.error('Error updating order:', error);
      throw error;
    }
  }

  /**
   * Update order status
   */
  static async updateOrderStatus(orderId: string, newStatus: OrderStatus, notes?: string): Promise<Order> {
    try {
      const order = await this.getOrderById(orderId);
      if(!order) {
        throw new Error('Order not found');
      }

      // Add timeline entry
      const timelineEntry = {
        id: `timeline_${Date.now()}`,`
        status: newStatus,
        timestamp: new Date().toISOString(),
        notes: notes || `Status changed to ${newStatus} 
      };

      const updatedOrder = await this.updateOrder(orderId, {
        status: newStatus,
        timeline: [...order.timeline, timelineEntry]);

      return updatedOrder;
    } catch (error) {
      console.error('Error updating order status:', error);
      throw error;
    }
  }

  /**
   * Add payment to order
   */
  static async addPayment(orderId: string, amount: number, method: string, notes?: string): Promise<Order> {
    try {
      const order = await this.getOrderById(orderId);
      if(!order) {
        throw new Error('Order not found');
     `}

      const payment = {
        id: `payment_${Date.now()}`,`
        amount,
        method,
        date: new Date().toISOString(),
        notes
      };

      const newPaidAmount = order.paidAmount + amount;
      const newBalanceAmount = order.total - newPaidAmount;
      
      let paymentStatus = order.paymentStatus;
      if(newBalanceAmount <= 0) {
        paymentStatus = 'paid';
      } else if(newPaidAmount > 0) {
        paymentStatus = 'partial';
      }

      const updatedOrder = await this.updateOrder(orderId, {
        payments: [...order.payments, payment],
        paidAmount: newPaidAmount,
        balanceAmount: newBalanceAmount,
        paymentStatus
      });

      return updatedOrder;
    } catch (error) {
      console.error('Error adding payment:', error);
      throw error;
    }
  }

  /**
   * Get all orders
   */
  static async getAllOrders(): Promise<Order[]> {
    try {
      const ordersJson = await AsyncStorage.getItem(this.STORAGE_KEY);
      return ordersJson ? JSON.parse(ordersJson) : [];
    } catch (error) {
      console.error('Error getting orders:', error);
      return [];
    }
  }

  /**
   * Get order by ID
   */
  static async getOrderById(orderId: string): Promise<Order | null> {
    try {
      const orders = await this.getAllOrders();
      return orders.find(order => order.id === orderId) || null;
    } catch (error) {
      console.error('Error getting order by ID:', error);
      return null;
    }
  }

  /**
   * Save single order
   */
  private static async saveOrder(order: Order): Promise<void> {
    try {
      const orders = await this.getAllOrders();
      const existingIndex = orders.findIndex(o => o.id === order.id);
      
      if(existingIndex >= 0) {
        orders[existingIndex] = order;
      } else {
        orders.push(order);
      }

      await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(orders));
    } catch (error) {
      console.error('Error saving order:', error);
      throw error;
    }
  }

  /**
   * Delete order (archive)
   */
  static async deleteOrder(orderId: string): Promise<boolean> {
    try {
      const updatedOrder = await this.updateOrder(orderId, {
        isArchived: true,
        archivedAt: new Date().toISOString()
      });
      return true;
    } catch (error) {
      console.error('Error deleting order:', error);
      return false;
    }
  }

  /**
   * Filter and sort orders
   */
  static filterAndSortOrders(
    orders: Order[], 
    filters: OrderFilters = {}, 
    sort: OrderSortOptions = { field: 'createdAt', direction: 'desc' }
  ): Order[] {
    let filteredOrders = [...orders];
    // Apply filters
    if(filters.status && filters.status.length > 0) {
      filteredOrders = filteredOrders.filter(order => filters.status!.includes(order.status));
    }

    if(filters.type && filters.type.length > 0) {
      filteredOrders = filteredOrders.filter(order => filters.type!.includes(order.type));
    }

    if(filters.priority && filters.priority.length > 0) {
      filteredOrders = filteredOrders.filter(order => filters.priority!.includes(order.priority));
    }

    if(filters.paymentStatus && filters.paymentStatus.length > 0) {
      filteredOrders = filteredOrders.filter(order => filters.paymentStatus!.includes(order.paymentStatus));
    }

    if(filters.customer) {
      const customerQuery = filters.customer.toLowerCase();
      filteredOrders = filteredOrders.filter(order => 
        order.customerName.toLowerCase().includes(customerQuery) ||
        order.customerPhone.includes(customerQuery)
      );
    }

    if(filters.search) {
      const searchQuery = filters.search.toLowerCase();
      filteredOrders = filteredOrders.filter(order =>
        order.orderNumber.toLowerCase().includes(searchQuery) ||
        order.customerName.toLowerCase().includes(searchQuery) ||
        order.customerPhone.includes(searchQuery) ||
        order.notes?.toLowerCase().includes(searchQuery) ||
        order.items.some(item => item.garmentType.toLowerCase().includes(searchQuery))
      );
    }

    if(filters.dateRange) {
      const startDate = new Date(filters.dateRange.start);
      const endDate = new Date(filters.dateRange.end);
      filteredOrders = filteredOrders.filter(order => {
        const orderDate = new Date(order.createdAt);
        return orderDate >= startDate && orderDate <= endDate;
      });
    }

    // Apply sorting
    filteredOrders.sort((a, b) => {
      let aValue: unknown = a[sort.field];
    let bValue: unknown = b[sort.field];
      // Handle date fields
      if(sort.field === 'createdAt' || sort.field === 'dueDate') {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      }

      // Handle string fields
      if(typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if(sort.direction === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    return filteredOrders;
  }

  /**
   * Calculate order statistics
   */
  static calculateStats(orders: Order[]): OrderStats {
    const activeOrders = orders.filter(order => !order.isArchived);

    const stats: OrderStats = {
      total: activeOrders.length,
      byStatus: {} as Record<OrderStatus, number>,
      byType: {} as Record<OrderType, number>,
      byPriority: {} as Record<Priority, number>,
      revenue: {
        total: 0,
        paid: 0,
        pending: 0
      },
      averageOrderValue: 0,
      completionRate: 0
    };

    // Initialize counters
    const statusCounts = {} as Record<string, number>;
    const typeCounts = {} as Record<string, number>;
    const priorityCounts = {} as Record<string, number>;

    let totalRevenue = 0;
    let paidRevenue = 0;
    let completedOrders = 0;

    activeOrders.forEach(order => {
      // Count by status
      statusCounts[order.status] = (statusCounts[order.status] || 0) + 1;

      // Count by type
      typeCounts[order.type] = (typeCounts[order.type] || 0) + 1;

      // Count by priority
      priorityCounts[order.priority] = (priorityCounts[order.priority] || 0) + 1;

      // Revenue calculations
      totalRevenue += order.total;
      paidRevenue += order.paidAmount;

      // Completion rate
      if(order.status === 'completed') {
        completedOrders++;
      }
    });

    stats.byStatus = statusCounts as Record<OrderStatus, number>;
    stats.byType = typeCounts as Record<OrderType, number>;
    stats.byPriority = priorityCounts as Record<Priority, number>;

    stats.revenue.total = totalRevenue;
    stats.revenue.paid = paidRevenue;
    stats.revenue.pending = totalRevenue - paidRevenue;

    stats.averageOrderValue = activeOrders.length > 0 ? totalRevenue / activeOrders.length : 0;
    stats.completionRate = activeOrders.length > 0 ? (completedOrders / activeOrders.length) * 100 : 0;

    return stats;
  }

  /**
   * Get orders due soon (within next 7 days)
   */
  static getOrdersDueSoon(orders: Order[]): Order[] {
    const sevenDaysFromNow = new Date();
    sevenDaysFromNow.setDate(sevenDaysFromNow.getDate() + 7);

    return orders.filter(order => {
      if(order.isArchived || order.status === 'completed' || order.status === 'cancelled') {
        return false;
      }

      const dueDate = new Date(order.dueDate);
      return dueDate <= sevenDaysFromNow;
    });
  }

  /**
   * Get overdue orders
   */
  static getOverdueOrders(orders: Order[]): Order[] {
    const today = new Date();
    today.setHours(23, 59, 59, 999);

    return orders.filter(order => {
      if(order.isArchived || order.status === 'completed' || order.status === 'cancelled') {
        return false;
      }

      const dueDate = new Date(order.dueDate);
      return dueDate < today;
    });
  }

  /**
   * Clear all orders (for testing/reset)
   */
  static async clearAllOrders(): Promise<void> {
    try {
      await AsyncStorage.removeItem(this.STORAGE_KEY);
      await AsyncStorage.removeItem(this.COUNTER_KEY);
    } catch (error) {
      console.error('Error clearing orders:', error);
      throw error;
    }
  }
}
