// REAL PDF PRINT SERVICE - Professional PDF generation and printing
import { Alert, Share, Platform   } from 'react-native';

// Safely import print modules with error handling
let Print = null;
let Sharing = null;
let FileSystem = null;
let printAvailable = false;

try {
  Print = require('expo-print');
  Sharing = require('expo-sharing');
  FileSystem = require('expo-file-system');
  printAvailable = true;
} catch (error) {

  printAvailable = false;
}

class PrintService {
  constructor() {
    this.isInitialized = false;
  }

  // Generate invoice data for an order
  generateInvoiceData(order, customer) {
    try {
      const invoiceData = {
        // Invoice header
        invoiceNumber: `INV-${order.id.toString().padStart(4, '0')}`,
        invoiceDate: new Date().toLocaleDateString(),
        dueDate: order.due_date ? new Date(order.due_date).toLocaleDateString() : 'N/A',
        
        // Business details
        business: {
          name: '<PERSON><PERSON>',
          address: '123 Fashion Street, Tailor City',
          phone: '+91 98765 43210',
          email: '<EMAIL>',
        },
        
        // Customer details
        customer: {
          name: customer?.name || order.customer_name,
          phone: customer?.phone || 'N/A',
          email: customer?.email || 'N/A',
          address: customer?.address || 'N/A',
        },
        
        // Order details
        order: {
          id: order.id,
          garmentType: order.garment_type,
          quantity: order.quantity || 1,
          status: order.status,
          notes: order.notes || '',
        },
        
        // Financial details
        financial: {
          subtotal: order.amount,
          tax: 0, // No tax for now
          discount: 0, // No discount for now
          total: order.amount,
          advancePaid: order.advance_paid || 0,
          balance: order.amount - (order.advance_paid || 0),
        },
        
        // Measurements (if available)
        measurements: order.measurements || {},
        
        // Footer
        footer: {
          terms: 'Payment due within 30 days. Thank you for your business!',
          signature: 'Authorized Signature',
        },
      };
      
      return invoiceData;
    } catch (error) {
      console.error('Failed to generate invoice data:', error);
      return null;
    }
  }

  // Generate HTML invoice template
  generateInvoiceHTML(invoiceData) {
    try {
      const html = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>Invoice ${invoiceData.invoiceNumber}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; color: #333; }
            .header { text-align: center; border-bottom: 2px solid #6366f1; padding-bottom: 20px; margin-bottom: 30px; }
            .business-name { font-size: 28px; font-weight: bold; color: #6366f1; margin-bottom: 10px; }
            .invoice-title { font-size: 24px; font-weight: bold; margin: 20px 0; }
            .invoice-info { display: flex; justify-content: space-between; margin-bottom: 30px; }
            .section { margin-bottom: 20px; }
            .section-title { font-size: 16px; font-weight: bold; color: #6366f1; margin-bottom: 10px; border-bottom: 1px solid #e5e7eb; padding-bottom: 5px; }
            .details-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px; }
            .detail-item { margin-bottom: 8px; }
            .detail-label { font-weight: bold; color: #6b7280; }
            .detail-value { margin-left: 10px; }
            .order-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
            .order-table th, .order-table td { border: 1px solid #e5e7eb; padding: 12px; text-align: left; }
            .order-table th { background-color: #f9fafb; font-weight: bold; }
            .financial-summary { background-color: #f9fafb; padding: 20px; border-radius: 8px; margin: 20px 0; }
            .financial-row { display: flex; justify-content: space-between; margin-bottom: 8px; }
            .financial-total { font-weight: bold; font-size: 18px; border-top: 2px solid #6366f1; padding-top: 10px; margin-top: 10px; }
            .footer { margin-top: 40px; text-align: center; color: #6b7280; font-size: 14px; }
            .signature-section { margin-top: 40px; text-align: right; }
            @media print { body { margin: 0; }`}
          </style>
        </head>
        <body>
          <!-- Header -->
          <div class="header">
            <div class="business-name">${invoiceData.business.name}</div>
            <div>${invoiceData.business.address}</div>
            <div>Phone: ${invoiceData.business.phone} | Email: ${invoiceData.business.email}</div>
          </div>

          <!-- Invoice Title and Info -->
          <div class="invoice-info">
            <div>
              <div class="invoice-title">INVOICE</div>
              <div><strong>Invoice #:</strong> ${invoiceData.invoiceNumber}</div>
              <div><strong>Date:</strong> ${invoiceData.invoiceDate}</div>
              <div><strong>Due Date:</strong> ${invoiceData.dueDate}</div>
            </div>
            <div>
              <div><strong>Order #:</strong> ${invoiceData.order.id}</div>
              <div><strong>Status:</strong> ${invoiceData.order.status}</div>
            </div>
          </div>

          <!-- Customer and Order Details -->
          <div class="details-grid">
            <div class="section">
              <div class="section-title">Bill To:</div>
              <div class="detail-item">
                <span class="detail-label">Name:</span>
                <span class="detail-value">${invoiceData.customer.name}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">Phone:</span>
                <span class="detail-value">${invoiceData.customer.phone}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">Email:</span>
                <span class="detail-value">${invoiceData.customer.email}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">Address:</span>
                <span class="detail-value">${invoiceData.customer.address}</span>
              </div>
            </div>

            <div class="section">
              <div class="section-title">Order Details:</div>
              <div class="detail-item">
                <span class="detail-label">Garment:</span>
                <span class="detail-value">${invoiceData.order.garmentType}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">Quantity:</span>
                <span class="detail-value">${invoiceData.order.quantity}</span>
              </div>
              ${invoiceData.order.notes ? `
              <div class="detail-item">
                <span class="detail-label">Notes:</span>
                <span class="detail-value">${invoiceData.order.notes}</span>
              </div>
` : ''}
            </div>
          </div>

          <!-- Order Summary Table -->
          <table class="order-table">
            <thead>
              <tr>
                <th>Description</th>
                <th>Quantity</th>
                <th>Unit Price</th>
                <th>Total</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>${invoiceData.order.garmentType}</td>
                <td>${invoiceData.order.quantity}</td>
                <td>₹${(invoiceData.financial.subtotal / invoiceData.order.quantity).toLocaleString()}</td>
                <td>₹${invoiceData.financial.subtotal.toLocaleString()}</td>
              </tr>
            </tbody>
          </table>

          <!-- Financial Summary -->
          <div class="financial-summary">
            <div class="financial-row">
              <span>Subtotal:</span>
              <span>₹${invoiceData.financial.subtotal.toLocaleString()}</span>
            </div>
            <div class="financial-row">
              <span>Tax:</span>
              <span>₹${invoiceData.financial.tax.toLocaleString()}</span>
            </div>
            <div class="financial-row">
              <span>Discount:</span>
              <span>₹${invoiceData.financial.discount.toLocaleString()}</span>
            </div>
            <div class="financial-row financial-total">
              <span>Total:</span>
              <span>₹${invoiceData.financial.total.toLocaleString()}</span>
            </div>
            <div class="financial-row">
              <span>Advance Paid:</span>
              <span>₹${invoiceData.financial.advancePaid.toLocaleString()}</span>
            </div>
            <div class="financial-row" style="color: ${invoiceData.financial.balance > 0 ? '#ef4444' : '#10b981'};">
              <span>Balance Due:</span>
              <span>₹${invoiceData.financial.balance.toLocaleString()}</span>
            </div>
          </div>

          <!-- Footer -->
          <div class="footer">
            <div>${invoiceData.footer.terms}</div>
          </div>

          <!-- Signature -->
          <div class="signature-section">
            <div style="margin-top: 60px; border-top: 1px solid #000; width: 200px; margin-left: auto;">
              ${invoiceData.footer.signature}
            </div>
          </div>
        </body>
        </html>
      `;
      
      return html;
    } catch (error) {
      console.error('Failed to generate invoice HTML:', error);
      return null;
    }
  }

  // Print invoice with real PDF generation
  async printInvoice($3) {
    try {
      const invoiceData = this.generateInvoiceData(order, customer);
      if(!invoiceData) {
        throw new Error('Failed to generate invoice data');
      }

      const html = this.generateInvoiceHTML(invoiceData);
      if(!html) {
        throw new Error('Failed to generate invoice HTML');
      }

      // Check if printing is available
      let isAvailable = false;
      if(printAvailable && Print && Print.isAvailableAsync) {
        try {
          isAvailable = await Print.isAvailableAsync();
        } catch (error) {

          isAvailable = false;
        }
      }

      if(isAvailable) {
        // Show options for real PDF generation
        Alert.alert(
          'Invoice Options',
          `Invoice ${invoiceData.invoiceNumber} is ready.\n\nOrder: ${order.garment_type}\nCustomer: ${order.customer_name}\nAmount: ₹${order.amount.toLocaleString()}`,
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Share PDF', onPress: () => this.generateAndSharePDF(html, invoiceData) },
            { text: 'Print', onPress: () => this.printPDF(html, invoiceData) },
          ]
        );
      } else {
        // Fallback to sharing text
        Alert.alert(
          'Print Invoice',
          `Printing not available. Invoice ${invoiceData.invoiceNumber} can be shared instead.\n\nOrder: ${order.garment_type}\nCustomer: ${order.customer_name}\nAmount: ₹${order.amount.toLocaleString()}`,
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Share', onPress: () => this.shareInvoice(invoiceData) },
          ]
        );
      }

      return true;
    } catch (error) {
      console.error('Failed to print invoice:', error);
      Alert.alert('Print Error', 'Failed to prepare invoice for printing');
      return false;
    }
  }

  // Generate and share PDF
  async generateAndSharePDF($3) {
    try {

      if(!printAvailable || !Print || !Print.printToFileAsync) {
        throw new Error('PDF generation not available');
      }

      // Generate PDF from HTML
      const { uri  } = await Print.printToFileAsync({
        html: html,
        base64: false,
      });

      // Check if sharing is available
      let isAvailable = false;
      if(Sharing && Sharing.isAvailableAsync) {
        try {
          isAvailable = await Sharing.isAvailableAsync();
        } catch (error) {

          isAvailable = false;
        }
      }

      if(isAvailable) {
        // Share the PDF file
        await Sharing.shareAsync(uri, {
          mimeType: 'application/pdf',
          dialogTitle: `Invoice ${invoiceData.invoiceNumber}`,
          UTI: 'com.adobe.pdf',
        });

        Alert.alert('Success', 'Invoice PDF has been shared successfully!');
      } else {
        // Fallback to text sharing
        await this.shareInvoice(invoiceData);
      }

      return true;
    } catch (error) {
      console.error('Failed to generate/share PDF:', error);
      Alert.alert('PDF Error', 'Failed to generate PDF. Falling back to text sharing.');
      // Fallback to text sharing
      await this.shareInvoice(invoiceData);
      return false;
    }
  }

  // Print PDF directly
  async printPDF($3) {
    try {

      if(!printAvailable || !Print || !Print.printAsync) {
        throw new Error('Printing not available');
      }

      // Print directly
      await Print.printAsync({
        html: html,
        printerUrl: undefined, // Let user select printer
      });

      Alert.alert('Success', 'Invoice has been sent to printer!');
      return true;
    } catch (error) {
      console.error('Failed to print PDF:', error);
      Alert.alert('Print Error', 'Printing not available in this environment. You can try sharing the PDF instead.');
      return false;
    }
  }

  // Share invoice data (fallback)
  async shareInvoice(invoiceData) {
    try {
      const shareContent = `
Invoice: ${invoiceData.invoiceNumber}
Date: ${invoiceData.invoiceDate}

Customer: ${invoiceData.customer.name}
Order: ${invoiceData.order.garmentType}
Amount: ₹${invoiceData.financial.total.toLocaleString()}
Balance: ₹${invoiceData.financial.balance.toLocaleString()}

Generated by Tailora
      `.trim();

      await Share.share({
        message: shareContent,
        title: `Invoice ${invoiceData.invoiceNumber}`,
      });

      return true;
    } catch (error) {
      console.error('Failed to share invoice:', error);
      Alert.alert('Share Error', 'Failed to share invoice');
      return false;
    }
  }

  // Generate receipt for payment
  async generateReceipt(order, paymentAmount, paymentMethod) {
    try {
      const receiptData = {
        receiptNumber: `RCP-${Date.now()}`,
        date: new Date().toLocaleDateString(),
        time: new Date().toLocaleTimeString(),
        order: order,
        payment: {
          amount: paymentAmount,
          method: paymentMethod,
        },
      };

      // Check if PDF generation is available
      let isAvailable = false;
      if(printAvailable && Print && Print.isAvailableAsync) {
        try {
          isAvailable = await Print.isAvailableAsync();
        } catch (error) {

          isAvailable = false;
        }
      }

      if(isAvailable) {
        Alert.alert(
          'Payment Receipt',
          `Receipt: ${receiptData.receiptNumber}\nDate: ${receiptData.date}\n\nOrder #${order.id}\nPayment: ₹${paymentAmount.toLocaleString()}\nMethod: ${paymentMethod}\n\nThank you for your payment!`,
          [
            { text: 'OK' },
            { text: 'Share Text', onPress: () => this.shareReceipt(receiptData) },
            { text: 'Share PDF', onPress: () => this.generateReceiptPDF(receiptData) },
          ]
        );
      } else {
        Alert.alert(
          'Payment Receipt',
          `Receipt: ${receiptData.receiptNumber}\nDate: ${receiptData.date}\n\nOrder #${order.id}\nPayment: ₹${paymentAmount.toLocaleString()}\nMethod: ${paymentMethod}\n\nThank you for your payment!`,
          [
            { text: 'OK' },
            { text: 'Share', onPress: () => this.shareReceipt(receiptData) },
          ]
        );
      }

      return receiptData;
    } catch (error) {
      console.error('Failed to generate receipt:', error);
      Alert.alert('Receipt Error', 'Failed to generate receipt');
      return null;
    }
  }

  // Generate receipt PDF
  async generateReceiptPDF(receiptData) {
    try {

      // Generate simple receipt HTML
      const receiptHTML = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>Receipt ${receiptData.receiptNumber}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; color: #333; text-align: center; }
            .header { border-bottom: 2px solid #6366f1; padding-bottom: 20px; margin-bottom: 30px; }
            .business-name { font-size: 24px; font-weight: bold; color: #6366f1; margin-bottom: 10px; }
            .receipt-title { font-size: 20px; font-weight: bold; margin: 20px 0; }
            .receipt-info { text-align: left; max-width: 400px; margin: 0 auto; }
            .info-row { display: flex; justify-content: space-between; margin-bottom: 10px; padding: 8px 0; border-bottom: 1px dotted #ccc; }
            .info-label { font-weight: bold; }
            .total-row { font-size: 18px; font-weight: bold; color: #6366f1; border-top: 2px solid #6366f1; padding-top: 10px; margin-top: 20px; }
            .footer { margin-top: 40px; color: #6b7280; font-size: 14px;`}
          </style>
        </head>
        <body>
          <div class="header">
            <div class="business-name">Tailora</div>
            <div>Payment Receipt</div>
          </div>

          <div class="receipt-title">RECEIPT</div>

          <div class="receipt-info">
            <div class="info-row">
              <span class="info-label">Receipt #:</span>
              <span>${receiptData.receiptNumber}</span>
            </div>
            <div class="info-row">
              <span class="info-label">Date:</span>
              <span>${receiptData.date}</span>
            </div>
            <div class="info-row">
              <span class="info-label">Time:</span>
              <span>${receiptData.time}</span>
            </div>
            <div class="info-row">
              <span class="info-label">Order #:</span>
              <span>${receiptData.order.id}</span>
            </div>
            <div class="info-row">
              <span class="info-label">Customer:</span>
              <span>${receiptData.order.customer_name}</span>
            </div>
            <div class="info-row">
              <span class="info-label">Payment Method:</span>
              <span>${receiptData.payment.method}</span>
            </div>
            <div class="info-row total-row">
              <span class="info-label">Amount Paid:</span>
              <span>₹${receiptData.payment.amount.toLocaleString()}</span>
            </div>
          </div>

          <div class="footer">
            <div>Thank you for your payment!</div>
            <div>Generated by Tailora</div>
          </div>
        </body>
        </html>
      `;

      if(!printAvailable || !Print || !Print.printToFileAsync) {
        throw new Error('PDF generation not available');
      }

      // Generate PDF
      const { uri  } = await Print.printToFileAsync({
        html: receiptHTML,
        base64: false,
      });

      // Share the PDF
      let isAvailable = false;
      if(Sharing && Sharing.isAvailableAsync) {
        try {
          isAvailable = await Sharing.isAvailableAsync();
        } catch (error) {

          isAvailable = false;
        }
      }
      if(isAvailable) {
        await Sharing.shareAsync(uri, {
          mimeType: 'application/pdf',
          dialogTitle: `Receipt ${receiptData.receiptNumber}`,
          UTI: 'com.adobe.pdf',
        });

        Alert.alert('Success', 'Receipt PDF has been shared successfully!');
      } else {
        // Fallback to text sharing
        await this.shareReceipt(receiptData);
      }

      return true;
    } catch (error) {
      console.error('Failed to generate receipt PDF:', error);
      Alert.alert('PDF Error', 'Failed to generate receipt PDF. Falling back to text sharing.');
      await this.shareReceipt(receiptData);
      return false;
    }
  }

  // Share receipt (text fallback)
  async shareReceipt(receiptData) {
    try {
      const shareContent = `
Payment Receipt: ${receiptData.receiptNumber}
Date: ${receiptData.date} ${receiptData.time}

Order #${receiptData.order.id}
Customer: ${receiptData.order.customer_name}
Payment: ₹${receiptData.payment.amount.toLocaleString()}
Method: ${receiptData.payment.method}

Thank you for your payment!
Generated by Tailora
      `.trim();

      await Share.share({
        message: shareContent,
        title: `Receipt ${receiptData.receiptNumber}`,
      });

      return true;
    } catch (error) {
      console.error('Failed to share receipt:', error);
      return false;
    }
  }

  // Check if PDF generation is available
  async isPDFGenerationAvailable($3) {
    try {
      return await Print.isAvailableAsync();
    } catch (error) {

      return false;
    }
  }

  // Get PDF capabilities info
  async getPDFCapabilities($3) {
    try {
      const isAvailable = await Print.isAvailableAsync();
      const canShare = await Sharing.isAvailableAsync();

      return { canGeneratePDF: isAvailable,
        canShareFiles: canShare,
        platform: Platform.OS,
      };
    } catch (error) {
      console.error('Failed to get PDF capabilities:', error);
      return { canGeneratePDF: false,
        canShareFiles: false,
        platform: Platform.OS,
      };
    }
  }
}

// Export singleton instance
export default new PrintService();
