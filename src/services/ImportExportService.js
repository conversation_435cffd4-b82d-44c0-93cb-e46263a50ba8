/**
 * ImportExportService - Complete import/export functionality for tailoring business data
 */

import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import * as DocumentPicker from 'expo-document-picker';

export class ImportExportService {
  
  /**
   * Export all business data to JSON file
   */
  static async exportToJSON($3) {
    try {
      const timestamp = new Date().toISOString().split('T')[0];
      const defaultFilename = `tailoring_backup_${timestamp}.json ;
      const finalFilename = filename || defaultFilename;

      // Prepare export data with metadata
      const exportData = {
        metadata: {
          exportDate: new Date().toISOString(),
          version: '1.0.0',
          appName: 'Elite Tailoring Management',
          dataTypes: ['customers', 'orders', 'garments', 'measurements', 'fabrics', 'settings'],
        data: {
          customers: data.customers || [],
          orders: data.orders || [],
          garments: data.garments || data.products || [],
          measurements: data.measurements || [],
          fabrics: data.fabrics || [],
          settings: data.settings || {}
        },
        statistics: {
          totalCustomers: (data.customers || []).length,
          totalOrders: (data.orders || []).length,
          totalGarments: (data.garments || data.products || []).length,
          totalMeasurements: (data.measurements || []).length,
          totalFabrics: (data.fabrics || []).length
        }
      };

      // Convert to JSON string with formatting
      const jsonString = JSON.stringify(exportData, null, 2);

      // Create file path
      const fileUri = FileSystem.documentDirectory + finalFilename;

      // Write file
      await FileSystem.writeAsStringAsync(fileUri, jsonString, {
        encoding: FileSystem.EncodingType.UTF8,
      });

      // Share the file
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(fileUri, {
          mimeType: 'application/json',
          dialogTitle: 'Export Tailoring Data',
        });
      }

      return { success: true,
        filePath: fileUri,
        filename: finalFilename,
        size: jsonString.length,
        recordCount: exportData.statistics
        };

    } catch (error) {
      console.error('Export error: ', error);
      throw new Error(`Export failed: ${error.message} );`
    }
  }

  /**
   * Export data to CSV format
   */
  static async exportToCSV($3) {
    try {
      const timestamp = new Date().toISOString().split('T')[0];
      let csvContent = '';
      let filename = '';

      switch(dataType) {
        case 'customers':
          filename = `customers_${timestamp}.csv ;
          csvContent = this.generateCustomersCSV(data.customers || []);
          break;
        case 'orders':
          filename = `orders_${timestamp}.csv`;
          csvContent = this.generateOrdersCSV(data.orders || []);
          break;
        case 'garments':
          filename = `garments_${timestamp}.csv ;
          csvContent = this.generateGarmentsCSV(data.garments || data.products || []);
          break;
        default:
          throw new Error('Unsupported data type for CSV export');
      }

      const fileUri = FileSystem.documentDirectory + filename;
      await FileSystem.writeAsStringAsync(fileUri, csvContent, {
        encoding: FileSystem.EncodingType.UTF8,
      });

      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(fileUri, {
          mimeType: 'text/csv',
          dialogTitle: `Export ${dataType} to CSV`,
        });
      }

      return { success: true,
        filePath: fileUri,
        filename: filename
        };

    } catch (error) {
      console.error('CSV Export error: ', error);
      throw new Error(`CSV Export failed: ${error.message} );
    }
  }

  /**
   * Import data from JSON file
   */
  static async importFromJSON($3) {
    try {
      // Pick document
      const result = await DocumentPicker.getDocumentAsync({
        type: 'application/json',
        copyToCacheDirectory: true,
      });

      if(result.canceled) {
        return { success: false, message: 'Import cancelled'   };
      }

      // Read file content
      const fileContent = await FileSystem.readAsStringAsync(result.assets[0].uri, {
        encoding: FileSystem.EncodingType.UTF8,
      });

      // Parse JSON
      const importedData = JSON.parse(fileContent);

      // Validate data structure
      const validationResult = this.validateImportData(importedData);
      if(!validationResult.isValid) {
        throw new Error(`Invalid data format: ${validationResult.errors.join(', ')} );`
      }

      return { success: true,
        data: importedData.data || importedData, // Support both new and legacy formats
        metadata: importedData.metadata,
        filename: result.assets[0].name
        };

    } catch (error) {
      console.error('Import error: ', error);
      throw new Error(`Import failed: ${error.message} );
    }
  }

  /**
   * Generate CSV content for customers
   */
  static generateCustomersCSV(customers) {
    const headers = ['ID', 'Name', 'Email', 'Phone', 'Address', 'VIP Status', 'Created Date'];
    const csvRows = [headers.join(',')];

    customers.forEach(customer => {
      const row = [
        customer.id || '',
        `"${customer.name || ''}"`,
        customer.email || '',
        customer.phone || '',
        `"${customer.address || ''}" ,
        customer.isVIP ? 'Yes' : 'No',
        customer.createdAt || ''
      ];
      csvRows.push(row.join(','));
    });

    return csvRows.join('\n');
  }

  /**
   * Generate CSV content for orders
   */
  static generateOrdersCSV(orders) {
    const headers = ['Order ID', 'Customer', 'Garment Type', 'Price', 'Status', 'Due Date', 'Created Date'];
    const csvRows = [headers.join(',')];

    orders.forEach(order => {
      const row = [
        order.id || '',
        `"${order.customerName || order.customer || ''}"`,
        `"${order.garmentType || order.orderType || ''}" ,
        order.price || order.total || 0,
        order.status || '',
        order.dueDate || order.deliveryDate || '',
        order.createdAt || order.date || ''
      ];
      csvRows.push(row.join(','));
    });

    return csvRows.join('\n');
  }

  /**
   * Generate CSV content for garments
   */
  static generateGarmentsCSV(garments) {
    const headers = ['ID', 'Name', 'Category', 'Price', 'Description', 'Created Date'];
    const csvRows = [headers.join(',')];

    garments.forEach(garment => {
      const row = [
        garment.id || '',
        `"${garment.name || ''}"`,
        `"${garment.category || ''}" ,
        garment.price || 0,
        `"${garment.description || ''}"`,
        garment.createdAt || ''
      ];
      csvRows.push(row.join(','));
    });

    return csvRows.join('\n');
  }

  /**
   * Validate imported data structure
   */
  static validateImportData(data) {
    const errors = [];

    // Check if it's the new format with metadata
    if(data.metadata && data.data) {
      data = data.data;
    }

    // Validate required fields
    if(!data || typeof data !== 'object') {
      errors.push('Invalid data format');
      return { isValid: false, errors   };
    }

    // Check data types
    if (data.customers && !Array.isArray(data.customers)) {
      errors.push('Customers data must be an array');
    }

    if (data.orders && !Array.isArray(data.orders)) {
      errors.push('Orders data must be an array');
    }

    if (data.garments && !Array.isArray(data.garments)) {
      errors.push('Garments data must be an array');
    }

    if (data.products && !Array.isArray(data.products)) {
      errors.push('Products data must be an array');
    }

    return { isValid: errors.length === 0,
      errors
      };
  }

  /**
   * Create sample data file for testing
   */
  static async createSampleData($3) {
    const sampleData = {
      metadata: {
        exportDate: new Date().toISOString(),
        version: '1.0.0',
        appName: 'Elite Tailoring Management - Sample Data',
        dataTypes: ['customers', 'orders', 'garments'],
      data: {
        customers: [
          {
            id: 'sample_customer_1',
            name: 'John Doe',
            email: '<EMAIL>',
            phone: '+880 1700-000001',
            address: 'Dhaka, Bangladesh',
            isVIP: false,
            createdAt: new Date().toISOString()
          },
          {
            id: 'sample_customer_2',
            name: 'Jane Smith',
            email: '<EMAIL>',
            phone: '+880 1700-000002',
            address: 'Chittagong, Bangladesh',
            isVIP: true,
            createdAt: new Date().toISOString()
          }
        ],
        orders: [
          {
            id: 'sample_order_1',
            customerName: 'John Doe',
            garmentType: 'Shirt',
            price: 800,
            status: 'Pending',
            dueDate: '2024-01-15',
            createdAt: new Date().toISOString()
          }
        ],
        garments: [
          {
            id: 'sample_garment_1',
            name: 'Formal Shirt',
            category: 'Men',
            price: 800,
            description: 'Classic formal shirt',
            createdAt: new Date().toISOString()
          }
        ],
        settings: {
          shopName: 'Sample Tailoring Shop',
          currency: 'BDT'
        }
      }
    };

    return this.exportToJSON(sampleData.data, 'sample_tailoring_data.json');
  }
}

export default ImportExportService;
