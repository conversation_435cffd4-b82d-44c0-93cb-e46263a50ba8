/**
 * FontService - System Font Management for MD3 Typography
 * Uses platform-native fonts with proper MD3 scaling and weights
 */

// Remove Platform dependency - use safe defaults

// Platform-specific font families that look great and are always available
const PLATFORM_FONTS = {
  ios: {
    thin: 'HelveticaNeue-Thin',
    light: 'HelveticaNeue-Light',
    regular: 'HelveticaNeue',
    medium: 'HelveticaNeue-Medium',
    bold: 'HelveticaNeue-Bold',
    black: 'HelveticaNeue-CondensedBlack'},
  android: {
    thin: 'sans-serif-thin',
    light: 'sans-serif-light',
    regular: 'sans-serif',
    medium: 'sans-serif-medium',
    bold: 'sans-serif-bold',
    black: 'sans-serif-black'},
  default: {
    thin: 'System',
    light: 'System',
    regular: 'System',
    medium: 'System',
    bold: 'System',
    black: 'System'}
};

class FontService {
  constructor() {
    this.fontsLoaded = true; // System fonts are always available
    // Use Android fonts as safe default (works on all platforms)
    this.platformFonts = PLATFORM_FONTS.android;
  }

  /**
   * No loading needed for system fonts - always ready
   */
  async loadFonts($3) {

    return true;
  }

  /**
   * Check if fonts are loaded (always true for system fonts)
   */
  areFontsLoaded() {
    return true;
  }

  /**
   * Get platform-appropriate font family for MD3 typography
   */
  getFontFamily(weight = '400') {
    const weightMap = {
      '100': this.platformFonts.thin,
      '300': this.platformFonts.light,
      '400': this.platformFonts.regular,
      '500': this.platformFonts.medium,
      '700': this.platformFonts.bold,
      '900': this.platformFonts.black};

    return weightMap[weight] || this.platformFonts.regular;
  }

  /**
   * Get MD3 typography styles with platform-optimized fonts
   */
  getTypographyStyles() {
    return {
      displayLarge: {
        fontFamily: this.getFontFamily('400'),
        fontSize: 57,
        lineHeight: 64,
        letterSpacing: -0.25,
        fontWeight: '400'},
      displayMedium: {
        fontFamily: this.getFontFamily('400'),
        fontSize: 45,
        lineHeight: 52,
        letterSpacing: 0,
        fontWeight: '400'},
      displaySmall: {
        fontFamily: this.getFontFamily('400'),
        fontSize: 36,
        lineHeight: 44,
        letterSpacing: 0,
        fontWeight: '400'},
      headlineLarge: {
        fontFamily: this.getFontFamily('400'),
        fontSize: 32,
        lineHeight: 40,
        letterSpacing: 0,
        fontWeight: '400'},
      headlineMedium: {
        fontFamily: this.getFontFamily('400'),
        fontSize: 28,
        lineHeight: 36,
        letterSpacing: 0,
        fontWeight: '400'},
      headlineSmall: {
        fontFamily: this.getFontFamily('400'),
        fontSize: 24,
        lineHeight: 32,
        letterSpacing: 0,
        fontWeight: '400'},
      titleLarge: {
        fontFamily: this.getFontFamily('500'),
        fontSize: 22,
        lineHeight: 28,
        letterSpacing: 0,
        fontWeight: '500'},
      titleMedium: {
        fontFamily: this.getFontFamily('500'),
        fontSize: 16,
        lineHeight: 24,
        letterSpacing: 0.15,
        fontWeight: '500'},
      titleSmall: {
        fontFamily: this.getFontFamily('500'),
        fontSize: 14,
        lineHeight: 20,
        letterSpacing: 0.1,
        fontWeight: '500'},
      labelLarge: {
        fontFamily: this.getFontFamily('500'),
        fontSize: 14,
        lineHeight: 20,
        letterSpacing: 0.1,
        fontWeight: '500'},
      labelMedium: {
        fontFamily: this.getFontFamily('500'),
        fontSize: 12,
        lineHeight: 16,
        letterSpacing: 0.5,
        fontWeight: '500'},
      labelSmall: {
        fontFamily: this.getFontFamily('500'),
        fontSize: 11,
        lineHeight: 16,
        letterSpacing: 0.5,
        fontWeight: '500'},
      bodyLarge: {
        fontFamily: this.getFontFamily('400'),
        fontSize: 16,
        lineHeight: 24,
        letterSpacing: 0.15,
        fontWeight: '400'},
      bodyMedium: {
        fontFamily: this.getFontFamily('400'),
        fontSize: 14,
        lineHeight: 20,
        letterSpacing: 0.25,
        fontWeight: '400'},
      bodySmall: {
        fontFamily: this.getFontFamily('400'),
        fontSize: 12,
        lineHeight: 16,
        letterSpacing: 0.4,
        fontWeight: '400'}};
  }
}

export default new FontService();
