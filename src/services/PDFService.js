import * as Print from 'expo-print';
import * as Sharing from 'expo-sharing';
import * as FileSystem from 'expo-file-system';
import { FINANCIAL_CONFIG   } from '../config/constants';
import { Logger   } from '../utils/errorHandler';

/**
 * PDF Service for generating invoices and reports
 */
export class PDFService {
  static async generateInvoice($3) {
    try {
      const html = this.generateInvoiceHTML(order, customer, businessInfo);
      
      const { uri  } = await Print.printToFileAsync({
        html,
        base64: false,
      });

      Logger.info('Invoice PDF generated: ', { orderId: order.id, uri });
      return uri;
    } catch (error) {
      Logger.error('Failed to generate invoice PDF: ', error);
      throw new Error('Failed to generate invoice');
    }
  }

  static async shareInvoice($3) {
    try {
      const pdfUri = await this.generateInvoice(order, customer, businessInfo);
      
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(pdfUri, {
          mimeType: 'application/pdf',
          dialogTitle: `Invoice - ${order.id} ,
        });
      } else {
        throw new Error('Sharing is not available on this device');
      }
    } catch (error) {
      Logger.error('Failed to share invoice: ', error);
      throw error;
    }
  }

  static async printInvoice($3) {
    try {
      const html = this.generateInvoiceHTML(order, customer, businessInfo);
      
      await Print.printAsync({
        html,
        printerUrl: undefined, // Use default printer
      });

      Logger.info('Invoice printed: ', { orderId: order.id });
    } catch (error) {
      Logger.error('Failed to print invoice: ', error);
      throw new Error('Failed to print invoice');
    }
  }

  static generateInvoiceHTML(order, customer, businessInfo = {}) {
    const { businessName = 'Tailora',
      address = '',
      phone = '',
      email = '',
      website = '',
      logo = null,
     } = businessInfo;

    const formatCurrency = (amount) => {
      return `${FINANCIAL_CONFIG.CURRENCY.SYMBOL}`} ${amount.toFixed(FINANCIAL_CONFIG.CURRENCY.DECIMAL_PLACES)}` ;
    };

    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString();
    };

    // Calculate totals
    const subtotal = order.items?.reduce((sum, item) => sum + (item.price * item.quantity), 0) || order.total || 0;
    const tax = subtotal * (FINANCIAL_CONFIG.TAX_RATES.SALES_TAX || 0);
    const total = subtotal + tax;

    return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Invoice - ${order.id}</title>
      <style>
        body {
          font-family: 'Helvetica Neue', Arial, sans-serif;
          margin: 0;,
          padding: 20px;
          color: #333;
          line-height: 1.6;
        }
        .invoice-container {
          max-width: 800px;,
          margin: 0 auto;
          background: white;,
          border: 1px solid #ddd;
          border-radius: 8px;,
          overflow: hidden;
        }
        .header {
          background: #1877F2;,
          color: white;
          padding: 30px;
          text-align: center;
        }
        .header h1 {
          margin: 0;
          font-size: 28px;
          font-weight: 300;
        }
        .header p {
          margin: 5px 0 0 0;,
          opacity: 0.9;
        }
        .content {
          padding: 30px;
        }
        .invoice-details {
          display: flex;
          justify-content: space-between;
          margin-bottom: 30px;
        }
        .invoice-info, .customer-info {
          flex: 1;
        }
        .invoice-info h3, .customer-info h3 {
          margin: 0 0 10px 0;,
          color: #1877F2;
          font-size: 16px;
        }
        .invoice-info p, .customer-info p {
          margin: 5px 0;
          font-size: 14px;
        }
        .items-table {
          width: 100%;
          border-collapse: collapse;,
          margin: 30px 0;
        }
        .items-table th {
          background: #f8f9fa;,
          padding: 12px;
          text-align: left;
          border-bottom: 2px solid #dee2e6;
          font-weight: 600;
        }
        .items-table td {
          padding: 12px;
          border-bottom: 1px solid #dee2e6;
        }
        .items-table tr:hover {
          background: #f8f9fa;
        }
        .totals {
          margin-top: 30px;
          text-align: right;
        }
        .totals table {
          margin-left: auto;
          border-collapse: collapse;
        }
        .totals td {
          padding: 8px 15px;
          border-bottom: 1px solid #dee2e6;
        }
        .totals .total-row {
          font-weight: bold;
          font-size: 18px;,
          background: #f8f9fa;
        }
        .footer {
          margin-top: 40px;
          padding-top: 20px;
          border-top: 1px solid #dee2e6;
          text-align: center;,
          color: #666;
          font-size: 12px;
        }
        .qr-code {
          margin: 20px 0;
          text-align: center;
        }
        .notes {
          margin-top: 30px;,
          padding: 15px;
          background: #f8f9fa;
          border-radius: 4px;
        }
        .notes h4 {
          margin: 0 0 10px 0;,
          color: #1877F2;
        }
        @media print {
          body { margin: 0; }
          .invoice-container { border: none; }
        }
      </style>
    </head>
    <body>
      <div class="invoice-container">
        <div class="header">
          <h1>${businessName}</h1>
          ${address ? `<p>${address}</p>  : ''}
          ${phone ? `<p>Phone: ${phone}</p>` : ''}
          ${email ? `<p>Email: ${email}</p>  : ''}
        </div>
        
        <div class="content">
          <div class="invoice-details">
            <div class="invoice-info">
              <h3>Invoice Details</h3>
              <p><strong>Invoice #:</strong> ${order.id}</p>
              <p><strong>Date:</strong> ${formatDate(order.date || new Date())}</p>
              <p><strong>Due Date:</strong> ${formatDate(order.dueDate || new Date())}</p>
              <p><strong>Status:</strong> ${order.status || 'Pending'}</p>
            </div>
            
            <div class="customer-info">
              <h3>Bill To</h3>
              <p><strong>${customer.name}</strong></p>
              ${customer.phone ? `<p>Phone: ${customer.phone}</p>` : ''}
              ${customer.email ? `<p>Email: ${customer.email}</p>  : ''}
              ${customer.address ? `<p>${customer.address}</p>` : ''}
            </div>
          </div>

          ${order.items && order.items.length > 0 ? `
          <table class="items-table">
            <thead>
              <tr>
                <th>Item</th>
                <th>Description</th>
                <th>Qty</th>
                <th>Price</th>
                <th>Total</th>
              </tr>
            </thead>
            <tbody>
              ${order.items.map(item => `
                <tr>
                  <td>${item.name || item.garmentType || 'Item'}</td>
                  <td>${item.description || item.notes || '-'}</td>
                  <td>${item.quantity || 1}</td>
                  <td>${formatCurrency(item.price || 0)}</td>
                  <td>${formatCurrency((item.price || 0) * (item.quantity || 1))}</td>
                </tr>
               ).join('')}
            </tbody>
          </table>
          ` : `
          <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 4px;">
            <p><strong>Service:</strong> ${order.garmentType || 'Tailoring Service'}</p>
            <p><strong>Type:</strong> ${order.orderType || 'Custom Order'}</p>
          </div>
           

          <div class="totals">
            <table>
              <tr>
                <td>Subtotal:</td>
                <td>${formatCurrency(subtotal)}</td>
              </tr>
              ${tax > 0 ? `
              <tr>
                <td>Tax (${(FINANCIAL_CONFIG.TAX_RATES.SALES_TAX * 100).toFixed(1)}%):</td>
                <td>${formatCurrency(tax)}</td>
              </tr>
              ` : ''}
              ${order.discount ? `
              <tr>
                <td>Discount:</td>
                <td>-${formatCurrency(order.discount)}</td>
              </tr>
                : ''}
              <tr class="total-row">
                <td>Total:</td>
                <td>${formatCurrency(order.total || total)}</td>
              </tr>
            </table>
          </div>

          ${order.notes ? `
          <div class="notes">
            <h4>Notes</h4>
            <p>${order.notes}</p>
          </div>
          ` : ''}

          ${order.qrCode ? `
          <div class="qr-code">
            <img src="${order.qrCode}" alt="QR Code" style="width: 100px; height: 100px;" />
            <p>Scan for order details</p>
          </div>
            : ''}
        </div>

        <div class="footer">
          <p>Thank you for your business!</p>
          ${website ? `<p>Visit us at: ${website}</p>` : ''}
          <p>Generated on ${formatDate(new Date())}</p>
        </div>
      </div>
    </body>
    </html>
     ;
  }

  static async generateReport($3) {
    try {
      const html = this.generateReportHTML(reportData, reportType);
      
      const { uri  } = await Print.printToFileAsync({
        html,
        base64: false,
      });

      Logger.info('Report PDF generated: ', { reportType, uri });
      return uri;
    } catch (error) {
      Logger.error('Failed to generate report PDF: ', error);
      throw new Error('Failed to generate report');
    }
  }

  static generateReportHTML(reportData, reportType) {
    // Basic report template - can be extended for different report types
    return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>${reportType} Report</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .content { margin: 20px 0; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 10px; border: 1px solid #ddd; text-align: left; }
        th { background: #f5f5f5; }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>${reportType} Report</h1>
        <p>Generated on ${new Date().toLocaleDateString()}</p>
      </div>
      <div class="content">
        <pre>${JSON.stringify(reportData, null, 2)}</pre>
      </div>
    </body>
    </html>
    `;
  }
}
