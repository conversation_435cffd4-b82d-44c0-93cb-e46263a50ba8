/**
 * UnifiedHeader - Single universal header component with layout capabilities
 * Combines CommonHeader and AppLayout functionality into one component
 * Features responsive design and consistent styling across all screens
 */

import React, { useState, useCallback, useMemo } from 'react';
import { View, StyleSheet, Dimensions, TouchableOpacity, ScrollView   } from 'react-native';
// FIXED: Cleaned up and consolidated imports
import { Text, Surface, Avatar   } from 'react-native-paper';
import { useTheme   } from '../context/ThemeContext';
import { useSafeAreaInsets   } from 'react-native-safe-area-context';
import LocalIcon from './LocalIcon';
import { useData   } from '../context/DataContext';
import BottomNavBar from './BottomNavBar';
import navigationService from '../services/NavigationService';
import { SPACING, BORDER_RADIUS, TYPOGRAPHY   } from '../theme/designTokens';

const UnifiedHeader = ({
  // Header props
  title = "Tailora",
  showBackButton = false,
  onBackPress,
  rightComponent,
  // Layout props
  children,
  style, // This is for the main container
  contentStyle,
  fullScreen = false,
  // BottomNavBar props
  navigation,
  currentRoute,
  onPlusPress,
  showBottomNav = true,
}) => {
  const { theme  } = useTheme();
  const insets = useSafeAreaInsets();
  const { state  } = useData();
  const { user  } = useData(); // Assuming user is in data context

  // FIXED: Styles are moved inside and memoized for performance and theme access
  const styles = useMemo(() => createStyles(theme, insets), [theme, insets]);

  const handleProfilePress = useCallback(() => {
    navigationService.navigate('MyProfile');
  }, []);

  const handleNotificationsPress = useCallback(() => {
    navigationService.navigate('Notifications');
  }, []);

  if(fullScreen) {
    return <View style={styles.fullScreenContainer}>{children}</View>;
  }

  const HeaderContent = () => (
    <Surface style={styles.headerContainer} elevation={2}>
      <View style={styles.headerRow}>
        <View style={styles.leftSection}>
          {showBackButton && (
            <IconButton icon="arrow-left" size={24} onPress={onBackPress || navigation.goBack} style={styles.backButton} />
          )}
          <Text variant="headlineSmall" style={styles.title} numberOfLines={1}>{title}</Text>
        </View>
        <View style={styles.rightSection}>
          {rightComponent ? rightComponent : (
            <>
              <TouchableOpacity onPress={handleNotificationsPress}>
                <LocalIcon name="bell" size={24} color={theme.colors.onSurfaceVariant} />
              </TouchableOpacity>
              <TouchableOpacity onPress={handleProfilePress}>
                {user?.avatar ? (
                  <Avatar.Image size={32} source={{ uri: user.avatar }}} style={styles.profileAvatar} />
                ) : (
                  <Avatar.Text size={32} label={user?.name?.substring(0, 2) || 'U'} style={styles.profileAvatar} />
                )}
              </TouchableOpacity>
            </>
          )}
        </View>
      </View>
    </Surface>
  );

  return (
    <View style={[styles.layoutContainer, style]}>
      <HeaderContent />
      <ScrollView contentContainerStyle={[styles.content, contentStyle]} showsVerticalScrollIndicator={false}>
        {children}
      </ScrollView>
      {showBottomNav && (
        <BottomNavBar
          navigation={navigation}
          currentRoute={currentRoute}
          onPlusPress={onPlusPress}
        />
      )}
    </View>
  );
};

// This function creates the stylesheet object, allowing it to use the theme from props.
const createStyles = (theme, insets) => StyleSheet.create({
  layoutContainer: { flex: 1, backgroundColor: theme.colors.background },
  fullScreenContainer: { flex: 1 },
  content: { flexGrow: 1, padding: SPACING.md },
  headerContainer: { backgroundColor: theme.colors.surface, paddingTop: insets.top, borderBottomWidth: 1, borderBottomColor: theme.colors.outlineVariant },
  headerRow: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', paddingHorizontal: SPACING.sm, height: 60 },
  leftSection: { flexDirection: 'row', alignItems: 'center', flex: 1 },
  backButton: { margin: 0 },
  title: { ...TYPOGRAPHY.titleLarge, flexShrink: 1 },
  rightSection: { flexDirection: 'row', alignItems: 'center', gap: SPACING.sm },
  profileAvatar: { borderWidth: 1, borderColor: theme.colors.outlineVariant },
});

// Pre-configured layout variants for common use cases
export const DashboardLayout = (props) => <UnifiedHeader currentRoute="Dashboard" title="Dashboard" {...props} />;
export const ProductsLayout = (props) => <UnifiedHeader currentRoute="Products" title="Products" {...props} />;
export const OrdersLayout = (props) => <UnifiedHeader currentRoute="Orders" title="Orders" {...props} />;
export const ScanLayout = (props) => <UnifiedHeader currentRoute="Scan" title="Scan Code" showBottomNav={false} showSearch={false} {...props} />;
export const FinancialLayout = (props) => <UnifiedHeader currentRoute="Financial" title="Financial" showSearch={false} {...props} />;
export const SettingsLayout = (props) => <UnifiedHeader currentRoute="Settings" title="Settings" showSearch={false} {...props} />;
export const DetailLayout = (props) => <UnifiedHeader showBottomNav={false} showSearch={false} showBackButton={true} {...props} />;
export const ModalLayout = (props) => <UnifiedHeader showBottomNav={false} showSearch={false} {...props} />;

export default UnifiedHeader;
export { UnifiedHeader as CommonHeader   };