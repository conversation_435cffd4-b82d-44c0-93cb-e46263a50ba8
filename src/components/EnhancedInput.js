import React, { useState, useMemo, useCallback } from 'react';
// FIXED: Corrected and cleaned up imports
import { View, TextInput, Text, TouchableOpacity, StyleSheet } from 'react-native';
import ValidationService from '../services/ValidationService';

// ENHANCED INPUT - A custom, light-weight text input component
const EnhancedInput = ({
  label,
  placeholder,
  value,
  onChangeText,
  error,
  helperText,
  leftIcon,
  rightIcon,
  onRightIconPress,
  secureTextEntry,
  keyboardType = 'default',
  multiline = false,
  numberOfLines = 1,
  editable = true,
  style, // This is the container style
  // Validation props
  required = false,
  validationType, // 'phone', 'email', 'number', 'currency', 'name'
  onValidation,
  validateOnBlur = true,
  validateOnChange = false,
  ...props
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [isSecureVisible, setIsSecureVisible] = useState(!secureTextEntry);
  const [validationError, setValidationError] = useState(null);
  const [hasBeenBlurred, setHasBeenBlurred] = useState(false);

  // Memoized validation function
  const validateValue = useCallback((val) => {
    if (!validationType) return null;
    let errorMessage = null;
    if (required) {
      errorMessage = ValidationService.required(val, label || 'Field');
      if (errorMessage) return errorMessage;
    }
    if (val) {
      switch (validationType) {
        case 'email': errorMessage = ValidationService.email(val); break;
        case 'phone': errorMessage = ValidationService.phone(val); break;
        case 'number': errorMessage = ValidationService.number(val); break;
        case 'currency': errorMessage = ValidationService.currency(val); break;
        case 'name': errorMessage = ValidationService.minLength(val, 2, label || 'Name'); break;
      }
    }
    return errorMessage;
  }, [required, validationType, label]);

  // FIXED: Corrected useCallback syntax for all handlers
  const handleFocus = useCallback(() => setIsFocused(true), []);

  const handleBlur = useCallback(() => {
    setIsFocused(false);
    setHasBeenBlurred(true);
    if (validateOnBlur) {
      const err = validateValue(value);
      setValidationError(err);
      if (onValidation) onValidation(err === null, err);
    }
  }, [validateOnBlur, value, onValidation, validateValue]);

  const handleChangeText = useCallback((text) => {
    const cleanedText = validationType ? ValidationService.cleanInput(text, validationType) : text;
    if (onChangeText) onChangeText(cleanedText);
    if (validateOnChange && hasBeenBlurred) {
      const err = validateValue(cleanedText);
      setValidationError(err);
      if (onValidation) onValidation(err === null, err);
    }
  }, [onChangeText, validateOnChange, hasBeenBlurred, onValidation, validationType, validateValue]);

  const toggleSecureEntry = () => setIsSecureVisible(prev => !prev);

  // FIXED: Stylesheet moved inside component and memoized for performance
  const styles = useMemo(() => StyleSheet.create({
    container: { marginBottom: 16 },
    label: { fontSize: 14, fontWeight: '600', color: '#374151', marginBottom: 8 },
    requiredAsterisk: { color: '#ef4444' },
    inputContainer: { flexDirection: 'row', alignItems: 'center', borderWidth: 1.5, borderRadius: 12, paddingHorizontal: 12, transition: 'border-color 0.2s' },
    input: { flex: 1, fontSize: 16, color: '#1f2937', paddingVertical: 12 },
    multilineContainer: { alignItems: 'flex-start', paddingVertical: 8 },
    multilineInput: { textAlignVertical: 'top', minHeight: 80, paddingTop: 12 },
    icon: { fontSize: 20, color: '#9ca3af' },
    leftIcon: { marginRight: 12 },
    rightIcon: { marginLeft: 12 },
    helperText: { fontSize: 12, color: '#6b7280', marginTop: 4, marginLeft: 4 },
    errorText: { color: '#ef4444' },
  }), []);
  
  // Dynamic styles calculated on each render
  const getBorderColor = isFocused ? '#4f46e5' : (error || validationError ? '#ef4444' : '#d1d5db');
  const getBackgroundColor = editable ? '#ffffff' : '#f9fafb';
  const getLabelColor = error || validationError ? styles.errorText : {};

  const renderIcon = (iconName) => {
      const iconMap = { search: '🔍', person: '👤', email: '📧', clear: '✕', check: '✓' };
      return iconMap[iconName] || '❓';
  };

  return (
    <View style={[styles.container, containerStyle}>
      {label && (
        <Text style={[styles.label, getLabelColor}>
          {label}{required && <Text style={styles.requiredAsterisk}> *</Text>}
        </Text>
      )}
      <View style={[styles.inputContainer, { borderColor: getBorderColor, backgroundColor: getBackgroundColor }, !editable && { opacity: 0.7 }, multiline && styles.multilineContainer, style}>
        {leftIcon && <Text style={[styles.icon, styles.leftIcon}>{renderIcon(leftIcon)}</Text>}
        <TextInput
          style={[styles.input, multiline && styles.multilineInput}
          placeholder={placeholder}
          placeholderTextColor="#9ca3af"
          value={value}
          onChangeText={handleChangeText}
          onFocus={handleFocus}
          onBlur={handleBlur}
          secureTextEntry={secureTextEntry && !isSecureVisible}
          keyboardType={keyboardType}
          multiline={multiline}
          numberOfLines={numberOfLines}
          editable={editable}
          underlineColorAndroid="transparent"
          {...props}
        />
        {secureTextEntry ? (
          <TouchableOpacity onPress={toggleSecureEntry} style={styles.rightIcon}>
            <Text style={styles.icon}>{isSecureVisible ? '🙈' : '👁️'}</Text>
          </TouchableOpacity>
        ) : rightIcon && (
          <TouchableOpacity onPress={onRightIconPress} style={styles.rightIcon}>
            <Text style={styles.icon}>{renderIcon(rightIcon)}</Text>
          </TouchableOpacity>
        )}
      </View>
      {(error || validationError || helperText) && (
        <Text style={[styles.helperText, (error || validationError) && styles.errorText}>
          {error || validationError || helperText}
        </Text>
      )}
    </View>
  );
};

export default EnhancedInput;