import React, { useState, useCallback } from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import { Text } from 'react-native-paper';
import { useTheme } from '../context/ThemeContext';
import LocalIcon from './LocalIcon';

const UnifiedStatusPicker = ({
  selectedStatus,
  onStatusChange,
  statusOptions = [],
  title = "Status",
  style,
  disabled = false,
}) => {
  const { theme } = useTheme();
  const [showPicker, setShowPicker] = useState(false);

  const getCurrentOption = () => {
    return statusOptions.find(option => option.value === selectedStatus) || statusOptions[0];
  };

  const handleStatusSelect = useCallback((newStatus) => {
    setShowPicker(false);
    if (onStatusChange && newStatus !== selectedStatus) {
      onStatusChange(newStatus);
    }
  }, [onStatusChange, selectedStatus]);

  if (!statusOptions.length) return null;

  return (
    <View style={styles.container}>
      {title && (
        <Text variant="titleMedium" style={styles.title}>
          {title}
        </Text>
      )}
      
      <TouchableOpacity
        style={[
          styles.selector,
          {
            borderColor: theme.colors.outline,
            backgroundColor: theme.colors.surface,
            opacity: disabled ? 0.6 : 1,
          },
          style
        ]}
        onPress={() => !disabled && setShowPicker(!showPicker)}
        disabled={disabled}
      >
        <View style={styles.selectorContent}>
          <View style={[
            styles.statusIndicator,
            { backgroundColor: getCurrentOption()?.color + '15' || theme.colors.primary + '15' }
          ]}>
            <LocalIcon name={getCurrentOption()?.icon || 'circle'} 
              size={20} 
              color={getCurrentOption()?.color || theme.colors.primary} 
            />
          </View>
          <Text 
            variant="bodyLarge" 
            style={{
              color: theme.colors.onSurface,
              fontWeight: '600',
              flex: 1,
            }}
          >
            {getCurrentOption()?.label || selectedStatus}
          </Text>
          <LocalIcon name={showPicker ? "chevron-up" : "chevron-down"} 
            size={20} 
            color={theme.colors.onSurfaceVariant} 
          />
        </View>
      </TouchableOpacity>

      {showPicker && (
        <View style={[
          styles.options,
          { backgroundColor: theme.colors.surface }
        ]}>
          {statusOptions.map((option) => (
            <TouchableOpacity
              key={option.value}
              style={[
                styles.option,
                {
                  backgroundColor: selectedStatus === option.value 
                    ? option.color + '10' 
                    : 'transparent',
                  borderColor: selectedStatus === option.value 
                    ? option.color 
                    : theme.colors.outline + '30',
                }
              ]}
              onPress={() => handleStatusSelect(option.value)}
            >
              <View style={[
                styles.statusIndicator,
                { backgroundColor: option.color + '15' }
              ]}>
                <LocalIcon name={option.icon} size={18} color={option.color} />
              </View>
              <Text 
                variant="bodyMedium" 
                style={{
                  color: selectedStatus === option.value ? option.color : theme.colors.onSurface,
                  fontWeight: selectedStatus === option.value ? '600' : '400',
                  flex: 1,
                }}
              >
                {option.label}
              </Text>
              {selectedStatus === option.value && (
                <LocalIcon name="check" size={16} color={option.color} />
              )}
            </TouchableOpacity>
          ))}
        </View>
      )}
    </View>
  );
};

export default UnifiedStatusPicker;

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  title: {
    fontWeight: '600',
    marginBottom: 8,
  },
  selector: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 12,
  },
  selectorContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  statusIndicator: {
    width: 32,
    height: 32,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  options: {
    marginTop: 8,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    gap: 12,
  },
});