import React from 'react';
import { View, StyleSheet   } from 'react-native';
import { Text, Card   } from 'react-native-paper';
import { View, StyleSheet   } from 'react-native';

const InventoryStats = ({ stats = {} }) => {
  return (
    <View style={styles.card}>
      <Card style={styles.card}>
        <Card.Content>
          <Text variant="titleMedium">Total Items</Text>
          <Text variant="headlineMedium">{stats.total || 0}</Text>
        </Card.Content>
      </Card>
      <Card style={styles.card}>
        <Card.Content>
          <Text variant="titleMedium">Low Stock</Text>
          <Text variant="headlineMedium">{stats.lowStock || 0}</Text>
        </Card.Content>
      </Card>
    </View>
  );
};






export default React.memo(InventoryStats);

const styles = StyleSheet.create({
  card: {
    margin: 16,
    elevation: 2
},
  container: {
    flexDirection: 'row',
    padding: 16
},
  statCard: {
    flex: 1,
    margin: 8
}
});