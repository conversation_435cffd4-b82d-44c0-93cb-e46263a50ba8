import React, { useCallback } from 'react';
import { View, FlatList, StyleSheet } from 'react-native';
import { List, Card } from 'react-native-paper';

const InventoryList = ({ data = []) => {
  const renderItem = useCallback(({ item }) => (
    <Card style={styles.card}>
      <List.Item
        title={item.name}
        description={item.description}
        left={props => <List.Icon {...props} icon="package" />}
      />
    </Card>
  ), []);

  return (
    <FlatList
      data={data}
      renderItem={renderItem}
      keyExtractor={item => item.id}
      style={styles.card}
    />
  );
};






export default React.memo(InventoryList);

const styles = StyleSheet.create({
  list: {
    flex: 1
},
  card: {
    margin: 8
}
});