import React, { useState, useCallback } from 'react';
import { View, StyleSheet } from 'react-native';
import { Searchbar } from 'react-native-paper';
import { View, StyleSheet } from 'react-native';

const InventoryFilters = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilters, setSelectedFilters] = useState([]);

  const onChangeSearch = useCallback((query) => {
    setSearchQuery(query);
  }, []);

  return (
    <View style={styles.card}>
      <Searchbar
        placeholder="Search..."
        onChangeText={onChangeSearch}
        value={searchQuery}
        style={styles.card}
      />
      <View style={styles.card}>
        {/* Filter chips will be rendered here */}
      </View>
    </View>
  );
};






export default React.memo(InventoryFilters);

const styles = StyleSheet.create({
  card: {
    margin: 16,
    elevation: 2
},
  container: {
    padding: 16
},
  searchbar: {
    marginBottom: 16
},
  filtersRow: {
    flexDirection: 'row',
    flexWrap: 'wrap'
}
});