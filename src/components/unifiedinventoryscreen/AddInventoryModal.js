import React, { useState, useCallback } from 'react';
import { View, StyleSheet   } from 'react-native';
import { Modal, Portal, Text, Button, Card   } from 'react-native-paper';
import { StyleSheet   } from 'react-native';

const AddInventoryModal = ({ visible, onDismiss }) => {
  const handleSubmit = useCallback(() => {
    // Handle form submission
    onDismiss();
  }, [onDismiss]);

  return (
    <Portal>
      <Modal visible={visible} onDismiss={onDismiss} contentContainerStyle={styles.modal}>
        <Card>
          <Card.Content>
            <Text variant="titleLarge">Add Inventory Item</Text>
            {/* Modal content */}
          </Card.Content>
          <Card.Actions>
            <Button onPress={onDismiss}>Cancel</Button>
            <Button mode="contained" onPress={handleSubmit}>Save</Button>
          </Card.Actions>
        </Card>
      </Modal>
    </Portal>
  );
};

const styles = StyleSheet.create({
  card: {
    margin: 16,
    elevation: 2
},
  modal: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20
}
});

export default React.memo(AddInventoryModal);
