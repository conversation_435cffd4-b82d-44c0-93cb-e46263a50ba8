/**
 * UnifiedFilterChips - Consistent filter chips component for all screens
 * Provides horizontal scrollable filter chips with unified styling
 */

import React from 'react';
import { ScrollView, StyleSheet, View   } from 'react-native';
import { Chip   } from 'react-native-paper';
// Assuming useTheme is from your custom context
import { useTheme   } from '../context/ThemeContext'; 

const UnifiedFilterChips = ({
  filters = [],
  selectedFilter,
  onFilterChange,
  style,
  chipStyle,
  showCounts = false,
  data = [],
  countField = 'status',
  horizontal = true,
  contentContainerStyle,
}) => {
  const { theme  } = useTheme();

  const getFilterCount = (filter) => {
    if (!showCounts || !data || data.length === 0) return null;
    if (filter === 'All') return data.length;
    return data.filter(item => item && item[countField] === filter).length;
  };

  const renderChips = () => {
    return filters.map((filter) => {
      const count = getFilterCount(filter);
      const label = count !== null ? `${filter} (${count})` : filter;
      const isSelected = selectedFilter === filter;

      return (
        <Chip
          key={filter}
          selected={isSelected}
          onPress={() => onFilterChange?.(filter)}
          style={[styles.chip, chipStyle}
          mode={isSelected ? 'flat' : 'outlined'}>
          {label}
        </Chip>
      );
    });
  };

  if(!horizontal) {
    return (
      <View style={[styles.verticalContainer, style}>
        {renderChips()}
      </View>
    );
  }

  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      style={[styles.container, style}
      contentContainerStyle={[styles.contentContainer, contentContainerStyle]}>
      {renderChips()}
    </ScrollView>
  );
};

export default React.memo(UnifiedFilterChips);

// FIXED: Corrected syntax errors in the stylesheet
const styles = StyleSheet.create({
  container: {
    flexGrow: 0,
  },
  contentContainer: {
    paddingVertical: 8,
    gap: 8,
  },
  verticalContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  chip: {
    // marginRight is handled by the gap property in the container
  },
});