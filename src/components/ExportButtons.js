/**
 * ExportButtons - Reusable component for PDF and XLSX export functionality
 */

import React, { useCallback, useMemo } from 'react';
import { View, StyleSheet, Alert   } from 'react-native';
import { Button   } from 'react-native-paper';
import { useTheme   } from '../context/ThemeContext';
import LocalIcon from './LocalIcon';

const ExportButtons = ({
  onExportPDF,
  onExportXLSX,
  disabled = false,
  style,
  buttonStyle = 'horizontal' // 'horizontal' or 'vertical'
}) => {
  const { theme  } = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  const handlePDFExport = useCallback(async () => {
    try {
      if(onExportPDF) {
        await onExportPDF();
      } else {
        Alert.alert('Export PDF', 'PDF export functionality will be implemented soon.');
      }
    } catch (error) {
      Alert.alert('Export Error', 'Failed to export PDF. Please try again.');
    }
  }, [onExportPDF]);

  const handleXLSXExport = useCallback(async () => {
    try {
      if(onExportXLSX) {
        await onExportXLSX();
      } else {
        Alert.alert('Export XLSX', 'XLSX export functionality will be implemented soon.');
      }
    } catch (error) {
      Alert.alert('Export Error', 'Failed to export XLSX. Please try again.');
    }
  }, [onExportXLSX]);

  const containerStyle = buttonStyle === 'vertical'
    ? styles.verticalContainer
    : styles.horizontalContainer;

  return (
    <View style={[containerStyle, style}>
      <Button
        mode="outlined"
        onPress={handlePDFExport}
        disabled={disabled}
        icon={({ size, color }) => (
          <LocalIcon name="file-pdf-box" size={16} color={color} />
        )}
        style={[
          buttonStyle === 'vertical' ? styles.verticalButton : styles.horizontalButton,
          { borderColor: theme.colors.outline }
        }
        textColor={theme.colors.onSurfaceVariant}
        compact={true}
        labelStyle={styles.buttonLabel}>
        PDF
      </Button>

      <Button
        mode="outlined"
        onPress={handleXLSXExport}
        disabled={disabled}
        icon={({ size, color }) => (
          <LocalIcon name="file-excel-box" size={16} color={color} />
        )}
        style={[
          buttonStyle === 'vertical' ? styles.verticalButton : styles.horizontalButton,
          { borderColor: theme.colors.outline }
        }
        textColor={theme.colors.onSurfaceVariant}
        compact={true}
        labelStyle={styles.buttonLabel}>
        XLSX
      </Button>
    </View>
  );
};






export default React.memo(ExportButtons);

const createStyles = (theme) => StyleSheet.create({
  horizontalContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12},
  verticalContainer: {
    flexDirection: 'column',
    gap: 8},
  horizontalButton: {
    flex: 1,
    height: 40,
    justifyContent: 'center',
    borderRadius: 8},
  verticalButton: {
    height: 40,
    justifyContent: 'center',
    borderRadius: 8},
  buttonLabel: {
    fontSize: 12,
    fontWeight: '500'}});