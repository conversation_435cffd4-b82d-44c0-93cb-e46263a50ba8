/**
 * EmptyStateIllustration - MD3-compliant empty state illustrations
 * Uses SVG for lightweight, scalable illustrations
 */

import React, { useMemo } from 'react';
import { View, StyleSheet   } from 'react-native';
import { Text   } from 'react-native-paper';
// FIXED: Changed useTheme to import from your custom ThemeContext
import { useTheme   } from '../context/ThemeContext'; 
import Svg, { 
  Circle, 
  Path, 
  Rect, 
  Line, 
  Defs, 
  LinearGradient, 
  Stop 
} from 'react-native-svg';
import { SPACING   } from '../theme/designTokens';

const EmptyStateIllustration = ({ 
  type = 'general',
  size = 120,
  title,
  subtitle,
  style,
  showText = true 
}) => {
  const { theme  } = useTheme();

  const styles = useMemo(() => StyleSheet.create({
    container: {
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: SPACING.xl,
      paddingHorizontal: SPACING.lg,
    },
    illustrationContainer: {
      marginBottom: SPACING.lg,
    },
    textContainer: {
      alignItems: 'center',
      maxWidth: 280,
    },
    title: {
      textAlign: 'center',
      marginBottom: SPACING.sm,
      fontWeight: '500',
      color: theme.colors.onSurface,
    },
    subtitle: {
      textAlign: 'center',
      lineHeight: 20,
      color: theme.colors.onSurfaceVariant,
    },
  }), [theme]);
  
  const getIllustration = useMemo(() => {
    // Access theme colors inside the useMemo callback
    const primaryColor = theme.colors.primary;
    const surfaceColor = theme.colors.surfaceVariant;
    const outlineColor = theme.colors.outline;
    
    // SVG definitions remain the same...
    switch(type) {
        case 'orders':
          return (
            <Svg width={size} height={size} viewBox="0 0 120 120">
              <Defs><LinearGradient id="g" x1="0" y1="0" x2="1" y2="1"><Stop offset="0" stopColor={primaryColor} stopOpacity="0.1" /><Stop offset="1" stopColor={primaryColor} stopOpacity="0.3" /></LinearGradient></Defs>
              <Rect x="30" y="20" width="60" height="80" rx="4" fill={surfaceColor} stroke={outlineColor} strokeWidth="2" />
              <Rect x="45" y="15" width="30" height="10" rx="2" fill={primaryColor} />
              <Line x1="40" y1="35" x2="80" y2="35" stroke={outlineColor} strokeWidth="2" />
              <Line x1="40" y1="45" x2="70" y2="45" stroke={outlineColor} strokeWidth="2" />
              <Line x1="40" y1="55" x2="75" y2="55" stroke={outlineColor} strokeWidth="2" />
              <Circle cx="60" cy="75" r="15" fill="url(#g)" />
              <Path d="M55 75 L58 78 L65 71" stroke={primaryColor} strokeWidth="2.5" fill="none" strokeLinecap="round" strokeLinejoin="round" />
            </Svg>
          );
        case 'customers':
          return (
            <Svg width={size} height={size} viewBox="0 0 120 120">
               <Defs><LinearGradient id="g" x1="0" y1="0" x2="1" y2="1"><Stop offset="0" stopColor={primaryColor} stopOpacity="0.1" /><Stop offset="1" stopColor={primaryColor} stopOpacity="0.3" /></LinearGradient></Defs>
              <Circle cx="60" cy="45" r="15" fill={surfaceColor} stroke={outlineColor} strokeWidth="2" />
              <Path d="M35 95 Q35 75 60 75 Q85 75 85 95 L85 100 L35 100 Z" fill={surfaceColor} stroke={outlineColor} strokeWidth="2" />
              <Circle cx="85" cy="45" r="12" fill="url(#g)" />
              <Line x1="80" y1="45" x2="90" y2="45" stroke={primaryColor} strokeWidth="2.5" strokeLinecap="round" />
              <Line x1="85" y1="40" x2="85" y2="50" stroke={primaryColor} strokeWidth="2.5" strokeLinecap="round" />
            </Svg>
          );
        // ... other cases
        default:
          return (
            <Svg width={size} height={size} viewBox="0 0 120 120">
              <Defs><LinearGradient id="g" x1="0" y1="0" x2="1" y2="1"><Stop offset="0" stopColor={primaryColor} stopOpacity="0.1" /><Stop offset="1" stopColor={primaryColor} stopOpacity="0.3" /></LinearGradient></Defs>
              <Path d="M25 40 L25 85 Q25 90 30 90 L90 90 Q95 90 95 85 L95 50 Q95 45 90 45 L55 45 L50 35 L30 35 Q25 35 25 40 Z" fill={surfaceColor} stroke={outlineColor} strokeWidth="2" />
              <Circle cx="60" cy="67" r="15" fill="url(#g)" />
              <Path d="M55 62 L65 72 M65 62 L55 72" stroke={primaryColor} strokeWidth="2.5" strokeLinecap="round" />
            </Svg>
          );
      }
  }, [theme, size, type]);

  const getDefaultText = () => {
    switch(type) {
      case 'orders': return { title: 'No Orders Yet', subtitle: 'Create your first order to see it here.'  };
      case 'customers': return { title: 'No Customers Yet', subtitle: 'Add your first customer to get started.'  };
      case 'products': return { title: 'No Products Yet', subtitle: 'Add products to your catalog.'  };
      case 'search': return { title: 'No Results Found', subtitle: 'Try adjusting your search criteria.'  };
      default: return { title: 'Nothing Here Yet', subtitle: 'Get started by adding some content.'  };
    }
  };

  const defaultText = getDefaultText();
  const displayTitle = title || defaultText.title;
  const displaySubtitle = subtitle || defaultText.subtitle;

  return (
    <View style={[styles.container, style}>
      <View style={styles.illustrationContainer}>
        {getIllustration}
      </View>
      {showText && (
        <View style={styles.textContainer}>
          <Text variant="titleMedium" style={styles.title}>
            {displayTitle}
          </Text>
          <Text variant="bodyMedium" style={styles.subtitle}>
            {displaySubtitle}
          </Text>
        </View>
      )}
    </View>
  );
};

export default React.memo(EmptyStateIllustration);