/**
 * LocalIcon - Ultra-lightweight local SVG icon component
 * Uses inline SVGs for critical icons, dramatically reducing bundle size
 * No external dependencies on icon libraries
 */

import React from 'react';
import { View, StyleSheet } from 'react-native';
import Svg, { Path, Circle, Rect, Line, Polyline, Polygon } from 'react-native-svg';
import { useTheme } from '../context/ThemeContext';

// Icon sizes
const ICON_SIZES = {
  small: 16,
  medium: 20,
  large: 24,
  extraLarge: 28,
  huge: 32,
  jumbo: 48,
};

// Critical icons as inline SVGs (most commonly used)
const INLINE_ICONS = {
  // Navigation icons (Bottom Nav)
  'home': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" stroke={color} strokeWidth="2" fill="none"/>
      <Polyline points="9,22 9,12 15,12 15,22" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),
  
  'shopping-bag': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z" stroke={color} strokeWidth="2" fill="none"/>
      <Line x1="3" y1="6" x2="21" y2="6" stroke={color} strokeWidth="2"/>
      <Path d="M16 10a4 4 0 0 1-8 0" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),
  
  'users': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" stroke={color} strokeWidth="2" fill="none"/>
      <Circle cx="9" cy="7" r="4" stroke={color} strokeWidth="2" fill="none"/>
      <Path d="M23 21v-2a4 4 0 0 0-3-3.87" stroke={color} strokeWidth="2" fill="none"/>
      <Path d="M16 3.13a4 4 0 0 1 0 7.75" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),
  
  'camera': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z" stroke={color} strokeWidth="2" fill="none"/>
      <Circle cx="12" cy="13" r="4" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  // Dashboard icons
  'CurrencyDollar': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Line x1="12" y1="1" x2="12" y2="23" stroke={color} strokeWidth="2"/>
      <Path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),
  
  'TrendUp': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Polyline points="23,6 13.5,15.5 8.5,10.5 1,18" stroke={color} strokeWidth="2" fill="none"/>
      <Polyline points="17,6 23,6 23,12" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),
  
  'ClipboardText': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2" stroke={color} strokeWidth="2" fill="none"/>
      <Rect x="8" y="2" width="8" height="4" rx="1" ry="1" stroke={color} strokeWidth="2" fill="none"/>
      <Path d="M9 12h6" stroke={color} strokeWidth="2"/>
      <Path d="M9 16h6" stroke={color} strokeWidth="2"/>
    </Svg>
  ),
  
  'Clock': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Circle cx="12" cy="12" r="10" stroke={color} strokeWidth="2" fill="none"/>
      <Polyline points="12,6 12,12 16,14" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),
  
  'Users': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" stroke={color} strokeWidth="2" fill="none"/>
      <Circle cx="9" cy="7" r="4" stroke={color} strokeWidth="2" fill="none"/>
      <Path d="M23 21v-2a4 4 0 0 0-3-3.87" stroke={color} strokeWidth="2" fill="none"/>
      <Path d="M16 3.13a4 4 0 0 1 0 7.75" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  // Toast icons
  'check-circle': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" stroke={color} strokeWidth="2" fill="none"/>
      <Polyline points="22,4 12,14.01 9,11.01" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),
  
  'alert-circle': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Circle cx="12" cy="12" r="10" stroke={color} strokeWidth="2" fill="none"/>
      <Line x1="12" y1="8" x2="12" y2="12" stroke={color} strokeWidth="2"/>
      <Line x1="12" y1="16" x2="12.01" y2="16" stroke={color} strokeWidth="2"/>
    </Svg>
  ),
  
  'alert-triangle': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z" stroke={color} strokeWidth="2" fill="none"/>
      <Line x1="12" y1="9" x2="12" y2="13" stroke={color} strokeWidth="2"/>
      <Line x1="12" y1="17" x2="12.01" y2="17" stroke={color} strokeWidth="2"/>
    </Svg>
  ),
  
  'info': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Circle cx="12" cy="12" r="10" stroke={color} strokeWidth="2" fill="none"/>
      <Line x1="12" y1="16" x2="12" y2="12" stroke={color} strokeWidth="2"/>
      <Line x1="12" y1="8" x2="12.01" y2="8" stroke={color} strokeWidth="2"/>
    </Svg>
  ),
  
  'x': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Line x1="18" y1="6" x2="6" y2="18" stroke={color} strokeWidth="2"/>
      <Line x1="6" y1="6" x2="18" y2="18" stroke={color} strokeWidth="2"/>
    </Svg>
  ),

  // Additional critical icons
  'Package': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Line x1="16.5" y1="9.4" x2="7.5" y2="4.21" stroke={color} strokeWidth="2"/>
      <Path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z" stroke={color} strokeWidth="2" fill="none"/>
      <Polyline points="3.27,6.96 12,12.01 20.73,6.96" stroke={color} strokeWidth="2" fill="none"/>
      <Line x1="12" y1="22.08" x2="12" y2="12" stroke={color} strokeWidth="2"/>
    </Svg>
  ),
  
  'Star': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),
  
  'Ruler': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path d="M21.3 8.7l-5.6-5.6c-.4-.4-1-.4-1.4 0L8.7 8.7c-.4.4-.4 1 0 1.4l5.6 5.6c.4.4 1 .4 1.4 0l5.6-5.6c.4-.4.4-1 0-1.4z" stroke={color} strokeWidth="2" fill="none"/>
      <Line x1="14" y1="10" x2="16" y2="12" stroke={color} strokeWidth="2"/>
      <Line x1="12" y1="8" x2="14" y2="10" stroke={color} strokeWidth="2"/>
      <Line x1="10" y1="6" x2="12" y2="8" stroke={color} strokeWidth="2"/>
    </Svg>
  ),

  'plus': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Line x1="12" y1="5" x2="12" y2="19" stroke={color} strokeWidth="2"/>
      <Line x1="5" y1="12" x2="19" y2="12" stroke={color} strokeWidth="2"/>
    </Svg>
  ),

  'trending-up': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Polyline points="23,6 13.5,15.5 8.5,10.5 1,18" stroke={color} strokeWidth="2" fill="none"/>
      <Polyline points="17,6 23,6 23,12" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  'trending-down': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Polyline points="23,18 13.5,8.5 8.5,13.5 1,6" stroke={color} strokeWidth="2" fill="none"/>
      <Polyline points="17,18 23,18 23,12" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  // Additional missing icons
  'CalendarCheck': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Rect x="3" y="4" width="18" height="18" rx="2" ry="2" stroke={color} strokeWidth="2" fill="none"/>
      <Line x1="16" y1="2" x2="16" y2="6" stroke={color} strokeWidth="2"/>
      <Line x1="8" y1="2" x2="8" y2="6" stroke={color} strokeWidth="2"/>
      <Line x1="3" y1="10" x2="21" y2="10" stroke={color} strokeWidth="2"/>
      <Polyline points="9,14 11,16 15,12" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  'ChartLineUp': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Line x1="18" y1="20" x2="18" y2="10" stroke={color} strokeWidth="2"/>
      <Line x1="12" y1="20" x2="12" y2="4" stroke={color} strokeWidth="2"/>
      <Line x1="6" y1="20" x2="6" y2="14" stroke={color} strokeWidth="2"/>
      <Polyline points="22,6 18,10 14,6 10,10" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  'Receipt': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path d="M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1z" stroke={color} strokeWidth="2" fill="none"/>
      <Path d="M16 8h-6" stroke={color} strokeWidth="2"/>
      <Path d="M16 12h-6" stroke={color} strokeWidth="2"/>
      <Path d="M12 16h-2" stroke={color} strokeWidth="2"/>
    </Svg>
  ),

  'Calculator': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Rect x="4" y="2" width="16" height="20" rx="2" stroke={color} strokeWidth="2" fill="none"/>
      <Line x1="8" y1="6" x2="16" y2="6" stroke={color} strokeWidth="2"/>
      <Line x1="8" y1="10" x2="8" y2="10" stroke={color} strokeWidth="2"/>
      <Line x1="12" y1="10" x2="12" y2="10" stroke={color} strokeWidth="2"/>
      <Line x1="16" y1="10" x2="16" y2="10" stroke={color} strokeWidth="2"/>
      <Line x1="8" y1="14" x2="8" y2="14" stroke={color} strokeWidth="2"/>
      <Line x1="12" y1="14" x2="12" y2="14" stroke={color} strokeWidth="2"/>
      <Line x1="16" y1="14" x2="16" y2="14" stroke={color} strokeWidth="2"/>
      <Line x1="8" y1="18" x2="8" y2="18" stroke={color} strokeWidth="2"/>
      <Line x1="12" y1="18" x2="16" y2="18" stroke={color} strokeWidth="2"/>
    </Svg>
  ),

  'file-chart': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2Z" stroke={color} strokeWidth="2" fill="none"/>
      <Polyline points="14,2 14,8 20,8" stroke={color} strokeWidth="2" fill="none"/>
      <Line x1="8" y1="13" x2="8" y2="17" stroke={color} strokeWidth="2"/>
      <Line x1="12" y1="15" x2="12" y2="17" stroke={color} strokeWidth="2"/>
      <Line x1="16" y1="11" x2="16" y2="17" stroke={color} strokeWidth="2"/>
    </Svg>
  ),

  // Additional missing icons
  'search': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Circle cx="11" cy="11" r="8" stroke={color} strokeWidth="2" fill="none"/>
      <Path d="m21 21-4.35-4.35" stroke={color} strokeWidth="2"/>
    </Svg>
  ),

  'bell': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path d="M6 8A6 6 0 0 1 18 8c0 7 3 9 3 9H3s3-2 3-9" stroke={color} strokeWidth="2" fill="none"/>
      <Path d="M13.73 21C13.5 21.28 13.17 21.5 12.8 21.66C12.43 21.82 12.02 21.91 11.59 21.91C11.16 21.91 10.75 21.82 10.38 21.66C10.01 21.5 9.68 21.28 9.45 21" stroke={color} strokeWidth="2"/>
    </Svg>
  ),

  'Shirt': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path d="M20.38 8.53l-1.4-2.8A2 2 0 0 0 17.2 4.8H16V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v.8H6.8a2 2 0 0 0-1.78 1.13l-1.4 2.8a2 2 0 0 0 .18 2.08L6 13v7a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2v-7l2.2-2.39a2 2 0 0 0 .18-2.08z" stroke={color} strokeWidth="2" fill="none"/>
      <Path d="M10 4h4" stroke={color} strokeWidth="2"/>
    </Svg>
  ),

  'plus-circle': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Circle cx="12" cy="12" r="10" stroke={color} strokeWidth="2" fill="none"/>
      <Line x1="12" y1="8" x2="12" y2="16" stroke={color} strokeWidth="2"/>
      <Line x1="8" y1="12" x2="16" y2="12" stroke={color} strokeWidth="2"/>
    </Svg>
  ),

  'UserPlus': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" stroke={color} strokeWidth="2" fill="none"/>
      <Circle cx="8.5" cy="7" r="4" stroke={color} strokeWidth="2" fill="none"/>
      <Line x1="20" y1="8" x2="20" y2="14" stroke={color} strokeWidth="2"/>
      <Line x1="23" y1="11" x2="17" y2="11" stroke={color} strokeWidth="2"/>
    </Svg>
  ),

  'Scissors': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Circle cx="6" cy="6" r="3" stroke={color} strokeWidth="2" fill="none"/>
      <Circle cx="6" cy="18" r="3" stroke={color} strokeWidth="2" fill="none"/>
      <Line x1="20" y1="4" x2="8.12" y2="15.88" stroke={color} strokeWidth="2"/>
      <Line x1="14.47" y1="14.48" x2="20" y2="20" stroke={color} strokeWidth="2"/>
      <Line x1="8.12" y1="8.12" x2="12" y2="12" stroke={color} strokeWidth="2"/>
    </Svg>
  ),

  'grid': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Rect x="3" y="3" width="7" height="7" stroke={color} strokeWidth="2" fill="none"/>
      <Rect x="14" y="3" width="7" height="7" stroke={color} strokeWidth="2" fill="none"/>
      <Rect x="14" y="14" width="7" height="7" stroke={color} strokeWidth="2" fill="none"/>
      <Rect x="3" y="14" width="7" height="7" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  'ChartBar': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Line x1="12" y1="20" x2="12" y2="10" stroke={color} strokeWidth="2"/>
      <Line x1="18" y1="20" x2="18" y2="4" stroke={color} strokeWidth="2"/>
      <Line x1="6" y1="20" x2="6" y2="16" stroke={color} strokeWidth="2"/>
    </Svg>
  ),

  // MaterialCommunityIcons equivalents
  'account': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke={color} strokeWidth="2" fill="none"/>
      <Circle cx="12" cy="7" r="4" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  'phone': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  'email': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z" stroke={color} strokeWidth="2" fill="none"/>
      <Polyline points="22,6 12,13 2,6" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  'map-marker': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" stroke={color} strokeWidth="2" fill="none"/>
      <Circle cx="12" cy="10" r="3" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  'note-text': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke={color} strokeWidth="2" fill="none"/>
      <Polyline points="14,2 14,8 20,8" stroke={color} strokeWidth="2" fill="none"/>
      <Line x1="16" y1="13" x2="8" y2="13" stroke={color} strokeWidth="2"/>
      <Line x1="16" y1="17" x2="8" y2="17" stroke={color} strokeWidth="2"/>
      <Polyline points="10,9 9,9 8,9" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  'crown': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path d="M2 18h20l-2-12-3 7-5-10-5 10-3-7z" stroke={color} strokeWidth="2" fill="none"/>
      <Circle cx="7" cy="6" r="2" stroke={color} strokeWidth="2" fill="none"/>
      <Circle cx="17" cy="6" r="2" stroke={color} strokeWidth="2" fill="none"/>
      <Circle cx="12" cy="4" r="2" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  'check': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Polyline points="20,6 9,17 4,12" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  'pencil': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" stroke={color} strokeWidth="2" fill="none"/>
      <Path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  'delete': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Polyline points="3,6 5,6 21,6" stroke={color} strokeWidth="2" fill="none"/>
      <Path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" stroke={color} strokeWidth="2" fill="none"/>
      <Line x1="10" y1="11" x2="10" y2="17" stroke={color} strokeWidth="2"/>
      <Line x1="14" y1="11" x2="14" y2="17" stroke={color} strokeWidth="2"/>
    </Svg>
  ),

  'content-save': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z" stroke={color} strokeWidth="2" fill="none"/>
      <Polyline points="17,21 17,13 7,13 7,21" stroke={color} strokeWidth="2" fill="none"/>
      <Polyline points="7,3 7,8 15,8" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  'download': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" stroke={color} strokeWidth="2" fill="none"/>
      <Polyline points="7,10 12,15 17,10" stroke={color} strokeWidth="2" fill="none"/>
      <Line x1="12" y1="15" x2="12" y2="3" stroke={color} strokeWidth="2"/>
    </Svg>
  ),

  'share': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Circle cx="18" cy="5" r="3" stroke={color} strokeWidth="2" fill="none"/>
      <Circle cx="6" cy="12" r="3" stroke={color} strokeWidth="2" fill="none"/>
      <Circle cx="18" cy="19" r="3" stroke={color} strokeWidth="2" fill="none"/>
      <Line x1="8.59" y1="13.51" x2="15.42" y2="17.49" stroke={color} strokeWidth="2"/>
      <Line x1="15.41" y1="6.51" x2="8.59" y2="10.49" stroke={color} strokeWidth="2"/>
    </Svg>
  ),

  'eye': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke={color} strokeWidth="2" fill="none"/>
      <Circle cx="12" cy="12" r="3" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  'eye-off': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24" stroke={color} strokeWidth="2" fill="none"/>
      <Line x1="1" y1="1" x2="23" y2="23" stroke={color} strokeWidth="2"/>
    </Svg>
  ),

  'calendar': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Rect x="3" y="4" width="18" height="18" rx="2" ry="2" stroke={color} strokeWidth="2" fill="none"/>
      <Line x1="16" y1="2" x2="16" y2="6" stroke={color} strokeWidth="2"/>
      <Line x1="8" y1="2" x2="8" y2="6" stroke={color} strokeWidth="2"/>
      <Line x1="3" y1="10" x2="21" y2="10" stroke={color} strokeWidth="2"/>
    </Svg>
  ),

  'image': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke={color} strokeWidth="2" fill="none"/>
      <Circle cx="8.5" cy="8.5" r="1.5" stroke={color} strokeWidth="2" fill="none"/>
      <Polyline points="21,15 16,10 5,21" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  'folder': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  'credit-card': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Rect x="1" y="4" width="22" height="16" rx="2" ry="2" stroke={color} strokeWidth="2" fill="none"/>
      <Line x1="1" y1="10" x2="23" y2="10" stroke={color} strokeWidth="2"/>
    </Svg>
  ),

  'cog': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Circle cx="12" cy="12" r="3" stroke={color} strokeWidth="2" fill="none"/>
      <Path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  'shield': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  'lock': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke={color} strokeWidth="2" fill="none"/>
      <Circle cx="12" cy="16" r="1" stroke={color} strokeWidth="2" fill="none"/>
      <Path d="M7 11V7a5 5 0 0 1 10 0v4" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  'database': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path d="M21 5c0 1.66-4 3-9 3S3 6.66 3 5s4-3 9-3 9 1.34 9 3z" stroke={color} strokeWidth="2" fill="none"/>
      <Path d="M21 12c0 1.66-4 3-9 3s-9-1.34-9-3" stroke={color} strokeWidth="2" fill="none"/>
      <Path d="M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  'menu': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Line x1="3" y1="6" x2="21" y2="6" stroke={color} strokeWidth="2"/>
      <Line x1="3" y1="12" x2="21" y2="12" stroke={color} strokeWidth="2"/>
      <Line x1="3" y1="18" x2="21" y2="18" stroke={color} strokeWidth="2"/>
    </Svg>
  ),

  'dots-vertical': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Circle cx="12" cy="12" r="1" stroke={color} strokeWidth="2" fill="none"/>
      <Circle cx="12" cy="5" r="1" stroke={color} strokeWidth="2" fill="none"/>
      <Circle cx="12" cy="19" r="1" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  'dots-horizontal': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Circle cx="12" cy="12" r="1" stroke={color} strokeWidth="2" fill="none"/>
      <Circle cx="5" cy="12" r="1" stroke={color} strokeWidth="2" fill="none"/>
      <Circle cx="19" cy="12" r="1" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  'chevron-right': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Polyline points="9,18 15,12 9,6" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  'chevron-left': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Polyline points="15,18 9,12 15,6" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  'chevron-up': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Polyline points="18,15 12,9 6,15" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  'chevron-down': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Polyline points="6,9 12,15 18,9" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  'arrow-left': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Line x1="19" y1="12" x2="5" y2="12" stroke={color} strokeWidth="2"/>
      <Polyline points="12,19 5,12 12,5" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  'arrow-right': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Line x1="5" y1="12" x2="19" y2="12" stroke={color} strokeWidth="2"/>
      <Polyline points="12,5 19,12 12,19" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  'close': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Line x1="18" y1="6" x2="6" y2="18" stroke={color} strokeWidth="2"/>
      <Line x1="6" y1="6" x2="18" y2="18" stroke={color} strokeWidth="2"/>
    </Svg>
  ),

  'filter': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Polygon points="22,3 2,3 10,12.46 10,19 14,21 14,12.46" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  'sort': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Line x1="3" y1="6" x2="21" y2="6" stroke={color} strokeWidth="2"/>
      <Line x1="6" y1="12" x2="18" y2="12" stroke={color} strokeWidth="2"/>
      <Line x1="9" y1="18" x2="15" y2="18" stroke={color} strokeWidth="2"/>
    </Svg>
  ),

  'refresh': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Polyline points="23,4 23,10 17,10" stroke={color} strokeWidth="2" fill="none"/>
      <Polyline points="1,20 1,14 7,14" stroke={color} strokeWidth="2" fill="none"/>
      <Path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  // Missing icons from warnings
  'search': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Circle cx="11" cy="11" r="8" stroke={color} strokeWidth="2" fill="none"/>
      <Path d="m21 21-4.35-4.35" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  'bell': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9" stroke={color} strokeWidth="2" fill="none"/>
      <Path d="M13.73 21a2 2 0 0 1-3.46 0" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  'Shirt': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path d="M8 2h8v2l2 2v16H6V6l2-2V2z" stroke={color} strokeWidth="2" fill="none"/>
      <Path d="M6 6L4 8v4l2-2" stroke={color} strokeWidth="2" fill="none"/>
      <Path d="M18 6l2 2v4l-2-2" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  'plus-circle': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Circle cx="12" cy="12" r="10" stroke={color} strokeWidth="2" fill="none"/>
      <Line x1="12" y1="8" x2="12" y2="16" stroke={color} strokeWidth="2"/>
      <Line x1="8" y1="12" x2="16" y2="12" stroke={color} strokeWidth="2"/>
    </Svg>
  ),

  'UserPlus': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" stroke={color} strokeWidth="2" fill="none"/>
      <Circle cx="8.5" cy="7" r="4" stroke={color} strokeWidth="2" fill="none"/>
      <Line x1="20" y1="8" x2="20" y2="14" stroke={color} strokeWidth="2"/>
      <Line x1="23" y1="11" x2="17" y2="11" stroke={color} strokeWidth="2"/>
    </Svg>
  ),

  'Scissors': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Circle cx="6" cy="6" r="3" stroke={color} strokeWidth="2" fill="none"/>
      <Circle cx="6" cy="18" r="3" stroke={color} strokeWidth="2" fill="none"/>
      <Line x1="20" y1="4" x2="8.12" y2="15.88" stroke={color} strokeWidth="2"/>
      <Line x1="14.47" y1="14.48" x2="20" y2="20" stroke={color} strokeWidth="2"/>
      <Line x1="8.12" y1="8.12" x2="12" y2="12" stroke={color} strokeWidth="2"/>
    </Svg>
  ),

  'grid': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Rect x="3" y="3" width="7" height="7" stroke={color} strokeWidth="2" fill="none"/>
      <Rect x="14" y="3" width="7" height="7" stroke={color} strokeWidth="2" fill="none"/>
      <Rect x="14" y="14" width="7" height="7" stroke={color} strokeWidth="2" fill="none"/>
      <Rect x="3" y="14" width="7" height="7" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  'ChartBar': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Line x1="12" y1="20" x2="12" y2="10" stroke={color} strokeWidth="2"/>
      <Line x1="18" y1="20" x2="18" y2="4" stroke={color} strokeWidth="2"/>
      <Line x1="6" y1="20" x2="6" y2="16" stroke={color} strokeWidth="2"/>
    </Svg>
  ),

  // Missing icons from warnings
  'dollar-sign': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Line x1="12" y1="1" x2="12" y2="23" stroke={color} strokeWidth="2"/>
      <Path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  'zap': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Polygon points="13,2 3,14 12,14 11,22 21,10 12,10" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  'camera-plus': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z" stroke={color} strokeWidth="2" fill="none"/>
      <Circle cx="12" cy="13" r="4" stroke={color} strokeWidth="2" fill="none"/>
      <Line x1="12" y1="10" x2="12" y2="16" stroke={color} strokeWidth="2"/>
      <Line x1="9" y1="13" x2="15" y2="13" stroke={color} strokeWidth="2"/>
    </Svg>
  ),

  'clipboard-list': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2" stroke={color} strokeWidth="2" fill="none"/>
      <Rect x="8" y="2" width="8" height="4" rx="1" ry="1" stroke={color} strokeWidth="2" fill="none"/>
      <Line x1="9" y1="12" x2="15" y2="12" stroke={color} strokeWidth="2"/>
      <Line x1="9" y1="16" x2="15" y2="16" stroke={color} strokeWidth="2"/>
      <Line x1="9" y1="20" x2="15" y2="20" stroke={color} strokeWidth="2"/>
    </Svg>
  ),

  'account-group': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" stroke={color} strokeWidth="2" fill="none"/>
      <Circle cx="9" cy="7" r="4" stroke={color} strokeWidth="2" fill="none"/>
      <Path d="M23 21v-2a4 4 0 0 0-3-3.87" stroke={color} strokeWidth="2" fill="none"/>
      <Path d="M16 3.13a4 4 0 0 1 0 7.75" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  'account-supervisor': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" stroke={color} strokeWidth="2" fill="none"/>
      <Circle cx="9" cy="7" r="4" stroke={color} strokeWidth="2" fill="none"/>
      <Path d="M23 21v-2a4 4 0 0 0-3-3.87" stroke={color} strokeWidth="2" fill="none"/>
      <Path d="M16 3.13a4 4 0 0 1 0 7.75" stroke={color} strokeWidth="2" fill="none"/>
      <Path d="M19 8l2 2-2 2" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  'ruler': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path d="M21.3 8.7l-5.6-5.6c-.4-.4-1-.4-1.4 0L8.7 8.7c-.4.4-.4 1 0 1.4l5.6 5.6c.4.4 1 .4 1.4 0l5.6-5.6c.4-.4.4-1 0-1.4z" stroke={color} strokeWidth="2" fill="none"/>
      <Line x1="14" y1="10" x2="16" y2="12" stroke={color} strokeWidth="2"/>
      <Line x1="12" y1="8" x2="14" y2="10" stroke={color} strokeWidth="2"/>
      <Line x1="10" y1="6" x2="12" y2="8" stroke={color} strokeWidth="2"/>
    </Svg>
  ),

  'store': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" stroke={color} strokeWidth="2" fill="none"/>
      <Polyline points="9,22 9,12 15,12 15,22" stroke={color} strokeWidth="2" fill="none"/>
      <Line x1="9" y1="9" x2="15" y2="9" stroke={color} strokeWidth="2"/>
    </Svg>
  ),

  'file-pdf-box': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke={color} strokeWidth="2" fill="none"/>
      <Polyline points="14,2 14,8 20,8" stroke={color} strokeWidth="2" fill="none"/>
      <Line x1="16" y1="13" x2="8" y2="13" stroke={color} strokeWidth="2"/>
      <Line x1="16" y1="17" x2="8" y2="17" stroke={color} strokeWidth="2"/>
      <Polyline points="10,9 9,9 8,9" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  'clock': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Circle cx="12" cy="12" r="10" stroke={color} strokeWidth="2" fill="none"/>
      <Polyline points="12,6 12,12 16,14" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  'file-document': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke={color} strokeWidth="2" fill="none"/>
      <Polyline points="14,2 14,8 20,8" stroke={color} strokeWidth="2" fill="none"/>
      <Line x1="16" y1="13" x2="8" y2="13" stroke={color} strokeWidth="2"/>
      <Line x1="16" y1="17" x2="8" y2="17" stroke={color} strokeWidth="2"/>
      <Polyline points="10,9 9,9 8,9" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  'text': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Polyline points="4,7 4,4 20,4 20,7" stroke={color} strokeWidth="2" fill="none"/>
      <Line x1="9" y1="20" x2="15" y2="20" stroke={color} strokeWidth="2"/>
      <Line x1="12" y1="4" x2="12" y2="20" stroke={color} strokeWidth="2"/>
    </Svg>
  ),

  'clock-outline': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Circle cx="12" cy="12" r="10" stroke={color} strokeWidth="2" fill="none"/>
      <Polyline points="12,6 12,12 16,14" stroke={color} strokeWidth="2" fill="none"/>
    </Svg>
  ),

  'close-circle-outline': (size, color) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Circle cx="12" cy="12" r="10" stroke={color} strokeWidth="2" fill="none"/>
      <Line x1="15" y1="9" x2="9" y2="15" stroke={color} strokeWidth="2"/>
      <Line x1="9" y1="9" x2="15" y2="15" stroke={color} strokeWidth="2"/>
    </Svg>
  ),
};

const LocalIcon = ({
  name,
  size = 'large',
  color,
  style,
  containerSize,
  backgroundColor,
  borderRadius,
  containerStyle,
  ...props
}) => {
  const { theme } = useTheme();
  
  // Determine icon size
  const iconSize = typeof size === 'number' ? size : ICON_SIZES[size] || ICON_SIZES.large;
  
  // Determine icon color
  const iconColor = color || theme.colors.onSurface;
  
  // Get icon renderer
  const iconRenderer = INLINE_ICONS[name];
  
  if (!iconRenderer) {
    console.warn('LocalIcon: Icon "${name}" not found');
    // Return a simple fallback
    return (
      <Svg width={iconSize} height={iconSize} viewBox="0 0 24 24" fill="none">
        <Circle cx="12" cy="12" r="10" stroke={iconColor} strokeWidth="2" fill="none"/>
        <Line x1="12" y1="8" x2="12" y2="12" stroke={iconColor} strokeWidth="2"/>
        <Line x1="12" y1="16" x2="12.01" y2="16" stroke={iconColor} strokeWidth="2"/>
      </Svg>
    );
  }
  
  const iconElement = iconRenderer(iconSize, iconColor);
  
  // Return with container if needed
  if (containerSize || backgroundColor || borderRadius) {
    const touchTargetSize = containerSize || (iconSize < 24 ? 44 : iconSize + 20);
    
    return (
      <View
        style={[
          {
            width: touchTargetSize,
            height: touchTargetSize,
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: backgroundColor,
            borderRadius: borderRadius || (backgroundColor ? touchTargetSize / 2 : 0),
          },
          containerStyle,
        }>
        {iconElement}
      </View>
    );
  }
  
  return iconElement;
};

// Preset components for common use cases
export const NavigationLocalIcon = ({ name, ...props }) => (
  <LocalIcon name={name} {...props} />
);

export const ActionLocalIcon = ({ name, ...props }) => (
  <LocalIcon name={name} {...props} />
);

export const BusinessLocalIcon = ({ name, ...props }) => (
  <LocalIcon name={name} {...props} />
);

export const SettingsLocalIcon = ({ name, ...props }) => (
  <LocalIcon name={name} {...props} />
);

export const StatusLocalIcon = ({ name, ...props }) => (
  <LocalIcon name={name} {...props} />
);


const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default React.memo(LocalIcon);
