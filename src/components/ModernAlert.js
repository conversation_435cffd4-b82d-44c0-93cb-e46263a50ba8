/**
 * ModernAlert - Modern alert dialog component with beautiful design
 */

import React, { useCallback } from 'react';
import { View, StyleSheet, Dimensions   } from 'react-native';
import { Dialog   } from 'react-native-paper';
import { Portal   } from 'react-native-paper';
import { Text   } from 'react-native-paper';
import { Button   } from 'react-native-paper';
import LocalIcon from './LocalIcon';
import { useTheme   } from '../context/ThemeContext';

const { width  } = Dimensions.get('window');

const ModernAlert = ({
  visible,
  onDismiss,
  title,
  message,
  type = 'info', // 'success', 'error', 'warning', 'info', 'confirm'
  buttons = [],
  icon,
  dismissable = true,
}) => {
  const { theme  } = useTheme();

  const getTypeConfig = () => {
    switch(type) {
      case 'success':
        return { icon: icon || 'check-circle',
          iconColor: '#4CAF50',
          backgroundColor: '#E8F5E8',
       };
      case 'error':
        return { icon: icon || 'alert-circle',
          iconColor: '#F44336',
          backgroundColor: '#FFEBEE',
       };
      case 'warning':
        return { icon: icon || 'alert',
          iconColor: '#FF9800',
          backgroundColor: '#FFF3E0',
       };
      case 'confirm':
        return { icon: icon || 'help-circle',
          iconColor: theme.colors.primary,
          backgroundColor: theme.colors.primaryContainer,
       };
      default:
        return { icon: icon || 'information',
          iconColor: theme.colors.primary,
          backgroundColor: theme.colors.primaryContainer,
       };
    }
  };

  const typeConfig = getTypeConfig();

  const defaultButtons = buttons.length > 0 ? buttons : [
    {
      text: 'OK',
      onPress: onDismiss,
      mode: 'contained',
    }
  ];

  return (
    <Portal>
      <Dialog
        visible={visible}
        onDismiss={dismissable ? onDismiss : undefined}
        dismissable={dismissable}
        style={styles.dialog}>
        <View style={styles.content}>
          {/* Icon Section */}
          <View style={[
            styles.iconContainer,
            { backgroundColor: typeConfig.backgroundColor }
          }>
            <LocalIcon name={typeConfig.icon}
              size={32}
              color={typeConfig.iconColor}
            />
          </View>

          {/* Content Section */}
          <View style={styles.textContainer}>
            {title && (
              <Text
                variant="headlineSmall"
                style={styles.title}>
                {title}
              </Text>
            )}
            {message && (
              <Text
                variant="bodyMedium"
                style={styles.message}>
                {message}
              </Text>
            )}
          </View>

          {/* Buttons Section */}
          <View style={styles.buttonsContainer}>
            {defaultButtons.map((button, index) => (
              <Button
                key={index}
                mode={button.mode || 'outlined'}
                onPress={button.onPress}
                style={[
                  styles.button,
                  index > 0 && styles.buttonSpacing
                }
                contentStyle={styles.buttonContent}
                labelStyle={[
                  styles.buttonLabel,
                  { color: button.mode === 'contained' ? theme.colors.onPrimary : theme.colors.primary }
                }>
                {button.text}
              </Button>
            ))}
          </View>
        </View>
      </Dialog>
    </Portal>
  );
};






export default React.memo(ModernAlert);

const styles = StyleSheet.create({
  dialog: {
    borderRadius: 24,
    maxWidth: width * 0.9,
    alignSelf: 'center',
    backgroundColor: '#ffffff',
  },
  content: {
    padding: 24,
    alignItems: 'center',
  },
  iconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  textContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    textAlign: 'center',
    fontWeight: '600',
    marginBottom: 8,
    color: '#333333',
  },
  message: {
    textAlign: 'center',
    lineHeight: 20,
    color: '#666666',
  },
  buttonsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
  },
  button: {
    flex: 1,
    borderRadius: 12,
  },
  buttonSpacing: {
    marginLeft: 12,
  },
  buttonContent: {
    height: 48,
    paddingHorizontal: 16,
  },
  buttonLabel: {
    fontSize: 16,
    fontWeight: '600',
  },
});