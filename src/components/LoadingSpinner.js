// LOADING SPINNER COMPONENT - Reusable loading spinner
import React, { useEffect, useRef } from 'react';
import { View, Text, StyleSheet, Animated, Easing } from 'react-native';
// Removed LocalIcon import - using inline icons

const LoadingSpinner = ({
  size = 'medium', // 'small', 'medium', 'large'
  color = '#6366f1',
  message,
  showMessage = true,
  style,
  variant = 'spinner', // 'spinner', 'dots', 'pulse'
}) => {
  const spinValue = useRef(new Animated.Value(0)).current;
  const pulseValue = useRef(new Animated.Value(1)).current;
  const dotsValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (variant === 'spinner') {
      const spinAnimation = Animated.loop(
        Animated.timing(spinValue, {
          toValue: 1,
          duration: 1000,
          easing: Easing.linear,
          useNativeDriver: true,
        })
      );
      spinAnimation.start();
      return () => spinAnimation.stop();
    } else if (variant === 'pulse') {
      const pulseAnimation = Animated.loop(
        Animated.sequence([
          Animated.timing(pulseValue, {
            toValue: 1.2,
            duration: 600,
            useNativeDriver: true,
          }),
          Animated.timing(pulseValue, {
            toValue: 1,
            duration: 600,
            useNativeDriver: true,
          }),
        ])
      );
      pulseAnimation.start();
      return () => pulseAnimation.stop();
    } else if (variant === 'dots') {
      const dotsAnimation = Animated.loop(
        Animated.timing(dotsValue, {
          toValue: 1,
          duration: 1200,
          useNativeDriver: true,
        })
      );
      dotsAnimation.start();
      return () => dotsAnimation.stop();
    }
  }, [variant, spinValue, pulseValue, dotsValue]);

  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return { width: 20, height: 20, iconSize: 16 };
      case 'large':
        return { width: 48, height: 48, iconSize: 32 };
      default:
        return { width: 32, height: 32, iconSize: 24 };
    }
  };

  const sizeStyles = getSizeStyles();

  const renderSpinner = () => {
    if (variant === 'spinner') {
      const spin = spinValue.interpolate({
        inputRange: [0, 1],
        outputRange: ['0deg', '360deg'],
      });

      return (
        <Animated.View
          style={[
            styles.spinner,
            {
              width: sizeStyles.width,
              height: sizeStyles.height,
              borderColor: `${color}20`,
              borderTopColor: color,
              transform: [{ rotate: spin }],
            },
          }
        />
      );
    } else if (variant === 'pulse') {
      return (
        <Animated.View
          style={[
            styles.pulseContainer,
            {
              width: sizeStyles.width,
              height: sizeStyles.height,
              transform: [{ scale: pulseValue }],
            },
          }>
          <Text style={{ fontSize: sizeStyles.iconSize }}>
            🔄
          </Text>
        </Animated.View>
      );
    } else if (variant === 'dots') {
      const dot1Opacity = dotsValue.interpolate({
        inputRange: [0, 0.2, 0.4, 1],
        outputRange: [0.3, 1, 0.3, 0.3],
      });
      const dot2Opacity = dotsValue.interpolate({
        inputRange: [0, 0.2, 0.4, 0.6, 1],
        outputRange: [0.3, 0.3, 1, 0.3, 0.3],
      });
      const dot3Opacity = dotsValue.interpolate({
        inputRange: [0, 0.4, 0.6, 0.8, 1],
        outputRange: [0.3, 0.3, 0.3, 1, 0.3],
      });

      const dotSize = sizeStyles.width / 4;

      return (
        <View style={styles.dotsContainer}>
          <Animated.View
            style={[
              styles.dot,
              {
                width: dotSize,
                height: dotSize,
                backgroundColor: color,
                opacity: dot1Opacity,
              },
            }
          />
          <Animated.View
            style={[
              styles.dot,
              {
                width: dotSize,
                height: dotSize,
                backgroundColor: color,
                opacity: dot2Opacity,
              },
            }
          />
          <Animated.View
            style={[
              styles.dot,
              {
                width: dotSize,
                height: dotSize,
                backgroundColor: color,
                opacity: dot3Opacity,
              },
            }
          />
        </View>
      );
    }
  };

  const getMessageSize = () => {
    switch (size) {
      case 'small':
        return 12;
      case 'large':
        return 16;
      default:
        return 14;
    }
  };

  return (
    <View style={[styles.container, style}>
      {renderSpinner()}
      {showMessage && message && (
        <Text
          style={[
            styles.message,
            {
              fontSize: getMessageSize(),
              color: color,
              marginTop: size === 'small' ? 4 : 8,
            },
          }>
          {message}
        </Text>
      )}
    </View>
  );
};






export default LoadingSpinner;

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  spinner: {
    borderWidth: 2,
    borderRadius: 50,
    borderStyle: 'solid',
  },
  pulseContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  dotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 4,
  },
  dot: {
    borderRadius: 50,
  },
  message: {
    textAlign: 'center',
    fontWeight: '500',
  },
});