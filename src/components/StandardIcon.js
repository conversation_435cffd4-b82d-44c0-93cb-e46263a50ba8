/**
 * MD3Icon - Material Design 3 compliant icon component
 * Uses MaterialCommunityIcons with MD3 sizing and styling
 */

import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useTheme } from '../context/ThemeContext';
import LocalIcon from './LocalIcon';
import { getStandardIcon, isStandardIcon } from '../constants/iconMappings';
import { getLocalIcon } from '../utils/iconMigration';

// MD3 Icon Sizes
const MD3_ICON_SIZES = {
  small: 16,
  medium: 20,
  large: 24,
  extraLarge: 32,
  huge: 48,
};

const MD3Icon = ({
  // Standard props
  category, // 'action', 'status', 'business', 'navigation', 'ui'
  name, // The semantic name (e.g., 'edit', 'pending', 'product')

  // Direct icon override
  icon, // Direct MaterialCommunityIcons name (bypasses standardization)

  // MD3 Visual props
  size = 'large', // 'small', 'medium', 'large', 'extraLarge', 'huge' or number
  color, // Auto-detects from theme if not provided
  variant = 'default', // 'default', 'filled', 'outlined'

  // Container props
  containerSize, // Optional container size for touch targets
  backgroundColor,

  // Style props
  style,
  containerStyle,

  // Accessibility
  accessibilityLabel,

  // Debug mode
  debug = false,
}) => {
  const { theme } = useTheme();
  // Determine the actual icon to use
  let finalIcon;

  if (icon) {
    // Direct icon specified - use as is
    finalIcon = icon;
  } else if (category && name) {
    // Use standardized mapping
    finalIcon = getStandardIcon(category, name);
  } else if (name) {
    // Try to find icon by name across all categories
    finalIcon = getStandardIcon(null, name);
  } else {
    // Fallback
    finalIcon = 'help-circle-outline';
  }

  // Apply variant suffix for outlined icons
  if (variant === 'outlined' && !finalIcon.includes('-outline')) {
    finalIcon = finalIcon + '-outline';
  }

  // Determine icon size
  const iconSize = typeof size === 'number' ? size : MD3_ICON_SIZES[size] || MD3_ICON_SIZES.large;

  // Determine icon color (MD3 theme-aware)
  const iconColor = color || theme.colors.onSurface;

  // Determine container size for touch targets (MD3 minimum 48dp)
  const touchTargetSize = containerSize || (iconSize < 24 ? 48 : iconSize + 24);

  // Debug logging
  if (debug || __DEV__) {
    if (!isStandardIcon(finalIcon) && !icon) {
      console.warn('MD3Icon: Non-standard icon "${finalIcon}" used for ${category}:${name}');
    }
  }

  // Convert to Feather icon name
  const featherIconName = getLocalIcon(finalIcon);

  const iconElement = (
    <LocalIcon
      name={featherIconName}
      size={iconSize}
      color={iconColor}
      style={[styles.style2, style]}
      accessibilityLabel={accessibilityLabel || `${category} ${name} icon`}
    />
  );

  // Return with container if needed for touch targets or background
  if (containerSize || backgroundColor) {
    return (
      <View
        style={[
          styles.style1,
          {
            width: touchTargetSize,
            height: touchTargetSize,
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: backgroundColor,
            borderRadius: touchTargetSize / 2,
          },
          containerStyle,
        ]}
      >
        {iconElement}
      </View>
    );
  }

  return iconElement;
};

// MD3 Preset components for common use cases
export const ActionIcon = ({ name, ...props }) => (
  <MD3Icon category="action" name={name} {...props} />
);

export const StatusIcon = ({ name, ...props }) => (
  <MD3Icon category="status" name={name} {...props} />
);

export const BusinessIcon = ({ name, ...props }) => (
  <MD3Icon category="business" name={name} {...props} />
);

export const NavigationIcon = ({ name, ...props }) => (
  <MD3Icon category="navigation" name={name} {...props} />
);

export const UIIcon = ({ name, ...props }) => (
  <MD3Icon category="ui" name={name} {...props} />
);

// Common action icons as components
export const EditIcon = (props) => <ActionIcon name="edit" {...props} />;
export const DeleteIcon = (props) => <ActionIcon name="delete" {...props} />;
export const AddIcon = (props) => <ActionIcon name="add" {...props} />;
export const SaveIcon = (props) => <ActionIcon name="save" {...props} />;
export const CancelIcon = (props) => <ActionIcon name="cancel" {...props} />;
export const SearchIcon = (props) => <ActionIcon name="search" {...props} />;
export const FilterIcon = (props) => <ActionIcon name="filter" {...props} />;
export const MenuIcon = (props) => <ActionIcon name="menu" {...props} />;
export const SettingsIcon = (props) => <ActionIcon name="settings" {...props} />;
export const RefreshIcon = (props) => <ActionIcon name="refresh" {...props} />;

// Common status icons as components
export const PendingIcon = (props) => <StatusIcon name="pending" {...props} />;
export const ProcessingIcon = (props) => <StatusIcon name="processing" {...props} />;
export const CompletedIcon = (props) => <StatusIcon name="completed" {...props} />;
export const CancelledIcon = (props) => <StatusIcon name="cancelled" {...props} />;
export const SuccessIcon = (props) => <StatusIcon name="success" {...props} />;
export const ErrorIcon = (props) => <StatusIcon name="error" {...props} />;
export const WarningIcon = (props) => <StatusIcon name="warning" {...props} />;
export const InfoIcon = (props) => <StatusIcon name="info" {...props} />;

// Common business icons as components
export const ProductIcon = (props) => <BusinessIcon name="product" {...props} />;
export const OrderIcon = (props) => <BusinessIcon name="order" {...props} />;
export const CustomerIcon = (props) => <BusinessIcon name="customer" {...props} />;
export const SalesIcon = (props) => <BusinessIcon name="sales" {...props} />;
export const RevenueIcon = (props) => <BusinessIcon name="revenue" {...props} />;
export const AnalyticsIcon = (props) => <BusinessIcon name="analytics" {...props} />;
export const ReportsIcon = (props) => <BusinessIcon name="reports" {...props} />;
export const DashboardIcon = (props) => <BusinessIcon name="dashboard" {...props} />;

// Common navigation icons as components
export const DashboardNavIcon = (props) => <NavigationIcon name="dashboard" {...props} />;
export const ScanNavIcon = (props) => <NavigationIcon name="scan" {...props} />;
export const PlusNavIcon = (props) => <NavigationIcon name="plus" {...props} />;
export const OrdersNavIcon = (props) => <NavigationIcon name="orders" {...props} />;
export const SettingsNavIcon = (props) => <NavigationIcon name="settings" {...props} />;

// Icon with automatic color based on status
export const StatusIconWithColor = ({ status, size = 20, ...props }) => {
  const statusColors = {
    pending: '#FF9800',
    processing: '#2196F3',
    ready: '#4CAF50',
    completed: '#8BC34A',
    cancelled: '#F44336',
    success: '#4CAF50',
    error: '#F44336',
    warning: '#FF9800',
    info: '#2196F3',
    active: '#4CAF50',
    inactive: '#9E9E9E',
  };

  return (
    <StatusIcon
      name={status}
      size={size}
      color={statusColors[status] || '#9E9E9E'}
      {...props}
    />
  );
};

// Icon with automatic color based on action
export const ActionIconWithColor = ({ action, theme, size = 20, ...props }) => {
  const getActionColor = (actionName, theme) => {
    const actionColors = {
      edit: theme?.colors?.primary || '#2196F3',
      delete: theme?.colors?.error || '#F44336',
      add: theme?.colors?.primary || '#4CAF50',
      save: theme?.colors?.primary || '#4CAF50',
      cancel: theme?.colors?.onSurfaceVariant || '#9E9E9E',
      search: theme?.colors?.onSurface || '#424242',
      filter: theme?.colors?.onSurface || '#424242',
      menu: theme?.colors?.onSurfaceVariant || '#9E9E9E',
      settings: theme?.colors?.onSurfaceVariant || '#9E9E9E',
    };

    return actionColors[actionName] || theme?.colors?.onSurface || '#424242';
  };

  return (
    <ActionIcon
      name={action}
      size={size}
      color={getActionColor(action, theme)}
      {...props}
    />
  );
};

// Backward compatibility
export const StandardIcon = MD3Icon;


const styles = StyleSheet.create({
  style1: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  style2: {
    // Icon styles
  },
});

export default React.memo(MD3Icon);
