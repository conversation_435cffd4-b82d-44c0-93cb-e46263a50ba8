/**
 * UnifiedIcon - Universal icon component for Tailora app
 * Prioritizes Phosphor Icons with fallbacks to <PERSON>ather and MaterialCommunity
 * Provides seamless migration path and consistent API
 */

import React, { useMemo } from 'react';
import { View, StyleSheet } from 'react-native';
import { useTheme } from '../context/ThemeContext';

// Import icon components
import PhosphorIcon from './PhosphorIcon';
import LocalIcon from './LocalIcon';

// Import mappings
import { 
  getLocalIcon, 
  getMigratedLocalIcon, 
  isLocalIcon,
  MIGRATION_MAP 
} from '../constants/phosphorIconMappings';

// Icon library priorities
const ICON_LIBRARIES = {
  PHOSPHOR: 'phosphor',
  FEATHER: 'feather',
  MATERIAL: 'material',
};

// Unified icon sizes
const UNIFIED_ICON_SIZES = {
  small: 16,
  medium: 20,
  large: 24,
  extraLarge: 28,
  huge: 32,
  jumbo: 48,
};

const UnifiedIcon = ({
  // Icon identification
  name, // Semantic name or direct icon name
  category, // Optional category for semantic lookup
  library = 'auto', // 'auto', 'phosphor', 'feather', 'material'
  
  // Visual properties
  size = 'large',
  weight = 'regular', // For Phosphor icons
  color,
  colorContext = 'surface',
  
  // Container properties
  containerSize,
  backgroundColor,
  borderRadius,
  
  // Style properties
  style,
  containerStyle,
  
  // Accessibility
  accessibilityLabel,
  
  // Migration and fallback
  fallbackIcon = 'Question',
  enableMigration = true,
  
  // Debug mode
  debug = false,
}) => {
  const { theme } = useTheme();
  
  // Create styles with theme
  const styles = useMemo(() => createStyles(theme), [theme]);
  
  // Determine icon size
  const iconSize = typeof size === 'number' ? size : UNIFIED_ICON_SIZES[size] || UNIFIED_ICON_SIZES.large;
  
  // Determine icon color
  const getIconColor = (colorContext) => {
    const colorMap = {
      primary: theme.colors.primary,
      secondary: theme.colors.secondary,
      tertiary: theme.colors.tertiary,
      surface: theme.colors.onSurface,
      surfaceVariant: theme.colors.onSurfaceVariant,
      background: theme.colors.onBackground,
      error: theme.colors.error,
      warning: theme.colors.warning || '#FF9800',
      success: theme.colors.success || '#4CAF50',
      info: theme.colors.info || '#2196F3',
    };
    return colorMap[colorContext] || theme.colors.onSurface;
  };
  
  const iconColor = color || getIconColor(colorContext);
  
  // Icon resolution logic
  const resolveIcon = () => {
    let resolvedIcon = name;
    let resolvedLibrary = library;
    
    // Step 1: Try semantic lookup if category provided
    if (category && library === 'auto') {
      const semanticIcon = getLocalIcon(category, name);
      if (semanticIcon && semanticIcon !== 'Question') {
        resolvedIcon = semanticIcon;
        resolvedLibrary = ICON_LIBRARIES.PHOSPHOR;
        if (debug) console.log(`UnifiedIcon: Found semantic Phosphor icon "${semanticIcon}" for ${category}:${name}`);
        return { icon: resolvedIcon, library: resolvedLibrary };
      }
    }
    
    // Step 2: Try migration mapping if enabled
    if (enableMigration && library === 'auto') {
      const migratedIcon = getMigratedLocalIcon(name);
      if (migratedIcon && migratedIcon !== 'Question') {
        resolvedIcon = migratedIcon;
        resolvedLibrary = ICON_LIBRARIES.PHOSPHOR;
        if (debug) console.log(`UnifiedIcon: Migrated "${name}" to Phosphor icon "${migratedIcon}"`);
        return { icon: resolvedIcon, library: resolvedLibrary };
      }
    }
    
    // Step 3: Auto-detect library or use specified
    if (library === 'auto') {
      // Try Phosphor first (preferred)
      if (isLocalIcon(name)) {
        resolvedLibrary = ICON_LIBRARIES.PHOSPHOR;
      } else {
        // Fallback to Feather for common icons
        resolvedLibrary = ICON_LIBRARIES.FEATHER;
      }
    }
    
    if (debug) console.log(`UnifiedIcon: Using ${resolvedLibrary} library for icon "${resolvedIcon}"`);
    return { icon: resolvedIcon, library: resolvedLibrary };
  };
  
  const { icon: finalIcon, library: finalLibrary } = resolveIcon();
  
  // Render appropriate icon component
  const renderIcon = () => {
    try {
      switch (finalLibrary) {
        case ICON_LIBRARIES.PHOSPHOR:
        case 'phosphor':
          return (
            <PhosphorIcon
              name={finalIcon}
              size={iconSize}
              weight={weight}
              color={iconColor}
              style={style}
              accessibilityLabel={accessibilityLabel}
              debug={debug}
            />
          );
          
        case ICON_LIBRARIES.FEATHER:
        case 'feather':
          return (
            <LocalIcon
              name={finalIcon}
              size={iconSize}
              color={iconColor}
              style={style}
            />
          );
          
        case ICON_LIBRARIES.MATERIAL:
        case 'material':
          return (
            <LocalIcon
              name={finalIcon}
              size={iconSize}
              color={iconColor}
              style={style}
            />
          );
          
        default:
          // Fallback to Phosphor with fallback icon
          return (
            <PhosphorIcon
              name={fallbackIcon}
              size={iconSize}
              weight={weight}
              color={iconColor}
              style={style}
              accessibilityLabel={accessibilityLabel}
              debug={debug}
            />
          );
      }
    } catch (error) {
      if (debug) console.warn(`UnifiedIcon: Error rendering ${finalLibrary} icon "${finalIcon}":`, error);
      
      // Ultimate fallback to Phosphor Question icon
      return (
        <PhosphorIcon
          name="Question"
          size={iconSize}
          weight={weight}
          color={iconColor}
          style={style}
          accessibilityLabel={accessibilityLabel || 'Unknown icon'}
        />
      );
    }
  };
  
  const iconElement = renderIcon();
  
  // Return with container if needed
  if (containerSize || backgroundColor || borderRadius) {
    const touchTargetSize = containerSize || (iconSize < 24 ? 44 : iconSize + 20);
    
    return (
      <View
        style={[
          styles.iconContainer,
          {
            width: touchTargetSize,
            height: touchTargetSize,
            backgroundColor: backgroundColor,
            borderRadius: borderRadius || (backgroundColor ? touchTargetSize / 2 : 0),
          },
          containerStyle,
        ]}
      >
        {iconElement}
      </View>
    );
  }
  
  return iconElement;
};

// Create styles function
const createStyles = (theme) => StyleSheet.create({
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
});

// Preset components for common use cases
export const PrimaryUnifiedIcon = ({ name, ...props }) => (
  <UnifiedIcon name={name} colorContext="primary" {...props} />
);

export const SecondaryUnifiedIcon = ({ name, ...props }) => (
  <UnifiedIcon name={name} colorContext="secondary" {...props} />
);

export const SurfaceUnifiedIcon = ({ name, ...props }) => (
  <UnifiedIcon name={name} colorContext="surface" {...props} />
);

export const VariantUnifiedIcon = ({ name, ...props }) => (
  <UnifiedIcon name={name} colorContext="surfaceVariant" {...props} />
);

// Navigation-specific unified icon
export const NavigationUnifiedIcon = ({ name, ...props }) => (
  <UnifiedIcon name={name} category="navigation" {...props} />
);

// Action-specific unified icon
export const ActionUnifiedIcon = ({ name, ...props }) => (
  <UnifiedIcon name={name} category="action" {...props} />
);

// Status-specific unified icon
export const StatusUnifiedIcon = ({ name, ...props }) => (
  <UnifiedIcon name={name} category="status" {...props} />
);

// Business-specific unified icon
export const BusinessUnifiedIcon = ({ name, ...props }) => (
  <UnifiedIcon name={name} category="business" {...props} />
);

// Settings-specific unified icon
export const SettingsUnifiedIcon = ({ name, ...props }) => (
  <UnifiedIcon name={name} category="settings" {...props} />
);

// Interactive unified icon with container
export const InteractiveUnifiedIcon = ({ name, onPress, ...props }) => (
  <UnifiedIcon 
    name={name} 
    containerSize={44}
    backgroundColor="transparent"
    {...props}
  />
);

// Unified icon with background circle
export const CircleUnifiedIcon = ({ name, backgroundColor, ...props }) => {
  const { theme } = useTheme();
  return (
    <UnifiedIcon 
      name={name} 
      containerSize={40}
      backgroundColor={backgroundColor || theme.colors.primaryContainer}
      borderRadius={20}
      colorContext="primary"
      {...props}
    />
  );
};

// Export sizes for external use
export { UNIFIED_ICON_SIZES, ICON_LIBRARIES };

// Migration utilities
export const migrateToPhosphor = (iconName, library = 'feather') => {
  if (library === 'feather') {
    return getMigratedLocalIcon(iconName);
  }
  return iconName;
};

export const createIconMigrationReport = (iconsUsed) => {
  const report = {
    total: iconsUsed.length,
    migrated: 0,
    needsMigration: 0,
    notFound: 0,
    details: []
  };

  iconsUsed.forEach(iconName => {
    const phosphorEquivalent = getMigratedLocalIcon(iconName);
    const status = phosphorEquivalent === 'Question' ? 'not_found' : 'migrated';

    report.details.push({
      original: iconName,
      phosphor: phosphorEquivalent,
      status
    });

    if (status === 'migrated') {
      report.migrated++;
    } else {
      report.notFound++;
    }
  });

  return report;
};

export default React.memo(UnifiedIcon);
