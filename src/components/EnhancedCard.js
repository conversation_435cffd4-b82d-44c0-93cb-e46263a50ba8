import { Text   } from 'react-native-paper';
import React, { useCallback } from 'react';
import { View, TouchableOpacity, Text, StyleSheet   } from 'react-native';
import LocalIcon, { NavigationLocalIcon, ActionLocalIcon, BusinessLocalIcon, SettingsLocalIcon, StatusLocalIcon } from './LocalIcon';

// ENHANCED CARD - Better than basic View, lighter than react-native-paper
const EnhancedCard = ({
  children,
  onPress,
  variant = 'default', // default, elevated, outlined, flat
  padding = 'medium', // none, small, medium, large
  margin = 'none', // none, small, medium, large
  borderRadius = 'medium', // none, small, medium, large, round
  style,
  ...props
}) => {
  const getVariantStyles = () => {
    switch(variant) {
      case 'elevated':
        return {
          backgroundColor: '#ffffff',
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 4},
          shadowOpacity: 0.1,
          shadowRadius: 6,
          elevation: 4};
      case 'outlined':
        return {
          backgroundColor: '#ffffff',
          borderWidth: 1,
          borderColor: '#e5e7eb',
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 1},
          shadowOpacity: 0.05,
          shadowRadius: 2,
          elevation: 1};
      case 'flat':
        return { backgroundColor: '#f8fafc',
          borderWidth: 0};
      default: // default
        return {
          backgroundColor: '#ffffff',
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 2},
          shadowOpacity: 0.1,
          shadowRadius: 4,
          elevation: 3};
    }
  };

  const getPaddingStyles = () => {
    switch(padding) {
      case 'none':
        return { padding: 0  };
      case 'small':
        return { padding: 12  };
      case 'medium':
        return { padding: 16  };
      case 'large':
        return { padding: 20  };
      default:
        return { padding: 16  };
    }
  };

  const getMarginStyles = () => {
    switch(margin) {
      case 'none':
        return { margin: 0  };
      case 'small':
        return { margin: 8  };
      case 'medium':
        return { margin: 12  };
      case 'large':
        return { margin: 16  };
      default:
        return { margin: 0  };
    }
  };

  const getBorderRadiusStyles = () => {
    switch(borderRadius) {
      case 'none':
        return { borderRadius: 0  };
      case 'small':
        return { borderRadius: 8  };
      case 'medium':
        return { borderRadius: 12  };
      case 'large':
        return { borderRadius: 16  };
      case 'round':
        return { borderRadius: 999  };
      default:
        return { borderRadius: 12  };
    }
  };

  const variantStyles = getVariantStyles();
  const paddingStyles = getPaddingStyles();
  const marginStyles = getMarginStyles();
  const borderRadiusStyles = getBorderRadiusStyles();

  const cardStyles = [
    styles.card,
    variantStyles,
    paddingStyles,
    marginStyles,
    borderRadiusStyles,
    style];
  if(onPress) {
    return (
      <TouchableOpacity
        style={cardStyles}
        onPress={onPress}
        activeOpacity={0.7}
        {...props}>
        {children}
      </TouchableOpacity>
    );
  }

  return (
    <View style={cardStyles} {...props}>
      {children}
    </View>
  );
};

// Specialized card components
export const StatCard = ({ title, value, icon, color = '#6366f1', onPress, style }) => (
  <EnhancedCard
    variant="default"
    padding="medium"
    onPress={onPress}
    style={[{ borderLeftWidth: 4, borderLeftColor: color }, style}>
    <View style={styles.statHeader}>
      {icon && <Text style={styles.statIcon}>
        {icon === 'orders' ? '📋' : icon === 'customers' ? '👥' : icon === 'money' ? '💰' : '❓'}
      </Text>}
      <Text style={styles.statTitle}>{title}</Text>
    </View>
    <Text style={styles.statValue}>{value}</Text>
  </EnhancedCard>
);

export const ActionCard = ({ title, subtitle, icon, onPress, style }) => (
  <EnhancedCard
    variant="default"
    padding="medium"
    onPress={onPress}
    style={[styles.actionCard, style}>
    {icon && (
      <View style={styles.actionIconContainer}>
        <Text style={styles.actionIcon}>
          {icon === 'add' ? '➕' : icon === 'edit' ? '✏️' : icon === 'delete' ? '🗑️' : '❓'}
        </Text>
      </View>
    )}
    <Text style={styles.actionTitle}>{title}</Text>
    {subtitle && <Text style={styles.actionSubtitle}>{subtitle}</Text>}
  </EnhancedCard>
);

export const InfoCard = ({ children, variant = 'info', style }) => {
  const getInfoVariantStyles = () => {
    switch(variant) {
      case 'success':
        return { backgroundColor: '#f0fdf4', borderColor: '#22c55e'  };
      case 'warning':
        return { backgroundColor: '#fffbeb', borderColor: '#f59e0b'  };
      case 'error':
        return { backgroundColor: '#fef2f2', borderColor: '#ef4444'  };
      default: // info
        return { backgroundColor: '#eff6ff', borderColor: '#3b82f6'  };
    }
  };

  return (
    <EnhancedCard
      variant="outlined"
      padding="medium"
      style={[getInfoVariantStyles(), style}>
      {children}
    </EnhancedCard>
  );
};

export default React.memo(EnhancedCard);

const styles = StyleSheet.create({
  card: {
    overflow: 'hidden'},
  statHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8},
  statIcon: {
    fontSize: 20,
    marginRight: 8},
  statTitle: {
    fontSize: 14,
    color: '#6b7280',
    fontWeight: '500'},
  statValue: {
    fontSize: 24,
    fontWeight: 'bold'},
  actionCard: {
    alignItems: 'center',
    minHeight: 100,
    justifyContent: 'center'},
  actionIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#f3f4f6',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8},
  actionIcon: {
    fontSize: 24},
  actionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1f2937',
    textAlign: 'center'},
  actionSubtitle: {
    fontSize: 12,
    color: '#6b7280',
    textAlign: 'center',
    marginTop: 4}});