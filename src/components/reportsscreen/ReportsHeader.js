import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, IconButton } from 'react-native-paper';

const ReportsHeader = () => {
  return (
    <View style={styles.card}>
      <Text variant="headlineMedium">Reports Dashboard</Text>
      <IconButton icon="refresh" onPress={() => {}} />
    </View>
  );
};

export default React.memo(ReportsHeader);

const styles = StyleSheet.create({
  card: {
    margin: 16,
    elevation: 2
},
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16
}
});