/**
 * Native Chart Components - Lightweight SVG-based charts
 * Replaces react-native-chart-kit to reduce dependencies
 */
import React from 'react';
import { View, Dimensions, StyleSheet   } from 'react-native';
import { Text   } from 'react-native-paper';
import { useTheme   } from '../../context/ThemeContext';
// FIXED: Removed invalid semicolon from import
import Svg, { 
  Line, 
  Circle, 
  Rect, 
  Path, 
  Text as SvgText,
  G,
  Defs,
  LinearGradient,
  Stop
} from 'react-native-svg';

const { width: screenWidth  } = Dimensions.get('window');

// Line Chart Component
export const LineChart = ({ 
  data, 
  width = screenWidth - 40, 
  height = 220,
  bezier = false,
  style = {}
}) => {
  const { theme  } = useTheme();
  const { labels = [], datasets = []  } = data;
  const dataset = datasets[0]?.data || [];
  
  if (!dataset.length) return null;

  const padding = 40;
  const chartWidth = width - padding * 2;
  const chartHeight = height - padding * 2;
  
  const maxValue = Math.max(...dataset, 0);
  const minValue = Math.min(...dataset, 0);
  const valueRange = maxValue - minValue || 1;
  
  const points = dataset.map((value, index) => ({
    x: padding + (index * chartWidth) / (dataset.length > 1 ? dataset.length - 1 : 1),
    y: padding + chartHeight - ((value - minValue) / valueRange) * chartHeight
  }));

  const pathData = points.reduce((path, point, index) => {
    if (index === 0) return `M ${point.x} ${point.y}`;
    if(bezier) {
      const prevPoint = points[index - 1];
      const cpx1 = prevPoint.x + (point.x - prevPoint.x) / 3;
      const cpy1 = prevPoint.y;
      const cpx2 = point.x - (point.x - prevPoint.x) / 3;
      const cpy2 = point.y;
      return `${path} C ${cpx1},${cpy1} ${cpx2},${cpy2} ${point.x},${point.y}`;
    }
    return `${path} L ${point.x} ${point.y}`;
  }, '');

  return (
    <View style={[styles.chartContainer, style]}>
      <Svg width={width} height={height}>
        <Defs>
          <LinearGradient id="lineGradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <Stop offset="0%" stopColor={theme.colors.primary} stopOpacity="0.8" />
            <Stop offset="100%" stopColor={theme.colors.primary} stopOpacity="0.2" />
          </LinearGradient>
        </Defs>
        {[0, 1, 2, 3, 4].map(i => <Line key={i} x1={padding} y1={padding + (i * chartHeight) / 4} x2={width - padding} y2={padding + (i * chartHeight) / 4} stroke={theme.colors.outline} strokeWidth="0.5" opacity="0.3" />)}
        <Path d={pathData} stroke={theme.colors.primary} strokeWidth="3" fill="none" strokeLinecap="round" strokeLinejoin="round" />
        {points.map((point, index) => <Circle key={index} cx={point.x} cy={point.y} r="4" fill={theme.colors.primary} stroke={theme.colors.surface} strokeWidth="2" />)}
        {labels.slice(0, dataset.length).map((label, index) => <SvgText key={index} x={points[index]?.x || 0} y={height - 10} fontSize="12" fill={theme.colors.onSurface} textAnchor="middle">{label}</SvgText>)}
      </Svg>
    </View>
  );
};

// Bar Chart Component
export const BarChart = ({ 
  data, 
  width = screenWidth - 40, 
  height = 220,
  showValuesOnTopOfBars = false,
  style = {}
}) => {
  const { theme  } = useTheme();
  const { labels = [], datasets = []  } = data;
  const dataset = datasets[0]?.data || [];
  
  if (!dataset.length) return null;

  const padding = 40;
  const chartWidth = width - padding * 2;
  const chartHeight = height - padding * 2;
  
  const maxValue = Math.max(...dataset, 0);
  const barWidth = chartWidth / dataset.length * 0.6;
  const barSpacing = chartWidth / dataset.length * 0.4;

  return (
    <View style={[styles.chartContainer, style]}>
      <Svg width={width} height={height}>
        {[0, 1, 2, 3, 4].map(i => <Line key={i} x1={padding} y1={padding + (i * chartHeight) / 4} x2={width - padding} y2={padding + (i * chartHeight) / 4} stroke={theme.colors.outline} strokeWidth="0.5" opacity="0.3" />)}
        {dataset.map((value, index) => {
          const barHeight = (value / (maxValue || 1)) * chartHeight;
          const x = padding + index * (barWidth + barSpacing) + barSpacing / 2;
          const y = padding + chartHeight - barHeight;
          return (
            <G key={index}>
              <Rect x={x} y={y} width={barWidth} height={barHeight} fill={theme.colors.primary} rx="4" />
              {showValuesOnTopOfBars && <SvgText x={x + barWidth / 2} y={y - 5} fontSize="12" fill={theme.colors.onSurface} textAnchor="middle">{value}</SvgText>}
            </G>
          );
        })}
        {labels.slice(0, dataset.length).map((label, index) => <SvgText key={index} x={padding + index * (barWidth + barSpacing) + barSpacing / 2 + barWidth / 2} y={height - 10} fontSize="12" fill={theme.colors.onSurface} textAnchor="middle">{label}</SvgText>)}
      </Svg>
    </View>
  );
};

// Pie Chart Component
export const PieChart = ({ 
  data = [], 
  width = screenWidth - 40, 
  height = 220,
  style = {}
}) => {
  const { theme  } = useTheme();
  if (!data.length) return null;

  const radius = Math.min(width, height) / 2 - 40;
  const centerX = width / 2;
  const centerY = height / 2;
  
  const total = data.reduce((sum, item) => sum + item.population, 0);
  let currentAngle = -Math.PI / 2;

  return (
    <View style={[styles.chartContainer, style]}>
      <Svg width={width} height={height}>
        {data.map((item, index) => {
          const sliceAngle = (item.population / total) * 2 * Math.PI;
          const x1 = centerX + radius * Math.cos(currentAngle);
          const y1 = centerY + radius * Math.sin(currentAngle);
          currentAngle += sliceAngle;
          const x2 = centerX + radius * Math.cos(currentAngle);
          const y2 = centerY + radius * Math.sin(currentAngle);
          const largeArcFlag = sliceAngle > Math.PI ? 1 : 0;
          const pathData = `M ${centerX},${centerY} L ${x1},${y1} A ${radius},${radius} 0 ${largeArcFlag} 1 ${x2},${y2} Z`;
          return <Path key={index} d={pathData} fill={item.color || theme.colors.primary} stroke={theme.colors.surface} strokeWidth="2" />;
        })}
      </Svg>
      <View style={styles.legendContainer}>
        {data.map((item, index) => (
          <View key={index} style={styles.legendItem}>
            <View style={[styles.legendColorBox, { backgroundColor: item.color || theme.colors.primary }]} />
            <Text style={{ color: theme.colors.onSurface }}>{item.name}</Text>
          </View>
        ))}
      </View>
    </View>
  );
};

// Progress Chart Component
export const ProgressChart = ({
  data = {},
  width = screenWidth - 40,
  height = 220,
  strokeWidth = 16,
  radius = 80,
  chartConfig = {},
  style = {}
}) => {
  const { theme  } = useTheme();
  const { data: values = []  } = data;
  const value = values[0] || 0;
  const percentage = Math.round(value * 100);

  const circumference = 2 * Math.PI * radius;
  const strokeDashoffset = circumference - (value * circumference);

  return (
    <View style={[styles.chartContainer, style]}>
      <Svg width={width} height={height}>
        <Circle cx={width / 2} cy={height / 2} r={radius} stroke={theme.colors.outline} strokeWidth={strokeWidth} fill="none" opacity={0.3} />
        <Circle cx={width / 2} cy={height / 2} r={radius} stroke={chartConfig.color ? chartConfig.color(1) : theme.colors.primary} strokeWidth={strokeWidth} fill="none" strokeDasharray={circumference} strokeDashoffset={strokeDashoffset} strokeLinecap="round" transform={`rotate(-90 ${width / 2} ${height / 2}) } />
        <SvgText x={width / 2} y={height / 2} fontSize="24" fontWeight="bold" fill={theme.colors.onSurface} textAnchor="middle" dy="8">{`${percentage}%`}</SvgText>
      </Svg>
    </View>
  );
};

// Contribution Graph (simplified version)
export const ContributionGraph = ({ style = {} }) => {
  const { theme  } = useTheme();
  return (
    <View style={[styles.chartContainer, style]}>
      <Text style={{ color: theme.colors.onSurface }>Contribution Graph (Simplified)</Text>
      <Text style={{ color: theme.colors.onSurfaceVariant, marginTop: 8 }>Feature available in full version</Text>
    </View>
  );
};

// FIXED: Stylesheet is now static and correct.
const styles = StyleSheet.create({
  chartContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 8,
  },
  legendContainer: {
    marginTop: 16,
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    margin: 4,
  },
  legendColorBox: {
    width: 12,
    height: 12,
    marginRight: 4,
    borderRadius: 2,
  },
});