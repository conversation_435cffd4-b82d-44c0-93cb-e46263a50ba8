/**
 * CompactMeasurementUnitSelector - Compact 3-button measurement unit selector
 */

import React, { useMemo } from 'react';
import { View, StyleSheet   } from 'react-native';
import { SegmentedButtons   } from 'react-native-paper';
import { useTheme   } from '../context/ThemeContext';
import { SPACING, BORDER_RADIUS   } from '../theme/designTokens';

const CompactMeasurementUnitSelector = ({ 
  value, 
  onValueChange, 
  options = [
    { key: 'inches', label: 'Inches', icon: 'ruler' },
    { key: 'cm', label: 'CM', icon: 'ruler' },
    { key: 'feet', label: 'Feet', icon: 'ruler' }
  ],
  style = {} 
}) => {
  const { theme  } = useTheme();

  // FIXED: Stylesheet moved inside component and memoized for performance
  const styles = useMemo(() => StyleSheet.create({
    container: {
      marginVertical: SPACING.xs},
    segmentedButtons: {
      borderRadius: BORDER_RADIUS.md,
      backgroundColor: theme.colors.surfaceVariant},
    button: {
      borderRadius: BORDER_RADIUS.sm,
      minHeight: 36,
      borderWidth: 0, // Remove default border to control with background color
    }}), [theme]);

  return (
    <View style={[styles.container, style]}>
      <SegmentedButtons
        value={value}
        onValueChange={onValueChange}
        buttons={options.map(option => {
          const isSelected = value === option.key;
          return {
            value: option.key,
            label: option.label,
            icon: option.icon,
            style: [
              styles.button,
              {
                backgroundColor: isSelected 
                  ? theme.colors.primaryContainer 
                  : 'transparent', // Use transparent for non-selected
              }
            ],
            // FIXED: Corrected syntax for labelStyle
            labelStyle: {
              color: isSelected 
                ? theme.colors.onPrimaryContainer 
                : theme.colors.onSurface,
              fontSize: 12,
              fontWeight: isSelected ? '600' : '400'},
            showSelectedCheck: false};
        })}
        // FIXED: Corrected style prop
        style={styles.segmentedButtons}
        density="small"
      />
    </View>
  );
};

export default React.memo(CompactMeasurementUnitSelector);