import React, { useCallback } from 'react';
import { View, StyleSheet, Alert   } from 'react-native';
import { Text   } from 'react-native-paper';
import { Button   } from 'react-native-paper';
import { Card   } from 'react-native-paper';
import { Surface   } from 'react-native-paper';


/**
 * Error Boundary Component to catch and handle React errors gracefully
 */
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true,
      error,
      errorId: Date.now().toString(),
   };
  }

  componentDidCatch(error, errorInfo) {
    // Log the error with detailed information
    const errorDetails = {
      error: error.toString(),
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      errorId: this.state.errorId,
    };

    console.error('React Error Boundary caught an error:', errorDetails);

    // Check for specific error types
    if (error.message.includes('Maximum call stack size exceeded')) {
      console.error('CRITICAL: Maximum call stack exceeded - potential infinite loop detected');

      Alert.alert(
        'Critical Error',
        'A maximum call stack error was detected and prevented. The app has been stabilized.',
        [{ text: 'OK' }]
      );
    }

    this.setState({
      error,
      errorInfo,
    });

    // In development, you might want to send this to a crash reporting service
    if(__DEV__) {
      console.error('Error Boundary Details:', errorDetails);
    }
  }

  handleRetry = () => {
    // Reset error state and try to recover
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    });

    // Reset any tracking systems
    // SafeRecursion.resetAll(); // Commented out for web compatibility

    // If there's a custom retry handler, call it
    if(this.props.onRetry) {
      this.props.onRetry();
    }
  };

  handleReload = () => {
    // Force a complete app reload if possible
    if(this.props.onReload) {
      this.props.onReload();
    } else {
      // Fallback: try to reset the app state
      this.handleRetry();
    }
  };

  render() {
    if(this.state.hasError) {
      // Custom fallback UI
      if(this.props.fallback) {
        return this.props.fallback(this.state.error, this.handleRetry);
      }

      // Default error UI
      return (
        <View style={styles.container}>
          <Surface style={styles.errorContainer} elevation={2}>
            <Card style={styles.errorCard}>
              <Card.Content>
                <Text variant="headlineSmall" style={styles.errorTitle}>
                  Oops! Something went wrong
                </Text>

                <Text variant="bodyMedium" style={styles.errorMessage}>
                  We encountered an unexpected error. Don't worry, your data is safe.
                </Text>

                {__DEV__ && this.state.error && (
                  <View style={styles.debugInfo}>
                    <Text variant="bodySmall" style={styles.debugTitle}>
                      Debug Information:
                    </Text>
                    <Text variant="bodySmall" style={styles.debugText}>
                      {this.state.error.toString()}
                    </Text>
                  </View>
                )}

                <View style={styles.buttonContainer}>
                  <Button
                    mode="contained"
                    onPress={this.handleRetry}
                    style={styles.retryButton}>
                    Try Again
                  </Button>

                  <Button
                    mode="outlined"
                    onPress={this.handleReload}
                    style={styles.reloadButton}>
                    Reload App
                  </Button>
                </View>

                <Text variant="bodySmall" style={styles.errorId}>
                  Error ID: {this.state.errorId}
                </Text>
              </Card.Content>
            </Card>
          </Surface>
        </View>
      );
    }

    return this.props.children;
  }
}

/**
 * Higher-order component to wrap components with error boundary
 */
export const withErrorBoundary = (Component, errorBoundaryProps = {}) => {
  const WrappedComponent = (props) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  return WrappedComponent;
};

/**
 * Hook to handle errors in functional components
 */
export const useErrorHandler = () => {
  const [error, setError] = React.useState(null);

  const resetError = React.useCallback(() => {
    setError(null);
  }, []);

  const handleError = React.useCallback((error) => {
    console.error('Component error handled:', error);
    setError(error);
  }, []);

  // Throw error to be caught by error boundary
  if(error) {
    throw error;
  }

  return { handleError, resetError  };
};

/**
 * Safe component wrapper that catches render errors
 */
export const SafeComponent = ({ children, fallback, onError }) => {
  try {
    return children;
  } catch (error) {
    console.error('Safe component caught render error:', error);

    if(onError) {
      onError(error);
    }

    if(fallback) {
      return fallback(error);
    }

    return (
      <View style={styles.safeComponentError}>
        <Text>Component failed to render</Text>
      </View>
    );
  }
};






export default ErrorBoundary;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  errorContainer: {
    width: '100%',
    maxWidth: 400,
    borderRadius: 12,
  },
  errorCard: {
    padding: 20,
  },
  errorTitle: {
    textAlign: 'center',
    marginBottom: 16,
    color: '#d32f2f',
    fontWeight: 'bold',
  },
  errorMessage: {
    textAlign: 'center',
    marginBottom: 20,
    color: '#666',
    lineHeight: 22,
  },
  debugInfo: {
    backgroundColor: '#f5f5f5',
    padding: 12,
    borderRadius: 8,
    marginBottom: 20,
  },
  debugTitle: {
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  debugText: {
    fontFamily: 'monospace',
    fontSize: 12,
    color: '#666',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  retryButton: {
    flex: 1,
    marginRight: 8,
  },
  reloadButton: {
    flex: 1,
    marginLeft: 8,
  },
  errorId: {
    textAlign: 'center',
    color: '#999',
    fontSize: 10,
  },
  safeComponentError: {
    padding: 16,
    backgroundColor: '#ffebee',
    borderRadius: 8,
    alignItems: 'center',
  },
});