import { Text } from 'react-native-paper';
// QR CODE DISPLAY COMPONENT - Show generated QR codes
import React, { useState, useCallback } from 'react';
import { View, Text, Image, StyleSheet, Alert, Share, Dimensions } from 'react-native';
import EnhancedCard from './EnhancedCard';
import EnhancedButton from './EnhancedButton';
// Removed LocalIcon import - using inline icons

const { width } = Dimensions.get('window');

const QRCodeDisplay = ({ 
  data, 
  title = 'QR Code', 
  subtitle = '', 
  size = 200,
  onClose,
  showActions = true 
}) => {
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);

  if (!data) {
    return (
      <EnhancedCard variant="default" padding="large">
        <View style={styles.style1}>
          <LocalIcon name="alert" size={48} color="#ef4444" />
          <Text style={styles.style1}>No QR Data</Text>
          <Text style={styles.style1}>Unable to generate QR code</Text>
        </View>
      </EnhancedCard>
    );
  }

  // Generate QR code URL
  const qrCodeURL = `https://api.qrserver.com/v1/create-qr-code/?size=${size}x${size}&data=${encodeURIComponent(data)}`;

  const handleShare = useCallback(async () => {
    try {
      await Share.share({
        message: `${title}\n\nQR Code Data: ${data}`,
        title: title,
        url: qrCodeURL,
      }), []);
    } catch (error) {
      console.error('Failed to share QR code:', error);
      Alert.alert('Share Error', 'Failed to share QR code');
    }
  };

  const handleSave = useCallback(() => {
    // In a real app, you would save the QR code to device gallery
    Alert.alert(
      'Save QR Code',
      'QR code would be saved to your device gallery.',
      [
        { text: 'OK' }
      ]
    ), []);
  };

  const handleImageLoad = useCallback(() => {
    setImageLoading(false), []);
    setImageError(false);
  };

  const handleImageError = useCallback(() => {
    setImageLoading(false);
    setImageError(true);
  }, []);

  return (
    <EnhancedCard variant="default" padding="large" style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>{title}</Text>
          {subtitle ? <Text style={styles.subtitle}>{subtitle}</Text> : null}
        </View>
        {onClose && (
          <EnhancedButton
            variant="ghost"
            size="small"
            icon="close"
            onPress={onClose}
          />
        )}
      </View>

      {/* QR Code */}
      <View style={styles.qrContainer}>
        {imageLoading && (
          <View style={[styles.qrPlaceholder, { width: size, height: size }}>
            <Text style={styles.loadingIcon}>🔄</Text>
            <Text style={styles.loadingText}>Generating QR Code...</Text>
          </View>
        )}
        
        {imageError && (
          <View style={[styles.qrPlaceholder, { width: size, height: size }}>
            <Text style={styles.errorIcon}>⚠️</Text>
            <Text style={styles.loadingText}>Failed to load QR code</Text>
          </View>
        )}

        <Image
          source={{ uri: qrCodeURL }}
          style={[
            styles.qrImage,
            { width: size, height: size },
            (imageLoading || imageError) && styles.hidden
          }
          onLoad={handleImageLoad}
          onError={handleImageError}
          resizeMode="contain"
        />
      </View>

      {/* Data Preview */}
      <View style={styles.dataContainer}>
        <Text style={styles.dataLabel}>QR Code Data:</Text>
        <Text style={styles.dataText} numberOfLines={3}>
          {data}
        </Text>
      </View>

      {/* Actions */}
      {showActions && (
        <View style={styles.actionsContainer}>
          <EnhancedButton
            variant="outline"
            size="medium"
            title="Share"
            icon="share"
            onPress={handleShare}
            style={styles.actionButton}
          />
          <EnhancedButton
            variant="secondary"
            size="medium"
            title="Save"
            icon="download"
            onPress={handleSave}
            style={styles.actionButton}
          />
        </View>
      )}
    </EnhancedCard>
  );
};






export default QRCodeDisplay;

const styles = StyleSheet.create({
  container: {
    maxWidth: width - 40,
    alignSelf: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    color: '#6b7280',
  },
  qrContainer: {
    alignItems: 'center',
    marginBottom: 20,
    position: 'relative',
  },
  qrImage: {
    borderRadius: 8,
    backgroundColor: '#ffffff',
    padding: 10,
  },
  qrPlaceholder: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f3f4f6',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderStyle: 'dashed',
  },
  hidden: {
    position: 'absolute',
    opacity: 0,
  },
  loadingText: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 8,
    textAlign: 'center',
  },
  dataContainer: {
    backgroundColor: '#f9fafb',
    padding: 12,
    borderRadius: 8,
    marginBottom: 20,
  },
  dataLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 4,
  },
  dataText: {
    fontSize: 14,
    color: '#6b7280',
    fontFamily: 'monospace',
    lineHeight: 20,
  },
  actionsContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
  errorContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  errorTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#ef4444',
    marginTop: 12,
    marginBottom: 8,
  },
  errorText: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
  },
  loadingIcon: {
    fontSize: 32,
    color: '#6b7280',
  },
  errorIcon: {
    fontSize: 32,
    color: '#ef4444',
  },
});