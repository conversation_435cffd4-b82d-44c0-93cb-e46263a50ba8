/**
 * ImagePicker Component - Handles image selection from camera or gallery
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { View, StyleSheet, Alert, Image, TouchableOpacity   } from 'react-native';
import { Text, ActivityIndicator   } from 'react-native-paper';
// Assuming useTheme is from your custom context, not react-native-paper
import { useTheme   } from '../context/ThemeContext'; 
import LocalIcon from './LocalIcon';
import ImagePickerService from '../services/ImagePickerService';
import ImageOptimizationService from '../services/ImageOptimizationService';
import { SPACING, BORDER_RADIUS, TYPOGRAPHY   } from '../theme/designTokens';

const ImagePicker = ({
  onImageSelected,
  currentImage,
  style,
  placeholder = "Add Image",
  showEditIcon = true,
  size = 80,
  borderRadius = BORDER_RADIUS.lg
}) => {
  const { theme  } = useTheme();
  const [isLoading, setIsLoading] = useState(false);
  const [displayImage, setDisplayImage] = useState(currentImage);

  useEffect(() => {
    setDisplayImage(currentImage);
  }, [currentImage]);

  const handleImageAction = async (action) => {
    try {
      setIsLoading(true);
      const options = { allowsEditing: true, aspect: [1, 1], quality: 0.9 };
      const imageUri = action === 'camera' 
        ? await ImagePickerService.takePhoto(options)
        : await ImagePickerService.selectFromGallery(options);

      if(imageUri) {
        const optimizedResult = await ImageOptimizationService.optimizeImage(imageUri, {
          useCase: 'profile', enableWebP: true, quality: 0.85
        });
        const finalUri = optimizedResult?.uri || imageUri;
        setDisplayImage(finalUri);
        onImageSelected(finalUri);
      }
    } catch (error) {
      // Don't show alert if user cancels picker
      if(error.code !== 'E_PICKER_CANCELLED') {
          console.error(`Error with ${action}:`, error);
          Alert.alert('Error', `Failed to get image. Please try again.`);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const showImageSourceOptions = () => {
    Alert.alert('Select Image Source', 'Choose how you want to add an image', [
      { text: 'Camera', onPress: () => handleImageAction('camera') },
      { text: 'Gallery', onPress: () => handleImageAction('gallery') },
      { text: 'Cancel', style: 'cancel' }], { cancelable: true });
  };

  const removeImage = () => {
    Alert.alert('Remove Image', 'Are you sure you want to remove this image?', [
      { text: 'Cancel', style: 'cancel' },
      { text: 'Remove', onPress: () => { onImageSelected(null); }, style: 'destructive' }]);
  };
  
  // FIXED: Stylesheet moved inside component and memoized for performance
  const styles = useMemo(() => StyleSheet.create({
    container: { marginBottom: SPACING.md },
    imageContainer: { width: size, height: size, borderRadius, overflow: 'hidden', position: 'relative', backgroundColor: theme.colors.surfaceVariant },
    image: { width: '100%', height: '100%', resizeMode: 'cover' },
    imageOverlay: { position: 'absolute', top: SPACING.xs, right: SPACING.xs, flexDirection: 'row', gap: SPACING.xs },
    overlayButton: { width: 28, height: 28, borderRadius: 14, justifyContent: 'center', alignItems: 'center' },
    editButton: { backgroundColor: `${theme.colors.surface} E6` },
    removeButton: { backgroundColor: `${theme.colors.error} E6` },
    placeholderContainer: { width: size, height: size, borderWidth: 1, borderStyle: 'dashed', justifyContent: 'center', alignItems: 'center', padding: SPACING.md, flexDirection: 'column', gap: SPACING.sm, borderRadius, backgroundColor: theme.colors.surfaceVariant, borderColor: theme.colors.outline },
    placeholderText: { ...TYPOGRAPHY.bodySmall, color: theme.colors.onSurfaceVariant, textAlign: 'center' },
    loadingOverlay: { position: 'absolute', top: 0, left: 0, right: 0, bottom: 0, justifyContent: 'center', alignItems: 'center', backgroundColor: 'rgba(0,0,0,0.3)', borderRadius }}), [theme, size, borderRadius]);

  return (
    <View style={[styles.container, style]}>
      {displayImage ? (
        <TouchableOpacity style={styles.imageContainer} onPress={showImageSourceOptions} disabled={isLoading}>
          <Image source={{ uri: displayImage }} style={styles.image} />
          {showEditIcon && !isLoading && (
            <View style={styles.imageOverlay}>
              <TouchableOpacity style={[styles.overlayButton, styles.removeButton} onPress={(e) => { e.stopPropagation(); removeImage(); }>
                <LocalIcon name="x" size={16} color={theme.colors.onError} />
              </TouchableOpacity>
            </View>
          )}
          {isLoading && <View style={styles.loadingOverlay}><ActivityIndicator animating={true} color={theme.colors.primary} /></View>}
        </TouchableOpacity>
      ) : (
        <TouchableOpacity style={styles.placeholderContainer} onPress={showImageSourceOptions} disabled={isLoading}>
          {isLoading ? (
            <ActivityIndicator animating={true} color={theme.colors.primary} />
          ) : (
            <>
              <LocalIcon name="camera-plus" size={24} color={theme.colors.onSurfaceVariant} />
              <Text style={styles.placeholderText}>{placeholder}</Text>
            </>
          )}
        </TouchableOpacity>
      )}
    </View>
  );
};

export default ImagePicker;