import React, { useCallback } from 'react';
import { View,
  StyleSheet,
  Modal,
  Dimensions,
  } from 'react-native';
import { Text   } from 'react-native-paper';
import { Button   } from 'react-native-paper';
import { IconButton   } from 'react-native-paper';
import { Surface   } from 'react-native-paper';
import { Portal   } from 'react-native-paper';
import { useTheme   } from '../context/ThemeContext';

const { width: screenWidth, height: screenHeight  } = Dimensions.get('window');

const SPACING = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
};

const BORDER_RADIUS = {
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
};

// Modal Types
export const MODAL_TYPES = {
  CONFIRMATION: 'confirmation',
  ALERT: 'alert',
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info',
};

const ConfirmationModal = ({
  visible = false,
  type = MODAL_TYPES.CONFIRMATION,
  title = 'Confirm Action',
  message = 'Are you sure you want to proceed?',
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  onConfirm,
  onCancel,
  onDismiss,
  showCancel = true,
  confirmButtonMode = 'contained',
  cancelButtonMode = 'outlined',
  icon,
  destructive = false,
}) => {
  const { theme  } = useTheme();

  // Get modal configuration based on type
  const getModalConfig = () => {
    switch(type) {
      case MODAL_TYPES.SUCCESS:
        return { icon: icon || 'check-circle',
          iconColor: '#4CAF50',
          confirmText: confirmText || 'OK',
          showCancel: false,
         };

      case MODAL_TYPES.ERROR:
        return { icon: icon || 'alert-circle',
          iconColor: '#F44336',
          confirmText: confirmText || 'OK',
          showCancel: false,
         };

      case MODAL_TYPES.WARNING:
        return { icon: icon || 'alert',
          iconColor: '#FF9800',
          confirmText: confirmText || 'Proceed',
          showCancel: showCancel,
         };

      case MODAL_TYPES.INFO:
        return { icon: icon || 'information',
          iconColor: '#2196F3',
          confirmText: confirmText || 'OK',
          showCancel: false,
         };

      case MODAL_TYPES.ALERT:
        return { icon: icon || 'alert-circle-outline',
          iconColor: theme.colors.primary,
          confirmText: confirmText || 'OK',
          showCancel: false,
         };

      case MODAL_TYPES.CONFIRMATION:
      default:
        return { icon: icon || 'help-circle-outline',
          iconColor: theme.colors.primary,
          confirmText: confirmText || 'Confirm',
          showCancel: showCancel,
         };
    }
  };

  const config = getModalConfig();

  const handleConfirm = useCallback(() => {
    if(onConfirm) {
      onConfirm();
    }
    if(onDismiss) {
      onDismiss();
    }
  }, [onConfirm, onDismiss]);

  const handleCancel = useCallback(() => {
    if(onCancel) {
      onCancel();
    }
    if(onDismiss) {
      onDismiss();
    }
  }, [onCancel, onDismiss]);

  const getConfirmButtonColor = () => {
    if (destructive) return theme.colors.error;
    if (type === MODAL_TYPES.SUCCESS) return '#4CAF50';
    if (type === MODAL_TYPES.ERROR) return '#F44336';
    if (type === MODAL_TYPES.WARNING) return '#FF9800';
    return theme.colors.primary;
  };

  return (
    <Portal>
      <Modal
        visible={visible}
        transparent
        animationType="fade"
        onRequestClose={handleCancel}
        statusBarTranslucent
      >
        <View style={styles.style2}>
          <Surface 
            style={styles.style6}
            elevation={8}>
            {/* Close button for non-critical modals */}
            {!destructive && type !== MODAL_TYPES.ERROR && (
              <IconButton
                icon="close"
                size={20}
                onPress={handleCancel}
                style={styles.style2}
              />
            )}

            {/* Icon */}
            {config.icon && (
              <View style={styles.style2}>
                <IconButton
                  icon={config.icon}
                  size={48}
                  iconColor={config.iconColor}
                  style={styles.style5}
                />
              </View>
            )}

            {/* Title */}
            <Text 
              variant="headlineSmall" 
              style={styles.style4}>
              {title}
            </Text>

            {/* Message */}
            <Text 
              variant="bodyLarge" 
              style={styles.style3}>
              {message}
            </Text>

            {/* Buttons */}
            <View style={styles.style2}>
              {config.showCancel && (
                <Button
                  mode={cancelButtonMode}
                  onPress={handleCancel}
                  style={styles.style2}
                  contentStyle={styles.buttonContent}>
                  {cancelText}
                </Button>
              )}

              <Button
                mode={confirmButtonMode}
                onPress={handleConfirm}
                style={styles.style1}
                contentStyle={styles.buttonContent}
                textColor={
                  confirmButtonMode === 'contained' 
                    ? theme.colors.onPrimary 
                    : getConfirmButtonColor()
                }>
                {config.confirmText}
              </Button>
            </View>
          </Surface>
        </View>
      </Modal>
    </Portal>
  );
};






export default React.memo(ConfirmationModal);

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.lg,
  },
  modal: {
    borderRadius: BORDER_RADIUS.xl,
    padding: SPACING.xl,
    maxWidth: screenWidth * 0.9,
    minWidth: screenWidth * 0.7,
    maxHeight: screenHeight * 0.8,
  },
  closeButton: {
    position: 'absolute',
    top: SPACING.sm,
    right: SPACING.sm,
    margin: 0,
    zIndex: 1,
  },
  iconContainer: {
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  icon: {
    margin: 0,
  },
  title: {
    textAlign: 'center',
    fontWeight: 'bold',
    marginBottom: SPACING.md,
  },
  message: {
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: SPACING.xl,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: SPACING.md,
  },
  button: {
    flex: 1,
    borderRadius: BORDER_RADIUS.md,
  },
  buttonContent: {
    paddingVertical: SPACING.sm,
  },
  cancelButton: {
    // Additional styles for cancel button if needed
  },
  confirmButton: {
    // Additional styles for confirm button if needed
  },
  singleButton: {
    // When there's only one button, it takes full width
  },
  style1: {
    flex: 1,
    borderRadius: BORDER_RADIUS.md,
  },
  style2: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.lg,
  },
  style3: {
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: SPACING.xl,
  },
  style4: {
    textAlign: 'center',
    fontWeight: 'bold',
    marginBottom: SPACING.md,
  },
  style5: {
    margin: 0,
  },
  style6: {
    borderRadius: BORDER_RADIUS.xl,
    padding: SPACING.xl,
    maxWidth: screenWidth * 0.9,
    minWidth: screenWidth * 0.7,
    maxHeight: screenHeight * 0.8,
  },
});