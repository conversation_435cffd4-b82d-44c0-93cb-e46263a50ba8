/**
 * QRCodeGenerator - Component for generating and displaying QR codes
 */

import React, { useRef, useEffect, useCallback, useMemo } from 'react';
import { View, StyleSheet, Alert   } from 'react-native';
import { Text   } from 'react-native-paper';
import { Card   } from 'react-native-paper';
import { IconButton   } from 'react-native-paper';
// import QRCode from 'react-native-qrcode-svg';
import { useTheme   } from '../context/ThemeContext';
import QRCodeService from '../services/QRCodeService';

const QRCodeGenerator = ({
  type,
  entity,
  size = 200,
  showActions = true,
  showData = true,
  style,
  onGenerated,
}) => {
  const { theme  } = useTheme();
  const qrRef = useRef(null);

  // Generate QR data
  const qrData = QRCodeService.generateQRData(type, entity);
  const qrString = JSON.stringify(qrData);

  // Register QR ref with service
  useEffect(() => {
    if(qrRef.current) {
      QRCodeService.registerQRRef(`${type} _${entity.id}`,` qrRef.current);
    }
  }, [type, entity.id]);

  // Call onGenerated callback when QR is ready
  useEffect(() => {
    if(onGenerated && qrRef.current) {
      onGenerated(qrRef.current, qrData);
    }
  }, [onGenerated, qrData]);

  const handleSave = useCallback(async () => {
    try {
      const result = await QRCodeService.saveQRCodeAsImage(type, entity);
      if(result.success) {
        Alert.alert('Success', 'QR code saved to gallery!');
      } else {
        Alert.alert('Error', result.error || 'Failed to save QR code');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to save QR code');
    }
  }, [type, entity]);

  const handleShare = useCallback(async () => {
    try {
      const result = await QRCodeService.shareQRCode(type, entity);
      if(!result.success) {
        Alert.alert('Error', result.error || 'Failed to share QR code');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to share QR code');
    }
  }, [type, entity]);

  const getTitle = () => {
    switch(type) {
      case 'order':
        return `Order #${entity.id}`;
      case 'product':
      case 'garment':
        return entity.name;
      case 'customer':
        return entity.name;
      case 'appointment':
        return `Appointment - ${entity.date}`;`
      case 'measurement':
        return `Measurements - ${entity.garmentType}`;
      case 'fabric':
        return entity.name;
      default:
        return 'QR Code';
    }
  };

  const getSubtitle = () => {
    switch(type) {
      case 'order':
        return `${entity.customerName || entity.customer} ` • ৳${entity.total}`;
      case 'product':
      case 'garment':
        return `${entity.category} ` • ৳${entity.price}`;
      case 'customer':
        return entity.phone || entity.email;
      case 'appointment':
        return `${entity.time} ` • ${entity.type}`;
      case 'measurement':
        return `Customer ID: ${entity.customerId}`;`
      case 'fabric':
        return `${entity.type} • ${entity.color}`;`
      default:
        return '';
    }
  };

  const styles = useMemo(() => StyleSheet.create({
    container: {
      margin: 8,
    },
    content: {
      alignItems: 'center',
      padding: 16,
    },
    header: {
      alignItems: 'center',
      marginBottom: 16,
    },
    title: {
      fontWeight: '600',
      textAlign: 'center',
      color: theme.colors.onSurface,
    },
    subtitle: {
      marginTop: 4,
      textAlign: 'center',
      color: theme.colors.onSurfaceVariant,
    },
    qrContainer: {
      padding: 16,
      borderRadius: 8,
      backgroundColor: 'white',
      marginBottom: 16,
    },
    dataContainer: {
      alignItems: 'center',
      marginBottom: 16,
    },
    dataLabel: {
      fontWeight: '500',
      marginBottom: 4,
      color: theme.colors.onSurfaceVariant,
    },
    dataText: {
      textAlign: 'center',
      color: theme.colors.onSurfaceVariant,
    },
    actions: {
      flexDirection: 'row',
      gap: 8,
    },
    actionButton: {
      margin: 0,
    },
    qrPlaceholder: {
      textAlign: 'center',
      padding: 20,
    },
  }), [theme]);

  return (
    <Card style={[styles.container, style}>
      <Card.Content style={styles.content}>
        {showData && (
          <View style={styles.header}>
            <Text variant="titleMedium" style={styles.title}>
              {getTitle()}
            </Text>
            {getSubtitle() && (
              <Text variant="bodySmall" style={styles.subtitle}>
                {getSubtitle()}
              </Text>
            )}
          </View>
        )}

        <View style={styles.qrContainer}>
          {/* <QRCode
            ref={qrRef}
            value={qrString}
            size={size}
            color={theme.colors.onSurface}
            backgroundColor={theme.colors.surface}
            logo={null}
            logoSize={30}
            logoBackgroundColor="transparent"
            logoMargin={2}
            logoBorderRadius={15}
            quietZone={10}
          /> */}
          <Text style={styles.qrPlaceholder}>QR Code Placeholder</Text>
        </View>

        {showData && (
          <View style={styles.dataContainer}>
            <Text variant="bodySmall" style={styles.dataLabel}>
              QR Code Data:
            </Text>
            <Text variant="bodySmall" style={styles.dataText}>
              {type.charAt(0).toUpperCase() + type.slice(1)} • ID: {entity.id}
            </Text>
          </View>
        )}

        {showActions && (
          <View style={styles.actions}>
            <IconButton
              icon="download"
              mode="outlined"
              onPress={handleSave}
              iconColor={theme.colors.primary}
              style={styles.actionButton}
            />
            <IconButton
              icon="share"
              mode="outlined"
              onPress={handleShare}
              iconColor={theme.colors.primary}
              style={styles.actionButton}
            />
          </View>
        )}
      </Card.Content>
    </Card>
  );
};

export default QRCodeGenerator;