import React from 'react';
import { View, ActivityIndicator, StyleSheet   } from 'react-native';
import { Text   } from 'react-native-paper';

const LoadingScreen = ({ message = 'Loading...', size = 'large', color = '#007AFF' }) => {
  return (
    <View style={styles.container}>
      <ActivityIndicator size={size} color={color} />
      <Text style={styles.message}>{message}</Text>
    </View>
  );
};






export default LoadingScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    padding: 20},
  message: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
    textAlign: 'center'}});