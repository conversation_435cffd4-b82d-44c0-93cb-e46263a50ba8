/**
 * LocalIcon - Phosphor Icons component for Tailora app
 * Provides a consistent interface for using Phosphor Icons throughout the app
 * Supports all Phosphor icon weights and automatic theme integration
 */

import React from 'react';
import { View, StyleSheet   } from 'react-native';
import { useTheme   } from '../context/ThemeContext';

// Import optimized Phosphor icons instead of the entire library
// Assuming OptimizedLocalIcon is a valid component that accepts these props.
import OptimizedLocalIcon from './LocalIcon';

// Phosphor Icon Sizes following Material Design 3 guidelines
const PHOSPHOR_ICON_SIZES = {
  small: 16,
  medium: 20,
  large: 24,
  extraLarge: 28,
  huge: 32,
  jumbo: 48,
};

// Phosphor Icon Weights
const PHOSPHOR_WEIGHTS = {
  thin: 'thin',
  light: 'light',
  regular: 'regular',
  bold: 'bold',
  fill: 'fill',
  duotone: 'duotone',
};

// Color context mapping for automatic theme integration
const getPhosphorColor = (colorContext, theme) => {
  const colorMap = {
    primary: theme.colors.primary,
    secondary: theme.colors.secondary,
    tertiary: theme.colors.tertiary,
    surface: theme.colors.onSurface,
    surfaceVariant: theme.colors.onSurfaceVariant,
    background: theme.colors.onBackground,
    error: theme.colors.error,
    warning: theme.colors.warning || '#FF9800',
    success: theme.colors.success || '#4CAF50',
    info: theme.colors.info || '#2196F3',
    disabled: theme.colors.onSurfaceDisabled || theme.colors.onSurfaceVariant,
    inverse: theme.colors.inverseOnSurface,
  };
  
  return colorMap[colorContext] || theme.colors.onSurface;
};

const PhosphorIcon = ({
  name,
  size = 'large',
  weight = 'regular',
  color,
  colorContext = 'surface',
  containerSize,
  backgroundColor,
  borderRadius,
  style,
  containerStyle,
  accessibilityLabel,
  debug = false,
}) => {
  const { theme  } = useTheme();
  
  const iconSize = typeof size === 'number' ? size : PHOSPHOR_ICON_SIZES[size] || PHOSPHOR_ICON_SIZES.large;
  const iconColor = color || getPhosphorColor(colorContext, theme);
  const iconWeight = PHOSPHOR_WEIGHTS[weight] || 'regular';
  
  // FIXED: Corrected the props passed to OptimizedLocalIcon
  const iconElement = (
    <OptimizedLocalIcon
      name={name}
      size={iconSize}
      weight={iconWeight}
      color={iconColor}
      style={style}
      debug={debug}
    />
  );

  const touchTargetSize = containerSize || (iconSize < 24 ? 44 : iconSize + 20);
  
  if(containerSize || backgroundColor || borderRadius) {
    // FIXED: Corrected the View component syntax and created the style object inline
    // because it depends on props.
    const dynamicContainerStyle = {
      width: touchTargetSize,
      height: touchTargetSize,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: backgroundColor,
      borderRadius: borderRadius !== undefined ? borderRadius : (backgroundColor ? touchTargetSize / 2 : 0),
    };

    return (
      <View style={[dynamicContainerStyle, containerStyle}>
        {iconElement}
      </View>
    );
  }
  
  return iconElement;
};

// Preset components for common use cases
export const PrimaryPhosphorIcon = (props) => <PhosphorIcon colorContext="primary" {...props} />;
export const SecondaryPhosphorIcon = (props) => <PhosphorIcon colorContext="secondary" {...props} />;
export const SurfacePhosphorIcon = (props) => <PhosphorIcon colorContext="surface" {...props} />;
export const VariantPhosphorIcon = (props) => <PhosphorIcon colorContext="surfaceVariant" {...props} />;

export const InteractivePhosphorIcon = (props) => (
  <PhosphorIcon containerSize={44} backgroundColor="transparent" {...props} />
);

export const CirclePhosphorIcon = ({ backgroundColor, ...props }) => {
  const { theme  } = useTheme();
  return (
    <PhosphorIcon 
      containerSize={40}
      backgroundColor={backgroundColor || theme.colors.primaryContainer}
      borderRadius={20}
      colorContext="primary"
      {...props}
    />
  );
};

export const FilledPhosphorIcon = (props) => <PhosphorIcon weight="fill" {...props} />;
export const BoldPhosphorIcon = (props) => <PhosphorIcon weight="bold" {...props} />;
export const LightPhosphorIcon = (props) => <PhosphorIcon weight="light" {...props} />;
export const DuotonePhosphorIcon = (props) => <PhosphorIcon weight="duotone" {...props} />;

export { PHOSPHOR_ICON_SIZES, PHOSPHOR_WEIGHTS   };

// FIXED: The stylesheet is no longer needed for the broken styles.
// It can be removed or left empty.
const styles = StyleSheet.create({});

export default React.memo(PhosphorIcon);