/**
 * BottomNavBar - Complete navigation bar with Dashboard, Orders, Plus, Garments, QR Scanner
 * Features floating plus button and active state management
 * QR Scanner tab replaces Settings tab (settings now accessible via profile icon)
 */

import React, { useCallback } from 'react';
import { TouchableOpacity, StyleSheet, View } from 'react-native';
import { Text, Surface } from 'react-native-paper';
// FIXED: Changed useTheme to import from your custom ThemeContext
import { useTheme } from '../context/ThemeContext'; 
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { NavigationLocalIcon } from './LocalIcon';
import { SPACING } from '../theme/designTokens';

const BottomNavBar = ({
  navigation,
  currentRoute,
  onTabPress,
  style,
  backgroundColor,
  elevation = 8, // Increased default elevation for better visibility
}) => {
  const { theme } = useTheme();
  const insets = useSafeAreaInsets();

  const tabs = [
    { name: 'Dashboard', icon: 'home', label: 'Home', route: 'Dashboard' },
    { name: 'Orders', icon: 'shopping-bag', label: 'Orders', route: 'Orders' },
    { name: 'CRM', icon: 'users', label: 'Customers', route: 'CRM' },
    { name: 'Scan', icon: 'camera', label: 'Scan QR', route: 'Scan' },
  ];

  const handleTabPress = useCallback((tab) => {
    if (onTabPress) {
      onTabPress(tab.route);
    } else if (navigation) {
      navigation.navigate(tab.route);
    }
  }, [onTabPress, navigation]);

  const isTabActive = (tabName) => {
    // This logic correctly handles main routes and their potential sub-routes if named similarly
    return currentRoute === tabName || currentRoute?.startsWith(tabName);
  };

  const navBarBackgroundColor = backgroundColor || theme.colors.surface;

  return (
    <Surface
      style={[
        styles.container,
        {
          backgroundColor: navBarBackgroundColor,
          paddingBottom: insets.bottom > 0 ? insets.bottom : SPACING.sm, // Ensure padding even without insets
          borderTopColor: theme.colors.outlineVariant,
        },
        style
      }
      elevation={elevation}>
      {tabs.map((tab) => {
        const isActive = isTabActive(tab.name);
        const color = isActive ? theme.colors.primary : theme.colors.onSurfaceVariant;

        return (
          <TouchableOpacity
            key={tab.name}
            style={styles.tab}
            onPress={() => handleTabPress(tab)}
            activeOpacity={0.7}>
            <NavigationLocalIcon
              name={tab.icon}
              size={isActive ? 24 : 22} // Make active icon slightly larger
              color={color}
            />
            <Text
              style={[
                styles.tabLabel,
                {
                  color: color,
                  fontWeight: isActive ? '700' : '500', // Bolder font for active tab
                }
              }>
              {tab.label}
            </Text>
          </TouchableOpacity>
        );
      })}
    </Surface>
  );
};

export default React.memo(BottomNavBar);

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    height: 65, // Standard height for bottom nav
    paddingTop: SPACING.sm,
    paddingHorizontal: SPACING.sm,
    borderTopWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabLabel: {
    marginTop: SPACING.xs,
    fontSize: 10,
    lineHeight: 12,
  },
});