/**
 * UnifiedEmptyState - Consistent empty state component for all screens
 * Provides unified empty state with icon, title, description, and optional action
 */

import React, { useCallback } from 'react';
import { View, StyleSheet   } from 'react-native';
import { Text   } from 'react-native-paper';
import { Button   } from 'react-native-paper';
import { useTheme   } from '../context/ThemeContext';
import EmptyStateIllustration from './EmptyStateIllustration';

const UnifiedEmptyState = ({
  // Content
  icon = 'inbox-outline',
  title = 'No items found',
  description,

  // Action
  actionLabel,
  onActionPress,

  // Type-specific presets
  type, // 'products', 'orders', 'activities', 'search', 'error'

  // Styling
  style,
  iconSize = 64,
  iconColor,

  // Search specific
  searchQuery,
}) => {
  const { theme  } = useTheme();

  // Get preset configuration based on type
  const getPresetConfig = () => {
    switch(type) {
      case 'products':
        return { icon: 'package-variant-closed',
          title: 'No products found',
          description: 'Start by adding your first product to get started.',
          actionLabel: 'Add Product',
         };

      case 'orders':
        return { icon: 'clipboard-text-outline',
          title: 'No orders found',
          description: 'Orders will appear here once customers start placing them.',
          actionLabel: 'Create Order',
         };

      case 'activities':
        return { icon: 'history',
          title: 'No activities found',
          description: 'Activity history will appear here as you use the app.',
         };

      case 'search':
        return {
          icon: 'magnify',
          title: searchQuery ? `No results for "${searchQuery}"` : 'Start searching',
          description: searchQuery
            ? 'Try adjusting your search terms or filters.'
            : 'Enter a search term to find items.',
        };

      case 'error':
        return { icon: 'alert-circle-outline',
          title: 'Something went wrong',
          description: 'Unable to load data. Please try again.',
          actionLabel: 'Retry',
         };

      case 'network':
        return { icon: 'wifi-off',
          title: 'No internet connection',
          description: 'Please check your connection and try again.',
          actionLabel: 'Retry',
         };

      default:
        return { icon: icon,
          title: title,
          description: description,
          actionLabel: actionLabel,
         };
    }
  };

  const config = getPresetConfig();
  const finalIcon = icon !== 'inbox-outline' ? icon : config.icon;
  const finalTitle = title !== 'No items found' ? title : config.title;
  const finalDescription = description || config.description;
  const finalActionLabel = actionLabel || config.actionLabel;
  const finalIconColor = iconColor || theme.colors.onSurfaceVariant;

  return (
    <View style={styles.style1}>
      <View style={styles.style1}>
        {/* MD3 Illustration */}
        <EmptyStateIllustration
          type={type || 'general'}
          title={finalTitle}
          subtitle={finalDescription}
          size={120}
          showText={false}
        />

        {/* Title */}
        <Text
          variant="headlineSmall"
          style={styles.style3}>
          {finalTitle}
        </Text>

        {/* Description */}
        {finalDescription && (
          <Text
            variant="bodyMedium"
            style={styles.style2}>
            {finalDescription}
          </Text>
        )}

        {/* Action Button */}
        {finalActionLabel && onActionPress && (
          <Button
            mode="contained"
            onPress={onActionPress}
            style={styles.style1}
            contentStyle={styles.actionButtonContent}>
            {finalActionLabel}
          </Button>
        )}
      </View>
    </View>
  );
};






export default React.memo(UnifiedEmptyState);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 48,
  },
  content: {
    alignItems: 'center',
    maxWidth: 300,
  },
  iconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    textAlign: 'center',
    fontWeight: '600',
    marginBottom: 12,
    lineHeight: 28,
  },
  description: {
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  actionButton: {
    borderRadius: 24,
    minWidth: 140,
  },
  actionButtonContent: {
    paddingHorizontal: 24,
    paddingVertical: 8,
  },
  style1: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 48,
  },
  style2: {
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  style3: {
    textAlign: 'center',
    fontWeight: '600',
    marginBottom: 12,
    lineHeight: 28,
  },
});