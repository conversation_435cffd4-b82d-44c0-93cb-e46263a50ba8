/**
 * Date Picker Component
 * Simple date picker modal for order due dates
 */

import React, { useState, useCallback } from 'react';
import { View, StyleSheet } from 'react-native';
import { Modal } from 'react-native-paper';
import { Portal } from 'react-native-paper';
import { Text } from 'react-native-paper';
import { Button } from 'react-native-paper';
import { Surface } from 'react-native-paper';
import { TextInput } from 'react-native-paper';
import { useTheme } from '../context/ThemeContext';

interface DatePickerProps {
  visible: boolean;,
  onDismiss: () => void;
  onConfirm: (date: Date) => void;
  initialDate?: Date;
  title?: string;
}

const DatePicker: React.FC<DatePickerProps> = ({
  visible,
  onDismiss,
  onConfirm,
  initialDate = new Date(),
  title = 'Select Date'
}) => {
  const { theme } = useTheme();
  const [selectedDate, setSelectedDate] = useState(initialDate);
  const [dateString, setDateString] = useState(
    initialDate.toISOString().split('T')[0]
  );

  const handleDateStringChange = useCallback((text: string) => {
    setDateString(text);
    
    // Try to parse the date
    const date = new Date(text);
    if (!isNaN(date.getTime())) {
      setSelectedDate(date);
    }
  }, []);

  const handleConfirm = useCallback(() => {
    onConfirm(selectedDate);
    onDismiss();
  }, [selectedDate, onConfirm, onDismiss]);

  const handleCancel = useCallback(() => {
    // Reset to initial date
    setSelectedDate(initialDate);
    setDateString(initialDate.toISOString().split('T')[0]);
    onDismiss();
  }, [initialDate, onDismiss]);

  // Quick date options
  const getQuickDate = useCallback((days: number) => {
    const date = new Date();
    date.setDate(date.getDate() + days);
    return date;
  }, []);

  const handleQuickDate = useCallback((days: number) => {
    const date = getQuickDate(days);
    setSelectedDate(date);
    setDateString(date.toISOString().split('T')[0]);
  }, [getQuickDate]);

  const formatDate = useCallback((date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }, []);

  return (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={onDismiss}
        contentContainerStyle={[
          styles.modal,
          { backgroundColor: theme.colors.surface }
        }>
        <Surface style={styles.style5}} elevation={0}>
          {/* Header */}
          <Text variant="headlineSmall" style={styles.style2}}>
            {title}
          </Text>

          {/* Selected Date Display */}
          <View style={styles.style4}}>
            <Text variant="bodyLarge" style={styles.style3}}>
              {formatDate(selectedDate)}
            </Text>
          </View>

          {/* Date Input */}
          <TextInput
            label="Date (YYYY-MM-DD)"
            value={dateString}
            onChangeText={handleDateStringChange}
            style={styles.style1}
            mode="outlined"
            placeholder="2024-01-01"
          />

          {/* Quick Date Options */}
          <Text variant="titleMedium" style={styles.style2]>
            Quick Select
          </Text>
          
          <View style={styles.style1}>
            <Button
              mode="outlined"
              onPress={() => handleQuickDate(0)}
              style={styles.style1}
              compact
            >
              Today
            </Button>
            <Button
              mode="outlined"
              onPress={() => handleQuickDate(1)}
              style={styles.style1}
              compact
            >
              Tomorrow
            </Button>
            <Button
              mode="outlined"
              onPress={() => handleQuickDate(7)}
              style={styles.style1}
              compact
            >
              1 Week
            </Button>
            <Button
              mode="outlined"
              onPress={() => handleQuickDate(14)}
              style={styles.style1}
              compact
            >
              2 Weeks
            </Button>
          </View>

          {/* Action Buttons */}
          <View style={styles.style1}>
            <Button
              mode="outlined"
              onPress={handleCancel}
              style={styles.style1}>
              Cancel
            </Button>
            <Button
              mode="contained"
              onPress={handleConfirm}
              style={styles.style1}>
              Confirm
            </Button>
          </View>
        </Surface>
      </Modal>
    </Portal>
  );
};






export default DatePicker;

const styles = StyleSheet.create({
  modal: {
    margin: 20,
    borderRadius: 12,
    padding: 0,
    maxHeight: '80%',
  },
  container: {
    padding: 24,
    borderRadius: 12,
  },
  title: {
    fontWeight: '600',
    marginBottom: 24,
    textAlign: 'center',
  },
  dateDisplay: {
    padding: 16,
    borderRadius: 8,
    marginBottom: 24,
    alignItems: 'center',
  },
  dateText: {
    fontWeight: '500',
  },
  input: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontWeight: '500',
    marginBottom: 12,
  },
  quickDateContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 24,
  },
  quickDateButton: {
    flex: 0,
    minWidth: 80,
  },
  actionContainer: {
    flexDirection: 'row',
    gap: 12,
    justifyContent: 'flex-end',
  },
  actionButton: {
    minWidth: 100,
  },
  style1: {
  },
  style2: {
    { color: theme.colors.onSurface,
  },
  style3: {
    { color: theme.colors.onPrimaryContainer,
  },
  style4: {
    { backgroundColor: theme.colors.primaryContainer,
  },
  style5: {
    { backgroundColor: theme.colors.surface,
  },
});