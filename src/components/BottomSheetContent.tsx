import React from 'react';
import { View, StyleSheet, Image   } from 'react-native';
import { Text   } from 'react-native-paper';
import { Surface   } from 'react-native-paper';
import { Chip   } from 'react-native-paper';
import { Divider   } from 'react-native-paper';
import { useTheme   } from '../context/ThemeContext';

interface ProductDetailsProps {
  product: {
    id: string;
    name: string;
    sku: string;
    price: number;
    category: string;
    description?: string;
    image?: string;
    stock: number;
    status: string;
    createdAt: string;
  };
}

export const ProductDetails: React.FC<ProductDetailsProps> = ({ product }) => {
  const { theme  } = useTheme();

  // Handle undefined product
  if(!product) {
    return (
      <View style={styles.infoItem}>
          <Text variant="bodyLarge" style={styles.sectionTitle}>
            Product details not available
          </Text>
        </View>
    );
  }

  // Determine product status
  const productStatus = product.status || (product.stock > 0 ? 'active' : 'inactive');
  const isActive = productStatus === 'active' || product.stock > 0;

  return (
    <View style={styles.section}>
      {product.image && (
        <Surface style={styles.imageContainer} elevation={1}>
          <Image source={{ uri: product.image }} style={styles.productImage} resizeMode="cover" />
        </Surface>
      )}

      <View style={styles.section}>
        <Text variant="bodyLarge" style={styles.sectionTitle}>
          Product Information
        </Text>
        <View style={styles.section}>
          <InfoItem label="SKU" value={product.sku || 'N/A'} />
          <InfoItem label="Category" value={product.category || 'Uncategorized'} />
          <InfoItem label="Stock" value={`${product.stock || 0} units`} />
          <InfoItem label="Created" value={product.createdAt ? new Date(product.createdAt).toLocaleDateString() : 'N/A'} />
        </View>
      </View>

      <Divider style={styles.divider} />

      <View style={styles.section}>
        <Text variant="bodyLarge" style={styles.sectionTitle}>
          Status & Pricing
        </Text>
        <View style={styles.section}>
          <Chip
            mode="outlined"
            style={styles.statusChip}>
            {productStatus.toUpperCase()}
          </Chip>
          <Text variant="headlineSmall" style={styles.price}>
            ৳{(product.price || 0).toFixed(2)}
          </Text>
        </View>
      </View>

      {product.description && (
        <>
          <Divider style={styles.divider} />
          <View style={styles.section}>
            <Text variant="bodyLarge" style={styles.sectionTitle}>
              Description
            </Text>
            <Text variant="bodyMedium" style={styles.description}>
              {product.description}
            </Text>
          </View>
        </>
      )}
    </View>
  );
};

interface OrderDetailsProps {
  order: {
    id: string;
    customerName: string;
    customerEmail: string;
    customerPhone: string;
    items: Array<{
      productName: string;
      quantity: number;
      price: number;
      total: number;
    }>;
    subtotal: number;
    tax: number;
    total: number;
    status: string;
    createdAt: string;
    notes?: string;
  };
}

export const OrderDetails: React.FC<OrderDetailsProps> = ({ order }) => {
  const { theme  } = useTheme();

  return (
    <View style={styles.container}>
      <View style={styles.section}>
        <Text variant="bodyLarge" style={styles.sectionTitle}>
          Customer Information
        </Text>
        <View style={styles.section}>
          <Text variant="titleMedium" style={styles.customerInfo}>
            {order.customerName}
          </Text>
          <Text variant="bodyMedium" style={styles.infoValue}>
            {order.customerEmail}
          </Text>
          <Text variant="bodyMedium" style={styles.infoValue}>
            {order.customerPhone}
          </Text>
        </View>
      </View>

      <Divider style={styles.divider} />

      <View style={styles.section}>
        <Text variant="bodyLarge" style={styles.sectionTitle}>
          Order Items
        </Text>
        {order.items.map((item, index) => (
          <Surface key={index} style={styles.orderItem} elevation={0}>
            <View style={styles.itemHeader}>
              <Text variant="bodyLarge" style={styles.infoValue}>
                {item.productName}
              </Text>
              <Text variant="bodyLarge" style={styles.price}>
                ৳{item.total.toFixed(2)}
              </Text>
            </View>
            <Text variant="bodySmall" style={styles.infoValue}>
              {item.quantity} × ৳{item.price.toFixed(2)}
            </Text>
          </Surface>
        ))}
      </View>

      <Divider style={styles.divider} />

      <View style={styles.section}>
        <Text variant="bodyLarge" style={styles.sectionTitle}>
          Order Summary
        </Text>
        <View style={styles.summaryRow}>
          <Text variant="bodyMedium" style={styles.infoLabel}>Subtotal</Text>
          <Text variant="bodyMedium" style={styles.infoValue}>৳{order.subtotal.toFixed(2)}</Text>
        </View>
        <View style={styles.summaryRow}>
          <Text variant="bodyMedium" style={styles.infoLabel}>Tax</Text>
          <Text variant="bodyMedium" style={styles.infoValue}>৳{order.tax.toFixed(2)}</Text>
        </View>
        <Divider style={styles.divider} />
        <View style={styles.summaryRow}>
          <Text variant="titleMedium" style={styles.infoLabel}>Total</Text>
          <Text variant="titleMedium" style={styles.totalAmount}>
            ৳{order.total.toFixed(2)}
          </Text>
        </View>
      </View>

      <View style={styles.dateInfo}>
        <View style={styles.section}>
          <Chip
            mode="outlined"
            style={styles.statusChip}>
            {order.status.toUpperCase()}
          </Chip>
          <Text variant="bodySmall" style={styles.infoValue}>
            {new Date(order.createdAt).toLocaleDateString()}
          </Text>
        </View>
      </View>

      {order.notes && (
        <>
          <Divider style={styles.divider} />
          <View style={styles.section}>
            <Text variant="bodyLarge" style={styles.sectionTitle}>
              Notes
            </Text>
            <Text variant="bodyMedium" style={styles.infoValue}>
              {order.notes}
            </Text>
          </View>
        </>
      )}
    </View>
  );
};

interface CustomerDetailsProps {
  customer: {
    id: string;
    name: string;
    email: string;
    phone: string;
    address?: string;
    totalOrders: number;
    totalSpent: number;
    status: string;
    createdAt: string;
    lastOrderDate?: string;
  };
}

export const CustomerDetails: React.FC<CustomerDetailsProps> = ({ customer }) => {
  const { theme  } = useTheme();

  return (
    <View style={styles.container}>
      <View style={styles.section}>
        <Text variant="bodyLarge" style={styles.sectionTitle}>
          Contact Information
        </Text>
        <View style={styles.section}>
          <InfoItem label="Email" value={customer.email} />
          <InfoItem label="Phone" value={customer.phone} />
          {customer.address && <InfoItem label="Address" value={customer.address} />}
        </View>
      </View>

      <Divider style={styles.divider} />

      <View style={styles.section}>
        <Text variant="bodyLarge" style={styles.sectionTitle}>
          Order Statistics
        </Text>
        <View style={styles.section}>
          <Surface style={styles.statCard} elevation={1}>
            <Text variant="headlineSmall" style={styles.statValue}>
              {customer.totalOrders}
            </Text>
            <Text variant="bodySmall" style={styles.statLabel}>
              Total Orders
            </Text>
          </Surface>
          <Surface style={styles.statCard} elevation={1}>
            <Text variant="headlineSmall" style={styles.statValue}>
              ৳{customer.totalSpent.toFixed(0)}
            </Text>
            <Text variant="bodySmall" style={styles.statLabel}>
              Total Spent
            </Text>
          </Surface>
        </View>
      </View>

      <Divider style={styles.divider} />

      <View style={styles.section}>
        <Text variant="bodyLarge" style={styles.sectionTitle}>
          Account Status
        </Text>
        <View style={styles.section}>
          <Chip
            mode="outlined"
            style={styles.statusChip}>
            {customer.status.toUpperCase()}
          </Chip>
          <View style={styles.statsGrid}>
            <Text variant="bodySmall" style={styles.infoValue}>
              Member since {new Date(customer.createdAt).toLocaleDateString()}
            </Text>
            {customer.lastOrderDate && (
              <Text variant="bodySmall" style={styles.infoValue}>
                Last order: {new Date(customer.lastOrderDate).toLocaleDateString()}
              </Text>
            )}
          </View>
        </View>
      </View>
    </View>
  );
};

const InfoItem: React.FC<{ label: string; value: string}> = ({ label, value }) => {
  const { theme  } = useTheme();
  return (
    <View style={styles.infoItem}>
      <Text variant="bodySmall" style={styles.infoLabel}>
        {label}
      </Text>
      <Text variant="bodyMedium" style={styles.infoValue}>
        {value}
      </Text>
    </View>
  );
};

const getOrderStatusColor = (status: string, theme: any) => {
  if(!theme || !theme.colors) {
    return '#E0E0E0'; // Fallback color
  }
  
  switch (status.toLowerCase()) {
    case 'completed':
      return theme.colors.primaryContainer;
    case 'processing':
      return theme.colors.secondaryContainer;
    case 'pending':
      return theme.colors.tertiaryContainer;
    case 'cancelled':
      return theme.colors.errorContainer;
    default:
      return theme.colors.surfaceVariant;
  }
};






export default { ProductDetails, OrderDetails, CustomerDetails };

const styles = StyleSheet.create({
  container: {
    paddingBottom: 16},
  section: {
    marginBottom: 16},
  sectionTitle: {
    fontWeight: '600',
    marginBottom: 12},
  imageContainer: {
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 16,
    height: 200},
  productImage: {
    width: '100%',
    height: '100%'},
  infoGrid: {
    gap: 8},
  infoItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4},
  infoLabel: {
    fontWeight: '500'},
  infoValue: {
    flex: 1,
    textAlign: 'right'},
  statusRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between'},
  statusChip: {
    alignSelf: 'flex-start'},
  price: {
    fontWeight: '700'},
  description: {
    lineHeight: 20},
  divider: {
    marginVertical: 16},
  customerInfo: {
    gap: 8},
  orderItem: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 8},
  itemHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4},
  itemTotal: {
    fontWeight: '600'},
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4},
  totalAmount: {
    fontWeight: '700'},
  statsGrid: {
    flexDirection: 'row',
    gap: 12},
  statCard: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center'},
  statValue: {
    fontWeight: '700',
    marginBottom: 4},
  statLabel: {
    textAlign: 'center',
    fontWeight: '500'},
  dateInfo: {
    alignItems: 'flex-end'}});