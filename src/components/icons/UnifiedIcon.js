import React from 'react';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import Ionicons from '@expo/vector-icons/Ionicons';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import { ICON_MAPPINGS } from './IconConstants';

/**
 * Unified Icon Component
 * Provides a single interface for all icons with automatic family selection
 */
const UnifiedIcon = ({ name, size = 24, color = '#000', family = 'auto', ...props }) => {
  // Auto-select icon family based on icon name
  const getIconFamily = (iconName) => {
    if (family !== 'auto') {
      return family;
    }
    
    // Check if icon exists in MaterialIcons first (preferred)
    if (ICON_MAPPINGS.MaterialIcons.includes(iconName)) {
      return 'MaterialIcons';
    }
    
    // Fallback to Ionicons
    if (ICON_MAPPINGS.Ionicons.includes(iconName)) {
      return 'Ionicons';
    }
    
    // Default to MaterialIcons
    return 'MaterialIcons';
  };

  const iconFamily = getIconFamily(name);
  const iconProps = { name, size, color, ...props };

  switch (iconFamily) {
    case 'MaterialIcons':
      return <MaterialIcons {...iconProps} />;
    case 'Ionicons':
      return <Ionicons {...iconProps} />;
    case 'FontAwesome':
      return <FontAwesome {...iconProps} />;
    default:
      return <MaterialIcons {...iconProps} />;
  }
};

export default React.memo(UnifiedIcon);
