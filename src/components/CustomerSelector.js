/**
 * CustomerSelector - Component for selecting customers with search functionality
 */

import React, { useState, useMemo, useCallback } from 'react';
import { View, StyleSheet } from 'react-native';
import { Text } from 'react-native-paper';
import { Searchbar } from 'react-native-paper';
import { List } from 'react-native-paper';
import { Avatar } from 'react-native-paper';
import { Button } from 'react-native-paper';
import { Surface } from 'react-native-paper';
import { TouchableRipple } from 'react-native-paper';
import { useTheme } from '../context/ThemeContext';
import { SPACING, BORDER_RADIUS } from '../theme/designTokens';
import navigationService from '../services/NavigationService';

const CustomerSelector = ({
  customers = [],
  selectedCustomer,
  onCustomerSelect,
  error,
  placeholder = "Search customers...",
  showAddButton = true,
}) => {
  const { theme } = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [isExpanded, setIsExpanded] = useState(false);

  const filteredCustomers = useMemo(() => {
    if (!searchQuery) return customers.slice(0, 5); // Show only first 5 when not searching

    return customers.filter(customer =>
      customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      customer.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      customer.phone.includes(searchQuery)
    );
  }, [customers, searchQuery]);

  const handleCustomerPress = useCallback((customer) => {
    onCustomerSelect(customer), []);
    setIsExpanded(false);
    setSearchQuery('');
  };

  const handleAddCustomer = useCallback(() => {
    navigationService.navigate('AddCustomer'), []);
  };

  const getCustomerInitials = (name) => {
    return name.split(' ').map(word => word[0]).join('').substring(0, 2).toUpperCase();
  };

  const renderCustomerItem = ({ item: customer }) => (
    <TouchableRipple
      onPress={() => handleCustomerPress(customer)}
      style={[
        styles.customerItem,
        {
          backgroundColor: selectedCustomer?.id === customer.id
            ? theme.colors.primaryContainer
            : theme.colors.surface,
        }
      }>
      <View style={styles.customerContent}>
        <Avatar.Text
          size={40}
          label={getCustomerInitials(customer.name)}
          style={{ backgroundColor: theme.colors.primary }}
        />
        <View style={styles.customerInfo}>
          <Text variant="bodyLarge" style={{ color: theme.colors.onSurface }}>
            {customer.name}
          </Text>
          <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
            {customer.email}
          </Text>
          <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
            {customer.phone}
          </Text>
        </View>
        {customer.isVIP && (
          <View style={[
            styles.vipBadge,
            { backgroundColor: theme.colors.tertiary }
          }>
            <Text variant="labelSmall" style={{ color: theme.colors.onTertiary }}>
              VIP
            </Text>
          </View>
        )}
      </View>
    </TouchableRipple>
  );

  return (
    <View style={styles.container}>
      {/* Selected Customer Display */}
      {selectedCustomer && !isExpanded ? (
        <Surface
          style={[
            styles.selectedCustomer,
            { backgroundColor: theme.colors.primaryContainer }
          }
          elevation={1}>
          <View style={styles.customerContent}>
            <Avatar.Text
              size={40}
              label={getCustomerInitials(selectedCustomer.name)}
              style={{ backgroundColor: theme.colors.primary }}
            />
            <View style={styles.customerInfo}>
              <Text variant="bodyLarge" style={{ color: theme.colors.onPrimaryContainer }}>
                {selectedCustomer.name}
              </Text>
              <Text variant="bodySmall" style={{ color: theme.colors.onPrimaryContainer }}>
                {selectedCustomer.email}
              </Text>
            </View>
            <Button
              mode="text"
              onPress={() => setIsExpanded(true)}
              textColor={theme.colors.primary}}>
              Change
            </Button>
          </View>
        </Surface>
      ) : (
        <>
          {/* Search Bar */}
          <Searchbar
            placeholder={placeholder}
            onChangeText={setSearchQuery}
            value={searchQuery}
            style={[
              styles.searchbar,
              { backgroundColor: theme.colors.surface },
              error && { borderColor: theme.colors.error, borderWidth: 1 }
            }
            iconColor={theme.colors.onSurfaceVariant}
            placeholderTextColor={theme.colors.onSurfaceVariant}
            onFocus={() => setIsExpanded(true)}
          />

          {/* Customer List */}
          {isExpanded && (
            <Surface
              style={[
                styles.customerList,
                { backgroundColor: theme.colors.surface }
              }
              elevation={2}>
              {filteredCustomers.length > 0 ? (
                <View style={styles.list}>
                  {filteredCustomers.map((customer) => (
                    <View key={customer.id.toString()}>
                      {renderCustomerItem({ item: customer })}
                    </View>
                  ))}
                </View>
              ) : (
                <View style={styles.emptyState}>
                  <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
                    {searchQuery ? 'No customers found' : 'No customers available'}
                  </Text>
                  {showAddButton && (
                    <Button
                      mode="outlined"
                      onPress={handleAddCustomer}
                      style={styles.addButton}
                      icon="plus"
                    >
                      Add Customer
                    </Button>
                  )}
                </View>
              )}

              {/* Add Customer Button */}
              {showAddButton && filteredCustomers.length > 0 && (
                <View style={styles.addButtonContainer}>
                  <Button
                    mode="outlined"
                    onPress={handleAddCustomer}
                    style={styles.addButton}
                    icon="plus"
                  >
                    Add New Customer
                  </Button>
                </View>
              )}

              {/* Close Button */}
              <View style={styles.closeButtonContainer}>
                <Button
                  mode="text"
                  onPress={() => {
                    setIsExpanded(false);
                    setSearchQuery('');
                  }}
                >
                  Close
                </Button>
              </View>
            </Surface>
          )}
        </>
      )}

      {/* Error Message */}
      {error && (
        <Text style={[
          styles.errorText,
          { color: theme.colors.error }
        }>
          {error}
        </Text>
      )}
    </View>
  );
};

export default CustomerSelector;

const styles = StyleSheet.create({
  container: {
    marginBottom: SPACING.md,
  },
  selectedCustomer: {
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.md,
  },
  searchbar: {
    marginBottom: SPACING.sm,
    borderRadius: BORDER_RADIUS.lg,
  },
  customerList: {
    borderRadius: BORDER_RADIUS.lg,
    maxHeight: 300,
    overflow: 'hidden',
  },
  list: {
    maxHeight: 200,
  },
  customerItem: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
  },
  customerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  customerInfo: {
    flex: 1,
    marginLeft: SPACING.md,
  },
  vipBadge: {
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.sm,
  },
  emptyState: {
    padding: SPACING.lg,
    alignItems: 'center',
  },
  addButtonContainer: {
    padding: SPACING.md,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  addButton: {
    marginTop: SPACING.sm,
  },
  closeButtonContainer: {
    padding: SPACING.sm,
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  errorText: {
    fontSize: 12,
    marginTop: SPACING.xs,
  },
});