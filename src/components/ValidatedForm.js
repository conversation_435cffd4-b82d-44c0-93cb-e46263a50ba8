// VALIDATED FORM COMPONENT - Form wrapper with validation
import React, { useState, useRef, useImperativeHandle, forwardRef } from 'react';
import { View, StyleSheet   } from 'react-native';
import ValidationService from '../services/ValidationService';

const ValidatedForm = forwardRef(({
  children,
  onValidationChange,
  validateOnMount = false,
  style}, ref) => {
  const [fieldValidations, setFieldValidations] = useState({});
  const [isFormValid, setIsFormValid] = useState(false);
  const fieldsRef = useRef({});

  // Register a field for validation
  const registerField = (fieldName, isValid, error) => {
    setFieldValidations(prev => {
      const newValidations = {
        ...prev,
        [fieldName]: { isValid, error }
      };
      
      // Check if entire form is valid
      const formValid = Object.values(newValidations).every(field => field.isValid);
      setIsFormValid(formValid);
      
      if(onValidationChange) {
        onValidationChange(formValid, newValidations);
      }
      
      return newValidations;
    });
  };

  // Validate all fields
  const validateAll = () => {
    const errors = {};
    let allValid = true;

    Object.entries(fieldsRef.current).forEach(([fieldName, fieldRef]) => {
      if(fieldRef && fieldRef.validate) {
        const { isValid, error  } = fieldRef.validate();
        if(!isValid) {
          errors[fieldName] = error;
          allValid = false;
        }
      }
    });

    return { isValid: allValid,
      errors
   };
  };

  // Get form data
  const getFormData = () => {
    const data = {};
    Object.entries(fieldsRef.current).forEach(([fieldName, fieldRef]) => {
      if(fieldRef && fieldRef.getValue) {
        data[fieldName] = fieldRef.getValue();
      }
    });
    return data;
  };

  // Reset form
  const reset = () => {
    Object.values(fieldsRef.current).forEach(fieldRef => {
      if(fieldRef && fieldRef.reset) {
        fieldRef.reset();
      }
    });
    setFieldValidations({});
    setIsFormValid(false);
  };

  // Expose methods to parent
  useImperativeHandle(ref, () => ({
    validateAll,
    getFormData,
    reset,
    isValid: isFormValid,
    fieldValidations,
    registerField: (fieldName, fieldRef) => {
      fieldsRef.current[fieldName] = fieldRef;
    }
  }));

  // Clone children and inject validation props
  const enhancedChildren = React.Children.map(children, (child, index) => {
    if (React.isValidElement(child) && child.props.name) {
      const fieldName = child.props.name;
      
      return React.cloneElement(child, {
        onValidation: (isValid, error) => {
          registerField(fieldName, isValid, error);
          if(child.props.onValidation) {
            child.props.onValidation(isValid, error);
          }
        },
        ref: (fieldRef) => {
          if(fieldRef) {
            fieldsRef.current[fieldName] = fieldRef;
          }
          if(child.ref) {
            if(typeof child.ref === 'function') {
              child.ref(fieldRef);
            } else {
              child.ref.current = fieldRef;
            }
          }
        }
      });
    }
    return child;
  });

  return (
    <View style={styles.style1}>
      {enhancedChildren}
    </View>
  );
});



ValidatedForm.displayName = 'ValidatedForm';




export default ValidatedForm;

const styles = StyleSheet.create({
  container: {
    flex: 1},
  style1: {
  }});