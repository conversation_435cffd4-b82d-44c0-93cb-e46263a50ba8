import React, { useState, useCallback } from 'react';
import { View, StyleSheet   } from 'react-native';
import { Text, Card   } from 'react-native-paper';
import { StyleSheet   } from 'react-native';

const PricingSection = () => {
  const [pricing, setPricing] = useState({ price: 0, advance: 0, discount: 0 });

  const updatePricing = useCallback((field, value) => {
    setPricing(prev => ({ ...prev, [field]: value }));
  }, []);

  return (
    <Card style={styles.card}>
      <Card.Content>
        <Text variant="titleMedium">Pricing</Text>
        {/* Pricing form fields */}
      </Card.Content>
    </Card>
  );
};






export default React.memo(PricingSection);

const styles = StyleSheet.create({
  card: {
    margin: 16
}
});