import React, { useState, useCallback } from 'react';
import { View, StyleSheet   } from 'react-native';
import { Text, Card   } from 'react-native-paper';
import { View, StyleSheet   } from 'react-native';

const GarmentDetails = () => {
  const [garmentType, setGarmentType] = useState('');

  const updateGarmentType = useCallback((type) => {
    setGarmentType(type);
  }, []);

  return (
    <Card style={styles.card}>
      <Card.Content>
        <Text variant="titleMedium">Garment Details</Text>
        {/* Garment details form */}
      </Card.Content>
    </Card>
  );
};






export default React.memo(GarmentDetails);

const styles = StyleSheet.create({
  card: {
    margin: 16,
    elevation: 2
}
});