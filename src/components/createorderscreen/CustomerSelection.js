import React, { useState, useCallback } from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, Card } from 'react-native-paper';
import { View, StyleSheet } from 'react-native';

const CustomerSelection = () => {
  const [selectedCustomer, setSelectedCustomer] = useState(null);

  const selectCustomer = useCallback((customer) => {
    setSelectedCustomer(customer);
  }, []);

  return (
    <Card style={styles.card}>
      <Card.Content>
        <Text variant="titleMedium">Customer Selection</Text>
        {/* Customer selection UI */}
      </Card.Content>
    </Card>
  );
};






export default React.memo(CustomerSelection);

const styles = StyleSheet.create({
  card: {
    margin: 16,
    elevation: 2
}
});