import React, { useState, useCallback } from 'react';
import { View, StyleSheet   } from 'react-native';
import { Text, Card   } from 'react-native-paper';

const MeasurementForm = () => {
  const [measurements, setMeasurements] = useState({});

  const updateMeasurement = useCallback((key, value) => {
    setMeasurements(prev => ({ ...prev, [key]: value }));
  }, []);

  return (
    <Card style={styles.card}>
      <Card.Content>
        <Text variant="titleMedium">Measurements</Text>
        {/* Measurement form fields */}
      </Card.Content>
    </Card>
  );
};






export default React.memo(MeasurementForm);

const styles = StyleSheet.create({
  card: {
    margin: 16,
    elevation: 2}});