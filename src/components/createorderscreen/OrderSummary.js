import React from 'react';
import { View, StyleSheet   } from 'react-native';
import { Text, Card, Button   } from 'react-native-paper';
import { StyleSheet   } from 'react-native';

const OrderSummary = () => {
  return (
    <Card style={styles.card}>
      <Card.Content>
        <Text variant="titleMedium">Order Summary</Text>
        {/* Order summary details */}
        <Button mode="contained" style={styles.card}>
          Create Order
        </Button>
      </Card.Content>
    </Card>
  );
};






export default React.memo(OrderSummary);

const styles = StyleSheet.create({
  card: {
    margin: 16
},
  submitButton: {
    marginTop: 16
}
});