/**
 * SplashScreen - Custom splash screen with 2-second duration
 * Shows app logo and branding for 2 seconds before main app loads
 */

import React, { useEffect } from 'react';
import { View, StyleSheet, Image, StatusBar   } from 'react-native';
import { Text, ActivityIndicator   } from 'react-native-paper'; // Added ActivityIndicator
import { useTheme   } from '../context/ThemeContext';

const SplashScreen = ({ onFinish }) => {
  const { theme  } = useTheme(); // Theme context is available if needed for dynamic styling

  useEffect(() => {
    const timer = setTimeout(() => {
      onFinish();
    }, 2000); // 2 seconds

    return () => clearTimeout(timer);
  }, [onFinish]);

  // CORRECTED: Replaced broken style references with correct ones from the stylesheet.
  return (
    <View style={styles.container}>
      <StatusBar backgroundColor="#1a237e" barStyle="light-content" />
      
      {/* App Logo */}
      <View style={styles.logoContainer}>
        <Image 
          source={require('../../assets/splash-icon.png')} 
          style={styles.logo}
          resizeMode="contain"
        />
        
        {/* App Name */}
        <Text variant="headlineLarge" style={styles.appName}>Tailora</Text>
        <Text variant="titleMedium" style={styles.tagline}>Professional Tailoring Management</Text>
      </View>

      {/* Loading Indicator */}
      {/* CORRECTED: Replaced empty Views with a functional ActivityIndicator */}
      <View style={styles.loadingContainer}>
        <ActivityIndicator animating={true} color="#ffffff" />
      </View>
    </View>
  );
};

export default SplashScreen;

// CORRECTED: Fixed all syntax errors in the StyleSheet object.
const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    backgroundColor: '#1a237e', // Moved background color here
  },
  logoContainer: {
    alignItems: 'center',
    flex: 1, // Added to help center content vertically
    justifyContent: 'center'},
  logo: {
    width: 120,
    height: 120,
    marginBottom: 24},
  appName: {
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 8,
    letterSpacing: 1},
  tagline: {
    color: '#ffffff',
    opacity: 0.8,
    textAlign: 'center'},
  loadingContainer: {
    position: 'absolute',
    bottom: 80,
    width: '100%',
    alignItems: 'center'}});