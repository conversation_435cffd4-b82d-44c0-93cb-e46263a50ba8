/**
 * LocalIconButton - Reusable icon button component
 * Provides consistent icon buttons with variants, sizes, and states
 */

import React, { useCallback } from 'react';
import { TouchableOpacity, StyleSheet, View } from 'react-native';
import { useTheme } from '../context/ThemeContext';
import { Text } from 'react-native-paper';
import LocalIcon from './LocalIcon';

const LocalIconButton = ({
  // Icon props
  icon,
  iconSize,
  iconColor,
  
  // Button props
  onPress,
  onLongPress,
  disabled = false,
  loading = false,
  
  // Variants
  variant = 'default', // 'default', 'primary', 'secondary', 'danger', 'success', 'ghost'
  size = 'medium', // 'small', 'medium', 'large'
  shape = 'rounded', // 'rounded', 'circular', 'square'
  
  // Label
  label,
  labelPosition = 'bottom', // 'bottom', 'right', 'none'
  
  // Styling
  style,
  containerStyle,
  
  // Accessibility
  accessibilityLabel,
  accessibilityHint,
  
  // Badge
  badge,
  badgeColor,
  
  // States
  selected = false,
}) => {
  const { theme } = useTheme();

  // Size configurations
  const sizeConfig = {
    small: {
      iconSize: 16,
      containerSize: 32,
      padding: 8,
      labelSize: 10,
    },
    medium: {
      iconSize: 20,
      containerSize: 40,
      padding: 10,
      labelSize: 12,
    },
    large: {
      iconSize: 24,
      containerSize: 48,
      padding: 12,
      labelSize: 14,
    },
  };

  const config = sizeConfig[size];

  // Variant configurations
  const getVariantStyles = () => {
    const variants = {
      default: {
        backgroundColor: theme.colors.surface,
        borderColor: theme.colors.outline,
        iconColor: theme.colors.onSurface,
        borderWidth: 1,
      },
      primary: {
        backgroundColor: theme.colors.primary,
        borderColor: theme.colors.primary,
        iconColor: theme.colors.onPrimary,
        borderWidth: 0,
      },
      secondary: {
        backgroundColor: theme.colors.secondary,
        borderColor: theme.colors.secondary,
        iconColor: theme.colors.onSecondary,
        borderWidth: 0,
      },
      danger: {
        backgroundColor: theme.colors.error,
        borderColor: theme.colors.error,
        iconColor: theme.colors.onError,
        borderWidth: 0,
      },
      success: {
        backgroundColor: '#4CAF50',
        borderColor: '#4CAF50',
        iconColor: '#FFFFFF',
        borderWidth: 0,
      },
      ghost: {
        backgroundColor: 'transparent',
        borderColor: 'transparent',
        iconColor: theme.colors.onSurface,
        borderWidth: 0,
      },
    };

    return variants[variant] || variants.default;
  };

  // Shape configurations
  const getShapeStyles = () => {
    const shapes = {
      rounded: { borderRadius: 8 },
      circular: { borderRadius: config.containerSize / 2 },
      square: { borderRadius: 0 },
    };

    return shapes[shape] || shapes.rounded;
  };

  const variantStyles = getVariantStyles();
  const shapeStyles = getShapeStyles();
  const styles = createStyles(theme, variantStyles, config, labelPosition, badgeColor);

  // Handle press states
  const getOpacity = () => {
    if (disabled) return 0.4;
    if (loading) return 0.7;
    return 1;
  };

  const renderIcon = () => {
    const finalIconSize = iconSize || config.iconSize;
    const finalIconColor = iconColor || variantStyles.iconColor;

    if (loading) {
      return (
        <LocalIcon name="loading"
          size={finalIconSize}
          color={finalIconColor}
          style={styles.loadingIcon}
        />
      );
    }

    return (
      <LocalIcon name={icon}
        size={finalIconSize}
        color={finalIconColor}
      />
    );
  };

  const renderBadge = () => {
    if (!badge) return null;

    return (
      <View style={styles.badge}>
        {typeof badge === 'number' && badge > 0 && (
          <Text style={styles.badgeText}>
            {badge > 99 ? '99+' : badge.toString()}
          </Text>
        )}
      </View>
    );
  };

  const renderLabel = () => {
    if (!label || labelPosition === 'none') return null;

    return (
      <Text
        variant="labelSmall"
        style={styles.label}
        numberOfLines={1}>
        {label}
      </Text>
    );
  };

  const containerStyles = [
    styles.container,
    {
      width: config.containerSize,
      height: config.containerSize,
      padding: config.padding,
      backgroundColor: selected 
        ? variantStyles.backgroundColor + '20' 
        : variantStyles.backgroundColor,
      borderColor: selected 
        ? variantStyles.borderColor 
        : variantStyles.borderColor,
      borderWidth: variantStyles.borderWidth,
      opacity: getOpacity(),
    },
    shapeStyles,
    style,
  ];

  const wrapperStyles = [
    styles.wrapper,
    labelPosition === 'right' && styles.wrapperRow,
    containerStyle,
  ];

  return (
    <TouchableOpacity
      style={wrapperStyles}
      onPress={onPress}
      onLongPress={onLongPress}
      disabled={disabled || loading}
      activeOpacity={0.7}
      accessibilityLabel={accessibilityLabel || label}
      accessibilityHint={accessibilityHint}
      accessibilityRole="button"
      accessibilityState={{
        disabled: disabled || loading,
        selected: selected,
      }}
    >
      <View style={containerStyles}>
        {renderIcon()}
        {renderBadge()}
      </View>
      {renderLabel()}
    </TouchableOpacity>
  );
};






const createStyles = (theme, variantStyles, config, labelPosition, badgeColor) => StyleSheet.create({
  wrapper: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  wrapperRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  label: {
    textAlign: 'center',
    fontWeight: '500',
    color: variantStyles.iconColor,
    fontSize: config.labelSize,
    marginTop: labelPosition === 'bottom' ? 4 : 0,
    marginLeft: labelPosition === 'right' ? 8 : 0,
  },
  badge: {
    position: 'absolute',
    minWidth: 16,
    height: 16,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 4,
    backgroundColor: badgeColor || theme.colors.error,
    top: -2,
    right: -2,
  },
  badgeText: {
    fontSize: 10,
    fontWeight: '600',
    lineHeight: 12,
    color: theme.colors.onError,
  },
  loadingIcon: {
    // Add rotation animation if needed
  },
});

export default React.memo(LocalIconButton);