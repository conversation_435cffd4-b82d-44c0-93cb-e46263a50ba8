/**
 * GlobalBadge - Standardized badge component for consistent styling across the app
 */

import React from 'react';
// FIXED: Corrected imports
import { View, Text, StyleSheet   } from 'react-native';
import { useTheme   } from '../context/ThemeContext';
import { SPACING   } from '../theme/designTokens';

const GlobalBadge = ({
  children,
  variant = 'default', // default, success, warning, error, info, primary, secondary
  size = 'medium', // small, medium, large
  style,
  textStyle,
  compact = false,
  ...props
}) => {
  const { theme  } = useTheme();

  const getVariantColors = () => {
    switch(variant) {
      case 'success':
        return { backgroundColor: theme.colors.tertiary + '20', textColor: theme.colors.tertiary, borderColor: theme.colors.tertiary + '40'  };
      case 'warning':
        return { backgroundColor: theme.colors.secondary + '20', textColor: theme.colors.secondary, borderColor: theme.colors.secondary + '40'  };
      case 'error':
        return { backgroundColor: theme.colors.error + '20', textColor: theme.colors.error, borderColor: theme.colors.error + '40'  };
      case 'info':
        return { backgroundColor: theme.colors.primary + '20', textColor: theme.colors.primary, borderColor: theme.colors.primary + '40'  };
      case 'primary':
        return { backgroundColor: theme.colors.primary, textColor: theme.colors.onPrimary, borderColor: theme.colors.primary  };
      case 'secondary':
        return { backgroundColor: theme.colors.secondary, textColor: theme.colors.onSecondary, borderColor: theme.colors.secondary  };
      default:
        // FIXED: Removed extra comma
        return { backgroundColor: theme.colors.surfaceVariant,
          textColor: theme.colors.onSurfaceVariant,
          borderColor: theme.colors.outline,
         };
    }
  };

  const getSizeStyles = () => {
    switch(size) {
      case 'small':
        return { paddingHorizontal: SPACING.xs + 2, paddingVertical: SPACING.xs / 2 - 1, fontSize: 10, minHeight: 20  };
      case 'large':
        return { paddingHorizontal: SPACING.md, paddingVertical: SPACING.xs, fontSize: 14, minHeight: 32  };
      default: // medium
        return { paddingHorizontal: SPACING.sm + 2, paddingVertical: SPACING.xs - 1, fontSize: 11, minHeight: 24  };
    }
  };

  const colors = getVariantColors();
  const sizeStyles = getSizeStyles();

  const badgeStyles = [
    styles.badge,
    {
      backgroundColor: colors.backgroundColor,
      borderColor: colors.borderColor,
      paddingHorizontal: compact ? sizeStyles.paddingHorizontal * 0.7 : sizeStyles.paddingHorizontal,
      paddingVertical: compact ? sizeStyles.paddingVertical * 0.7 : sizeStyles.paddingVertical,
      minHeight: compact ? sizeStyles.minHeight * 0.8 : sizeStyles.minHeight,
    },
    style,
  ];

  const badgeTextStyles = [
    styles.text,
    {
      color: colors.textColor,
      fontSize: compact ? sizeStyles.fontSize * 0.9 : sizeStyles.fontSize,
      fontWeight: '600',
    },
    textStyle,
  ];

  return (
    // FIXED: Applied the correct style arrays to the components
    <View style={badgeStyles} {...props}>
      <Text style={badgeTextStyles} numberOfLines={1}>
        {children}
      </Text>
    </View>
  );
};

export default React.memo(GlobalBadge);

// FIXED: Corrected syntax errors and removed unused style
const styles = StyleSheet.create({
  badge: {
    borderRadius: 12,
    borderWidth: 1,
    alignSelf: 'flex-start',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
  },
  text: {
    textAlign: 'center',
    includeFontPadding: false,
    textAlignVertical: 'center',
  },
});