/**
 * CompactThemeSelector - Compact 3-button theme selector for profile page
 */

import React, { useCallback } from 'react';
import { View, StyleSheet } from 'react-native';
import { SegmentedButtons } from 'react-native-paper';
import { useTheme } from '../context/ThemeContext';
import { SPACING, BORDER_RADIUS } from '../theme/designTokens';

const CompactThemeSelector = ({ style = {} }) => {
  const { theme, themeMode, setTheme, THEME_MODES, systemColorScheme } = useTheme();

  const themeOptions = [
    {
      value: THEME_MODES.LIGHT,
      label: 'Light',
      icon: 'white-balance-sunny',
    },
    {
      value: THEME_MODES.DARK,
      label: 'Dark', 
      icon: 'moon-waning-crescent',
    },
    {
      value: THEME_MODES.SYSTEM,
      label: 'System',
      icon: 'cog',
    },
  ];

  const handleThemeChange = useCallback(async (value) => {
    await setTheme(value);
  }, [setTheme]);

  return (
    <View style={styles.container}>
      <SegmentedButtons
        value={themeMode}
        onValueChange={handleThemeChange}
        buttons={themeOptions.map(option => ({
          value: option.value,
          label: option.label,
          icon: option.icon,
          style: [
            styles.button,
            {
              backgroundColor: themeMode === option.value 
                ? theme.colors.primaryContainer 
                : theme.colors.surface,
            }
          ],
          labelStyle: {
            color: themeMode === option.value 
              ? theme.colors.onPrimaryContainer 
              : theme.colors.onSurface,
            fontSize: 12,
            fontWeight: themeMode === option.value ? '600' : '400',
          },
          showSelectedCheck: false,
        }))}
        style={styles.segmentedButtons}
        density="small"
      />
    </View>
  );
};

export default React.memo(CompactThemeSelector);

const styles = StyleSheet.create({
  container: {
    marginVertical: SPACING.xs,
  },
  segmentedButtons: {
    borderRadius: BORDER_RADIUS.md,
  },
  button: {
    borderRadius: BORDER_RADIUS.sm,
    minHeight: 36,
  },
});