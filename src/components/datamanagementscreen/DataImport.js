import React, { useCallback } from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, Card, Button } from 'react-native-paper';
import { StyleSheet } from 'react-native';

const DataImport = () => {
  const handleImport = useCallback(() => {
    // Handle data import
  }, []);

  return (
    <Card style={styles.card}>
      <Card.Content>
        <Text variant="titleMedium">Import Data</Text>
        <Text>Import data from CSV, Excel, or JSON files</Text>
      </Card.Content>
      <Card.Actions>
        <Button mode="contained" onPress={handleImport}>Import</Button>
      </Card.Actions>
    </Card>
  );
};






export default React.memo(DataImport);

const styles = StyleSheet.create({
  card: {
    margin: 16
}
});