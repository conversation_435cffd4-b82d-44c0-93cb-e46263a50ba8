import React, { useCallback } from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, Card, Button } from 'react-native-paper';
import { StyleSheet } from 'react-native';

const DataBackup = () => {
  const handleBackup = useCallback(() => {
    // Handle data backup
  }, []);

  return (
    <Card style={styles.card}>
      <Card.Content>
        <Text variant="titleMedium">Backup Data</Text>
        <Text>Create a backup of all your data</Text>
      </Card.Content>
      <Card.Actions>
        <Button mode="contained" onPress={handleBackup}>Backup</Button>
      </Card.Actions>
    </Card>
  );
};






export default React.memo(DataBackup);

const styles = StyleSheet.create({
  card: {
    margin: 16
}
});