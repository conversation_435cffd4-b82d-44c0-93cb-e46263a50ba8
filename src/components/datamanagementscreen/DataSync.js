import React, { useCallback } from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, Card, Button } from 'react-native-paper';
import { StyleSheet } from 'react-native';

const DataSync = () => {
  const handleSync = useCallback(() => {
    // Handle data sync
  }, []);

  return (
    <Card style={styles.card}>
      <Card.Content>
        <Text variant="titleMedium">Sync Data</Text>
        <Text>Synchronize data with cloud storage</Text>
      </Card.Content>
      <Card.Actions>
        <Button mode="contained" onPress={handleSync}>Sync</Button>
      </Card.Actions>
    </Card>
  );
};






export default React.memo(DataSync);

const styles = StyleSheet.create({
  card: {
    margin: 16
}
});