import React, { useCallback } from 'react';
import { View, StyleSheet   } from 'react-native';
import { Text, Card, Button   } from 'react-native-paper';
import { StyleSheet   } from 'react-native';

const DataExport = () => {
  const handleExport = useCallback(() => {
    // Handle data export
  }, []);

  return (
    <Card style={styles.card}>
      <Card.Content>
        <Text variant="titleMedium">Export Data</Text>
        <Text>Export data to CSV, Excel, or JSON format</Text>
      </Card.Content>
      <Card.Actions>
        <Button mode="contained" onPress={handleExport}>Export</Button>
      </Card.Actions>
    </Card>
  );
};






export default React.memo(DataExport);

const styles = StyleSheet.create({
  card: {
    margin: 16
}
});