/**
 * StatCardGroup - Beautiful grid layout for statistics cards
 * Loved by users for its clean design and flexibility
 */

import React, { useMemo } from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Text, Surface } from 'react-native-paper';
// FIXED: Changed useTheme to import from your custom ThemeContext
import { useTheme } from '../context/ThemeContext'; 
import LocalIcon from './LocalIcon';
import { SPACING, BORDER_RADIUS, TYPOGRAPHY } from '../theme/designTokens';

const StatCardGroup = ({
  cards = [],
  columns = 2,
  showTitle = true,
  title,
  onCardPress,
  containerStyle,
  cardStyle,
}) => {
  const { theme } = useTheme();
  
  // FIXED: Styles moved inside component and memoized for performance
  const styles = useMemo(() => createStyles(theme), [theme]);

  if (!cards || cards.length === 0) {
    return null;
  }

  return (
    <View style={[styles.container, containerStyle]}>
      {showTitle && title && (
        <Text variant="titleLarge" style={styles.title}>
          {title}
        </Text>
      )}

      <View style={styles.grid}>
        {cards.map((card, index) => {
          const isLastCard = index === cards.length - 1;
          const isOddCount = cards.length % columns !== 0;
          const shouldSpanFullWidth = isOddCount && isLastCard;

          return (
            <View
              key={card.key || card.id || index}
              style={[
                styles.cardWrapper,
                {
                  width: shouldSpanFullWidth ? '100%' : `${100 / columns}%`,
                }
              ]}
            >
              <StatCard
                title={card.title}
                value={card.value}
                icon={card.icon}
                iconColor={card.iconColor}
                subtitle={card.subtitle}
                onPress={() => {
                  if (onCardPress) onCardPress(card, index);
                  else if (card.onPress) card.onPress();
                }}
                style={cardStyle}
              />
            </View>
          );
        })}
      </View>
    </View>
  );
};

const StatCard = ({
  title,
  value,
  icon,
  iconColor,
  onPress,
  style,
  subtitle,
}) => {
  const { theme } = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);

  const cardContent = (
    <Surface style={[styles.card, style]} elevation={1}>
      <View style={styles.cardContent}>
        {icon && (
          <View style={[styles.iconContainer, { backgroundColor: `${iconColor || theme.colors.primary}20` }]}>
            <LocalIcon
              name={icon}
              size={20}
              color={iconColor || theme.colors.primary}
            />
          </View>
        )}
        <View style={styles.textContent}>
          <Text style={styles.value} numberOfLines={1}>{value}</Text>
          <Text style={styles.cardTitle} numberOfLines={1}>{title}</Text>
          {subtitle && <Text style={styles.subtitle} numberOfLines={1}>{subtitle}</Text>}
        </View>
      </View>
    </Surface>
  );

  return onPress ? <TouchableOpacity onPress={onPress} style={styles.touchable}>{cardContent}</TouchableOpacity> : cardContent;
};

export default React.memo(StatCardGroup);

// This function creates the stylesheet object, allowing it to use the theme from props.
const createStyles = (theme) => StyleSheet.create({
  container: {
    marginBottom: SPACING.md,
  },
  title: {
    marginBottom: SPACING.md,
    color: theme.colors.onBackground,
    fontWeight: '600',
  },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -SPACING.xs,
  },
  cardWrapper: {
    marginBottom: SPACING.sm,
    paddingHorizontal: SPACING.xs,
  },
  touchable: {
    flex: 1,
  },
  card: {
    backgroundColor: theme.colors.surface,
    borderRadius: BORDER_RADIUS.lg,
    borderWidth: 1,
    borderColor: theme.colors.outlineVariant,
    overflow: 'hidden',
    flex: 1,
  },
  cardContent: {
    padding: SPACING.md,
  },
  iconContainer: {
      width: 40,
      height: 40,
      borderRadius: BORDER_RADIUS.md,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: SPACING.sm,
  },
  textContent: {
    flex: 1,
  },
  value: {
    color: theme.colors.onSurface,
    ...TYPOGRAPHY.headlineSmall,
    marginBottom: SPACING.xs,
  },
  cardTitle: {
    color: theme.colors.onSurfaceVariant,
    ...TYPOGRAPHY.titleSmall,
  },
  subtitle: {
    color: theme.colors.onSurfaceVariant,
    ...TYPOGRAPHY.bodySmall,
    opacity: 0.7,
    marginTop: SPACING.xs,
  },
});