/**
 * MD3Form<PERSON>ield - Material Design 3 compliant form field with validation
 * Includes proper error states, helper text, and accessibility
 */

import React, { useState, useCallback } from 'react';
import { View, StyleSheet   } from 'react-native';
import { TextInput,
  HelperText,
  Text,
  useTheme,
  SegmentedButtons,
  Switch,
  Chip
  } from '../utils/OptimizedPaperImports';
import MD3Icon from './StandardIcon';
import { SPACING   } from '../theme/designTokens';


const MD3FormField = ({
  // Field configuration
  type = 'text', // 'text', 'email', 'phone', 'number', 'password', 'textarea', 'select', 'switch', 'chips'
  label,
  placeholder,
  value,
  onChangeText,
  
  // Validation
  error,
  helperText,
  required = false,
  
  // Input specific props
  multiline = false,
  numberOfLines = 3,
  keyboardType = 'default',
  secureTextEntry = false,
  autoCapitalize = 'sentences',
  
  // Select/Chips options
  options = [], // For select and chips
  multiple = false, // For chips
  
  // Switch props
  onValueChange, // For switch
  
  // Styling
  style,
  inputStyle,
  mode = 'outlined',
  
  // Icons
  leftIcon,
  rightIcon,
  
  // Accessibility
  accessibilityLabel,
  accessibilityHint,
  
  // Other props
  disabled = false,
  ...otherProps
}) => {
  const { theme  } = useTheme();
  const [showPassword, setShowPassword] = useState(false);

  // Auto-detect keyboard type based on field type
  const getKeyboardType = () => {
    switch(type) {
      case 'email': return 'email-address';
      case 'phone': return 'phone-pad';
      case 'number': return 'numeric';
      default: return keyboardType;
  }
  };

  // Auto-detect secure text entry
  const getSecureTextEntry = () => {
    return type === 'password' && !showPassword;
  };

  // Render password toggle icon
  const renderPasswordToggle = () => {
    if (type !== 'password') return null;
    
    return (
      <MD3Icon
        icon={showPassword ? 'eye-off' : 'eye'}
        size="medium"
        color={theme.colors.onSurfaceVariant}
        onPress={() => setShowPassword(!showPassword)}
        containerSize={40}
        accessibilityLabel={showPassword ? 'Hide password' : 'Show password'}
      />
    );
  };

  // Render left icon
  const renderLeftIcon = () => {
    if (!leftIcon) return null;
    
    return (
      <MD3Icon
        {...(typeof leftIcon === 'string' ? { icon: leftIcon } : leftIcon)}
        size="medium"
        color={theme.colors.onSurfaceVariant}
      />
    );
  };

  // Render right icon
  const renderRightIcon = () => {
    if(type === 'password') {
      return renderPasswordToggle();
    }
    
    if (!rightIcon) return null;
    
    return (
      <MD3Icon
        {...(typeof rightIcon === 'string' ? { icon: rightIcon } : rightIcon)}
        size="medium"
        color={theme.colors.onSurfaceVariant}
      />
    );
  };

  // Render text input
  const renderTextInput = () => (
    <TextInput
      label={label + (required ? ' *' : '')}
      placeholder={placeholder}
      value={value}
      onChangeText={onChangeText}
      mode={mode}
      error={!!error}
      disabled={disabled}
      multiline={type === 'textarea' || multiline}
      numberOfLines={type === 'textarea' ? numberOfLines : 1}
      keyboardType={getKeyboardType()}
      secureTextEntry={getSecureTextEntry()}
      autoCapitalize={autoCapitalize}
      left={leftIcon ? <TextInput.Icon icon={() => renderLeftIcon()} /> : undefined}
      right={rightIcon || type === 'password' ? <TextInput.Icon icon={() => renderRightIcon()} /> : undefined}
      style={[styles.input, inputStyle}
      accessibilityLabel={accessibilityLabel || label}
      accessibilityHint={accessibilityHint}
      {...otherProps}
    />
  );

  // Render select field
  const renderSelect = () => (
    <View>
      <Text variant="bodyMedium" style={styles.style3}>
        {label + (required ? ' *' : '')}
      </Text>
      <SegmentedButtons
        value={value}
        onValueChange={onChangeText}
        buttons={options.map(option => ({
          value: typeof option === 'string' ? option : option.value,
          label: typeof option === 'string' ? option : option.label,
          disabled: disabled || (typeof option === 'object' && option.disabled),
        }))}
        style={styles.style4}
      />
    </View>
  );

  // Render switch field
  const renderSwitch = () => (
    <View style={styles.switchContainer}>
      <Text variant="bodyLarge" style={styles.style3]>
        {label + (required ? ' *' : '')}
      </Text>
      <Switch
        value={value}
        onValueChange={onValueChange || onChangeText}
        disabled={disabled}
        accessibilityLabel={accessibilityLabel || label}
        accessibilityHint={accessibilityHint}
      />
    </View>
  );

  // Render chips field
  const renderChips = () => (
    <View>
      <Text variant="bodyMedium" style={styles.style3}>
        {label + (required ? ' *' : '')}
      </Text>
      <View style={styles.chipsContainer}>
        {options.map((option, index) => {
          const optionValue = typeof option === 'string' ? option : option.value;
          const optionLabel = typeof option === 'string' ? option : option.label;
          const isSelected = multiple 
            ? (Array.isArray(value) && value.includes(optionValue))
            : value === optionValue;

          return (
            <Chip
              key={index}
              selected={isSelected}
              onPress={() => {
                if (disabled) return;
                
                if(multiple) {
                  const currentValues = Array.isArray(value) ? value : [];
                  const newValues = isSelected
                    ? currentValues.filter(v => v !== optionValue)
                    : [...currentValues, optionValue];
                  onChangeText(newValues);
                } else {
                  onChangeText(optionValue);
                }
              }}
              style={[
                styles.style2,
                error && { borderColor: theme.colors.error }
              }
              textStyle={[
                isSelected && { color: theme.colors.onPrimary }
              }
              disabled={disabled}>
              {optionLabel}
            </Chip>
          );
        })}
      </View>
    </View>
  );

  // Render appropriate field type
  const renderField = () => {
    switch(type) {
      case 'select':
        return renderSelect();
      case 'switch':
        return renderSwitch();
      case 'chips':
        return renderChips();
      default:
        return renderTextInput();
    }
  };

  return (
    <View style={[styles.container, style}>
      {renderField()}
      
      {/* Helper text or error message */}
      {(error || helperText) && (
        <HelperText type={error ? 'error' : 'info'} visible={!!(error || helperText)}>
          {error || helperText}
        </HelperText>
      )}
    </View>
  );
};






export default MD3FormField;

const styles = StyleSheet.create({
  container: {
    marginBottom: SPACING.md,
  },
  input: {
    backgroundColor: 'transparent',
  },
  selectLabel: {
    marginBottom: SPACING.sm,
    fontWeight: '500',
  },
  segmentedButtons: {
    marginTop: SPACING.xs,
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.md,
  },
  chipsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: SPACING.xs,
  },
  chip: {
    marginRight: SPACING.sm,
    marginBottom: SPACING.sm,
  },
  style1: {
    flex: 1,
  },
  style2: {
    marginRight: SPACING.sm,
    marginBottom: SPACING.sm,
  },
  style3: {
    marginBottom: SPACING.sm,
    fontWeight: '500',
  },
  style4: {
    marginTop: SPACING.xs,
  },
});