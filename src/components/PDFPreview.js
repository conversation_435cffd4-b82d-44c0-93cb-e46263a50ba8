import { Text   } from 'react-native-paper';
// PDF PREVIEW COMPONENT - Preview generated PDFs
import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, StyleSheet, Alert, Dimensions   } from 'react-native';
import { WebView   } from 'react-native-webview';
import EnhancedCard from './EnhancedCard';
import EnhancedButton from './EnhancedButton';
import LocalIcon, { NavigationLocalIcon, ActionLocalIcon, BusinessLocalIcon, SettingsLocalIcon, StatusLocalIcon } from './LocalIcon';

const { width, height  } = Dimensions.get('window');

const PDFPreview = ({ 
  html, 
  title = 'PDF Preview', 
  onClose,
  onShare,
  onPrint,
  showActions = true 
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  if(!html) {
    return (
      <EnhancedCard variant="default" padding="large">
        <View style={styles.style1}>
          <LocalIcon name="alert" size={48} color="#ef4444" />
          <Text style={styles.style1}>No Content</Text>
          <Text style={styles.style1}>No HTML content provided for preview</Text>
        </View>
      </EnhancedCard>
    );
  }

  const handleLoadStart = useCallback(() => {
    setIsLoading(true);
    setError(null);
  }, []);

  const handleLoadEnd = useCallback(() => {
    setIsLoading(false);
  }, []);

  const handleError = useCallback((syntheticEvent) => {
    const { nativeEvent  } = syntheticEvent;
    setIsLoading(false);
    setError(nativeEvent.description || 'Failed to load preview');
    console.error('WebView error:', nativeEvent);
  }, []);

  const handleShare = useCallback(() => {
    if(onShare) {
      onShare();
    } else {
      Alert.alert('Share', 'Share functionality not implemented');
    }
  }, [onShare]);

  const handlePrint = useCallback(() => {
    if(onPrint) {
      onPrint();
    } else {
      Alert.alert('Print', 'Print functionality not implemented');
    }
  }, [onPrint]);

  return (
    <View style={styles.style1}>
      {/* Header */}
      <EnhancedCard variant="flat" padding="medium" style={styles.style1}>
        <View style={styles.style1}>
          <View style={styles.style1}>
            <Text style={styles.style1}>{title}</Text>
            <Text style={styles.style1}>Preview</Text>
          </View>
          {onClose && (
            <EnhancedButton
              variant="ghost"
              size="small"
              icon="close"
              onPress={onClose}
            />
          )}
        </View>
      </EnhancedCard>

      {/* Preview Content */}
      <View style={styles.style1}>
        {error ? (
          <EnhancedCard variant="default" padding="large" style={styles.style1}>
            <View style={styles.style1}>
              <LocalIcon name="alert" size={48} color="#ef4444" />
              <Text style={styles.style1}>Preview Error</Text>
              <Text style={styles.style1}>{error}</Text>
              <EnhancedButton
                variant="outline"
                size="small"
                title="Retry"
                icon="refresh"
                onPress={() => {
                  setError(null);
                  setIsLoading(true);
                }
                style={styles.style1}
              />
            </View>
          </EnhancedCard>
        ) : (
          <>
            {isLoading && (
              <View style={styles.style1}>
                <EnhancedCard variant="default" padding="large">
                  <View style={styles.style1}>
                    <LocalIcon name="loading" size={32} color="#6366f1" />
                    <Text style={styles.style1}>Loading preview...</Text>
                  </View>
                </EnhancedCard>
              </View>
            )}
            
            <WebView
              source={{ html }
              style={styles.style1}
              onLoadStart={handleLoadStart}
              onLoadEnd={handleLoadEnd}
              onError={handleError}
              showsVerticalScrollIndicator={true}
              showsHorizontalScrollIndicator={false}
              scalesPageToFit={true}
              startInLoadingState={true}
              javaScriptEnabled={false}
              domStorageEnabled={false}
            />
          </>
        )}
      </View>

      {/* Actions */}
      {showActions && !error && (
        <EnhancedCard variant="flat" padding="medium" style={styles.style1}>
          <View style={styles.style1}>
            <EnhancedButton
              variant="outline"
              size="medium"
              title="Share PDF"
              icon="share"
              onPress={handleShare}
              style={styles.style1}
            />
            <EnhancedButton
              variant="primary"
              size="medium"
              title="Print"
              icon="print"
              onPress={handlePrint}
              style={styles.style1}
            />
          </View>
        </EnhancedCard>
      )}
    </View>
  );
};






export default PDFPreview;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 2,
  },
  subtitle: {
    fontSize: 14,
    color: '#6b7280',
  },
  previewContainer: {
    flex: 1,
    position: 'relative',
  },
  webview: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(249, 250, 251, 0.9)',
    zIndex: 1,
  },
  loadingContainer: {
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#6b7280',
    marginTop: 12,
  },
  errorCard: {
    margin: 20,
  },
  errorContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  errorTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#ef4444',
    marginTop: 12,
    marginBottom: 8,
  },
  errorText: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 20,
  },
  retryButton: {
    minWidth: 100,
  },
  actionsContainer: {
    backgroundColor: '#ffffff',
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  actions: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
  style1: {
    flex: 1,
  },
});