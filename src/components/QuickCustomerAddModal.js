/**
 * QuickCustomerAddModal - Quick customer addition modal
 * Minimal form for fast customer creation during order process
 */

import React, { useState, useCallback } from 'react';
import { View, StyleSheet, ScrollView, Alert   } from 'react-native';
import { Modal   } from 'react-native-paper';
import { Portal   } from 'react-native-paper';
import { Text   } from 'react-native-paper';
import { TextInput   } from 'react-native-paper';
import { Button   } from 'react-native-paper';
import { IconButton   } from 'react-native-paper';
import { Chip   } from 'react-native-paper';
import { SegmentedButtons   } from 'react-native-paper';

import { useTheme   } from '../context/ThemeContext';
import { useData   } from '../context/DataContext';
import { SPACING, BORDER_RADIUS   } from '../theme/designTokens';

const QuickCustomerAddModal = ({
  visible,
  onDismiss,
  onCustomerAdded}) => {
  const { theme  } = useTheme();
  const { actions  } = useData();
  
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    email: '',
    gender: 'Male',
    category: 'Men',
    isVIP: false});
  const [loading, setLoading] = useState(false);

  const categoryOptions = [
    { value: 'Men', label: 'Men', icon: 'account' },
    { value: 'Women', label: 'Women', icon: 'account-outline' },
    { value: 'Child', label: 'Child', icon: 'account-child' }];
  const genderOptions = [
    { value: 'Male', label: 'Male' },
    { value: 'Female', label: 'Female' }];
  const handleSave = useCallback(async () => {
    // Validation
    if (!formData.name.trim()) {
      Alert.alert('Validation Error', 'Customer name is required.');
      return;
    }

    if (!formData.phone.trim()) {
      Alert.alert('Validation Error', 'Phone number is required.');
      return;
    }

    setLoading(true);

    try {
      const customerData = {
        id: Date.now().toString(),
        name: formData.name.trim(),
        phone: formData.phone.trim(),
        email: formData.email.trim() || '',
        gender: formData.gender,
        category: formData.category,
        isVIP: formData.isVIP,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        // Additional fields for tailor management
        measurements: {},
        orders: [],
        totalOrders: 0,
        totalSpent: 0,
        lastOrderDate: null,
        notes: '',
        address: ''};

      await actions.addCustomer(customerData);
      
      Alert.alert(
        'Success!',
        `Customer "${formData.name}" has been added successfully.`,
        [
          {
            text: 'OK',
            onPress: () => {
              onCustomerAdded(customerData);
              handleClose();
            }
          }
        ]
      );
    } catch (error) {
      console.error('Error adding customer:', error);
      Alert.alert('Error', 'Failed to add customer. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = useCallback(() => {
    setFormData({
      name: '',
      phone: '',
      email: '',
      gender: 'Male',
      category: 'Men',
      isVIP: false});
    onDismiss();
  }, [onDismiss]);

  return (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={handleClose}
        contentContainerStyle={[
          styles.modalContainer,
          { backgroundColor: theme.colors.surface }
        }>
        <ScrollView showsVerticalScrollIndicator={false}>
          {/* Header */}
          <View style={styles.style1}>
            <Text variant="headlineSmall" style={styles.style5]>
              Quick Add Customer
            </Text>
            <IconButton
              icon="close"
              size={24}
              onPress={handleClose}
              iconColor={theme.colors.onSurface}
            />
          </View>

          {/* Form */}
          <View style={styles.style1}>
            {/* Name */}
            <TextInput
              label="Customer Name *"
              value={formData.name}
              onChangeText={(text) => setFormData(prev => ({ ...prev, name: text }))}
              mode="outlined"
              style={styles.style1}
              autoCapitalize="words"
              autoCorrect={false}
            />

            {/* Phone */}
            <TextInput
              label="Phone Number *"
              value={formData.phone}
              onChangeText={(text) => setFormData(prev => ({ ...prev, phone: text }))}
              mode="outlined"
              style={styles.style1}
              keyboardType="phone-pad"
              autoCorrect={false}
            />

            {/* Email */}
            <TextInput
              label="Email (Optional)"
              value={formData.email}
              onChangeText={(text) => setFormData(prev => ({ ...prev, email: text }))}
              mode="outlined"
              style={styles.style1}
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
            />

            {/* Category Selection */}
            <Text variant="titleSmall" style={styles.style3}>
              Category
            </Text>
            <View style={styles.style1}>
              {categoryOptions.map((option) => (
                <Chip
                  key={option.value}
                  mode={formData.category === option.value ? 'flat' : 'outlined'}
                  selected={formData.category === option.value}
                  onPress={() => setFormData(prev => ({ ...prev, category: option.value }))}
                  icon={option.icon}
                  style={styles.style4}>
                  {option.label}
                </Chip>
              ))}
            </View>

            {/* Gender Selection */}
            <Text variant="titleSmall" style={styles.style3}>
              Gender
            </Text>
            <SegmentedButtons
              value={formData.gender}
              onValueChange={(value) => setFormData(prev => ({ ...prev, gender: value }))}
              buttons={genderOptions}
              style={styles.style1}
            />

            {/* VIP Toggle */}
            <View style={styles.style1}>
              <Chip
                mode={formData.isVIP ? 'flat' : 'outlined'}
                selected={formData.isVIP}
                onPress={() => setFormData(prev => ({ ...prev, isVIP: !prev.isVIP }))}
                icon="crown"
                style={styles.style2}
                textStyle={{
                  color: formData.isVIP ? 'white' : theme.colors.onSurface}>
                VIP Customer
              </Chip>
            </View>
          </View>

          {/* Action Buttons */}
          <View style={styles.style1}>
            <Button
              mode="outlined"
              onPress={handleClose}
              style={styles.style1}
              disabled={loading}>
              Cancel
            </Button>
            <Button
              mode="contained"
              onPress={handleSave}
              style={styles.style1}
              loading={loading}
              disabled={loading}>
              Add Customer
            </Button>
          </View>
        </ScrollView>
      </Modal>
    </Portal>
  );
};






export default QuickCustomerAddModal;

const styles = StyleSheet.create({
  modalContainer: {
    margin: SPACING.lg,
    borderRadius: BORDER_RADIUS.xl,
    maxHeight: '90%'},
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.lg,
    paddingBottom: SPACING.md},
  form: {
    paddingHorizontal: SPACING.lg},
  input: {
    marginBottom: SPACING.md},
  sectionTitle: {
    fontWeight: '600',
    marginBottom: SPACING.sm,
    marginTop: SPACING.sm},
  categoryContainer: {
    flexDirection: 'row',
    gap: SPACING.sm,
    marginBottom: SPACING.md},
  categoryChip: {
    flex: 1},
  segmentedButtons: {
    marginBottom: SPACING.md},
  vipContainer: {
    alignItems: 'flex-start',
    marginBottom: SPACING.lg},
  vipChip: {
    alignSelf: 'flex-start'},
  actions: {
    flexDirection: 'row',
    gap: SPACING.md,
    padding: SPACING.lg,
    paddingTop: 0},
  button: {
    flex: 1},
  cancelButton: {
    marginRight: SPACING.sm},
  saveButton: {
    marginLeft: SPACING.sm},
  style1: {
  },
  style2: {
    backgroundColor: '#FF9800'},
  style3: {
    color: theme.colors.onSurface},
  style4: {
    backgroundColor: 'transparent'},
  style5: {
    color: theme.colors.onSurface,
    fontWeight: '600',
    flex: 1}});