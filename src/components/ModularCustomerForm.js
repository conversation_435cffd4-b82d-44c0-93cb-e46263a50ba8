/**
 * ModularCustomerForm - Example component using the modular data approach
 * Demonstrates how to use Collections, Forms, and Relations together
 */

import React, { useState, useEffect, useCallback } from 'react';
import { View, ScrollView, StyleSheet, Alert } from 'react-native';
import { Text } from 'react-native-paper';
import { TextInput } from 'react-native-paper';
import { Button } from 'react-native-paper';
import { Surface } from 'react-native-paper';
import { Switch } from 'react-native-paper';
import { SegmentedButtons } from 'react-native-paper';
import { HelperText } from 'react-native-paper';
import { useTheme } from '../context/ThemeContext';
import { useData } from '../context/DataContext';

const ModularCustomerForm = ({ customerId = null, onSave, onCancel }) => {
  const { theme } = useTheme();
  const { state, actions, loading } = useData();

  const [formData, setFormData] = useState({});
  const [formErrors, setFormErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formSchema, setFormSchema] = useState(null);

  useEffect(() => {
    // Set default form schema for customer
    const schema = {
      title: customerId ? 'Edit Customer' : 'Add Customer',
      fields: [
        { name: 'name', label: 'Name', type: 'text', placeholder: 'Enter customer name' },
        { name: 'phone', label: 'Phone', type: 'phone', placeholder: 'Enter phone number' },
        { name: 'email', label: 'Email', type: 'email', placeholder: 'Enter email address' },
        { name: 'address', label: 'Address', type: 'textarea', placeholder: 'Enter address' },
      ]
    };
    setFormSchema(schema);

    // Load existing customer data if editing
    if (customerId) {
      loadCustomerData();
    }
  }, [customerId]);

  const loadCustomerData = async () => {
    try {
      const customer = state.customers.find(c => c.id === customerId);
      if (customer) {
        setFormData(customer);
      } else {
        Alert.alert('Error', 'Customer not found');
      }
    } catch (err) {
      Alert.alert('Error', 'Failed to load customer data');
    }
  };

  const handleFieldChange = useCallback((fieldName, value) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: value
    })), []);

    // Clear field error when user starts typing
    if (formErrors[fieldName]) {
      setFormErrors(prev => ({
        ...prev,
        [fieldName]: undefined
      }));
    }
  };

  const validateForm = () => {
    const errors = {};

    if (!formData.name?.trim()) {
      errors.name = ['Name is required'];
    }

    if (!formData.phone?.trim()) {
      errors.phone = ['Phone is required'];
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = useCallback(async () => {
    if (!validateForm()) {
      Alert.alert('Validation Error', 'Please fix the errors and try again'), []);
      return;
    }

    setIsSubmitting(true);
    try {
      if (customerId) {
        await actions.updateCustomer(customerId, formData);
      } else {
        await actions.addCustomer(formData);
      }

      Alert.alert(
        'Success',
        `Customer ${customerId ? 'updated' : 'created'} successfully`,
        [{ text: 'OK', onPress: () => onSave?.(formData) }]
      );
    } catch (err) {
      Alert.alert('Error', 'An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderField = (field) => {
    const value = formData[field.name] || '';
    const hasError = formErrors[field.name];

    switch (field.type) {
      case 'text':
      case 'email':
      case 'phone':
        return (
          <View key={field.name} style={styles.fieldContainer}>
            <TextInput
              label={field.label}
              value={value}
              onChangeText={(text) => handleFieldChange(field.name, text)}
              mode="outlined"
              error={!!hasError}
              keyboardType={field.type === 'email' ? 'email-address' : field.type === 'phone' ? 'phone-pad' : 'default'}
              placeholder={field.placeholder}
              style={styles.textInput}
            />
            {hasError && (
              <HelperText type="error" visible={!!hasError}>
                {hasError[0]}
              </HelperText>
            )}
          </View>
        );

      case 'textarea':
        return (
          <View key={field.name} style={styles.fieldContainer}>
            <TextInput
              label={field.label}
              value={value}
              onChangeText={(text) => handleFieldChange(field.name, text)}
              mode="outlined"
              error={!!hasError}
              multiline
              numberOfLines={3}
              placeholder={field.placeholder}
              style={styles.textInput}
            />
            {hasError && (
              <HelperText type="error" visible={!!hasError}>
                {hasError[0]}
              </HelperText>
            )}
          </View>
        );

      case 'boolean':
        return (
          <View key={field.name} style={styles.fieldContainer}>
            <View style={styles.switchContainer}>
              <Text variant="bodyLarge" style={styles.fieldLabel}>
                {field.label}
              </Text>
              <Switch
                value={!!value}
                onValueChange={(checked) => handleFieldChange(field.name, checked)}
              />
            </View>
            {hasError && (
              <HelperText type="error" visible={!!hasError}>
                {hasError[0]}
              </HelperText>
            )}
          </View>
        );

      case 'select':
        return (
          <View key={field.name} style={styles.fieldContainer}>
            <Text variant="bodyMedium" style={styles.fieldLabel}>
              {field.label}
            </Text>
            <SegmentedButtons
              value={value}
              onValueChange={(selectedValue) => handleFieldChange(field.name, selectedValue)}
              buttons={field.options.map(option => ({
                value: option.value,
                label: option.label
              }))}
              style={styles.segmentedButtons}
            />
            {hasError && (
              <HelperText type="error" visible={!!hasError}>
                {hasError[0]}
              </HelperText>
            )}
          </View>
        );

      case 'date':
        return (
          <View key={field.name} style={styles.fieldContainer}>
            <TextInput
              label={field.label}
              value={value}
              onChangeText={(text) => handleFieldChange(field.name, text)}
              mode="outlined"
              error={!!hasError}
              placeholder="YYYY-MM-DD"
              style={styles.textInput}
            />
            {hasError && (
              <HelperText type="error" visible={!!hasError}>
                {hasError[0]}
              </HelperText>
            )}
          </View>
        );

      default:
        return null;
    }
  };

  if (!formSchema) {
    return (
      <View style={styles.loadingContainer}>
        <Text>Loading form...</Text>
      </View>
    );
  }

  return (
    <Surface style={styles.container} elevation={1}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <Text variant="headlineSmall" style={styles.title}>
          {formSchema.title}
        </Text>

        {formSchema.fields.map(renderField)}

        <View style={styles.buttonContainer}>
          <Button
            mode="outlined"
            onPress={onCancel}
            style={styles.button}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            mode="contained"
            onPress={handleSubmit}
            style={styles.button}
            loading={isSubmitting}
            disabled={loading || isSubmitting}
          >
            {customerId ? 'Update' : 'Create'} Customer
          </Button>
        </View>
      </ScrollView>
    </Surface>
  );
};

export default ModularCustomerForm;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    margin: 16,
    borderRadius: 12,
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    marginBottom: 24,
    fontWeight: '600',
    textAlign: 'center',
  },
  errorText: {
    marginBottom: 16,
    textAlign: 'center',
  },
  fieldContainer: {
    marginBottom: 16,
  },
  textInput: {
    backgroundColor: 'transparent',
  },
  fieldLabel: {
    marginBottom: 8,
    fontWeight: '500',
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  segmentedButtons: {
    marginTop: 4,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
    gap: 12,
  },
  button: {
    flex: 1,
  },
});