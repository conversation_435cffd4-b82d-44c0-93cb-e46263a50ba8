// QR SCANNER MODAL - Modal wrapper for QR scanner
import React, { useCallback } from 'react';
import { Modal, View, StyleSheet, StatusBar   } from 'react-native';
import QRScanner from './QRScanner';

const QRScannerModal = ({ 
  visible, 
  onClose, 
  onScan,
  animationType = 'slide',
  presentationStyle = 'fullScreen'
}) => {
  const handleScan = useCallback((data) => {
    if(onScan) {
      onScan(data), []);
    }
    // Auto-close modal after successful scan
    setTimeout(() => {
      onClose();
    }, 1000);
  };

  return (
    <Modal
      visible={visible}
      animationType={animationType}
      presentationStyle={presentationStyle}
      onRequestClose={onClose}>
      <StatusBar barStyle="light-content" backgroundColor="#000000" />
      <View style={styles.style1}>
        <QRScanner
          onScan={handleScan}
          onClose={onClose}
          isVisible={visible}
        />
      </View>
    </Modal>
  );
};






export default React.memo(QRScannerModal);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  style1: {
  },
});