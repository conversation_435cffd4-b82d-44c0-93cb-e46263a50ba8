/**
 * UnifiedSortMenu - Consistent sort menu component for all screens
 * Provides sort options with unified styling and behavior
 */

import React, { useState, useCallback } from 'react';
import { View, StyleSheet   } from 'react-native';
import { IconButton   } from 'react-native-paper';
import { Menu   } from 'react-native-paper';
import { Text   } from 'react-native-paper';
import { Divider   } from 'react-native-paper';
import LocalIcon from './LocalIcon';
import { useTheme   } from '../context/ThemeContext';
import { BORDER_RADIUS   } from '../theme/designTokens';

const UnifiedSortMenu = ({
  sortOptions = [],
  selectedSort = null,
  onSortChange,
  style,
  iconColor,
  disabled = false,
}) => {
  const { theme  } = useTheme();
  const [menuVisible, setMenuVisible] = useState(false);

  const openMenu = () => setMenuVisible(true);
  const closeMenu = () => setMenuVisible(false);

  const handleSortSelect = useCallback((sortOption) => {
    onSortChange(sortOption);
    closeMenu();
  }, [onSortChange]);

  const getSortIcon = (direction) => {
    switch(direction) {
      case 'asc':
        return 'sort-ascending';
      case 'desc':
        return 'sort-descending';
      default:
        return 'sort';
    }
  };

  const currentSort = sortOptions.find(option => option.key === selectedSort?.key);
  const currentIcon = currentSort ? getSortIcon(selectedSort?.direction) : 'sort';

  return (
    <View style={styles.container}>
      <Menu
        visible={menuVisible}
        onDismiss={closeMenu}
        anchor={
          <IconButton
            icon={currentIcon}
            size={24}
            iconColor={iconColor || theme.colors.onSurfaceVariant}
            onPress={openMenu}
            disabled={disabled}
            style={[
              styles.sortButton,
              {
                backgroundColor: currentSort ? theme.colors.primaryContainer : 'transparent',
              }
            }
          />
        }
        contentStyle={[styles.menuContent, { backgroundColor: theme.colors.surface }>
        <View style={styles.menuHeader}>
          <Text variant="titleSmall" style={[styles.headerText, { color: theme.colors.onSurface }}>
            Sort by
          </Text>
        </View>
        <Divider style={styles.headerDivider} />
        
        {sortOptions.map((option, index) => (
          <View key={option.key}>
            {/* Ascending option */}
            <Menu.Item
              onPress={() => handleSortSelect({ key: option.key, direction: 'asc' })}
              title={`${option.label}  (A-Z)`}
              leadingIcon={getSortIcon('asc')}
              titleStyle={{
                color: selectedSort?.key === option.key && selectedSort?.direction === 'asc'
                  ? theme.colors.primary
                  : theme.colors.onSurface
              }
              style={{
                backgroundColor: selectedSort?.key === option.key && selectedSort?.direction === 'asc'
                  ? theme.colors.primaryContainer
                  : 'transparent',
              }
            />
            
            {/* Descending option */}
            <Menu.Item
              onPress={() => handleSortSelect({ key: option.key, direction: 'desc' })}
              title={`${option.label}  (Z-A)`}
              leadingIcon={getSortIcon('desc')}
              titleStyle={{
                color: selectedSort?.key === option.key && selectedSort?.direction === 'desc'
                  ? theme.colors.primary
                  : theme.colors.onSurface
              }
              style={{
                backgroundColor: selectedSort?.key === option.key && selectedSort?.direction === 'desc'
                  ? theme.colors.primaryContainer
                  : 'transparent',
              }
            />
            
            {index < sortOptions.length - 1 && <Divider style={styles.optionDivider} />}
          </View>
        ))}
        
        {selectedSort && (
          <>
            <Divider style={styles.clearDivider} />
            <Menu.Item
              onPress={() => handleSortSelect(null)}
              title="Clear Sort"
              leadingIcon="close"
              titleStyle={{ color: theme.colors.error }
            />
          </>
        )}
      </Menu>
    </View>
  );
};

export default UnifiedSortMenu;

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  sortButton: {
    margin: 0,
    borderRadius: BORDER_RADIUS.md,
  },
  menuContent: {
    borderRadius: BORDER_RADIUS.lg,
    minWidth: 200,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  menuHeader: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingBottom: 8,
  },
  headerText: {
    fontWeight: '600',
  },
  headerDivider: {
    marginBottom: 8,
  },
  optionDivider: {
    marginVertical: 4,
  },
  clearDivider: {
    marginVertical: 8,
  },
});