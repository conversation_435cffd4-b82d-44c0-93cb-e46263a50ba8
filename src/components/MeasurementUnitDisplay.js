/**
 * MeasurementUnitDisplay - Component to display measurements with unit conversion
 */

import React, { useMemo } from 'react';
import { View, StyleSheet   } from 'react-native';
import { Text   } from 'react-native-paper';
import { Chip   } from 'react-native-paper';
import { useData   } from '../context/DataContext';
import { useTheme   } from '../context/ThemeContext';
import MeasurementService from '../services/MeasurementService';
import { SPACING   } from '../theme/designTokens';

const MeasurementUnitDisplay = ({ 
  value, 
  originalUnit = 'inches', 
  label = 'Measurement',
  showConversion = true,
  style = {} 
}) => {
  const { theme  } = useTheme();
  const { state  } = useData();
  
  const currentUnit = state.settings?.measurementUnit || 'inches';
  
  // Convert measurement to current unit if different
  const displayValue = originalUnit === currentUnit 
    ? value 
    : MeasurementService.convertMeasurement(value, originalUnit, currentUnit);
  
  const formattedValue = MeasurementService.formatMeasurement(displayValue, currentUnit);
  
  // Show conversion info if units are different
  const showConversionInfo = showConversion && originalUnit !== currentUnit;
  const originalFormatted = MeasurementService.formatMeasurement(value, originalUnit);

  const styles = useMemo(() => StyleSheet.create({
    container: {
      marginVertical: SPACING.xs},
    mainDisplay: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center'},
    label: {
      flex: 1,
      color: theme.colors.onSurfaceVariant},
    value: {
      fontWeight: '600',
      textAlign: 'right',
      color: theme.colors.onSurface},
    conversionInfo: {
      marginTop: SPACING.xs,
      alignItems: 'flex-end'},
    conversionChip: {
      backgroundColor: 'transparent',
      borderColor: theme.colors.outline},
    conversionText: {
      fontSize: 12}}), [theme]);

  return (
    <View style={[styles.container, style}>
      <View style={styles.mainDisplay}>
        <Text variant="bodyMedium" style={styles.label}>
          {label}
        </Text>
        <Text variant="titleMedium" style={styles.value}>
          {formattedValue}
        </Text>
      </View>
      
      {showConversionInfo && (
        <View style={styles.conversionInfo}>
          <Chip 
            mode="outlined" 
            compact
            style={styles.conversionChip}
            textStyle={[styles.conversionText, { color: theme.colors.onSurfaceVariant }>
            Originally: {originalFormatted}
          </Chip>
        </View>
      )}
    </View>
  );
};






export default MeasurementUnitDisplay;