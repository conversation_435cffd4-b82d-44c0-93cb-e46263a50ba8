import React, { useEffect, useRef, useCallback } from 'react';
import { View, StyleSheet, Animated, Modal, Dimensions } from 'react-native';
import { Text } from 'react-native-paper';
import LoadingSpinner from './LoadingSpinner';
import EnhancedCard from './EnhancedCard';

const { width, height } = Dimensions.get('window');

const LoadingOverlay = ({
  visible = false,
  message = 'Loading...',
  subMessage,
  variant = 'spinner', // 'spinner', 'dots', 'pulse'
  color = '#6366f1',
  backgroundColor = 'rgba(0, 0, 0, 0.5)',
  showCard = true,
  onRequestClose,
  style,
}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;

  useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 100,
          friction: 8,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 0.8,
          duration: 150,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible, fadeAnim, scaleAnim]);

  if (!visible) {
    return null;
  }

  const renderContent = () => {
    if (showCard) {
      return (
        <Animated.View
          style={[
            styles.cardContainer,
            {
              opacity: fadeAnim,
              transform: [{ scale: scaleAnim }],
            },
          ]}
        >
          <EnhancedCard variant="default" padding="large" style={styles.card}>
            <LoadingSpinner
              size="large"
              color={color}
              message={message}
              variant={variant}
              style={styles.spinner}
            />
            {subMessage && (
              <Text style={[
                styles.subMessage,
                { color: color }
              ]}>
                {subMessage}
              </Text>
            )}
          </EnhancedCard>
        </Animated.View>
      );
    } else {
      return (
        <Animated.View
          style={[
            styles.simpleContainer,
            {
              opacity: fadeAnim,
              transform: [{ scale: scaleAnim }],
            },
          ]}
        >
          <LoadingSpinner
            size="large"
            color="#ffffff"
            message={message}
            variant={variant}
            style={styles.spinner}
          />
          {subMessage && (
            <Text style={[
              styles.subMessage,
              { color: '#ffffff' }
            ]}>
              {subMessage}
            </Text>
          )}
        </Animated.View>
      );
    }
  };

  return (
    <Modal
      transparent
      visible={visible}
      animationType="none"
      onRequestClose={onRequestClose}
    >
      <Animated.View
        style={[
          styles.overlay,
          { backgroundColor, opacity: fadeAnim },
          style,
        ]}
      >
        {renderContent()}
      </Animated.View>
    </Modal>
  );
};

export default LoadingOverlay;

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    width,
    height,
  },
  cardContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 40,
  },
  card: {
    minWidth: 200,
    alignItems: 'center',
  },
  simpleContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 40,
  },
  spinner: {
    marginBottom: 16,
  },
  subMessage: {
    fontSize: 14,
    textAlign: 'center',
    marginTop: 8,
    opacity: 0.8,
    lineHeight: 20,
  },
});