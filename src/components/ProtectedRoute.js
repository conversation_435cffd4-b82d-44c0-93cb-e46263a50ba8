import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, Button, Surface } from 'react-native-paper';
import { useTheme } from '../context/ThemeContext';
import { useAuth } from '../context/AuthContext';
import { SPACING, BORDER_RADIUS } from '../theme/designTokens';
import LocalIcon from './LocalIcon';

/**
 * ProtectedRoute component for role and permission-based access control
 * Wraps content that requires specific authentication or authorization
 */
const ProtectedRoute = ({
  children,
  role,
  permission,
  fallback,
  showAccessDenied = true,
  onAccessDenied,
}) => {
  const { theme } = useTheme();
  const { user, hasPermission, isAdmin, isUser } = useAuth();

  // Check if user is authenticated
  if (!user) {
    return fallback || (
      showAccessDenied ? (
        <AccessDeniedView
          title="Authentication Required"
          message="Please log in to access this content."
          icon="login"
          theme={theme}
          onAction={onAccessDenied}
          actionText="Go to Login"
        />
      ) : null
    );
  }

  // Check role-based access
  if (role) {
    const hasRoleAccess = 
      (role === 'admin' && isAdmin()) ||
      (role === 'user' && (isUser() || isAdmin())) ||
      (Array.isArray(role) && role.includes(user.role));

    if (!hasRoleAccess) {
      return fallback || (
        showAccessDenied ? (
          <AccessDeniedView
            title="Access Denied"
            message="You don't have permission to access this content."
            icon="shield-lock"
            theme={theme}
            onAction={onAccessDenied}
            actionText="Go Back"
          />
        ) : null
      );
    }
  }

  // Check permission-based access
  if (permission) {
    const hasPermissionAccess = Array.isArray(permission)
      ? permission.some(p => hasPermission(p))
      : hasPermission(permission);

    if (!hasPermissionAccess) {
      return fallback || (
        showAccessDenied ? (
          <AccessDeniedView
            title="Access Denied"
            message="You don't have permission to access this content."
            icon="shield-lock"
            theme={theme}
            onAction={onAccessDenied}
            actionText="Go Back"
          />
        ) : null
      );
    }
  }

  // Render children if all checks pass
  return children;
};

const AccessDeniedView = ({ title, message, icon, theme, onAction, actionText }) => (
  <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
    <Surface style={[styles.accessDeniedCard, { backgroundColor: theme.colors.surface }]} elevation={2}>
      <View style={styles.accessDeniedContent}>
        <View style={[styles.iconContainer, { backgroundColor: theme.colors.errorContainer }]}>
          <LocalIcon name={icon} size={48} color={theme.colors.error} />
        </View>
        
        <Text variant="headlineSmall" style={[styles.title, { color: theme.colors.onSurface }>
          {title}
        </Text>
        
        <Text variant="bodyLarge" style={[styles.message, { color: theme.colors.onSurfaceVariant }>
          {message}
        </Text>
        
        {onAction && (
          <Button
            mode="contained"
            onPress={onAction}
            style={styles.actionButton}
            buttonColor={theme.colors.primary}>
            {actionText}
          </Button>
        )}
      </View>
    </Surface>
  </View>
);

// Higher-order component for protecting entire screens
export const withRoleProtection = (WrappedComponent, requiredRole, requiredPermission) => {
  return (props) => (
    <ProtectedRoute role={requiredRole} permission={requiredPermission}>
      <WrappedComponent {...props} />
    </ProtectedRoute>
  );
};

// Hook for checking permissions in components
export const usePermissions = () => {
  const { hasPermission, isAdmin, isUser, user } = useAuth();
  
  return {
    hasPermission,
    isAdmin,
    isUser,
    canViewReports: hasPermission('view_reports'),
    canViewFinancial: hasPermission('view_financial'),
    canManageEmployees: hasPermission('manage_employees'),
    canCreateProducts: hasPermission('create_products'),
    canEditProducts: hasPermission('edit_products'),
    canDeleteProducts: hasPermission('delete_products'),
    canCreateCustomers: hasPermission('create_customers'),
    canEditCustomers: hasPermission('edit_customers'),
    canDeleteCustomers: hasPermission('delete_customers'),
    canCreateOrders: hasPermission('create_orders'),
    canEditOrders: hasPermission('edit_orders'),
    canDeleteOrders: hasPermission('delete_orders'),
    userRole: user?.role,
    userName: user?.name,
  };
};

export default ProtectedRoute;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.lg,
  },
  accessDeniedCard: {
    borderRadius: BORDER_RADIUS.xl,
    padding: SPACING.xl,
    maxWidth: 400,
    width: '100%',
  },
  accessDeniedContent: {
    alignItems: 'center',
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  title: {
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: SPACING.md,
  },
  message: {
    textAlign: 'center',
    marginBottom: SPACING.xl,
    lineHeight: 24,
  },
  actionButton: {
    borderRadius: BORDER_RADIUS.lg,
    paddingHorizontal: SPACING.xl,
  },
});