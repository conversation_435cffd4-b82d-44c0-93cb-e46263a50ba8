/**
 * SearchResultsContent - Dynamic content for search results
 * Renders different layouts based on sheet state and search data
 */

import React, { useState, useCallback } from 'react';
import { View, StyleSheet, FlatList, TouchableOpacity } from 'react-native';
import { Text } from 'react-native-paper';
import { Chip } from 'react-native-paper';
import { Divider } from 'react-native-paper';
import { IconButton } from 'react-native-paper';
import { Surface } from 'react-native-paper';
import { Searchbar } from 'react-native-paper';
import { SegmentedButtons } from 'react-native-paper';
import LocalIcon from '../LocalIcon';
import { SPACING, BORDER_RADIUS } from '../../theme/designTokens';
import { SHEET_STATES } from '../DynamicBottomSheet';

const SearchResultsContent = ({ 
  state, 
  searchResults = [], 
  theme, 
  onContentChange 
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [sortBy, setSortBy] = useState('relevance');

  // Filter and sort results
  const filteredResults = searchResults
    .filter(item => {
      if (filterType === 'all') return true;
      return item.type === filterType;
    })
    .filter(item => {
      if (!searchQuery) return true;
      return item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
             item.description?.toLowerCase().includes(searchQuery.toLowerCase());
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'distance':
          return parseFloat(a.distance) - parseFloat(b.distance);
        case 'rating':
          return (b.rating || 0) - (a.rating || 0);
        case 'name':
          return a.name.localeCompare(b.name);
        default:
          return 0;
      }
    });

  // Get unique types for filter
  const availableTypes = [...new Set(searchResults.map(item => item.type))];

  // Render different content based on sheet state
  switch (state) {
    case SHEET_STATES.PEEK:
      return renderPeekContent(filteredResults, theme);
    case SHEET_STATES.HALF:
      return renderHalfContent(filteredResults, searchQuery, setSearchQuery, theme);
    case SHEET_STATES.FULL:
      return renderFullContent(
        filteredResults, 
        searchQuery, 
        setSearchQuery,
        filterType,
        setFilterType,
        sortBy,
        setSortBy,
        availableTypes,
        theme
      );
    default:
      return null;
  }
};

// Peek state - show count and top results
const renderPeekContent = (results, theme) => (
  <View style={styles.style2}>
    <View style={styles.style2}>
      <LocalIcon name="magnify" size={24} color={theme.colors.primary} />
      <Text variant="titleMedium" style={styles.style1}>
        {results.length} Results Found
      </Text>
    </View>
    
    {results.length > 0 && (
      <View style={styles.style2}>
        {results.slice(0, 2).map((item, index) => (
          <View key={item.id || index} style={styles.style2}>
            <View style={styles.style5}>
              <LocalIcon name={getTypeIcon(item.type)} size={16} color={getTypeColor(item.type)} />
            </View>
            <View style={styles.style2}>
              <Text variant="bodyMedium" style={styles.style1}]} numberOfLines={1}>
                {item.name}
              </Text>
              <Text variant="bodySmall" style={styles.style4}>
                {item.distance} • {item.type}
              </Text>
            </View>
            {item.rating && (
              <View style={styles.style2}>
                <LocalIcon name="star" size={12} color="#FFD700" />
                <Text variant="bodySmall" style={styles.style1}>
                  {item.rating}
                </Text>
              </View>
            )}
          </View>
        ))}
        
        {results.length > 2 && (
          <Text variant="bodySmall" style={styles.style8}>
            +{results.length - 2} more results
          </Text>
        )}
      </View>
    )}
  </View>
);

// Half state - searchable list
const renderHalfContent = (results, searchQuery, setSearchQuery, theme) => (
  <View style={styles.style2}>
    <View style={styles.style2}>
      <Text variant="headlineSmall" style={styles.style1}>
        Search Results
      </Text>
      <Text variant="bodyMedium" style={styles.style4}>
        {results.length} found
      </Text>
    </View>

    <Searchbar
      placeholder="Filter results..."
      onChangeText={setSearchQuery}
      value={searchQuery}
      style={styles.style7}}
      inputStyle={{ color: theme.colors.onSurface }}
      iconColor={theme.colors.onSurfaceVariant}
      placeholderTextColor={theme.colors.onSurfaceVariant}
    />

    <FlatList
      data={results}
      keyExtractor={(item, index) => item.id || index.toString()}
      renderItem={({ item }) => renderResultItem(item, theme, 'compact')}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={styles.resultsList}
      ItemSeparatorComponent={() => <View style={styles.style2} />}
    />
  </View>
);

// Full state - complete search interface
const renderFullContent = (
  results, 
  searchQuery, 
  setSearchQuery,
  filterType,
  setFilterType,
  sortBy,
  setSortBy,
  availableTypes,
  theme
) => (
  <View style={styles.style2}>
    {/* Header with search */}
    <View style={styles.style2}>
      <Text variant="headlineMedium" style={styles.style1}>
        Search Results
      </Text>
      
      <Searchbar
        placeholder="Search locations, services..."
        onChangeText={setSearchQuery}
        value={searchQuery}
        style={styles.style7}}
        inputStyle={{ color: theme.colors.onSurface }}
        iconColor={theme.colors.onSurfaceVariant}
        placeholderTextColor={theme.colors.onSurfaceVariant}
      />
    </View>

    {/* Filters and Sort */}
    <View style={styles.style2}>
      <Text variant="titleSmall" style={styles.style1}>
        Filter by type:
      </Text>
      <View style={styles.style2}>
        <Chip
          selected={filterType === 'all'}
          onPress={() => setFilterType('all')}
          style={styles.style2}
          showSelectedCheck={false}>
          All
        </Chip>
        {availableTypes.map(type => (
          <Chip
            key={type}
            selected={filterType === type}
            onPress={() => setFilterType(type)}
            style={styles.style2}
            showSelectedCheck={false}>
            {type}
          </Chip>
        ))}
      </View>

      <View style={styles.style2}>
        <Text variant="titleSmall" style={styles.style1}>
          Sort by:
        </Text>
        <SegmentedButtons
          value={sortBy}
          onValueChange={setSortBy}
          buttons={[
            { value: 'relevance', label: 'Relevance' },
            { value: 'distance', label: 'Distance' },
            { value: 'rating', label: 'Rating' },
          }
          style={styles.style2}
        />
      </View>
    </View>

    <Divider style={styles.style2} />

    {/* Results */}
    <View style={styles.style2}>
      <Text variant="titleMedium" style={styles.style1}>
        {results.length} Results
      </Text>
      {searchQuery && (
        <Text variant="bodyMedium" style={styles.style4}>
          for "{searchQuery}"
        </Text>
      )}
    </View>

    <FlatList
      data={results}
      keyExtractor={(item, index) => item.id || index.toString()}
      renderItem={({ item }) => renderResultItem(item, theme, 'detailed')}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={styles.fullResultsList}
      ItemSeparatorComponent={() => <View style={styles.style2} />}
    />
  </View>
);

// Result item renderer
const renderResultItem = (item, theme, variant = 'compact') => {
  const isDetailed = variant === 'detailed';
  
  return (
    <TouchableOpacity onPress={() => console.log('Selected:', item.name)}>
      <Surface style={styles.style6}]} elevation={1}>
        <View style={styles.style2}>
          <View style={styles.style5}>
            <LocalIcon name={getTypeIcon(item.type)} size={isDetailed ? 24 : 20} color={getTypeColor(item.type)} />
          </View>
          
          <View style={styles.style2}>
            <Text 
              variant={isDetailed ? "titleMedium" : "bodyLarge"} 
              style={styles.style1}]} numberOfLines={1}>
              {item.name}
            </Text>
            
            {isDetailed && item.description && (
              <Text 
                variant="bodyMedium" 
                style={styles.style4}]} numberOfLines={2}>
                {item.description}
              </Text>
            )}
            
            <View style={styles.style2}>
              <Text variant="bodySmall" style={styles.style4}>
                {item.distance}
              </Text>
              <Text variant="bodySmall" style={styles.style4}>
                •
              </Text>
              <Chip 
                mode="outlined" 
                compact 
                style={styles.style3}}
                textStyle={{ color: getTypeColor(item.type), fontSize: 11 }}
              >
                {item.type}
              </Chip>
            </View>
          </View>
          
          <View style={styles.style2}>
            {item.rating && (
              <View style={styles.style2}>
                <LocalIcon name="star" size={14} color="#FFD700" />
                <Text variant="bodySmall" style={styles.style1]>
                  {item.rating}
                </Text>
              </View>
            )}
            
            <IconButton
              icon="chevron-right"
              size={20}
              iconColor={theme.colors.onSurfaceVariant}
              onPress={() => console.log('View details:', item.name)}
            />
          </View>
        </View>
      </Surface>
    </TouchableOpacity>
  );
};

// Helper functions
const getTypeIcon = (type) => {
  const icons = {
    restaurant: 'silverware-fork-knife',
    hotel: 'bed',
    gas_station: 'gas-station',
    hospital: 'hospital-box',
    school: 'school',
    store: 'store',
    bank: 'bank',
    default: 'map-marker'
  };
  return icons[type] || icons.default;
};

const getTypeColor = (type) => {
  const colors = {
    restaurant: '#FF6B6B',
    hotel: '#4ECDC4',
    gas_station: '#45B7D1',
    hospital: '#96CEB4',
    school: '#FFEAA7',
    store: '#DDA0DD',
    bank: '#98D8C8',
    default: '#6C5CE7'
  };
  return colors[type] || colors.default;
};






export default SearchResultsContent;

const styles = StyleSheet.create({
  peekContainer: {
    paddingVertical: SPACING.sm,
  },
  peekHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  peekTitle: {
    marginLeft: SPACING.sm,
    fontWeight: '600',
  },
  peekResults: {
    gap: SPACING.sm,
  },
  peekResultItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  peekResultInfo: {
    flex: 1,
    marginLeft: SPACING.sm,
  },
  peekRating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  moreResults: {
    textAlign: 'center',
    marginTop: SPACING.sm,
    fontWeight: '500',
  },
  halfContainer: {
    flex: 1,
  },
  halfHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  halfTitle: {
    fontWeight: 'bold',
  },
  resultCount: {
    fontWeight: '500',
  },
  searchBar: {
    marginBottom: SPACING.md,
    elevation: 0,
  },
  resultsList: {
    paddingBottom: SPACING.lg,
  },
  fullContainer: {
    flex: 1,
  },
  fullHeader: {
    marginBottom: SPACING.lg,
  },
  fullTitle: {
    fontWeight: 'bold',
    marginBottom: SPACING.md,
  },
  filtersContainer: {
    marginBottom: SPACING.md,
  },
  filterLabel: {
    marginBottom: SPACING.sm,
    fontWeight: '500',
  },
  filterChips: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.sm,
    marginBottom: SPACING.md,
  },
  filterChip: {
    height: 32,
  },
  sortContainer: {
    marginTop: SPACING.sm,
  },
  segmentedButtons: {
    marginTop: SPACING.sm,
  },
  divider: {
    marginVertical: SPACING.md,
  },
  resultsHeader: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: SPACING.md,
  },
  resultsTitle: {
    fontWeight: '600',
  },
  searchInfo: {
    marginLeft: SPACING.sm,
  },
  fullResultsList: {
    paddingBottom: SPACING.xl,
  },
  resultItem: {
    borderRadius: BORDER_RADIUS.md,
    marginBottom: SPACING.xs,
  },
  resultContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.md,
  },
  resultIcon: {
    width: 40,
    height: 40,
    borderRadius: BORDER_RADIUS.md,
    justifyContent: 'center',
    alignItems: 'center',
  },
  resultInfo: {
    flex: 1,
    marginLeft: SPACING.md,
  },
  resultName: {
    fontWeight: '600',
    marginBottom: 2,
  },
  resultDescription: {
    marginBottom: SPACING.xs,
    lineHeight: 18,
  },
  resultMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  resultDistance: {
    fontSize: 12,
  },
  metaSeparator: {
    marginHorizontal: SPACING.xs,
  },
  typeChip: {
    height: 24,
  },
  resultActions: {
    alignItems: 'center',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  ratingText: {
    marginLeft: 2,
    fontSize: 12,
    fontWeight: '500',
  },
  separator: {
    height: SPACING.xs,
  },
  style1: {
    { color: theme.colors.onSurface,
  },
  style2: {
  },
  style3: {
    { borderColor: getTypeColor(item.type),
  },
  style4: {
    { color: theme.colors.onSurfaceVariant,
  },
  style5: {
    { backgroundColor: getTypeColor(item.type) + '20',
  },
  style6: {
    { backgroundColor: theme.colors.surface,
  },
  style7: {
    { backgroundColor: theme.colors.surfaceVariant,
  },
  style8: {
    { color: theme.colors.primary,
  },
});