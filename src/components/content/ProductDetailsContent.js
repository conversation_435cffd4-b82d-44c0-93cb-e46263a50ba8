/**
 * ProductDetailsContent - Dynamic content for product details
 * Renders different layouts based on sheet state and product data
 */

import React, { useState, useCallback } from 'react';
import { View, StyleSheet, Image, ScrollView   } from 'react-native';
import { Text   } from 'react-native-paper';
import { Button   } from 'react-native-paper';
import { Chip   } from 'react-native-paper';
import { Divider   } from 'react-native-paper';
import { IconButton   } from 'react-native-paper';
import { Surface   } from 'react-native-paper';
import { SegmentedButtons   } from 'react-native-paper';
import { SPACING, BORDER_RADIUS   } from '../../theme/designTokens';
import { SHEET_STATES   } from '../DynamicBottomSheet';

import LocalIcon from '../LocalIcon';

const ProductDetailsContent = ({ 
  state, 
  productData, 
  theme, 
  onContentChange 
}) => {
  const [selectedVariant, setSelectedVariant] = useState(0);
  const [quantity, setQuantity] = useState(1);

  if(!productData) {
    return (
      <View style={styles.emptyContainer}>
        <LocalIcon name="package-variant" size={48} color={theme.colors.onSurfaceVariant} />
        <Text variant="bodyLarge" style={[styles.emptyText, { color: theme.colors.onSurfaceVariant }]}>
          No product selected
        </Text>
      </View>
    );
  }

  switch(state) {
    case SHEET_STATES.PEEK:
      return renderPeekContent(productData, theme);
    case SHEET_STATES.HALF:
      return renderHalfContent(productData, selectedVariant, setSelectedVariant, theme);
    case SHEET_STATES.FULL:
      return renderFullContent(productData, selectedVariant, setSelectedVariant, quantity, setQuantity, theme);
    default:
      return null;
  }
};

const renderPeekContent = (product, theme) => (
  <View style={styles.peekContainer}>
    <View style={styles.peekHeader}>
      <Image source={{ uri: product.image }} style={styles.peekImage} />
      <View style={styles.peekInfo}>
        <Text variant="titleMedium" style={[styles.productName, { color: theme.colors.onSurface }]} numberOfLines={1}>
          {product.name}
        </Text>
        <Text variant="headlineSmall" style={[styles.price, { color: theme.colors.primary }]}>
          ৳{product.price}
        </Text>
      </View>
      <View style={styles.peekActions}>
        {product.rating && (
          <View style={styles.ratingContainer}>
            <LocalIcon name="star" size={16} color="#FFD700" />
            <Text variant="bodySmall" style={[styles.rating, { color: theme.colors.onSurface }]}>
              {product.rating}
            </Text>
          </View>
        )}
        <Chip
          mode="flat"
          compact
          style={[styles.stockChip, { backgroundColor: getStockColor(product.stock) + '20' }]}
          textStyle={{ color: getStockColor(product.stock) }}>
          {product.stock > 0 ? "In Stock" : "Out of Stock"}
        </Chip>
      </View>
    </View>
  </View>
);

const renderHalfContent = (product, selectedVariant, setSelectedVariant, theme) => (
  <View style={styles.halfContainer}>
    <View style={styles.halfHeader}>
      <Image source={{ uri: product.image }} style={styles.halfImage} />
      <View style={styles.halfInfo}>
        <Text variant="headlineSmall" style={[styles.productName, { color: theme.colors.onSurface }]}>
          {product.name}
        </Text>
        <Text variant="bodyMedium" style={[styles.category, { color: theme.colors.onSurfaceVariant }]}>
          {product.category}
        </Text>
        <Text variant="headlineMedium" style={[styles.price, { color: theme.colors.primary }]}>
          ৳{product.price}
        </Text>
      </View>
    </View>

    {product.variants && product.variants.length > 0 && (
      <View style={styles.variantsContainer}>
        <Text variant="titleSmall" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
          Variants:
        </Text>
        <SegmentedButtons
          value={selectedVariant.toString()}
          onValueChange={(value) => setSelectedVariant(parseInt(value))} buttons={product.variants.map((variant, index) => ({
            value: index.toString(), label: variant.name }))}
          style={styles.segmentedButtons}
        />
      </View>
    )}

    <View style={styles.quickStats}>
      <View style={styles.statItem}>
        <LocalIcon name="star" size={20} color="#FFD700" />
        <Text variant="bodyMedium" style={[styles.statText, { color: theme.colors.onSurface }]}>
          {product.rating || "N/A"}
        </Text>
      </View>
      <View style={styles.statItem}>
        <LocalIcon name="package-variant" size={20} color={theme.colors.primary} />
        <Text variant="bodyMedium" style={[styles.statText, { color: theme.colors.onSurface }]}>
          {product.stock} in stock
        </Text>
      </View>
      <View style={styles.statItem}>
        <LocalIcon name="truck-delivery" size={20} color={theme.colors.secondary} />
        <Text variant="bodyMedium" style={[styles.statText, { color: theme.colors.onSurface }]}>
          {product.deliveryTime || "2-3 days"}
        </Text>
      </View>
    </View>

    <View style={styles.quickActions}>
      <Button 
        mode="outlined" 
        icon="heart-outline"
        style={styles.actionButton}
        onPress={() => console.log('Add to wishlist')}>
        Wishlist
      </Button>
      <Button 
        mode="contained" 
        icon="cart-plus"
        style={[styles.actionButton, { flex: 2 }}}
        onPress={() => console.log('Add to cart')}
        disabled={product.stock === 0}>
        Add to Cart
      </Button>
    </View>
  </View>
);

const renderFullContent = (product, selectedVariant, setSelectedVariant, quantity, setQuantity, theme) => (
  <ScrollView style={styles.fullContainer} showsVerticalScrollIndicator={false}>
    {/* Product Images */}
    <View style={styles.imageContainer}>
      <Image source={{ uri: product.image }} style={styles.fullImage} />
      {product.images && product.images.length > 1 && (
        <ScrollView horizontal style={styles.thumbnailContainer} showsHorizontalScrollIndicator={false}>
          {product.images.map((image, index) => (
            <Image key={index} source={{ uri: image }} style={styles.thumbnail} />
          ))}
        </ScrollView>
      )}
    </View>

    {/* Product Info */}
    <View style={styles.productInfo}>
      <Text variant="headlineLarge" style={[styles.fullProductName, { color: theme.colors.onSurface }]}>
        {product.name}
      </Text>
      <Text variant="bodyLarge" style={[styles.category, { color: theme.colors.onSurfaceVariant }]}>
        {product.category}
      </Text>
      <Text variant="displaySmall" style={[styles.fullPrice, { color: theme.colors.primary }]}>
        ৳{product.price}
      </Text>
    </View>

    {/* Stats */}
    <View style={styles.statsGrid}>
      <Surface style={[styles.statCard, { backgroundColor: theme.colors.surfaceVariant ]} elevation={0}>
        <LocalIcon name="star" size={24} color="#FFD700" />
        <Text variant="titleMedium" style={[styles.statValue, { color: theme.colors.onSurface }]}>
          {product.rating || "N/A"}
        </Text>
        <Text variant="bodySmall" style={[styles.statLabel, { color: theme.colors.onSurfaceVariant }]}>
          Rating
        </Text>
      </Surface>
      
      <Surface style={[styles.statCard, { backgroundColor: theme.colors.surfaceVariant ]} elevation={0}>
        <LocalIcon name="package-variant" size={24} color={theme.colors.primary} />
        <Text variant="titleMedium" style={[styles.statValue, { color: theme.colors.onSurface }]}>
          {product.stock}
        </Text>
        <Text variant="bodySmall" style={[styles.statLabel, { color: theme.colors.onSurfaceVariant }]}>In Stock</Text>
      </Surface>
      
      <Surface style={[styles.statCard, { backgroundColor: theme.colors.surfaceVariant ]} elevation={0}>
        <LocalIcon name="truck-delivery" size={24} color={theme.colors.secondary} />
        <Text variant="titleMedium" style={[styles.statValue, { color: theme.colors.onSurface }]}>
          {product.deliveryTime || '2-3'}
        </Text>
        <Text variant="bodySmall" style={[styles.statLabel, { color: theme.colors.onSurfaceVariant }]}>
          Days
        </Text>
      </Surface>
    </View>

    <Divider style={styles.divider} />

    {/* Description */}
    <View style={styles.descriptionSection}>
      <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
        Description
      </Text>
      <Text variant="bodyMedium" style={[styles.description, { color: theme.colors.onSurface }]}>
        {product.description || 'No description available.'}
      </Text>
    </View>

    {/* Variants */}
    {product.variants && product.variants.length > 0 && (
      <View style={styles.variantsSection}>
        <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
          Variants
        </Text>
        <SegmentedButtons
          value={selectedVariant.toString()}
          onValueChange={(value) => setSelectedVariant(parseInt(value))} buttons={product.variants.map((variant, index) => ({
            value: index.toString(), label: variant.name }))}
          style={styles.segmentedButtons}
        />
      </View>
    )}

    {/* Quantity Selector */}
    <View style={styles.quantitySection}>
      <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
        Quantity
      </Text>
      <View style={styles.quantitySelector}>
        <IconButton
          icon="minus"
          mode="outlined"
          size={20}
          onPress={() => setQuantity(Math.max(1, quantity - 1))}
          disabled={quantity <= 1}
        />
        <Text variant="titleMedium" style={[styles.quantityText, { color: theme.colors.onSurface }]}>
          {quantity}
        </Text>
        <IconButton
          icon="plus"
          mode="outlined"
          size={20}
          onPress={() => setQuantity(Math.min(product.stock, quantity + 1))}
          disabled={quantity >= product.stock}
        />
      </View>
    </View>

    {/* Actions */}
    <View style={styles.fullActions}>
      <Button
        mode="outlined"
        icon="heart-outline"
        style={[styles.fullActionButton, { flex: 1 }}}
        onPress={() => console.log('Add to wishlist')}>
        Wishlist
      </Button>
      <Button
        mode="outlined"
        icon="share-variant"
        style={[styles.fullActionButton, { flex: 1 }}}
        onPress={() => console.log('Share product')}>
        Share
      </Button>
      <Button
        mode="contained"
        icon="cart-plus"
        style={[styles.fullActionButton, { flex: 2 }}}
        onPress={() => console.log('Add to cart', { quantity })}
        disabled={product.stock === 0}>
        Add to Cart
      </Button>
    </View>
  </ScrollView>
);

const getStockColor = (stock) => {
  if (stock === 0) return '#E17055';
  if (stock < 10) return '#FDCB6E';
  return '#00B894';
};

const styles = StyleSheet.create({
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: SPACING.xl},
  emptyText: {
    marginTop: SPACING.md,
    fontWeight: '600'},
  peekContainer: {
    paddingVertical: SPACING.sm},
  peekHeader: {
    flexDirection: 'row',
    alignItems: 'center'},
  peekImage: {
    width: 60,
    height: 60,
    borderRadius: BORDER_RADIUS.md,
    marginRight: SPACING.md},
  peekInfo: {
    flex: 1},
  peekActions: {
    alignItems: 'flex-end'},
  productName: {
    fontWeight: '600',
    marginBottom: SPACING.xs},
  price: {
    fontWeight: 'bold'},
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.xs},
  rating: {
    marginLeft: 4,
    fontWeight: '500'},
  stockChip: {
    height: 28},
  halfContainer: {
    paddingVertical: SPACING.sm},
  halfHeader: {
    flexDirection: 'row',
    marginBottom: SPACING.lg},
  halfImage: {
    width: 80,
    height: 80,
    borderRadius: BORDER_RADIUS.lg,
    marginRight: SPACING.md},
  halfInfo: {
    flex: 1,
    justifyContent: 'center'},
  category: {
    marginBottom: SPACING.xs},
  variantsContainer: {
    marginBottom: SPACING.lg},
  sectionTitle: {
    fontWeight: '600',
    marginBottom: SPACING.sm},
  segmentedButtons: {
    marginTop: SPACING.sm},
  quickStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: SPACING.lg},
  statItem: {
    alignItems: 'center'},
  statText: {
    marginTop: SPACING.xs,
    fontWeight: '500'},
  quickActions: {
    flexDirection: 'row',
    gap: SPACING.sm},
  actionButton: {
    flex: 1},
  fullContainer: {
    flex: 1},
  imageContainer: {
    marginBottom: SPACING.lg},
  fullImage: {
    width: '100%',
    height: 250,
    borderRadius: BORDER_RADIUS.lg,
    marginBottom: SPACING.md},
  thumbnailContainer: {
    flexDirection: 'row'},
  thumbnail: {
    width: 60,
    height: 60,
    borderRadius: BORDER_RADIUS.md,
    marginRight: SPACING.sm},
  productInfo: {
    marginBottom: SPACING.lg},
  fullProductName: {
    fontWeight: 'bold',
    marginBottom: SPACING.xs},
  fullPrice: {
    fontWeight: 'bold',
    marginTop: SPACING.sm},
  statsGrid: {
    flexDirection: 'row',
    gap: SPACING.sm,
    marginBottom: SPACING.lg},
  statCard: {
    flex: 1,
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    alignItems: 'center'},
  statValue: {
    fontWeight: 'bold',
    marginTop: SPACING.xs},
  statLabel: {
    marginTop: 2},
  divider: {
    marginVertical: SPACING.lg},
  descriptionSection: {
    marginBottom: SPACING.lg},
  description: {
    lineHeight: 22},
  variantsSection: {
    marginBottom: SPACING.lg},
  quantitySection: {
    marginBottom: SPACING.xl},
  quantitySelector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: SPACING.sm},
  quantityText: {
    marginHorizontal: SPACING.lg,
    fontWeight: 'bold',
    minWidth: 40,
    textAlign: 'center'},
  fullActions: {
    flexDirection: 'row',
    gap: SPACING.sm,
    marginBottom: SPACING.xl
  },
  fullActionButton: {
    borderRadius: BORDER_RADIUS.md
  }
});

export default ProductDetailsContent;