/**
 * MapFeatureContent - Dynamic content for map features
 * Renders different content based on sheet state and selected map feature
 */

import React, { useCallback } from 'react';
import { View, StyleSheet, Image   } from 'react-native';
import { Text   } from 'react-native-paper';
import { Button   } from 'react-native-paper';
import { Chip   } from 'react-native-paper';
import { Divider   } from 'react-native-paper';
import { IconButton   } from 'react-native-paper';
import { Surface   } from 'react-native-paper';
import LocalIcon from '../LocalIcon';
import { SPACING, BORDER_RADIUS   } from '../../theme/designTokens';
import { SHEET_STATES   } from '../DynamicBottomSheet';

const MapFeatureContent = ({ 
  state, 
  activeFeature, 
  theme, 
  onContentChange 
}) => {
  if(!activeFeature) {
    return (
      <View style={styles.style3}>
        <LocalIcon name="map-marker-off" size={48} color={theme.colors.onSurfaceVariant} />
        <Text variant="bodyLarge" style={styles.style5}>
          No map feature selected
        </Text>
        <Text variant="bodyMedium" style={styles.style5}>
          Tap on a location to see details
        </Text>
      </View>
    );
  }

  // Render different content based on sheet state
  switch(state) {
    case SHEET_STATES.PEEK:
      return renderPeekContent(activeFeature, theme);
    case SHEET_STATES.HALF:
      return renderHalfContent(activeFeature, theme);
    case SHEET_STATES.FULL:
      return renderFullContent(activeFeature, theme);
    default:
      return null;
  }
};

// Peek state - minimal info
const renderPeekContent = (feature, theme) => (
  <View style={styles.style3}>
    <View style={styles.style3}>
      <View style={styles.style8}>
        <LocalIcon name={getFeatureIcon(feature.type)} size={24} color={getFeatureColor(feature.type)} />
      </View>
      <View style={styles.style3}>
        <Text variant="titleMedium" style={styles.style4}>
          {feature.name}
        </Text>
        <Text variant="bodySmall" style={styles.style5}>
          {feature.address || feature.description}
        </Text>
      </View>
      <Chip
        mode="outlined"
        compact
        style={styles.style9}
        textStyle={{ color: getFeatureColor(feature.type), fontSize: 12 }>
        {feature.type}
      </Chip>
    </View>
    
    {feature.rating && (
      <View style={styles.style3}>
        <LocalIcon name="star" size={16} color="#FFD700" />
        <Text variant="bodySmall" style={styles.style4}>
          {feature.rating}
        </Text>
        {feature.distance && (
          <>
            <Text variant="bodySmall" style={styles.style5}>
              •
            </Text>
            <Text variant="bodySmall" style={styles.style5}>
              {feature.distance}
            </Text>
          </>
        )}
      </View>
    )}
  </View>
);

// Half state - more details
const renderHalfContent = (feature, theme) => (
  <View style={styles.style3}>
    {/* Header */}
    <View style={styles.style3}>
      <View style={styles.style3}>
        <View style={styles.style8}>
          <LocalIcon name={getFeatureIcon(feature.type)} size={32} color={getFeatureColor(feature.type)} />
        </View>
        <View style={styles.style3}>
          <Text variant="headlineSmall" style={styles.style4}>
            {feature.name}
          </Text>
          <Text variant="bodyMedium" style={styles.style5}>
            {feature.address}
          </Text>
        </View>
      </View>
      <IconButton
        icon="directions"
        mode="contained"
        size={20}
        iconColor={theme.colors.onPrimary}
        containerColor={theme.colors.primary}
      />
    </View>

    {/* Stats */}
    {(feature.rating || feature.distance || feature.status) && (
      <View style={styles.style3}>
        {feature.rating && (
          <View style={styles.style3}>
            <LocalIcon name="star" size={18} color="#FFD700" />
            <Text variant="bodyMedium" style={styles.style4}>
              {feature.rating}
            </Text>
          </View>
        )}
        {feature.distance && (
          <View style={styles.style3}>
            <LocalIcon name="map-marker-distance" size={18} color={theme.colors.primary} />
            <Text variant="bodyMedium" style={styles.style4}>
              {feature.distance}
            </Text>
          </View>
        )}
        {feature.status && (
          <Chip 
            mode="flat"
            compact
            style={styles.style7}
            textStyle={{ color: getStatusColor(feature.status) }>
            {feature.status}
          </Chip>
        )}
      </View>
    )}

    <Divider style={styles.style3} />

    {/* Description */}
    {feature.description && (
      <View style={styles.style3}>
        <Text variant="bodyMedium" style={styles.style4}>
          {feature.description}
        </Text>
      </View>
    )}

    {/* Quick Actions */}
    <View style={styles.style3}>
      <Button 
        mode="outlined" 
        icon="phone"
        style={styles.style3}
        onPress={() => console.log('Call feature')}>
        Call
      </Button>
      <Button 
        mode="outlined" 
        icon="web"
        style={styles.style3}
        onPress={() => console.log('Visit website')}>
        Website
      </Button>
      <Button 
        mode="contained" 
        icon="directions"
        style={styles.style3}
        onPress={() => console.log('Get directions')}>
        Directions
      </Button>
    </View>
  </View>
);

// Full state - complete details
const renderFullContent = (feature, theme) => (
  <View style={styles.style3}>
    {/* Hero Image */}
    {feature.image && (
      <Image source={{ uri: feature.image } style={styles.style3} />
    )}

    {/* Full Header */}
    <View style={styles.style3}>
      <Text variant="headlineMedium" style={styles.style4}>
        {feature.name}
      </Text>
      <Text variant="bodyLarge" style={styles.style5}>
        {feature.address}
      </Text>

      {/* Full Stats */}
      <View style={styles.style3}>
        {feature.rating && (
          <Surface style={styles.style6} elevation={0}>
            <LocalIcon name="star" size={20} color="#FFD700" />
            <Text variant="titleMedium" style={styles.style4}>
              {feature.rating}
            </Text>
            <Text variant="bodySmall" style={styles.style5}>
              Rating
            </Text>
          </Surface>
        )}
        
        {feature.distance && (
          <Surface style={styles.style6} elevation={0}>
            <LocalIcon name="map-marker-distance" size={20} color={theme.colors.primary} />
            <Text variant="titleMedium" style={styles.style4}>
              {feature.distance}
            </Text>
            <Text variant="bodySmall" style={styles.style5}>
              Distance
            </Text>
          </Surface>
        )}

        {feature.estimatedTime && (
          <Surface style={styles.style6} elevation={0}>
            <LocalIcon name="clock-outline" size={20} color={theme.colors.secondary} />
            <Text variant="titleMedium" style={styles.style4}>
              {feature.estimatedTime}
            </Text>
            <Text variant="bodySmall" style={styles.style5}>
              ETA
            </Text>
          </Surface>
        )}
      </View>
    </View>

    <Divider style={styles.style3} />

    {/* Detailed Information */}
    <View style={styles.style3}>
      <Text variant="titleMedium" style={styles.style4}>
        Details
      </Text>

      {feature.description && (
        <Text variant="bodyMedium" style={styles.style4}>
          {feature.description}
        </Text>
      )}

      {feature.hours && (
        <View style={styles.style3}>
          <LocalIcon name="clock-outline" size={20} color={theme.colors.onSurfaceVariant} />
          <Text variant="bodyMedium" style={styles.style4}>
            {feature.hours}
          </Text>
        </View>
      )}
      
      {feature.phone && (
        <View style={styles.style3}>
          <LocalIcon name="phone" size={20} color={theme.colors.onSurfaceVariant} />
          <Text variant="bodyMedium" style={styles.style4}>
            {feature.phone}
          </Text>
        </View>
      )}

      {feature.website && (
        <View style={styles.style3}>
          <LocalIcon name="web" size={20} color={theme.colors.onSurfaceVariant} />
          <Text variant="bodyMedium" style={styles.style4}>
            {feature.website}
          </Text>
        </View>
      )}
    </View>

    {/* Action Buttons */}
    <View style={styles.style3}>
      <Button 
        mode="outlined" 
        icon="phone"
        style={styles.style2}
        onPress={() => console.log('Call feature')}>
        Call
      </Button>
      <Button
        mode="outlined"
        icon="web"
        style={styles.style2}
        onPress={() => console.log('Visit website')}>
        Website
      </Button>
      <Button
        mode="contained"
        icon="directions"
        style={styles.style1}
        onPress={() => console.log('Get directions')}>
        Get Directions
      </Button>
    </View>
  </View>
);

// Helper functions
const getFeatureIcon = (type) => {
  const icons = {
    restaurant: 'silverware-fork-knife',
    hotel: 'bed',
    gas_station: 'gas-station',
    hospital: 'hospital-box',
    school: 'school',
    store: 'store',
    bank: 'bank',
    default: 'map-marker'
  };
  return icons[type] || icons.default;
};

const getFeatureColor = (type) => {
  const colors = {
    restaurant: '#FF6B6B',
    hotel: '#4ECDC4',
    gas_station: '#45B7D1',
    hospital: '#96CEB4',
    school: '#FFEAA7',
    store: '#DDA0DD',
    bank: '#98D8C8',
    default: '#6C5CE7'
  };
  return colors[type] || colors.default;
};

const getStatusColor = (status) => {
  const colors = {
    open: '#00B894',
    closed: '#E17055',
    busy: '#FDCB6E',
    default: '#74B9FF'
  };
  return colors[status] || colors.default;
};






export default React.memo(MapFeatureContent);

const styles = StyleSheet.create({
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: SPACING.xl,
  },
  emptyText: {
    marginTop: SPACING.md,
    fontWeight: '600',
  },
  emptySubtext: {
    marginTop: SPACING.xs,
    textAlign: 'center',
  },
  peekContainer: {
    paddingVertical: SPACING.sm,
  },
  peekHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  featureIcon: {
    width: 40,
    height: 40,
    borderRadius: BORDER_RADIUS.md,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.sm,
  },
  peekInfo: {
    flex: 1,
  },
  featureTitle: {
    fontWeight: '600',
    marginBottom: 2,
  },
  featureSubtitle: {
    fontSize: 13,
  },
  typeChip: {
    height: 28,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: SPACING.xs,
  },
  rating: {
    marginLeft: 4,
    fontWeight: '500',
  },
  separator: {
    marginHorizontal: SPACING.xs,
  },
  distance: {
    fontSize: 13,
  },
  halfContainer: {
    paddingVertical: SPACING.sm,
  },
  halfHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: SPACING.md,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  featureIconLarge: {
    width: 48,
    height: 48,
    borderRadius: BORDER_RADIUS.lg,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  headerInfo: {
    flex: 1,
  },
  statsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: SPACING.lg,
  },
  statText: {
    marginLeft: 4,
    fontWeight: '500',
  },
  statusChip: {
    marginLeft: 'auto',
  },
  divider: {
    marginVertical: SPACING.md,
  },
  descriptionContainer: {
    marginBottom: SPACING.md,
  },
  description: {
    lineHeight: 20,
  },
  quickActions: {
    flexDirection: 'row',
    gap: SPACING.sm,
  },
  actionButton: {
    flex: 1,
  },
  fullContainer: {
    paddingBottom: SPACING.lg,
  },
  heroImage: {
    width: '100%',
    height: 200,
    borderRadius: BORDER_RADIUS.lg,
    marginBottom: SPACING.lg,
  },
  fullHeader: {
    marginBottom: SPACING.lg,
  },
  fullTitle: {
    fontWeight: 'bold',
    marginBottom: SPACING.xs,
  },
  fullSubtitle: {
    marginBottom: SPACING.md,
  },
  fullStatsContainer: {
    flexDirection: 'row',
    gap: SPACING.sm,
  },
  statCard: {
    flex: 1,
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    alignItems: 'center',
  },
  statValue: {
    fontWeight: 'bold',
    marginTop: SPACING.xs,
  },
  statLabel: {
    marginTop: 2,
  },
  detailsSection: {
    marginBottom: SPACING.lg,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: SPACING.md,
  },
  fullDescription: {
    lineHeight: 22,
    marginBottom: SPACING.md,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  infoText: {
    marginLeft: SPACING.sm,
    flex: 1,
  },
  fullActions: {
    flexDirection: 'row',
    gap: SPACING.sm,
  },
  fullActionButton: {
    borderRadius: BORDER_RADIUS.md,
  },
  style1: {
    flex: 2,
  },
  style2: {
    flex: 1,
  },
  style3: {
  },
  style4: {
    color: '#000000',
  },
  style5: {
    color: '#666666',
  },
  style6: {
    backgroundColor: '#f5f5f5',
  },
  style7: {
    backgroundColor: '#e3f2fd',
  },
  style8: {
    backgroundColor: '#fff3e0',
  },
  style9: {
    borderColor: '#2196f3',
  },
});