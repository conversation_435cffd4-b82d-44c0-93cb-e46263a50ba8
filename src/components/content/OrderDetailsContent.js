/**
 * OrderDetailsContent - Dynamic content renderer for order details
 * Adapts content based on sheet state and provides rich order information
 */

import React, { useState, useCallback } from 'react';
import { View, StyleSheet, Image, TouchableOpacity, Alert, ScrollView   } from 'react-native';
import { Text   } from 'react-native-paper';
import { Button   } from 'react-native-paper';
import { Chip   } from 'react-native-paper';
import { Divider   } from 'react-native-paper';
import { Surface   } from 'react-native-paper';
import { Modal   } from 'react-native-paper';
import { Portal   } from 'react-native-paper';
import LocalIcon from '../LocalIcon';
import { SPACING, BORDER_RADIUS   } from '../../theme/designTokens';
import UnifiedStatusPicker from '../UnifiedStatusPicker';
import ImagePicker from '../ImagePicker';

// Define ORDER_SHEET_STATES locally to avoid circular dependency
const ORDER_SHEET_STATES = {
  PEEK: 'peek',
  HALF: 'half',
  FULL: 'full',
  CLOSED: 'closed',
  DASHBOARD: 'dashboard'
};

const OrderDetailsContent = ({
  state,
  orderData,
  theme,
  onContentChange,
  onEdit,
  onDelete,
  onUpdateStatus,
  onPrint,
  onShare,
  onViewInvoice,
  onImageUpdate}) => {
  const [selectedStatus, setSelectedStatus] = useState(orderData?.status || 'Pending');
  const [showStatusPicker, setShowStatusPicker] = useState(false);

  // Available order statuses
  const orderStatuses = [
    'Order Placed',
    'Measurements Taken',
    'Cutting Started',
    'Stitching In Progress',
    'First Fitting',
    'Adjustments',
    'Final Fitting',
    'Ready for Delivery',
    'Delivered',
    'Completed',
    'Cancelled',
    'On Hold'
  ];
  // Helper functions for order status
  const getStatusColor = (status) => {
    const colors = {
      'Pending': theme.colors.warning || '#FF9800',
      'In Progress': theme.colors.primary,
      'Completed': theme.colors.success || '#4CAF50',
      'Cancelled': theme.colors.error,
      'On Hold': theme.colors.outline};
    return colors[status] || theme.colors.primary;
  };

  const getStatusIcon = (status) => {
    const icons = {
      'Pending': 'clock-outline',
      'In Progress': 'progress-clock',
      'Completed': 'check-circle',
      'Cancelled': 'close-circle',
      'On Hold': 'pause-circle',
      'Fitting': 'account-tie',
      'Ready': 'package-variant',
      'Delivered': 'truck-delivery'};
    return icons[status] || 'information';
  };

  const getPaymentStatusColor = (status) => {
    const colors = {
      'paid': theme.colors.success || '#4CAF50',
      'partial': theme.colors.warning || '#FF9800',
      'unpaid': theme.colors.error};
    return colors[status] || theme.colors.outline;
  };

  const getOrderTypeIcon = (type) => {
    const icons = {
      'new_stitch': 'needle',
      'alteration': 'pencil-ruler',
      'repair': 'wrench',
      'custom_design': 'palette',
      'tailoring': 'scissors-cutting'};
    return icons[type] || 'package-variant';
  };

  const getDeliveryStatusColor = (status) => {
    const colors = {
      'pending': theme.colors.warning || '#FF9800',
      'ready': theme.colors.success || '#4CAF50',
      'delivered': theme.colors.primary};
    return colors[status] || theme.colors.outline;
  };

  const handleStatusUpdate = useCallback((newStatus) => {
    setSelectedStatus(newStatus);
    setShowStatusPicker(false);

    // Call the status update callback with the order and new status
    if(onUpdateStatus && orderData) {
      onUpdateStatus(orderData.id, newStatus);
    }

  }, [orderData, onUpdateStatus]);

  const handleImageUpdate = useCallback((imageUri) => {
    if(onImageUpdate && orderData) {
      onImageUpdate(orderData.id, imageUri);

    }
  }, [orderData, onImageUpdate]);

  const handleDelete = useCallback(() => {
    Alert.alert(
      'Confirm Delete',
      'Are you sure you want to delete this order?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Delete', style: 'destructive', onPress: () => onDelete?.(orderData) }
      ]
    );
  }, [orderData, onDelete]);

  // Always render full content - no dynamic rendering based on snap points
  const renderContent = () => {
    if(!orderData) {
      return (
        <View style={styles.emptyContainer}>
          <LocalIcon name="package-variant" size={48} color={theme.colors.outline} />
          <Text variant="bodyLarge" style={styles.emptyText}>
            No order selected
          </Text>
        </View>
      );
    }

    // Always show full content regardless of snap point
    return renderFullContent();
  };

  // Peek state - minimal info
  const renderPeekContent = () => (
    <View style={styles.peekContainer}>
      <View style={styles.headerRow}>
        <View style={styles.iconContainer}>
          <LocalIcon name={getStatusIcon(selectedStatus)} size={24} color={getStatusColor(selectedStatus)} />
        </View>
        <View style={styles.titleContainer}>
          <Text variant="headlineSmall" style={styles.title}>
            Order #{orderData.id}
          </Text>
          <Text variant="bodyMedium" style={styles.subtitle}>
            {orderData.customerName} • {orderData.date}
          </Text>
        </View>
        <Chip
          mode="flat"
          style={styles.statusChip}
          textStyle={{ color: getStatusColor(selectedStatus), fontWeight: '600' }>
          {selectedStatus}
        </Chip>
      </View>
    </View>
  );

  // Half state - detailed info
  const renderHalfContent = () => (
    <View style={styles.halfContainer}>
      {renderPeekContent()}

      <Divider style={styles.divider} />

      {/* Customer Info */}
      <Surface style={styles.section}>
        <View style={styles.sectionHeader}>
          <LocalIcon name="account" size={20} color={theme.colors.primary} />
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Customer Details
          </Text>
        </View>
        <View style={styles.customerInfo}>
          <Text variant="bodyLarge" style={styles.customerName}>
            {orderData.customerName}
          </Text>
          <Text variant="bodyMedium" style={styles.customerDetails}>
            {orderData.phone || orderData.customerPhone}
          </Text>
          <Text variant="bodyMedium" style={styles.customerDetails}>
            {orderData.email || orderData.customerEmail}
          </Text>
        </View>
      </Surface>

      {/* Order Summary */}
      <Surface style={styles.section}>
        <View style={styles.sectionHeader}>
          <LocalIcon name="package-variant" size={20} color={theme.colors.primary} />
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Order Summary
          </Text>
        </View>
        <View style={styles.orderMeta}>
          <View style={styles.metaRow}>
            <Text variant="bodyMedium" style={styles.metaLabel}>Total Amount:</Text>
            <Text variant="bodyLarge" style={styles.metaValue}>
              ৳{(orderData.total || orderData.totalAmount || 0).toFixed(2)}
            </Text>
          </View>
          <View style={styles.metaRow}>
            <Text variant="bodyMedium" style={styles.metaLabel}>Due Date:</Text>
            <Text variant="bodyMedium" style={styles.metaText}>
              {orderData.dueDate || 'Not set'}
            </Text>
          </View>
          {orderData.urgentOrder && (
            <View style={styles.metaRow}>
              <Text variant="bodyMedium" style={styles.metaLabel}>Priority:</Text>
              <Chip
                mode="flat"
                style={styles.urgentChip}
                textStyle={{ color: theme.colors.error, fontSize: 12, fontWeight: '600' }
                icon="alert"
              >
                URGENT
              </Chip>
            </View>
          )}
        </View>
      </Surface>

      {/* Payment Status */}
      {(orderData.paymentStatus || orderData.paidAmount !== undefined) && (
        <Surface style={styles.section}>
          <View style={styles.sectionHeader}>
            <LocalIcon name="credit-card" size={20} color={theme.colors.primary} />
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Payment Status
            </Text>
          </View>
          <View style={styles.orderMeta}>
            <View style={styles.metaRow}>
              <Text variant="bodyMedium" style={styles.metaLabel}>Status:</Text>
              <Chip
                mode="flat"
                style={styles.paymentChip}
                textStyle={{ color: getPaymentStatusColor(orderData.paymentStatus), fontSize: 12, fontWeight: '600' }>
                {(orderData.paymentStatus || 'unpaid').toUpperCase()}
              </Chip>
            </View>
            {orderData.paidAmount !== undefined && (
              <View style={styles.metaRow}>
                <Text variant="bodyMedium" style={styles.metaLabel}>Paid:</Text>
                <Text variant="bodyMedium" style={styles.paidAmount}>
                  ৳{(orderData.paidAmount || 0).toFixed(2)}
                </Text>
              </View>
            )}
            {orderData.balanceAmount !== undefined && orderData.balanceAmount > 0 && (
              <View style={styles.metaRow}>
                <Text variant="bodyMedium" style={styles.metaLabel}>Balance:</Text>
                <Text variant="bodyMedium" style={styles.balanceAmount}>
                  ৳{(orderData.balanceAmount || 0).toFixed(2)}
                </Text>
              </View>
            )}
          </View>
        </Surface>
      )}

      {/* Status Update */}
      <Surface style={styles.section}>
        <View style={styles.sectionHeader}>
          <LocalIcon name="cog" size={20} color={theme.colors.primary} />
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Update Status
          </Text>
        </View>
        <TouchableOpacity
          style={styles.statusButton}
          onPress={() => setShowStatusPicker(true)}>
          <LocalIcon name={getStatusIcon(selectedStatus)} size={20} color={getStatusColor(selectedStatus)} />
          <Text style={styles.statusButtonText}>
            {selectedStatus}
          </Text>
          <LocalIcon name="chevron-down" size={20} color={getStatusColor(selectedStatus)} style={styles.statusButtonIcon} />
        </TouchableOpacity>
      </Surface>

      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        <Button
          mode="outlined"
          icon="pencil"
          onPress={() => {
            onEdit?.(orderData);
            // Close the bottom sheet when edit is pressed
            // This will be handled by the parent component
          }
          style={styles.actionButton}>
          Edit
        </Button>
        <Button
          mode="outlined"
          icon="delete"
          onPress={handleDelete}
          style={styles.actionButton}
          textColor={theme.colors.error}>
          Delete
        </Button>
        <Button
          mode="contained"
          icon="file-document"
          onPress={() => onViewInvoice?.(orderData)}
          style={styles.actionButton}>
          Invoice
        </Button>
      </View>
    </View>
  );

  // Dashboard state - compact view for dashboard bottom sheet
  const renderDashboardContent = () => (
    <View style={styles.dashboardContainer}>
      {/* Header with Order ID and Status */}
      <View style={styles.dashboardHeader}>
        <View style={styles.dashboardTitleRow}>
          <Text variant="headlineMedium" style={styles.dashboardTitle}>
            Order #{orderData.id}
          </Text>
          <Chip
            mode="flat"
            style={styles.dashboardStatusChip}
            textStyle={{ color: getStatusColor(selectedStatus), fontWeight: '600' }>
            {selectedStatus}
          </Chip>
        </View>
        <Text variant="bodyMedium" style={styles.dashboardSubtitle}>
          {orderData.customerName} • {orderData.date}
        </Text>
      </View>

      <Divider style={styles.divider} />

      {/* Key Information Grid */}
      <View style={styles.dashboardGrid}>
        <View style={styles.dashboardGridItem}>
          <Text variant="bodySmall" style={styles.gridLabel}>Total Amount</Text>
          <Text variant="titleLarge" style={styles.gridValue}>
            ৳{(orderData.total || orderData.totalAmount || 0).toFixed(2)}
          </Text>
        </View>

        <View style={styles.dashboardGridItem}>
          <Text variant="bodySmall" style={styles.gridLabel}>Due Date</Text>
          <Text variant="bodyMedium" style={styles.gridText}>
            {orderData.dueDate || 'Not set'}
          </Text>
        </View>

        <View style={styles.dashboardGridItem}>
          <Text variant="bodySmall" style={styles.gridLabel}>Items</Text>
          <Text variant="bodyMedium" style={styles.gridText}>
            {(orderData.items && orderData.items.length) || 0} item{((orderData.items && orderData.items.length) || 0) !== 1 ? 's' : ''}
          </Text>
        </View>

        <View style={styles.dashboardGridItem}>
          <Text variant="bodySmall" style={styles.gridLabel}>Phone</Text>
          <Text variant="bodyMedium" style={styles.gridText}>
            {orderData.phone || orderData.customerPhone || 'No phone'}
          </Text>
        </View>
      </View>

      {/* Payment Status (if available) */}
      {(orderData.paymentStatus || orderData.paidAmount !== undefined) && (
        <>
          <Divider style={styles.divider} />
          <View style={styles.dashboardPayment}>
            <Text variant="titleSmall" style={styles.paymentTitle}>
              Payment Status
            </Text>
            <View style={styles.dashboardPaymentRow}>
              <Chip
                mode="flat"
                style={styles.paymentChip}
                textStyle={{ color: getPaymentStatusColor(orderData.paymentStatus), fontSize: 12, fontWeight: '600' }>
                {(orderData.paymentStatus || 'unpaid').toUpperCase()}
              </Chip>
              {orderData.paidAmount !== undefined && (
                <Text variant="bodyMedium" style={styles.paidAmount}>
                  Paid: ৳{(orderData.paidAmount || 0).toFixed(2)}
                </Text>
              )}
            </View>
          </View>
        </>
      )}

      {/* Quick Actions */}
      <View style={styles.dashboardActions}>
        <Button
          mode="outlined"
          icon="pencil"
          onPress={() => onEdit?.(orderData)}
          style={styles.dashboardActionButton}
          compact
        >
          Edit
        </Button>
        <Button
          mode="outlined"
          icon="delete"
          onPress={handleDelete}
          style={styles.dashboardActionButton}
          textColor={theme.colors.error}
          compact
        >
          Delete
        </Button>
        <Button
          mode="contained"
          icon="file-document"
          onPress={() => onViewInvoice?.(orderData)}
          style={styles.dashboardActionButton}
          compact
        >
          Invoice
        </Button>
      </View>
    </View>
  );

  // Full state - complete details
  const renderFullContent = () => (
    <View style={styles.fullContainer}>
      {renderHalfContent()}

      <Divider style={styles.divider} />

      {/* Order Type & Tags */}
      {(orderData.orderType || (orderData.tags && orderData.tags.length > 0)) && (
        <Surface style={styles.section}>
          <View style={styles.sectionHeader}>
            <LocalIcon name="tag" size={20} color={theme.colors.primary} />
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Order Type & Tags
            </Text>
          </View>
          <View style={styles.tagsContainer}>
            {orderData.orderType && (
              <Chip
                mode="flat"
                style={styles.typeChip}
                textStyle={{ color: theme.colors.primary, fontSize: 12, fontWeight: '600' }
                icon={getOrderTypeIcon(orderData.orderType)}>
                {orderData.orderType.replace('_', ' ').toUpperCase()}
              </Chip>
            )}
            {orderData.tags && orderData.tags.map((tag, index) => (
              <Chip
                key={index}
                mode="flat"
                style={styles.tagChip}
                textStyle={{ color: tag.color || theme.colors.secondary, fontSize: 12, fontWeight: '600' }>
                {tag.name}
              </Chip>
            ))}
          </View>
        </Surface>
      )}

      {/* Delivery & Fitting Information */}
      {(orderData.deliveryDate || orderData.fittingScheduled || orderData.deliveryStatus) && (
        <Surface style={styles.section}>
          <View style={styles.sectionHeader}>
            <LocalIcon name="calendar-clock" size={20} color={theme.colors.primary} />
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Delivery & Fitting
            </Text>
          </View>
          <View style={styles.orderMeta}>
            {orderData.deliveryDate && (
              <View style={styles.metaRow}>
                <Text variant="bodyMedium" style={styles.metaLabel}>Delivery Date:</Text>
                <Text variant="bodyMedium" style={styles.metaValue}>
                  {orderData.deliveryDate}
                </Text>
              </View>
            )}
            {orderData.deliveryStatus && (
              <View style={styles.metaRow}>
                <Text variant="bodyMedium" style={styles.metaLabel}>Delivery Status:</Text>
                <Chip
                  mode="flat"
                  style={styles.deliveryChip}
                  textStyle={{ color: getDeliveryStatusColor(orderData.deliveryStatus), fontSize: 12, fontWeight: '600' }>
                  {orderData.deliveryStatus.toUpperCase()}
                </Chip>
              </View>
            )}
            {orderData.fittingScheduled && (
              <View style={styles.metaRow}>
                <Text variant="bodyMedium" style={styles.metaLabel}>Fitting:</Text>
                <View style={styles.fittingInfo}>
                  <LocalIcon name="account-tie" size={16} color={theme.colors.tertiary} />
                  <Text variant="bodyMedium" style={styles.fittingText}>
                    {orderData.fittingDate ? `Scheduled for ${orderData.fittingDate}` : 'Scheduled'}
                  </Text>
                </View>
              </View>
            )}
          </View>
        </Surface>
      )}

      {/* Order Items */}
      {orderData.items && orderData.items.length > 0 && (
        <Surface style={styles.section}>
          <View style={styles.sectionHeader}>
            <LocalIcon name="format-list-bulleted" size={20} color={theme.colors.primary} />
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Order Items ({orderData.items.length})
            </Text>
          </View>
          {orderData.items.map((item, index) => (
            <View key={index} style={styles.orderItem}>
              <View style={styles.itemInfo}>
                <Text variant="bodyLarge" style={styles.itemName}>
                  {item.productName || item.name}
                </Text>
                <Text variant="bodyMedium" style={styles.itemDetails}>
                  Qty: {item.quantity} • ৳{(item.price || 0).toFixed(2)}
                </Text>
              </View>
              <Text variant="bodyLarge" style={styles.itemTotal}>
                ৳{((item.total || (item.quantity * item.price)) || 0).toFixed(2)}
              </Text>
            </View>
          ))}

          {/* Order Totals */}
          <Divider style={styles.divider} />
          <View style={styles.orderTotals}>
            {orderData.subtotal !== undefined && (
              <View style={styles.metaRow}>
                <Text variant="bodyMedium" style={styles.metaLabel}>Subtotal:</Text>
                <Text variant="bodyMedium" style={styles.metaText}>
                  ৳{(orderData.subtotal || 0).toFixed(2)}
                </Text>
              </View>
            )}
            {orderData.discount !== undefined && orderData.discount > 0 && (
              <View style={styles.metaRow}>
                <Text variant="bodyMedium" style={styles.metaLabel}>Discount:</Text>
                <Text variant="bodyMedium" style={styles.discountText}>
                  -৳{(orderData.discount || 0).toFixed(2)}
                </Text>
              </View>
            )}
            {orderData.tax !== undefined && (
              <View style={styles.metaRow}>
                <Text variant="bodyMedium" style={styles.metaLabel}>Tax:</Text>
                <Text variant="bodyMedium" style={styles.metaText}>
                  ৳{(orderData.tax || 0).toFixed(2)}
                </Text>
              </View>
            )}
            <View style={styles.totalRow}>
              <Text variant="titleMedium" style={styles.totalLabel}>Total:</Text>
              <Text variant="titleMedium" style={styles.totalAmount}>
                ৳{(orderData.total || 0).toFixed(2)}
              </Text>
            </View>
          </View>
        </Surface>
      )}

      {/* Notes */}
      {orderData.notes && (
        <Surface style={styles.section}>
          <View style={styles.sectionHeader}>
            <LocalIcon name="note-text" size={20} color={theme.colors.primary} />
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Notes
            </Text>
          </View>
          <Text variant="bodyMedium" style={styles.notesText}>
            {orderData.notes}
          </Text>
        </Surface>
      )}

      {/* Order Image with ImagePicker */}
      <Surface style={styles.section}>
        <View style={styles.sectionHeader}>
          <LocalIcon name="image" size={20} color={theme.colors.primary} />
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Reference Image
          </Text>
        </View>
        <View style={styles.imageContainer}>
          <ImagePicker
            currentImage={orderData.image}
            onImageSelected={handleImageUpdate}
            placeholder="Add Reference Image"
            size={120}
            borderRadius={BORDER_RADIUS.md}
          />
          {orderData.image && (
            <Text variant="bodySmall" style={styles.imageHint}>
              Tap to change image
            </Text>
          )}
        </View>
      </Surface>

      {/* Status Management */}
      <Surface style={styles.section}>
        <View style={styles.sectionHeader}>
          <LocalIcon name="cog" size={20} color={theme.colors.primary} />
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Status Management
          </Text>
        </View>
        <TouchableOpacity
          style={styles.statusButton}
          onPress={() => setShowStatusPicker(true)}>
          <LocalIcon name={getStatusIcon(selectedStatus)} size={20} color={getStatusColor(selectedStatus)} />
          <Text style={styles.statusButtonText}>
            {selectedStatus}
          </Text>
          <LocalIcon name="chevron-down" size={20} color={getStatusColor(selectedStatus)} style={styles.statusButtonIcon} />
        </TouchableOpacity>
      </Surface>

      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        <Button
          mode="outlined"
          icon="pencil"
          onPress={() => onEdit?.(orderData)}
          style={styles.actionButton}>
          Edit
        </Button>
        <Button
          mode="outlined"
          icon="delete"
          onPress={handleDelete}
          style={styles.actionButton}
          textColor={theme.colors.error}>
          Delete
        </Button>
        <Button
          mode="contained"
          icon="file-document"
          onPress={() => onViewInvoice?.(orderData)}
          style={styles.actionButton}>
          Invoice
        </Button>
      </View>

      {/* Status Picker Modal */}
      {showStatusPicker && (
        <UnifiedStatusPicker
          visible={showStatusPicker}
          currentStatus={selectedStatus}
          onStatusSelect={handleStatusUpdate}
          onDismiss={() => setShowStatusPicker(false)}
        />
      )}
    </View>
  );

  return (
    <View style={styles.container}>
      {renderContent()}

      {/* Status Picker Modal */}
      <Portal>
        <Modal
          visible={showStatusPicker}
          onDismiss={() => setShowStatusPicker(false)}
          contentContainerStyle={[
            styles.modalContainer,
            { backgroundColor: theme.colors.surface }
          ]}>
          <Text variant="headlineSmall" style={styles.modalTitle}>
            Update Order Status
          </Text>
          <Text variant="bodyMedium" style={styles.modalSubtitle}>
            Select new status for Order #{orderData?.id}
          </Text>

          <ScrollView style={styles.statusList} showsVerticalScrollIndicator={false}>
            {orderStatuses.map((status) => (
              <TouchableOpacity
                key={status}
                style={[
                  styles.statusOption,
                  selectedStatus === status && { backgroundColor: getStatusColor(status) + '15' }
                ]}
                onPress={() => handleStatusUpdate(status)}>
                <LocalIcon name={getStatusIcon(status)}
                  size={24}
                  color={getStatusColor(status)}
                />
                <Text
                  variant="bodyLarge"
                  style={[
                    styles.statusOptionText,
                    { color: theme.colors.onSurface },
                    selectedStatus === status && { fontWeight: '600', color: getStatusColor(status) }
                  }>
                  {status}
                </Text>
                {selectedStatus === status && (
                  <LocalIcon name="check" size={20} color={getStatusColor(status)} />
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>

          <View style={styles.modalActions}>
            <Button
              mode="outlined"
              onPress={() => setShowStatusPicker(false)}
              style={styles.modalButton}>
              Cancel
            </Button>
          </View>
        </Modal>
      </Portal>
    </View>
  );
};






export default OrderDetailsContent;

const styles = StyleSheet.create({
  container: {
    flex: 1},
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: SPACING.xl},
  peekContainer: {
    paddingVertical: SPACING.md},
  halfContainer: {
    paddingVertical: SPACING.md},
  fullContainer: {
    paddingVertical: SPACING.md},
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.md},
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center'},
  titleContainer: {
    flex: 1},
  title: {
    fontWeight: '600'},
  statusChip: {
    height: 32},
  urgentChip: {
    height: 28},
  paymentChip: {
    height: 28},
  deliveryChip: {
    height: 28},
  typeChip: {
    height: 28,
    marginRight: SPACING.sm,
    marginBottom: SPACING.sm},
  tagChip: {
    height: 28,
    marginRight: SPACING.sm,
    marginBottom: SPACING.sm},
  section: {
    borderRadius: BORDER_RADIUS.md,
    padding: SPACING.md,
    marginVertical: SPACING.sm},
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
    gap: SPACING.sm},
  sectionTitle: {
    fontWeight: '600'},
  customerInfo: {
    gap: SPACING.xs},
  orderMeta: {
    gap: SPACING.sm},
  metaRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'},
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
    paddingTop: SPACING.sm,
    marginTop: SPACING.sm},
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap'},
  fittingInfo: {
    flexDirection: 'row',
    alignItems: 'center'},
  orderItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.sm,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)'},
  itemInfo: {
    flex: 1},
  orderTotals: {
    gap: SPACING.sm},
  imageContainer: {
    alignItems: 'center'},
  orderImage: {
    width: 120,
    height: 120,
    borderRadius: BORDER_RADIUS.md,
    backgroundColor: 'rgba(0,0,0,0.05)'},
  statusButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.md,
    borderWidth: 1,
    borderRadius: BORDER_RADIUS.md},
  actionButtons: {
    flexDirection: 'row',
    gap: SPACING.md,
    marginTop: SPACING.md},
  actionButton: {
    flex: 1},
  enhancedActions: {
    gap: SPACING.md,
    marginTop: SPACING.lg,
    marginBottom: SPACING.md},
  primaryActions: {
    flexDirection: 'row',
    gap: SPACING.sm},
  secondaryActions: {
    flexDirection: 'row',
    gap: SPACING.sm},
  enhancedActionButton: {
    flex: 1},
  secondaryActionButton: {
    flex: 1},
  modalContainer: {
    margin: SPACING.lg,
    padding: SPACING.lg,
    borderRadius: BORDER_RADIUS.lg,
    maxHeight: '80%'},
  modalTitle: {
    textAlign: 'center',
    marginBottom: SPACING.sm},
  modalSubtitle: {
    textAlign: 'center',
    marginBottom: SPACING.lg},
  statusList: {
    maxHeight: 400},
  statusOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    marginBottom: SPACING.xs},
  statusOptionText: {
    flex: 1,
    marginLeft: SPACING.md},
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: SPACING.lg},
  modalButton: {
    minWidth: 120},
  dashboardContainer: {
    flex: 1},
  dashboardHeader: {
    marginBottom: SPACING.sm},
  dashboardTitleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'},
  dashboardTitle: {
    flex: 1,
    marginRight: SPACING.md},
  dashboardStatusChip: {
    alignSelf: 'flex-start'},
  dashboardGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between'},
  dashboardGridItem: {
    width: '48%',
    marginBottom: SPACING.md},
  dashboardPayment: {
    marginBottom: SPACING.md},
  dashboardPaymentRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between'},
  dashboardActions: {
    flexDirection: 'row',
    gap: SPACING.md,
    marginTop: SPACING.md},
  dashboardActionButton: {
    flex: 1},
  // Additional descriptive styles
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.xl,
    backgroundColor: theme.colors.surface},
  emptyText: {
    color: theme.colors.onSurfaceVariant,
    marginTop: SPACING.md,
    textAlign: 'center'},
  peekContainer: {
    padding: SPACING.md},
  halfContainer: {
    flex: 1},
  fullContainer: {
    flex: 1},
  subtitle: {
    color: theme.colors.onSurfaceVariant,
    marginTop: 2},
  divider: {
    marginVertical: SPACING.md},
  customerName: {
    color: theme.colors.onSurface,
    fontWeight: '600'},
  customerDetails: {
    color: theme.colors.onSurfaceVariant},
  metaLabel: {
    color: theme.colors.onSurfaceVariant},
  metaValue: {
    color: theme.colors.onSurface,
    fontWeight: '600'},
  metaText: {
    color: theme.colors.onSurface},
  paidAmount: {
    color: theme.colors.tertiary,
    fontWeight: '600'},
  balanceAmount: {
    color: theme.colors.error,
    fontWeight: '600'},
  statusButtonText: {
    color: theme.colors.onSurface,
    marginLeft: SPACING.sm,
    fontWeight: '600'},
  statusButtonIcon: {
    marginLeft: 'auto'},
  dashboardSubtitle: {
    color: theme.colors.onSurfaceVariant,
    marginTop: 4},
  gridLabel: {
    color: theme.colors.onSurfaceVariant,
    marginBottom: 4},
  gridValue: {
    color: theme.colors.onSurface,
    fontWeight: '700'},
  gridText: {
    color: theme.colors.onSurface,
    fontWeight: '600'},
  paymentTitle: {
    color: theme.colors.onSurface,
    marginBottom: SPACING.sm},
  fittingText: {
    color: theme.colors.tertiary,
    marginLeft: SPACING.xs,
    fontWeight: '600'},
  itemName: {
    color: theme.colors.onSurface,
    fontWeight: '600'},
  itemDetails: {
    color: theme.colors.onSurfaceVariant},
  itemTotal: {
    color: theme.colors.onSurface,
    fontWeight: '600'},
  discountText: {
    color: theme.colors.error},
  totalLabel: {
    color: theme.colors.onSurface,
    fontWeight: '700'},
  totalAmount: {
    color: theme.colors.primary,
    fontWeight: '700'},
  notesText: {
    color: theme.colors.onSurfaceVariant,
    lineHeight: 20},
  imageHint: {
    color: theme.colors.onSurfaceVariant,
    marginTop: SPACING.sm,
    textAlign: 'center'}});