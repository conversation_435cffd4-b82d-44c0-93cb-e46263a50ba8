/**
 * UserProfileContent - Dynamic content for user profiles
 */

import React, { useCallback } from 'react';
import { View, StyleSheet, Image   } from 'react-native';
import { Text   } from 'react-native-paper';
import { Button   } from 'react-native-paper';
import { Chip   } from 'react-native-paper';
import { Divider   } from 'react-native-paper';
import { Surface   } from 'react-native-paper';
import { Avatar   } from 'react-native-paper';
import { SPACING, BORDER_RADIUS   } from '../../theme/designTokens';
import { SHEET_STATES   } from '../DynamicBottomSheet';

import LocalIcon from '../LocalIcon';

const UserProfileContent = ({ state, userData, theme }) => {
  if(!userData) {
    return (
      <View style={styles.emptyContainer}>
        <LocalIcon name="account-off" size={48} color={theme.colors.onSurfaceVariant} />
        <Text variant="bodyLarge" style={styles.emptyText}>
          No user selected
        </Text>
      </View>
    );
  }

  switch(state) {
    case SHEET_STATES.PEEK:
      return renderPeekContent(userData, theme);
    case SHEET_STATES.HALF:
      return renderHalfContent(userData, theme);
    case SHEET_STATES.FULL:
      return renderFullContent(userData, theme);
    default:
      return null;
  }
};

const renderPeekContent = (user, theme) => (
  <View style={styles.peekContainer}>
    <View style={styles.peekHeader}>
      <Avatar.Image 
        size={50} 
        source={{ uri: user.avatar || 'https://via.placeholder.com/50' } 
        style={styles.peekAvatar}
      />
      <View style={styles.peekInfo}>
        <Text variant="titleMedium" style={styles.userName}>
          {user.name}
        </Text>
        <Text variant="bodySmall" style={styles.userRole}>
          {user.role || 'Customer'}
        </Text>
      </View>
      <Chip 
        mode="flat" 
        compact
        style={styles.statusChip}
        textStyle={{ color: getStatusColor(user.status) }>
        {user.status || 'Active'}
      </Chip>
    </View>
  </View>
);

const renderHalfContent = (user, theme) => (
  <View style={styles.halfContainer}>
    <View style={styles.halfHeader}>
      <Avatar.Image 
        size={80} 
        source={{ uri: user.avatar || 'https://via.placeholder.com/80' } 
        style={styles.halfAvatar}
      />
      <View style={styles.halfInfo}>
        <Text variant="headlineSmall" style={styles.userName}>
          {user.name}
        </Text>
        <Text variant="bodyMedium" style={styles.userRole}>
          {user.role || 'Customer'}
        </Text>
        <Text variant="bodyMedium" style={styles.userEmail}>
          {user.email}
        </Text>
      </View>
    </View>

    <View style={styles.quickStats}>
      <View style={styles.statItem}>
        <LocalIcon name="shopping" size={20} color={theme.colors.primary} />
        <Text variant="bodyMedium" style={styles.statText}>
          {user.totalOrders || 0}
        </Text>
        <Text variant="bodySmall" style={styles.statLabel}>
          Orders
        </Text>
      </View>
      <View style={styles.statItem}>
        <LocalIcon name="currency-bdt" size={20} color={theme.colors.secondary} />
        <Text variant="bodyMedium" style={styles.statText}>
          ৳{user.totalSpent || 0}
        </Text>
        <Text variant="bodySmall" style={styles.statLabel}>
          Spent
        </Text>
      </View>
      <View style={styles.statItem}>
        <LocalIcon name="calendar" size={20} color={theme.colors.tertiary} />
        <Text variant="bodyMedium" style={styles.statText}>
          {user.memberSince || 'N/A'}
        </Text>
        <Text variant="bodySmall" style={styles.statLabel}>
          Member
        </Text>
      </View>
    </View>

    <View style={styles.quickActions}>
      <Button
        mode="outlined"
        icon="message"
        style={styles.actionButton}
        onPress={() => console.log('Message user')}>
        Message
      </Button>
      <Button
        mode="contained"
        icon="account-edit"
        style={styles.actionButton}
        onPress={() => console.log('Edit profile')}>
        Edit Profile
      </Button>
    </View>
  </View>
);

const renderFullContent = (user, theme) => (
  <View style={styles.fullContainer}>
    {/* Profile Header */}
    <View style={styles.fullHeader}>
      <Avatar.Image 
        size={120} 
        source={{ uri: user.avatar || 'https://via.placeholder.com/120' } 
        style={styles.fullAvatar}
      />
      <Text variant="headlineLarge" style={styles.fullUserName}>
        {user.name}
      </Text>
      <Text variant="bodyLarge" style={styles.fullUserRole}>
        {user.role || 'Customer'}
      </Text>
      <Chip 
        mode="flat"
        style={styles.fullStatusChip}
        textStyle={{ color: getStatusColor(user.status) }>
        {user.status || 'Active'}
      </Chip>
    </View>

    {/* Stats Grid */}
    <View style={styles.statsGrid}>
      <Surface style={styles.statCard} elevation={0}>
        <LocalIcon name="shopping" size={24} color={theme.colors.primary} />
        <Text variant="titleMedium" style={styles.statValue}>
          {user.totalOrders || 0}
        </Text>
        <Text variant="bodySmall" style={styles.statCardLabel}>
          Total Orders
        </Text>
      </Surface>
      
      <Surface style={styles.statCard} elevation={0}>
        <LocalIcon name="currency-bdt" size={24} color={theme.colors.secondary} />
        <Text variant="titleMedium" style={styles.statValue}>
          ৳{user.totalSpent || 0}
        </Text>
        <Text variant="bodySmall" style={styles.statCardLabel}>
          Total Spent
        </Text>
      </Surface>
      
      <Surface style={styles.statCard} elevation={0}>
        <LocalIcon name="account-check" size={24} color="#10B981" />
        <Text variant="titleMedium" style={styles.statValue}>
          {user.status || 'Active'}
        </Text>
        <Text variant="bodySmall" style={styles.statCardLabel}>
          Status
        </Text>
      </Surface>
    </View>

    <Divider style={styles.divider} />

    {/* Contact Information */}
    <View style={styles.contactSection}>
      <Text variant="titleMedium" style={styles.sectionTitle}>
        Contact Information
      </Text>
      
      <View style={styles.contactItem}>
        <LocalIcon name="email" size={20} color={theme.colors.onSurfaceVariant} />
        <Text variant="bodyMedium" style={styles.contactText}>
          {user.email}
        </Text>
      </View>
      
      {user.phone && (
        <View style={styles.contactItem}>
          <LocalIcon name="phone" size={20} color={theme.colors.onSurfaceVariant} />
          <Text variant="bodyMedium" style={styles.contactText}>
            {user.phone}
          </Text>
        </View>
      )}
      
      {user.address && (
        <View style={styles.contactItem}>
          <LocalIcon name="map-marker" size={20} color={theme.colors.onSurfaceVariant} />
          <Text variant="bodyMedium" style={styles.contactText}>
            {user.address}
          </Text>
        </View>
      )}
    </View>

    <Divider style={styles.divider} />

    {/* Additional Info */}
    <View style={styles.additionalInfo}>
      <Text variant="titleMedium" style={styles.sectionTitle}>
        Additional Information
      </Text>
      
      <View style={styles.infoRow}>
        <Text variant="bodyMedium" style={styles.infoLabel}>
          Member Since:
        </Text>
        <Text variant="bodyMedium" style={styles.infoValue}>
          {user.memberSince || 'N/A'}
        </Text>
      </View>
      
      <View style={styles.infoRow}>
        <Text variant="bodyMedium" style={styles.infoLabel}>
          Last Order:
        </Text>
        <Text variant="bodyMedium" style={styles.infoValue}>
          {user.lastOrder || 'N/A'}
        </Text>
      </View>
      
      <View style={styles.infoRow}>
        <Text variant="bodyMedium" style={styles.infoLabel}>
          Preferred Payment:
        </Text>
        <Text variant="bodyMedium" style={styles.infoValue}>
          {user.preferredPayment || 'N/A'}
        </Text>
      </View>
    </View>

    {/* Action Buttons */}
    <View style={styles.fullActions}>
      <Button 
        mode="outlined" 
        icon="message"
        style={styles.fullActionButton}
        onPress={() => console.log('Message user')}>
        Message
      </Button>
      <Button 
        mode="outlined" 
        icon="phone"
        style={styles.fullActionButton}
        onPress={() => console.log('Call user')}>
        Call
      </Button>
      <Button 
        mode="contained" 
        icon="account-edit"
        style={styles.fullActionButton}
        onPress={() => console.log('Edit profile')}>
        Edit
      </Button>
    </View>
  </View>
);

const getStatusColor = (status) => {
  const colors = {
    active: '#00B894',
    inactive: '#E17055',
    pending: '#FDCB6E',
    default: '#74B9FF'
  };
  return colors[status?.toLowerCase()] || colors.default;
};






export default React.memo(UserProfileContent);

const styles = StyleSheet.create({
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: SPACING.xl,
  },
  emptyText: {
    marginTop: SPACING.md,
    fontWeight: '600',
  },
  peekContainer: {
    paddingVertical: SPACING.sm,
  },
  peekHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  peekAvatar: {
    marginRight: SPACING.md,
  },
  peekInfo: {
    flex: 1,
  },
  userName: {
    fontWeight: '600',
    marginBottom: 2,
  },
  userRole: {
    fontSize: 13,
  },
  statusChip: {
    height: 28,
  },
  halfContainer: {
    paddingVertical: SPACING.sm,
  },
  halfHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  halfAvatar: {
    marginRight: SPACING.md,
  },
  halfInfo: {
    flex: 1,
  },
  userEmail: {
    marginTop: SPACING.xs,
  },
  quickStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: SPACING.lg,
  },
  statItem: {
    alignItems: 'center',
  },
  statText: {
    marginTop: SPACING.xs,
    fontWeight: '500',
  },
  statLabel: {
    marginTop: 2,
  },
  quickActions: {
    flexDirection: 'row',
    gap: SPACING.sm,
  },
  actionButton: {
    flex: 1,
  },
  fullContainer: {
    paddingBottom: SPACING.lg,
  },
  fullHeader: {
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  fullAvatar: {
    marginBottom: SPACING.md,
  },
  fullUserName: {
    fontWeight: 'bold',
    marginBottom: SPACING.xs,
    textAlign: 'center',
  },
  fullUserRole: {
    marginBottom: SPACING.sm,
    textAlign: 'center',
  },
  fullStatusChip: {
    marginTop: SPACING.sm,
  },
  statsGrid: {
    flexDirection: 'row',
    gap: SPACING.sm,
    marginBottom: SPACING.lg,
  },
  statCard: {
    flex: 1,
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    alignItems: 'center',
  },
  statValue: {
    fontWeight: 'bold',
    marginTop: SPACING.xs,
  },
  statCardLabel: {
    marginTop: 2,
    textAlign: 'center',
  },
  divider: {
    marginVertical: SPACING.lg,
  },
  contactSection: {
    marginBottom: SPACING.lg,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: SPACING.md,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  contactText: {
    marginLeft: SPACING.sm,
    flex: 1,
  },
  additionalInfo: {
    marginBottom: SPACING.lg,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.sm,
  },
  infoLabel: {
    flex: 1,
  },
  infoValue: {
    flex: 1,
    textAlign: 'right',
    fontWeight: '500',
  },
  fullActions: {
    flexDirection: 'row',
    gap: SPACING.sm,
  },
  fullActionButton: {
    flex: 1,
    borderRadius: BORDER_RADIUS.md,
  },

});