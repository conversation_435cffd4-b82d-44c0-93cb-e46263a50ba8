/**
 * EmptyContent - Default content when no specific content type is set
 */

import React, { useCallback } from 'react';
import { View, StyleSheet   } from 'react-native';
import { Text   } from 'react-native-paper';
import { Button   } from 'react-native-paper';
import LocalIcon from '../LocalIcon';
import { SPACING   } from '../../theme/designTokens';

const EmptyContent = ({ theme }) => (
  <View style={styles.container}>
    <LocalIcon name="gesture-swipe-up" size={64} color={theme.colors.onSurfaceVariant} />
    <Text variant="headlineSmall" style={styles.title}>
      Dynamic Bottom Sheet
    </Text>
    <Text variant="bodyMedium" style={styles.description}>
      This bottom sheet dynamically renders content based on the active state and selected features.
    </Text>
    <Button 
      mode="contained" 
      onPress={() => console.log('Explore features')}
      style={styles.button}>
      Explore Features
    </Button>
  </View>
);






export default React.memo(EmptyContent);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SPACING.xl,
  },
  title: {
    fontWeight: 'bold',
    marginTop: SPACING.lg,
    marginBottom: SPACING.sm,
    textAlign: 'center',
  },
  description: {
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: SPACING.xl,
  },
  button: {
    marginTop: SPACING.md,
  },
});