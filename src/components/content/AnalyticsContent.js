/**
 * AnalyticsContent - Dynamic analytics content
 */

import React from 'react';
import { View, StyleSheet, ScrollView   } from 'react-native';
import { Text   } from 'react-native-paper';
import { Card   } from 'react-native-paper';
import { Surface   } from 'react-native-paper';
import LocalIcon from '../LocalIcon';
import { SPACING, BORDER_RADIUS   } from '../../theme/designTokens';
import { SHEET_STATES   } from '../DynamicBottomSheet';

const AnalyticsContent = ({ state, analyticsData, theme }) => {
  const defaultData = {
    totalRevenue: 125000,
    totalOrders: 342,
    avgOrderValue: 365,
    customerGrowth: 12.5,
    monthlyData: [
      { month: 'Jan', revenue: 15000, orders: 45 },
      { month: 'Feb', revenue: 18000, orders: 52 },
      { month: 'Mar', revenue: 22000, orders: 68 }]
  };

  const data = analyticsData || defaultData;

  switch(state) {
    case SHEET_STATES.PEEK:
      return renderPeekContent(data, theme);
    case SHEET_STATES.HALF:
      return renderHalfContent(data, theme);
    case SHEET_STATES.FULL:
      return renderFullContent(data, theme);
    default:
      return null;
  }
};

const renderPeekContent = (data, theme) => (
  <View style={styles.peekContainer}>
    <View style={styles.peekHeader}>
      <LocalIcon name="chart-line" size={24} color={theme.colors.primary} />
      <Text variant="titleMedium" style={[styles.peekTitle, { color: theme.colors.onSurface }]}>
        Analytics Overview
      </Text>
    </View>
    <View style={styles.peekStats}>
      <View style={styles.peekStat}>
        <Text variant="headlineSmall" style={[styles.peekValue, { color: theme.colors.primary }]}>
          ৳{(data.totalRevenue / 1000).toFixed(0)}K
        </Text>
        <Text variant="bodySmall" style={[styles.peekLabel, { color: theme.colors.onSurfaceVariant }]}>
          Revenue
        </Text>
      </View>
      <View style={styles.peekStat}>
        <Text variant="headlineSmall" style={[styles.peekValue, { color: theme.colors.secondary }]}>
          {data.totalOrders}
        </Text>
        <Text variant="bodySmall" style={[styles.peekLabel, { color: theme.colors.onSurfaceVariant }]}>
          Orders
        </Text>
      </View>
    </View>
  </View>
);

const renderHalfContent = (data, theme) => (
  <View style={styles.halfContainer}>
    <Text variant="headlineSmall" style={[styles.title, { color: theme.colors.onSurface }]}>
      Business Analytics
    </Text>

    <View style={styles.statsGrid}>
      <Surface style={[styles.statCard, { backgroundColor: theme.colors.surfaceVariant }]} elevation={0}>
        <LocalIcon name="currency-bdt" size={24} color={theme.colors.primary} />
        <Text variant="titleLarge" style={[styles.statValue, { color: theme.colors.onSurface }]}>
          ৳{data.totalRevenue.toLocaleString()}
        </Text>
        <Text variant="bodySmall" style={[styles.statLabel, { color: theme.colors.onSurfaceVariant }]}>
          Total Revenue
        </Text>
      </Surface>

      <Surface style={[styles.statCard, { backgroundColor: theme.colors.surfaceVariant }]} elevation={0}>
        <LocalIcon name="shopping" size={24} color={theme.colors.secondary} />
        <Text variant="titleLarge" style={[styles.statValue, { color: theme.colors.onSurface }]}>
          {data.totalOrders}
        </Text>
        <Text variant="bodySmall" style={[styles.statLabel, { color: theme.colors.onSurfaceVariant }]}>
          Total Orders
        </Text>
      </Surface>
    </View>

    <View style={styles.statsGrid}>
      <Surface style={[styles.statCard, { backgroundColor: theme.colors.surfaceVariant }]} elevation={0}>
        <LocalIcon name="calculator" size={24} color={theme.colors.tertiary} />
        <Text variant="titleLarge" style={[styles.statValue, { color: theme.colors.onSurface }]}>
          ৳{data.avgOrderValue}
        </Text>
        <Text variant="bodySmall" style={[styles.statLabel, { color: theme.colors.onSurfaceVariant }]}>
          Avg Order Value
        </Text>
      </Surface>

      <Surface style={[styles.statCard, { backgroundColor: theme.colors.surfaceVariant }]} elevation={0}>
        <LocalIcon name="trending-up" size={24} color="#00B894" />
        <Text variant="titleLarge" style={[styles.statValue, { color: theme.colors.onSurface }]}>
          +{data.customerGrowth}%
        </Text>
        <Text variant="bodySmall" style={[styles.statLabel, { color: theme.colors.onSurfaceVariant }]}>
          Growth Rate
        </Text>
      </Surface>
    </View>
  </View>
);

const renderFullContent = (data, theme) => (
  <ScrollView style={styles.fullContainer}>
    <Text variant="headlineMedium" style={[styles.fullTitle, { color: theme.colors.onSurface }]}>
      Detailed Analytics
    </Text>

    {/* Key Metrics */}
    <Card style={styles.metricsCard}>
      <Card.Content>
        <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
          Key Metrics
        </Text>
        <View style={styles.fullStatsGrid}>
          <Surface style={[styles.fullStatCard, { backgroundColor: theme.colors.surfaceVariant }]} elevation={0}>
            <LocalIcon name="currency-bdt" size={28} color={theme.colors.primary} />
            <Text variant="displaySmall" style={[styles.fullStatValue, { color: theme.colors.onSurface }]}>
              ৳{(data.totalRevenue / 1000).toFixed(0)}K
            </Text>
            <Text variant="bodyMedium" style={[styles.fullStatLabel, { color: theme.colors.onSurfaceVariant }]}>
              Total Revenue
            </Text>
          </Surface>
          
          <Surface style={[styles.fullStatCard, { backgroundColor: theme.colors.surfaceVariant }]} elevation={0}>
            <LocalIcon name="shopping" size={28} color={theme.colors.secondary} />
            <Text variant="displaySmall" style={[styles.fullStatValue, { color: theme.colors.onSurface }]}>
              {data.totalOrders}
            </Text>
            <Text variant="bodyMedium" style={[styles.fullStatLabel, { color: theme.colors.onSurfaceVariant }]}>
              Total Orders
            </Text>
          </Surface>
        </View>

        <View style={styles.fullStatsGrid}>
          <Surface style={[styles.fullStatCard, { backgroundColor: theme.colors.surfaceVariant }]} elevation={0}>
            <LocalIcon name="calculator" size={28} color={theme.colors.tertiary} />
            <Text variant="displaySmall" style={[styles.fullStatValue, { color: theme.colors.onSurface }]}>
              ৳{data.avgOrderValue}
            </Text>
            <Text variant="bodyMedium" style={[styles.fullStatLabel, { color: theme.colors.onSurfaceVariant }]}>
              Avg Order Value
            </Text>
          </Surface>
          
          <Surface style={[styles.fullStatCard, { backgroundColor: theme.colors.surfaceVariant }]} elevation={0}>
            <LocalIcon name="trending-up" size={28} color="#00B894" />
            <Text variant="displaySmall" style={[styles.fullStatValue, { color: theme.colors.onSurface }]}>
              +{data.customerGrowth}%
            </Text>
            <Text variant="bodyMedium" style={[styles.fullStatLabel, { color: theme.colors.onSurfaceVariant }]}>
              Growth Rate
            </Text>
          </Surface>
        </View>
      </Card.Content>
    </Card>
    
    {/* Monthly Breakdown */}
    <Card style={styles.monthlyCard}>
      <Card.Content>
        <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
          Monthly Performance
        </Text>
        {data.monthlyData.map((month, index) => (
          <View key={index} style={styles.monthlyItem}>
            <Text variant="titleSmall" style={[styles.monthName, { color: theme.colors.onSurface }]}>
              {month.month}
            </Text>
            <View style={styles.monthlyStats}>
              <Text variant="bodyMedium" style={[styles.monthlyValue, { color: theme.colors.primary }]}>
                ৳{month.revenue.toLocaleString()}
              </Text>
              <Text variant="bodyMedium" style={[styles.monthlyValue, { color: theme.colors.secondary }]}>
                {month.orders} orders
              </Text>
            </View>
          </View>
        ))}
      </Card.Content>
    </Card>
  </ScrollView>
);






export default React.memo(AnalyticsContent);

const styles = StyleSheet.create({
  peekContainer: {
    paddingVertical: SPACING.sm},
  peekHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md},
  peekTitle: {
    marginLeft: SPACING.sm,
    fontWeight: '600'},
  peekStats: {
    flexDirection: 'row',
    justifyContent: 'space-around'},
  peekStat: {
    alignItems: 'center'},
  peekValue: {
    fontWeight: 'bold'},
  peekLabel: {
    marginTop: SPACING.xs},
  halfContainer: {
    paddingVertical: SPACING.sm},
  title: {
    fontWeight: 'bold',
    marginBottom: SPACING.lg,
    textAlign: 'center'},
  statsGrid: {
    flexDirection: 'row',
    gap: SPACING.sm,
    marginBottom: SPACING.md},
  statCard: {
    flex: 1,
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    alignItems: 'center'},
  statValue: {
    fontWeight: 'bold',
    marginTop: SPACING.xs},
  statLabel: {
    marginTop: 2,
    textAlign: 'center'},
  fullContainer: {
    flex: 1},
  fullTitle: {
    fontWeight: 'bold',
    marginBottom: SPACING.lg,
    textAlign: 'center'},
  metricsCard: {
    marginBottom: SPACING.lg},
  sectionTitle: {
    fontWeight: '600',
    marginBottom: SPACING.md},
  fullStatsGrid: {
    flexDirection: 'row',
    gap: SPACING.sm,
    marginBottom: SPACING.md},
  fullStatCard: {
    flex: 1,
    padding: SPACING.lg,
    borderRadius: BORDER_RADIUS.lg,
    alignItems: 'center'},
  fullStatValue: {
    fontWeight: 'bold',
    marginTop: SPACING.sm},
  fullStatLabel: {
    marginTop: SPACING.xs,
    textAlign: 'center'},
  monthlyCard: {
    marginBottom: SPACING.xl},
  monthlyItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.sm,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)'},
  monthName: {
    fontWeight: '600'},
  monthlyStats: {
    alignItems: 'flex-end'},
  monthlyValue: {
    fontWeight: '500'
  }
});