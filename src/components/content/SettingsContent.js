/**
 * SettingsContent - Dynamic settings content
 */

import React, { useState, useCallback } from 'react';
import { View, StyleSheet, ScrollView   } from 'react-native';
import { Text   } from 'react-native-paper';
import { Card   } from 'react-native-paper';
import { Switch   } from 'react-native-paper';
import { List   } from 'react-native-paper';
import { Divider   } from 'react-native-paper';
import { Button   } from 'react-native-paper';
import { SegmentedButtons   } from 'react-native-paper';
import LocalIcon from '../LocalIcon';
import { SPACING   } from '../../theme/designTokens';
import { SHEET_STATES   } from '../DynamicBottomSheet';

const SettingsContent = ({ state, theme }) => {
  const [notifications, setNotifications] = useState(true);
  const [darkMode, setDarkMode] = useState(false);
  const [language, setLanguage] = useState('english');

  switch(state) {
    case SHEET_STATES.PEEK:
      return renderPeekContent(theme);
    case SHEET_STATES.HALF:
      return renderHalfContent(notifications, setNotifications, darkMode, setDarkMode, theme);
    case SHEET_STATES.FULL:
      return renderFullContent(notifications, setNotifications, darkMode, setDarkMode, language, setLanguage, theme);
    default:
      return null;
  }
};

const renderPeekContent = (theme) => (
  <View style={styles.peekContainer}>
    <View style={styles.peekHeader}>
      <LocalIcon name="cog" size={24} color={theme.colors.primary} />
      <Text variant="titleMedium" style={[styles.peekTitle, { color: theme.colors.onSurface }}>
        Settings
      </Text>
    </View>
    <Text variant="bodyMedium" style={[styles.peekSubtitle, { color: theme.colors.onSurfaceVariant }}>
      Customize your app experience
    </Text>
  </View>
);

const renderHalfContent = (notifications, setNotifications, darkMode, setDarkMode, theme) => (
  <View style={styles.halfContainer}>
    <Text variant="headlineSmall" style={[styles.title, { color: theme.colors.onSurface }}>
      Quick Settings
    </Text>
    
    <Card style={styles.settingsCard}>
      <Card.Content>
        <List.Item
          title="Notifications"
          description="Receive push notifications"
          left={props => <List.Icon {...props} icon="bell" />}
          right={() => (
            <Switch
              value={notifications}
              onValueChange={setNotifications}
            />
          )}
        />
        
        <Divider />
        
        <List.Item
          title="Dark Mode"
          description="Use dark theme"
          left={props => <List.Icon {...props} icon="theme-light-dark" />}
          right={() => (
            <Switch
              value={darkMode}
              onValueChange={setDarkMode}
            />
          )}
        />
      </Card.Content>
    </Card>
  </View>
);

const renderFullContent = (notifications, setNotifications, darkMode, setDarkMode, language, setLanguage, theme) => (
  <ScrollView style={styles.fullContainer}>
    <Text variant="headlineMedium" style={[styles.fullTitle, { color: theme.colors.onSurface }}>
      App Settings
    </Text>
    
    {/* General Settings */}
    <Card style={styles.settingsCard}>
      <Card.Content>
        <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }}>
          General
        </Text>
        
        <List.Item
          title="Notifications"
          description="Receive push notifications"
          left={props => <List.Icon {...props} icon="bell" />}
          right={() => (
            <Switch
              value={notifications}
              onValueChange={setNotifications}
            />
          )}
        />
        
        <List.Item
          title="Dark Mode"
          description="Use dark theme"
          left={props => <List.Icon {...props} icon="theme-light-dark" />}
          right={() => (
            <Switch
              value={darkMode}
              onValueChange={setDarkMode}
            />
          )}
        />
        
        <List.Item
          title="Language"
          description="App language"
          left={props => <List.Icon {...props} icon="translate" />}
          onPress={() => console.log('Language settings')}
        />
      </Card.Content>
    </Card>
    
    {/* Language Selection */}
    <Card style={styles.settingsCard}>
      <Card.Content>
        <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }}>
          Language
        </Text>
        <SegmentedButtons
          value={language}
          onValueChange={setLanguage}
          buttons={[
            { value: 'english', label: 'English' },
            { value: 'bangla', label: 'বাংলা' },
          }
        />
      </Card.Content>
    </Card>
    
    {/* Account Settings */}
    <Card style={styles.settingsCard}>
      <Card.Content>
        <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }}>
          Account
        </Text>
        
        <List.Item
          title="Profile"
          description="Edit your profile"
          left={props => <List.Icon {...props} icon="account" />}
          onPress={() => console.log('Profile settings')}
        />
        
        <List.Item
          title="Privacy"
          description="Privacy settings"
          left={props => <List.Icon {...props} icon="shield-account" />}
          onPress={() => console.log('Privacy settings')}
        />
        
        <List.Item
          title="Security"
          description="Security settings"
          left={props => <List.Icon {...props} icon="security" />}
          onPress={() => console.log('Security settings')}
        />
      </Card.Content>
    </Card>
    
    {/* Actions */}
    <View style={styles.actions}>
      <Button 
        mode="outlined" 
        icon="backup-restore"
        style={styles.actionButton}
        onPress={() => console.log('Backup data')}>
        Backup Data
      </Button>
      
      <Button 
        mode="contained" 
        icon="content-save"
        style={styles.actionButton}
        onPress={() => console.log('Save settings')}>
        Save Settings
      </Button>
    </View>
  </ScrollView>
);






export default SettingsContent;

const styles = StyleSheet.create({
  peekContainer: {
    paddingVertical: SPACING.md,
    alignItems: 'center',
  },
  peekHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  peekTitle: {
    marginLeft: SPACING.sm,
    fontWeight: '600',
  },
  peekSubtitle: {
    textAlign: 'center',
  },
  halfContainer: {
    paddingVertical: SPACING.sm,
  },
  title: {
    fontWeight: 'bold',
    marginBottom: SPACING.lg,
    textAlign: 'center',
  },
  settingsCard: {
    marginBottom: SPACING.md,
  },
  fullContainer: {
    flex: 1,
  },
  fullTitle: {
    fontWeight: 'bold',
    marginBottom: SPACING.lg,
    textAlign: 'center',
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: SPACING.sm,
  },
  actions: {
    flexDirection: 'row',
    gap: SPACING.sm,
    marginBottom: SPACING.xl,
  },
  actionButton: {
    flex: 1,
  },
});