/**
 * OrderFormContent - Dynamic order form content
 */

import React, { useState, useCallback } from 'react';
import { View, StyleSheet, ScrollView   } from 'react-native';
import { Text   } from 'react-native-paper';
import { TextInput   } from 'react-native-paper';
import { Button   } from 'react-native-paper';
import { Chip   } from 'react-native-paper';
import { Card   } from 'react-native-paper';
import { SegmentedButtons   } from 'react-native-paper';
import { SPACING   } from '../../theme/designTokens';
import { SHEET_STATES   } from '../DynamicBottomSheet';

const OrderFormContent = ({ state, theme }) => {
  const [formData, setFormData] = useState({
    customerName: '',
    phone: '',
    garmentType: '',
    quantity: '1',
    urgency: 'normal'
  });

  const updateField = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  switch(state) {
    case SHEET_STATES.PEEK:
      return renderPeekContent(theme);
    case SHEET_STATES.HALF:
      return renderHalfContent(formData, updateField, theme);
    case SHEET_STATES.FULL:
      return renderFullContent(formData, updateField, theme);
    default:
      return null;
  }};

const renderPeekContent = (theme) => (
  <View style={styles.peekContainer}>
      <Text variant="titleMedium" style={[styles.peekTitle, { color: theme.colors.onSurface }}}}>
        Quick Order Form
    </Text>
      <Text variant="bodyMedium" style={[styles.peekSubtitle, { color: theme.colors.onSurfaceVariant }}}}>
        Expand to create a new order
    </Text>
    </View>
);

const renderHalfContent = (formData, updateField, theme) => (
  <View style={styles.halfContainer}>
      <Text variant="headlineSmall" style={[styles.title, { color: theme.colors.onSurface }}}}>
        New Order
    </Text>
      <TextInput
      label="Customer Name"
      value={formData.customerName}
      onChangeText={(text) => updateField('customerName', text)}
      style={styles.input}
    /><TextInput
      label="Phone Number"
      value={formData.phone}
      onChangeText={(text) => updateField('phone', text)}
      keyboardType="phone-pad"
      style={styles.input}
    /><Button mode="contained" style={styles.submitButton}> Create Order
    </Button>
    </View>
);

const renderFullContent = (formData, updateField, theme) => (
  <ScrollView style={styles.fullContainer}><Text variant="headlineMedium" style={[styles.fullTitle, { color: theme.colors.onSurface }}}}>
        Create New Order
    </Text><Card style={styles.formCard}><Card.Content><TextInput
          label="Customer Name *"
          value={formData.customerName}
          onChangeText={(text) => updateField('customerName', text)}
          style={styles.input}
        /><TextInput
          label="Phone Number *"
          value={formData.phone}
          onChangeText={(text) => updateField('phone', text)}
          keyboardType="phone-pad"
          style={styles.input}
        /><Text variant="titleSmall" style={[styles.sectionTitle, { color: theme.colors.onSurface }}}}>
        Garment Type
        </Text><View style={styles.chipContainer}>
          {['Shirt', 'Pants', 'Dress', 'Suit'].map((type) => (
            <Chip
              key={type}
              selected={formData.garmentType === type}
              onPress={() => updateField('garmentType', type)}
              style={styles.chip}>
              {type}
            </Chip>
          ))}
        </View><TextInput
          label="Quantity"
          value={formData.quantity}
          onChangeText={(text) => updateField('quantity', text)}
          keyboardType="numeric"
          style={styles.input}
        /><Text variant="titleSmall" style={[styles.sectionTitle, { color: theme.colors.onSurface }}}}>
        Urgency
        </Text><SegmentedButtons
          value={formData.urgency}
          onValueChange={(value) => updateField('urgency', value)}
          buttons={[
            { value: 'normal', label: 'Normal' },
            { value: 'urgent', label: 'Urgent' },
            { value: 'rush', label: 'Rush' }]}
        /></Card.Content></Card><Button mode="contained" style={styles.fullSubmitButton}> Create Order
    </Button></ScrollView>
);






export default OrderFormContent;

const styles = StyleSheet.create({
  peekContainer: {
    paddingVertical: SPACING.md,
    alignItems: 'center'},
  peekTitle: {
    fontWeight: '600',
    marginBottom: SPACING.xs},
  peekSubtitle: {
    textAlign: 'center'},
  halfContainer: {
    paddingVertical: SPACING.sm},
  title: {
    fontWeight: 'bold',
    marginBottom: SPACING.lg,
    textAlign: 'center'},
  fullContainer: {
    flex: 1},
  fullTitle: {
    fontWeight: 'bold',
    marginBottom: SPACING.lg,
    textAlign: 'center'},
  formCard: {
    marginBottom: SPACING.lg},
  input: {
    marginBottom: SPACING.md},
  sectionTitle: {
    fontWeight: '600',
    marginBottom: SPACING.sm,
    marginTop: SPACING.sm},
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.sm,
    marginBottom: SPACING.md},
  chip: {
    marginRight: SPACING.xs},
  submitButton: {
    marginTop: SPACING.md},
  fullSubmitButton: {
    marginBottom: SPACING.xl}});