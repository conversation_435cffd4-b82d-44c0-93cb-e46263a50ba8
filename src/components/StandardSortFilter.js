/**
 * StandardSortFilter - Enhanced unified sorting and filtering component
 * Now integrated with global sort/filter system
 */

import React, { memo, useCallback, useMemo } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { Chip } from 'react-native-paper';
import { IconButton } from 'react-native-paper';
import { Menu } from 'react-native-paper';
import { Divider } from 'react-native-paper';
import { useTheme } from '../context/ThemeContext';
import { SPACING } from '../theme/designTokens';

const StandardSortFilter = memo(({
  filters = [],
  selectedFilter = 'all',
  onFilterChange,
  sortOptions = [],
  selectedSort = 'name_asc',
  onSortChange,
  showSortButton = true,
  showSortMenu = false,
  containerStyle = {},
  chipStyle = {},
  sortButtonStyle = {},
}) => {
  const { theme } = useTheme();
  const [sortMenuVisible, setSortMenuVisible] = React.useState(false);

  const dynamicStyles = useMemo(() => ({
    menuContent: {
      backgroundColor: theme.colors.surface,
      width: '100%',
      minWidth: 200,
    },
    selectedChipText: {
      color: theme.colors.onPrimaryContainer,
      fontSize: 12,
      fontWeight: '600',
    },
    unselectedChipText: {
      color: theme.colors.onSurfaceVariant,
      fontSize: 12,
      fontWeight: '400',
    },
    selectedMenuItemTitle: {
      color: theme.colors.primary,
      fontWeight: '600',
    },
    unselectedMenuItemTitle: {
      color: theme.colors.onSurface,
      fontWeight: '400',
    },
  }), [theme]);

  const handleSortPress = useCallback(() => {
    if (showSortMenu) {
      setSortMenuVisible(true);
    } else if (onSortChange && sortOptions.length > 0) {
      const currentIndex = sortOptions.findIndex(option => option.value === selectedSort);
      const nextIndex = (currentIndex + 1) % sortOptions.length;
      onSortChange(sortOptions[nextIndex].value);
    }
  }, [showSortMenu, onSortChange, sortOptions, selectedSort]);

  const handleSortSelect = useCallback((sortValue) => {
    onSortChange && onSortChange(sortValue);
    setSortMenuVisible(false);
  }, [onSortChange]);

  const getCurrentSortOption = () => {
    return sortOptions.find(option => option.value === selectedSort) || sortOptions[0];
  };

  const renderSortButton = () => {
    const currentSort = getCurrentSortOption();

    if (showSortMenu) {
      return (
        <View style={styles.style2}>
          <Menu
            visible={sortMenuVisible}
            onDismiss={() => setSortMenuVisible(false)}
            anchor={
              <IconButton
                icon={currentSort?.icon || 'sort'}
                size={20}
                onPress={handleSortPress}
                style={[styles.style3, sortButtonStyle}
                iconColor={theme.colors.onSurfaceVariant}
              />
            }
            contentStyle={dynamicStyles.menuContent}>
            {sortOptions.map((option, index) => (
              <React.Fragment key={`sort-option-${option.value}-${index}`}>
                <Menu.Item
                  onPress={() => handleSortSelect(option.value)}
                  title={option.label}
                  leadingIcon={option.icon}
                  titleStyle={selectedSort === option.value
                    ? dynamicStyles.selectedMenuItemTitle
                    : dynamicStyles.unselectedMenuItemTitle}
                  style={styles.style4}
                />
                {index < sortOptions.length - 1 && <Divider key={`divider-${index}`} />}
              </React.Fragment>
            ))}
          </Menu>
        </View>
      );
    }

    return (
      <IconButton
        icon={currentSort?.icon || 'sort'}
        size={20}
        onPress={handleSortPress}
        style={[styles.style3, sortButtonStyle}
        iconColor={theme.colors.onSurfaceVariant}
      />
    );
  };

  return (
    <View style={styles.style2}>
      <View style={styles.style2}>
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.filtersScrollContent}>
          {filters.map((filter, index) => {
            // Handle both string and object filters
            const filterValue = typeof filter === 'string' ? filter : filter.value;
            const filterLabel = typeof filter === 'string' ? filter : filter.label;

            return (
              <Chip
                key={`filter-${filterValue}-${index}`}
                mode={selectedFilter === filterValue ? 'flat' : 'outlined'}
                selected={selectedFilter === filterValue}
                onPress={() => onFilterChange && onFilterChange(filterValue)}
                style={[styles.style1, chipStyle}
                textStyle={selectedFilter === filterValue
                  ? dynamicStyles.selectedChipText
                  : dynamicStyles.unselectedChipText}>
                {filterLabel}
              </Chip>
            );
          })}
        </ScrollView>
      </View>
      
      {showSortButton && sortOptions.length > 0 && renderSortButton()}
    </View>
  );
});






export default StandardSortFilter;

const styles = StyleSheet.create({
  controlsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.sm,
    gap: SPACING.sm,
  },
  filtersContainer: {
    flex: 1,
  },
  filtersScrollContent: {
    paddingRight: SPACING.sm,
  },
  filterChip: {
    marginRight: SPACING.xs,
    height: 32,
  },
  sortButton: {
    margin: 0,
    width: 40,
    height: 40,
  },
  sortContainer: {
    position: 'relative',
  },
  style1: {
    marginRight: SPACING.xs,
    height: 32,
  },
  style2: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.sm,
    gap: SPACING.sm,
  },
  style3: {
    backgroundColor: 'transparent',
    margin: 0,
    width: 40,
    height: 40,
  },
  style4: {
    width: '100%',
  },
});