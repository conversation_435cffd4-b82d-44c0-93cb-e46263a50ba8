import React, { useState, useCallback } from 'react';
import { DatePickerModal   } from 'react-native-paper-dates';
import { useTheme   } from '../context/ThemeContext';

const DateRangePicker = ({
  visible,
  onDismiss,
  onConfirm,
  initialStartDate,
  initialEndDate,
  title = "Select Date Range"
}) => {
  const { theme  } = useTheme();
  const [range, setRange] = useState({
    startDate: initialStartDate || undefined,
    endDate: initialEndDate || undefined});

  const onDismissRange = useCallback(() => {
    setRange({
      startDate: initialStartDate || undefined,
      endDate: initialEndDate || undefined});
    onDismiss();
  }, [initialStartDate, initialEndDate, onDismiss]);

  const onConfirmRange = useCallback(({ startDate, endDate }) => {
    setRange({ startDate, endDate });
    onConfirm(startDate, endDate);
    onDismiss();
  }, [onConfirm, onDismiss]);

  return (
    <DatePickerModal
      locale="en"
      mode="range"
      visible={visible}
      onDismiss={onDismissRange}
      startDate={range.startDate}
      endDate={range.endDate}
      onConfirm={onConfirmRange}
      saveLabel="Apply"
      label={title}
      startLabel="From"
      endLabel="To"
    />
  );
};



export default DateRangePicker;
