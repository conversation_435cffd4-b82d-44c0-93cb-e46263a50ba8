import React, { useCallback } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView, Alert   } from 'react-native';
import { useCombinedNavigation, useNavigationGuard, usePermissions   } from '../hooks/useTypedNavigation';
import { commonLinks   } from '../utils/deepLinkUtils';

const TypedNavigationExample: React.FC = () => {
  const { navigateToTab, navigateToScreen, goBack, canGoBack  } = useCombinedNavigation();
  const { navigateWithAuth  } = useNavigationGuard();
  const { hasRole, isAdmin  } = usePermissions();

  const handleNavigateToCustomer = useCallback(() => {
    // Type-safe navigation with parameters
    navigateToScreen('CustomerDetails', { customerId: '123' });
  }, [navigateToScreen]);

  const handleNavigateToOrder = useCallback(() => {
    // Type-safe navigation with parameters
    navigateToScreen('OrderDetails', { orderId: '456' });
  }, [navigateToScreen]);

  const handleNavigateToSearch = useCallback(() => {
    // Type-safe navigation with optional parameters
    navigateToScreen('UniversalSearch', { query: 'fabric' });
  }, [navigateToScreen]);

  const handleCreateOrder = useCallback(() => {
    // Navigate with pre-filled parameters
    navigateToScreen('CreateOrder', { customerId: '123', productId: '789' });
  }, [navigateToScreen]);

  const handleNavigateToTab = useCallback((tabName: 'Dashboard' | 'Orders' | 'Products' | 'CRM' | 'Analytics') => {
    // Navigate to a specific tab
    navigateToTab(tabName);
  }, [navigateToTab]);

  const handleProtectedNavigation = useCallback(() => {
    // Navigate to a protected screen with authentication check
    navigateWithAuth('EmployeeManagement', undefined, true);
  }, [navigateWithAuth]);

  const handleGenerateDeepLink = useCallback(() => {
    // Generate and show deep link
    const link = commonLinks.customerDetails('123');
    Alert.alert('Deep Link Generated', link);
  }, []);

  const handleShareOrder = useCallback(() => {
    // Generate shareable deep link
    const link = commonLinks.orderDetails('456');
    Alert.alert('Share Order', `Share this link: ${link} );`
  }, []);

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Enhanced Navigation Examples</Text>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Stack Navigation</Text>
        
        <TouchableOpacity style={styles.button} onPress={handleNavigateToCustomer}>
          <Text style={styles.buttonText}>Go to Customer Details</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.button} onPress={handleNavigateToOrder}>
          <Text style={styles.buttonText}>Go to Order Details</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.button} onPress={handleNavigateToSearch}>
          <Text style={styles.buttonText}>Search with Filters</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.button} onPress={handleCreateOrder}>
          <Text style={styles.buttonText}>Create Order (Modal)</Text>
        </TouchableOpacity>
      </View>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Tab Navigation</Text>
        
        <View style={styles.buttonRow}>
          <TouchableOpacity 
            style={styles.button} 
            onPress={() => handleNavigateToTab('Dashboard')}>
            <Text style={styles.buttonText}>Dashboard</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.button} 
            onPress={() => handleNavigateToTab('Orders')}>
            <Text style={styles.buttonText}>Orders</Text>
          </TouchableOpacity>
        </View>
        
        <View style={styles.buttonRow}>
          <TouchableOpacity 
            style={styles.button} 
            onPress={() => handleNavigateToTab('Products')}>
            <Text style={styles.buttonText}>Products</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.button} 
            onPress={() => handleNavigateToTab('CRM')}>
            <Text style={styles.buttonText}>Customers</Text>
          </TouchableOpacity>
        </View>
        
        {hasRole('manager') && (
          <TouchableOpacity 
            style={styles.button} 
            onPress={() => handleNavigateToTab('Analytics')}>
            <Text style={styles.buttonText}>Analytics (Protected)</Text>
          </TouchableOpacity>
        )}
      </View>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Protected Navigation</Text>
        
        <TouchableOpacity style={styles.button} onPress={handleProtectedNavigation}>
          <Text style={styles.buttonText}>Employee Management {isAdmin ? '(Admin)' : '(Requires Auth)'}</Text>
        </TouchableOpacity>
      </View>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Deep Link Generation</Text>
        
        <TouchableOpacity style={styles.button} onPress={handleGenerateDeepLink}>
          <Text style={styles.buttonText}>Generate Customer Link</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.button} onPress={handleShareOrder}>
          <Text style={styles.buttonText}>Share Order Link</Text>
        </TouchableOpacity>
      </View>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Navigation Controls</Text>
        
        <TouchableOpacity 
          style={[styles.button, !canGoBack() && styles.disabledButton} 
          onPress={goBack}
          disabled={!canGoBack()}>
          <Text style={[styles.buttonText, !canGoBack() && styles.disabledText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};






export default React.memo(TypedNavigationExample);
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 24,
    textAlign: 'center',
  },
  section: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  button: {
    backgroundColor: '#007AFF',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  disabledText: {
    color: '#666',
  },
  tabRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderRadius: 6,
    marginHorizontal: 4,
    alignItems: 'center',
  },
  primaryTab: {
    backgroundColor: '#34C759',
  },
  secondaryTab: {
    backgroundColor: '#007AFF',
  },
  adminTab: {
    backgroundColor: '#FF9500',
    marginHorizontal: 0,
  },
  tabButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
});