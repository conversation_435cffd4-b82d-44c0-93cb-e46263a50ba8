import { Text   } from 'react-native-paper';
import React, { useCallback } from 'react';
import { TouchableOpacity, StyleSheet, ActivityIndicator   } from 'react-native';
// Removed LocalIcon import - using inline icons

// ENHANCED BUTTON - Better than basic TouchableOpacity, lighter than react-native-paper
const EnhancedButton = ({
  title,
  onPress,
  variant = 'primary', // primary, secondary, outline, ghost, danger
  size = 'medium', // small, medium, large
  icon,
  iconPosition = 'left', // left, right
  loading = false,
  disabled = false,
  fullWidth = false,
  style,
  textStyle,
  ...props
}) => {
  const getVariantStyles = () => {
    switch(variant) {
      case 'primary':
        return { backgroundColor: disabled ? '#9ca3af' : '#6366f1',
          borderColor: disabled ? '#9ca3af' : '#6366f1',
          borderWidth: 1,
         };
      case 'secondary':
        return { backgroundColor: disabled ? '#f3f4f6' : '#f8fafc',
          borderColor: disabled ? '#d1d5db' : '#e2e8f0',
          borderWidth: 1,
         };
      case 'outline':
        return { backgroundColor: 'transparent',
          borderColor: disabled ? '#d1d5db' : '#6366f1',
          borderWidth: 1,
         };
      case 'ghost':
        return { backgroundColor: 'transparent',
          borderColor: 'transparent',
          borderWidth: 0,
         };
      case 'danger':
        return { backgroundColor: disabled ? '#9ca3af' : '#ef4444',
          borderColor: disabled ? '#9ca3af' : '#ef4444',
          borderWidth: 1,
         };
      default:
        return { backgroundColor: '#6366f1',
          borderColor: '#6366f1',
          borderWidth: 1,
         };
    }
  };

  const getTextColor = () => {
    if(disabled) {
      return variant === 'secondary' ? '#9ca3af' : '#ffffff';
    }
    
    switch(variant) {
      case 'primary':
      case 'danger':
        return '#ffffff';
      case 'secondary':
        return '#374151';
      case 'outline':
        return '#6366f1';
      case 'ghost':
        return '#6366f1';
      default:
        return '#ffffff';
    }
  };

  const getSizeStyles = () => {
    switch(size) {
      case 'small':
        return { paddingHorizontal: 12,
          paddingVertical: 8,
          borderRadius: 8,
         };
      case 'medium':
        return { paddingHorizontal: 16,
          paddingVertical: 12,
          borderRadius: 10,
         };
      case 'large':
        return { paddingHorizontal: 20,
          paddingVertical: 16,
          borderRadius: 12,
         };
      default:
        return { paddingHorizontal: 16,
          paddingVertical: 12,
          borderRadius: 10,
         };
    }
  };

  const getTextSize = () => {
    switch(size) {
      case 'small':
        return 14;
      case 'medium':
        return 16;
      case 'large':
        return 18;
      default:
        return 16;
    }
  };

  const getIconSize = () => {
    switch(size) {
      case 'small':
        return 16;
      case 'medium':
        return 18;
      case 'large':
        return 20;
      default:
        return 18;
    }
  };

  const variantStyles = getVariantStyles();
  const sizeStyles = getSizeStyles();
  const textColor = getTextColor();
  const textSize = getTextSize();
  const iconSize = getIconSize();

  const renderContent = () => {
    if(loading) {
      return (
        <ActivityIndicator 
          size="small" 
          color={textColor} 
        />
      );
    }

    const iconElement = icon ? (
      <Text
        style={[
          { fontSize: iconSize, color: textColor },
          iconPosition === 'right' ? styles.iconRight : styles.iconLeft}>
        {icon === 'add' ? '➕' : icon === 'edit' ? '✏️' : icon === 'delete' ? '🗑️' : icon === 'save' ? '💾' : '❓'}
      </Text>
    ) : null;

    const textElement = title ? (
      <Text 
        style={[
          styles.text,
          { color: textColor, fontSize: textSize },
          textStyle}>
        {title}
      </Text>
    ) : null;

    if(iconPosition === 'right') {
      return (
        <>
          {textElement}
          {iconElement}
        </>
      );
    } else {
      return (
        <>
          {iconElement}
          {textElement}
        </>
      );
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.button,
        variantStyles,
        sizeStyles,
        fullWidth && styles.fullWidth,
        disabled && styles.disabled,
        style
      }
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.7}
      {...props}>
      {renderContent()}
    </TouchableOpacity>
  );
};






export default React.memo(EnhancedButton);

const styles = StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  fullWidth: {
    width: '100%',
  },
  disabled: {
    opacity: 0.6,
    shadowOpacity: 0,
    elevation: 0,
  },
  text: {
    fontWeight: '600',
    textAlign: 'center',
  },
  iconLeft: {
    marginRight: 8,
  },
  iconRight: {
    marginLeft: 8,
  },
});