import { Text } from 'react-native-paper';
// PAYMENT MODAL COMPONENT - Record payments and generate receipts
import React, { useState, useCallback } from 'react';
import { View, Text, StyleSheet, Alert, Modal } from 'react-native';
import EnhancedCard from './EnhancedCard';
import EnhancedButton from './EnhancedButton';
import EnhancedInput from './EnhancedInput';
// Removed LocalIcon import - using inline icons
import PrintService from '../services/PrintService';

const PaymentModal = ({ 
  visible, 
  onClose, 
  order,
  onPaymentRecorded 
}) => {
  const [amount, setAmount] = useState('');
  const [paymentMethod, setPaymentMethod] = useState('Cash');
  const [isProcessing, setIsProcessing] = useState(false);

  const paymentMethods = ['Cash', 'Card', 'UPI', 'Bank Transfer', 'Cheque'];

  const handlePayment = useCallback(async () => {
    try {
      const paymentAmount = parseFloat(amount);
      
      if (!paymentAmount || paymentAmount <= 0) {
        Alert.alert('Invalid Amount', 'Please enter a valid payment amount');
        return;
      }

      if (paymentAmount > (order.amount - (order.advance_paid || 0))) {
        Alert.alert('Amount Too High', 'Payment amount cannot exceed the remaining balance');
        return;
      }

      setIsProcessing(true);

      // Generate receipt
      const receipt = await PrintService.generateReceipt(order, paymentAmount, paymentMethod);
      
      if (receipt) {
        // Call the callback to update the order
        if (onPaymentRecorded) {
          onPaymentRecorded(paymentAmount, paymentMethod, receipt);
        }
        
        // Close modal
        handleClose();
      }
    } catch (error) {
      console.error('Payment processing failed:', error);
      Alert.alert('Payment Error', 'Failed to process payment');
    } finally {
      setIsProcessing(false);
    }
  }, [amount, order, paymentMethod, onPaymentRecorded]);

  const handleClose = useCallback(() => {
    setAmount('');
    setPaymentMethod('Cash');
    setIsProcessing(false);
    onClose();
  }, [onClose]);

  if (!order) {
    return null;
  }

  const remainingBalance = order.amount - (order.advance_paid || 0);

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleClose}>
      <View style={styles.container}>
        <EnhancedCard variant="flat" padding="large" style={styles.header}>
          <View style={styles.headerContent}>
            <View style={styles.titleContainer}>
              <Text style={styles.title}>Record Payment</Text>
              <Text style={styles.subtitle}>Order #{order.id} - {order.customer_name}</Text>
            </View>
            <EnhancedButton
              variant="ghost"
              size="small"
              icon="close"
              onPress={handleClose}
            />
          </View>
        </EnhancedCard>

        <View style={styles.content}>
          {/* Order Summary */}
          <EnhancedCard variant="default" margin="medium">
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Total Amount:</Text>
              <Text style={styles.summaryValue}>₹{order.amount.toLocaleString()}</Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Advance Paid:</Text>
              <Text style={styles.summaryValue}>₹{(order.advance_paid || 0).toLocaleString()}</Text>
            </View>
            <View style={[styles.summaryRow, styles.balanceRow}>
              <Text style={styles.balanceLabel}>Remaining Balance:</Text>
              <Text style={styles.balanceValue}>₹{remainingBalance.toLocaleString()}</Text>
            </View>
          </EnhancedCard>

          {/* Payment Form */}
          <EnhancedCard variant="default" margin="medium">
            <Text style={styles.sectionTitle}>Payment Details</Text>
            
            <EnhancedInput
              label="Payment Amount"
              placeholder="Enter amount"
              value={amount}
              onChangeText={setAmount}
              keyboardType="numeric"
              leftIcon="currency"
              style={styles.input}
            />

            <Text style={styles.methodLabel}>Payment Method</Text>
            <View style={styles.methodContainer}>
              {paymentMethods.map((method) => (
                <EnhancedButton
                  key={method}
                  variant={paymentMethod === method ? "primary" : "outline"}
                  size="small"
                  title={method}
                  onPress={() => setPaymentMethod(method)}
                  style={styles.methodButton}
                />
              ))}
            </View>
          </EnhancedCard>

          {/* Actions */}
          <View style={styles.actions}>
            <EnhancedButton
              variant="outline"
              size="large"
              title="Cancel"
              onPress={handleClose}
              style={styles.actionButton}
            />
            <EnhancedButton
              variant="primary"
              size="large"
              title="Record Payment"
              icon="check"
              loading={isProcessing}
              onPress={handlePayment}
              disabled={!amount || isProcessing}
              style={styles.actionButton}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default PaymentModal;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    color: '#6b7280',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  summaryLabel: {
    fontSize: 14,
    color: '#6b7280',
  },
  summaryValue: {
    fontSize: 14,
    color: '#1f2937',
    fontWeight: '500',
  },
  balanceRow: {
    borderBottomWidth: 0,
    paddingTop: 12,
    marginTop: 8,
    borderTopWidth: 2,
    borderTopColor: '#6366f1',
  },
  balanceLabel: {
    fontSize: 16,
    color: '#1f2937',
    fontWeight: 'bold',
  },
  balanceValue: {
    fontSize: 18,
    color: '#6366f1',
    fontWeight: 'bold',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 16,
  },
  input: {
    marginBottom: 16,
  },
  methodLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 12,
  },
  methodContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  methodButton: {
    minWidth: 80,
  },
  actions: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 20,
  },
  actionButton: {
    flex: 1,
  },
});