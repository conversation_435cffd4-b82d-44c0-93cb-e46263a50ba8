/**
 * Order Stats Component
 * Display key order statistics and metrics
 */

import React, { memo } from 'react';
import {
  View,
  StyleSheet,
  ViewStyle,
} from 'react-native';
import { Surface } from 'react-native-paper';
import { Text } from 'react-native-paper';
import LocalIcon from '../LocalIcon';
import { useTheme } from '../../context/ThemeContext';

interface OrderStatsProps {
  stats: OrderStatsType;
  overdueCount: number;
  dueSoonCount: number;
  style?: ViewStyle;
}

interface StatCardProps {
  icon: string;
  label: string;
  value: string | number;
  color: string;
  subtitle?: string;
}

const StatCard: React.FC<StatCardProps> = memo(({
  icon,
  label,
  value,
  color,
  subtitle,
}) => {
  const { theme } = useTheme();

  return (
    <Surface style={styles.statCard} elevation={1}>
      <View style={styles.statHeader}>
        <LocalIcon name={icon} size={20} color={color} />
        <Text variant="bodySmall" style={styles.statLabel}>
          {label}
        </Text>
      </View>
      <Text variant="titleLarge" style={styles.statValue}>
        {value}
      </Text>
      {subtitle && (
        <Text variant="bodySmall" style={styles.statSubtitle}>
          {subtitle}
        </Text>
      )}
    </Surface>
  );
});

const OrderStats: React.FC<OrderStatsProps> = memo(({
  stats,
  overdueCount,
  dueSoonCount,
  style,
}) => {
  const { theme } = useTheme();

  // Format currency
  const formatCurrency = (amount: number) => {
    if (amount >= 100000) {
      return `₹${(amount / 100000).toFixed(1)}L`;
    } else if (amount >= 1000) {
      return `₹${(amount / 1000).toFixed(1)}K`;
    } else {
      return `₹${amount.toLocaleString()}`;
    }
  };

  // Format percentage
  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  return (
    <View style={[styles.container, style]}>
      {/* Top Row - Key Metrics */}
      <View style={styles.row}>
        <StatCard
          icon="clipboard-text"
          label="Total Orders"
          value={stats.total}
          color={theme.colors.primary}
        />
        
        <StatCard
          icon="currency-inr"
          label="Total Revenue"
          value={formatCurrency(stats.revenue.total)}
          color="#4CAF50"
          subtitle={`Avg: ${formatCurrency(stats.averageOrderValue)}`}
        />
      </View>

      {/* Second Row - Status & Alerts */}
      <View style={styles.row}>
        <StatCard
          icon="check-circle"
          label="Completion Rate"
          value={formatPercentage(stats.completionRate)}
          color="#2E7D32"
        />
        
        <StatCard
          icon="alert-circle"
          label="Needs Attention"
          value={overdueCount + dueSoonCount}
          color={overdueCount > 0 ? theme.colors.error : '#FF9800'}
          subtitle={
            overdueCount > 0 
              ? `${overdueCount} overdue`
              : dueSoonCount > 0 
                ? `${dueSoonCount} due soon`
                : 'All on track'
          }
        />
      </View>

      {/* Third Row - Payment Status */}
      <View style={styles.row}>
        <StatCard
          icon="cash"
          label="Paid Amount"
          value={formatCurrency(stats.revenue.paid)}
          color="#4CAF50"
        />
        
        <StatCard
          icon="clock-outline"
          label="Pending Amount"
          value={formatCurrency(stats.revenue.pending)}
          color={stats.revenue.pending > 0 ? '#FF9800' : theme.colors.outline}
        />
      </View>
    </View>
  );
});

StatCard.displayName = 'StatCard';
OrderStats.displayName = 'OrderStats';

export default OrderStats;

const styles = StyleSheet.create({
  container: {
    paddingVertical: 8,
  },
  row: {
    flexDirection: 'row',
    marginBottom: 8,
    gap: 8,
  },
  statCard: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
  },
  statHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  statLabel: {
    marginLeft: 6,
    fontSize: 12,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  statValue: {
    fontWeight: '700',
    marginBottom: 2,
  },
  statSubtitle: {
    fontSize: 11,
  },
});