/**
 * Order Details Modal
 * Comprehensive order details view with actions
 */

import React, { memo, useState, useCallback, useMemo } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import { Modal as PaperModal, Text as PaperText, Button as PaperButton, IconButton, Divider, Surface as PaperSurface, ActivityIndicator, ButtonProps, TextProps, SurfaceProps, ModalProps } from 'react-native-paper';
import LocalIcon from '../LocalIcon';
import GlobalBadge from '../GlobalBadge';
import { useTheme } from '../../context/ThemeContext';
import { ThemeContextType } from '../../types/theme';
import { Order, ORDER_STATUS_LABELS, ORDER_TYPE_LABELS, PRIORITY_LABELS, OrderStatus } from '../../types/order';
import { useOrder } from '../../hooks/useOrders';
import { SPACING, BORDER_RADIUS, TYPOGRAPHY } from '../../theme/designTokens';

// Custom Button wrapper to fix TypeScript issues
type CustomButtonProps = Omit<ButtonProps, 'children'> & { children?: React.ReactNode };
const Button = ({ children, ...rest }: CustomButtonProps) => {
  return <PaperButton {...rest}>{children}</PaperButton>;
};

// Custom Text wrapper to fix TypeScript issues
type CustomTextProps = Omit<TextProps, 'children'> & { children?: React.ReactNode };
const Text = ({ children, ...rest }: CustomTextProps) => {
  return <PaperText {...rest}>{children}</PaperText>;
};

// Custom Surface wrapper to fix TypeScript issues
type CustomSurfaceProps = Omit<SurfaceProps, 'children'> & { children?: React.ReactNode };
const Surface = ({ children, ...rest }: CustomSurfaceProps) => {
  return <PaperSurface {...rest}>{children}</PaperSurface>;
};



// Custom Modal wrapper to fix TypeScript issues
type CustomModalProps = Omit<ModalProps, 'children'> & { children?: React.ReactNode };
const Modal = ({ children, ...rest }: CustomModalProps) => {
  return <PaperModal {...rest}>{children}</PaperModal>;
};

interface OrderDetailsModalProps {
  visible: boolean;
  order: Order | null;
  onDismiss: () => void;
  onEdit: (order: Order) => void;
  onRefresh: () => void;
}

// Helper components to keep the main render clean
const InfoSection: React.FC<{ title: string; children: React.ReactNode}> = ({ title, children }) => {
  const { theme } = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  return (
    <Surface style={styles.section} elevation={0}>
      <Text style={styles.sectionTitle}>{title}</Text>
      {children}
    </Surface>
  );
};

const InfoRow: React.FC<{ icon: string; text: string | undefined}> = ({ icon, text }) => {
  const { theme } = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  if (!text) return null;
  return (
    <View style={styles.infoRow}>
      <LocalIcon name={icon} size={20} color={theme.colors.onSurfaceVariant} />
      <Text style={styles.infoText}>{text}</Text>
    </View>
  );
};

const OrderDetailsModal = memo((props: OrderDetailsModalProps) => {
  const {
  visible,
  order,
  onDismiss,
  onEdit,
  onRefresh,
  } = props;
  const { theme } = useTheme();
  const { updateStatus, addPayment } = useOrder(order?.id || '');
  const [updating, setUpdating] = useState(false);
  const styles = useMemo(() => createStyles(theme), [theme]);

  if (!order) return null;

  const formatCurrency = (amount: number): string => `৳${(amount || 0).toLocaleString('en-IN')}`;
  const formatDate = (dateString: string): string => new Date(dateString).toLocaleDateString('en-GB', { day: '2-digit', month: 'long', year: 'numeric' });

  // FIXED: The `newStatus` parameter is now correctly and safely typed as OrderStatus
  const handleStatusUpdate = useCallback(async (newStatus: OrderStatus): Promise<void> => {
    setUpdating(true);
    try {
      await updateStatus(newStatus);
      onRefresh();
      Alert.alert('Success', 'Order status updated successfully');
    } catch (error) {
      console.error('Error updating status:', error);
      Alert.alert('Error', 'Failed to update order status');
    } finally {
      setUpdating(false);
    }
  }, [updateStatus, onRefresh]);

  const handleAddPayment = useCallback((): void => {
    Alert.prompt(
      'Add Payment',
      `Enter payment amount (Balance: ${formatCurrency(order.balanceAmount)})`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Add', onPress: async (amountStr: string) => {
            const amount = parseFloat(amountStr);
            if (!amountStr || isNaN(amount) || amount <= 0) {
              Alert.alert('Error', 'Please enter a valid positive amount.');
              return;
            }
            setUpdating(true);
            try {
              await addPayment(amount, 'cash', 'Payment from details modal');
              onRefresh();
            } catch (error) {
              Alert.alert('Error', 'Failed to add payment');
            } finally {
              setUpdating(false);
            }
        }},
      ],
      'plain-text', '', 'numeric'
    );
  }, [order.balanceAmount, addPayment, onRefresh]);

  // FIXED: statusFlow is now strongly typed to ensure it returns an array of OrderStatus
  const nextStatuses = useMemo((): OrderStatus[] => {
    const statusFlow: Record<OrderStatus, OrderStatus[]> = {
      draft: ['confirmed', 'cancelled'],
      confirmed: ['measurements_taken', 'on_hold', 'cancelled'],
      measurements_taken: ['cutting', 'on_hold'],
      cutting: ['stitching', 'on_hold'],
      stitching: ['first_fitting', 'on_hold'],
      first_fitting: ['adjustments', 'final_fitting'],
      adjustments: ['final_fitting'],
      final_fitting: ['ready'],
      ready: ['delivered', 'on_hold'],
      delivered: ['completed'],
      completed: [],
      cancelled: [],
      on_hold: ['confirmed', 'cutting', 'stitching', 'cancelled'],
    };
    return statusFlow[order.status] || [];
  }, [order.status]);

  const getStatusVariant = (status: string): 'success' | 'info' | 'error' | 'default' | 'warning' | 'primary' | 'secondary' => {
    const s = status?.toLowerCase();
    if (s === 'completed' || s === 'delivered') return 'success';
    if (s === 'ready') return 'info';
    if (s === 'cancelled') return 'error';
    return 'default';
  };

  return (
    <Modal visible={visible} onDismiss={onDismiss} contentContainerStyle={styles.modal}>
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Text style={styles.headerTitle}>{String(order.orderNumber)}</Text>
          {order.isUrgent && <LocalIcon name="fire" size={20} color={theme.colors.error} style={styles.urgentIcon} />}
        </View>
        <IconButton icon="close" onPress={onDismiss} />
      </View>

      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.statusSection}>
          <GlobalBadge variant={getStatusVariant(order.status)} style={{}} textStyle={{}>{ORDER_STATUS_LABELS[order.status}</GlobalBadge>
          <GlobalBadge variant="default" style={{}} textStyle={{}>{ORDER_TYPE_LABELS[order.type}</GlobalBadge>
          {order.priority !== 'normal' && <GlobalBadge variant={order.priority === 'urgent' ? 'error' : 'warning'} style={{}} textStyle={{}>{PRIORITY_LABELS[order.priority}</GlobalBadge>}
        </View>
        <Divider style={styles.divider} />
        
        <InfoSection title="Customer Information">
          <InfoRow icon="account" text={order.customerName} />
          <InfoRow icon="phone" text={order.customerPhone} />
          {order.customerEmail && <InfoRow icon="email" text={order.customerEmail} />}
        </InfoSection>

        <InfoSection title={`Order Items (${order.items.length})`}>
          {order.items.map((item) => (
            <View key={item.id} style={styles.itemRow}>
              <View style={styles.itemInfo}><Text style={styles.itemName}>{item.garmentType}</Text><Text style={styles.itemDetails}>Qty: {item.quantity}</Text></View>
              <Text style={styles.itemTotal}>{formatCurrency(item.price * item.quantity)}</Text>
            </View>
          ))}
        </InfoSection>

        <InfoSection title="Payment">
          <View style={styles.pricingRow}><Text style={styles.infoLabel}>Total Amount</Text><Text style={styles.totalAmount}>{formatCurrency(order.total)}</Text></View>
          <View style={styles.pricingRow}><Text style={styles.infoLabel}>Paid Amount</Text><Text style={[styles.infoValue, { color: '#4CAF50' }>{formatCurrency(order.paidAmount)}</Text></View>
          <View style={styles.pricingRow}><Text style={styles.infoLabel}>Balance</Text><Text style={[styles.infoValue, { color: order.balanceAmount > 0 ? theme.colors.error : '#4CAF50' }>{formatCurrency(order.balanceAmount)}</Text></View>
          {order.balanceAmount > 0 && (
            <Button 
              mode="outlined" 
              onPress={handleAddPayment} 
              disabled={updating} 
              style={styles.paymentButton}
              icon="cash"
            >
              Add Payment
            </Button>
          )}
        </InfoSection>

        {nextStatuses.length > 0 && (
          <InfoSection title="Update Status">
            {updating ? <ActivityIndicator animating={true} color={theme.colors.primary} /> : (
              <View style={styles.statusActions}>
                {nextStatuses.map(status => (
                  <Button 
                    key={status} 
                    mode="outlined" 
                    onPress={() => handleStatusUpdate(status)} 
                    style={styles.statusButton}>
                    {ORDER_STATUS_LABELS[status]
                  </Button>
                ))}
              </View>
            )}
          </InfoSection>
        )}

        <View style={styles.actions}>
          <Button 
            mode="outlined" 
            onPress={() => onEdit(order)} 
            style={styles.actionButton} 
            icon="pencil"
          >
            Edit
          </Button>
          <Button 
            mode="contained" 
            onPress={onDismiss} 
            style={styles.actionButton}>
            Close
          </Button>
        </View>
      </ScrollView>
    </Modal>
  );
});

OrderDetailsModal.displayName = 'OrderDetailsModal';
export default OrderDetailsModal;

// This function creates the stylesheet object, allowing it to be memoized and use the theme from props.
const createStyles = (theme: any) => StyleSheet.create({
  modal: { margin: 10, borderRadius: BORDER_RADIUS.lg, padding: SPACING.md, maxHeight: '95%', backgroundColor: theme.colors.surface },
  header: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: SPACING.md },
  headerLeft: { flexDirection: 'row', alignItems: 'center', flex: 1 },
  headerTitle: { fontSize: 24, fontWeight: '600', color: theme.colors.onSurface },
  urgentIcon: { marginLeft: SPACING.sm },
  statusSection: { flexDirection: 'row', flexWrap: 'wrap', gap: SPACING.sm, marginBottom: SPACING.md },
  divider: { marginVertical: SPACING.md, backgroundColor: theme.colors.outlineVariant },
  section: { padding: SPACING.md, borderRadius: BORDER_RADIUS.md, marginBottom: SPACING.md, backgroundColor: theme.colors.surfaceVariant },
  sectionTitle: { fontSize: 16, fontWeight: '600', marginBottom: SPACING.md, color: theme.colors.onSurface },
  infoRow: { flexDirection: 'row', alignItems: 'center', marginBottom: SPACING.sm },
  infoText: { fontSize: 14, marginLeft: SPACING.md, flex: 1, color: theme.colors.onSurface },
  itemRow: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start', paddingBottom: SPACING.sm, marginBottom: SPACING.sm, borderBottomWidth: 1, borderBottomColor: theme.colors.outlineVariant },
  itemInfo: { flex: 1 },
  itemName: { fontSize: 14, fontWeight: '600', color: theme.colors.onSurface },
  itemDetails: { color: theme.colors.onSurfaceVariant },
  itemTotal: { fontSize: 16, fontWeight: '700' },
  pricingRow: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: SPACING.sm },
  totalLabel: { fontSize: 18, fontWeight: '700' },
  totalAmount: { fontSize: 18, fontWeight: '700', color: theme.colors.primary },
  paymentButton: { marginTop: SPACING.md },
  infoLabel: { fontSize: 14, color: theme.colors.onSurfaceVariant },
  infoValue: { fontSize: 14, color: theme.colors.onSurface, fontWeight: '600' },
  dateRow: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: SPACING.sm },
  statusActions: { flexDirection: 'row', flexWrap: 'wrap', gap: SPACING.sm },
  statusButton: { marginBottom: SPACING.xs },
  actions: { flexDirection: 'row', justifyContent: 'space-around', marginTop: SPACING.lg, gap: SPACING.md },
  actionButton: { flex: 1 },
});