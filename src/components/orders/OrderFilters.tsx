/**
 * Order Filters Component
 * Advanced filtering and sorting for orders
 */

import React, { memo, useState, useCallback, useMemo } from 'react';
import { View, StyleSheet, ScrollView   } from 'react-native';
import { Text, Button, Chip, Divider, Surface   } from 'react-native-paper';
import { useTheme   } from '../../context/ThemeContext';
import { OrderSortOptions,
  OrderFilters as IOrderFilters,
  ORDER_STATUS_LABELS,
  ORDER_TYPE_LABELS,
  PRIORITY_LABELS,
  OrderStatus,
  OrderType,
  Priority,
  PaymentStatus
  } from '../../types/order';
import { SPACING, BORDER_RADIUS, TYPOGRAPHY   } from '../../theme/designTokens';

interface OrderFiltersProps {
  filters: IOrderFilters;
  sort: OrderSortOptions;
  onFiltersChange: (filters: Partial<IOrderFilters>) => void;
  onSortChange: (sort: OrderSortOptions) => void;
  onClose: () => void;
}

const OrderFilters: React.FC<OrderFiltersProps> = memo(({
  filters,
  sort,
  onFiltersChange,
  onSortChange,
  onClose}) => {
  const { theme  } = useTheme();
  const [localFilters, setLocalFilters] = useState<IOrderFilters>(filters);
  const [localSort, setLocalSort] = useState<OrderSortOptions>(sort);

  // FIXED: Created type-safe toggle functions for each filter category
  const createToggleHandler = <T extends 'status' | 'type' | 'priority' | 'paymentStatus'>(key: T) => {
    type FilterValue = IOrderFilters[T] extends (infer U)[] ? U : never;
    return (value: FilterValue) => {
      setLocalFilters((prev: IOrderFilters) => {
        const currentValues = (prev[key] || []) as FilterValue[];
        const newValues = currentValues.includes(value)
          ? currentValues.filter(v => v !== value)
          : [...currentValues, value];
        const updated = { ...prev };
        if(newValues.length > 0) {
          updated[key] = newValues;
        } else {
          delete updated[key];
        }
        return updated;
      });
    };
  };

  const toggleStatusFilter = createToggleHandler('status');
  const toggleTypeFilter = createToggleHandler('type');
  const togglePriorityFilter = createToggleHandler('priority');
  const togglePaymentStatusFilter = createToggleHandler('paymentStatus');

  const handleApply = useCallback(() => {
    onFiltersChange(localFilters);
    onSortChange(localSort);
    onClose();
  }, [localFilters, localSort, onFiltersChange, onSortChange, onClose]);

  const handleClear = useCallback(() => {
    const clearedFilters = {};
    const defaultSort = { field: 'createdAt' as const, direction: 'desc' as const };
    setLocalFilters(clearedFilters);
    setLocalSort(defaultSort);
    onFiltersChange(clearedFilters);
    onSortChange(defaultSort);
  }, [onFiltersChange, onSortChange]);

  const sortOptions = [
    { value: 'createdAt-desc', label: 'Newest First' }, { value: 'createdAt-asc', label: 'Oldest First' },
    { value: 'dueDate-asc', label: 'Due Date (Soon)' }, { value: 'dueDate-desc', label: 'Due Date (Later)' },
    { value: 'total-desc', label: 'Amount (High)' }, { value: 'total-asc', label: 'Amount (Low)' },
    { value: 'customerName-asc', label: 'Customer A-Z' }];
  const currentSortValue = `${localSort.field} -${localSort.direction}`;

  const styles = useMemo(() => StyleSheet.create({
    container: { flex: 1, padding: SPACING.md },
    header: { marginBottom: SPACING.lg, alignItems: 'center' },
    headerTitle: {
      fontSize: TYPOGRAPHY.youtube.h2.fontSize,
      fontWeight: TYPOGRAPHY.youtube.h2.fontWeight as '600',
      lineHeight: TYPOGRAPHY.youtube.h2.lineHeight,
      color: theme.colors.onSurface},
    section: { padding: SPACING.md, borderRadius: BORDER_RADIUS.lg, marginBottom: SPACING.md, backgroundColor: theme.colors.surfaceVariant },
    sectionTitle: {
      fontSize: TYPOGRAPHY.youtube.h4.fontSize,
      fontWeight: TYPOGRAPHY.youtube.h4.fontWeight as '600',
      lineHeight: TYPOGRAPHY.youtube.h4.lineHeight,
      color: theme.colors.onSurface,
      marginBottom: SPACING.md},
    chipContainer: { flexDirection: 'row', flexWrap: 'wrap', gap: SPACING.sm },
    chip: { marginBottom: SPACING.xs },
    divider: { marginVertical: SPACING.md, backgroundColor: theme.colors.outlineVariant },
    actions: { flexDirection: 'row', justifyContent: 'space-between', marginTop: SPACING.lg, gap: SPACING.md },
    actionButton: { flex: 1 }}), [theme.colors]);

  return (
    <ScrollView contentContainerStyle={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.header}><Text style={styles.headerTitle}>Filter & Sort Orders</Text></View>
      <Surface style={styles.section} elevation={0}>
        <Text style={styles.sectionTitle}>Sort By</Text>
        <View style={styles.chipContainer}>
          {sortOptions.map(option => (
            <Chip key={option.value} selected={currentSortValue === option.value} onPress={() => { const [field, direction] = option.value.split('-'); setLocalSort({ field: field as OrderSortOptions['field'], direction: direction as OrderSortOptions['direction'}); }} style={styles.chip}>{option.label}</Chip>
          ))}
        </View>
      </Surface>
      <Divider style={styles.divider} />
      <Surface style={styles.section} elevation={0}>
        <Text style={styles.sectionTitle}>Status</Text>
        <View style={styles.chipContainer}>
          {(Object.keys(ORDER_STATUS_LABELS) as Array<OrderStatus>).map(key => <Chip key={key} selected={localFilters.status?.includes(key)} onPress={() => toggleStatusFilter(key)} style={styles.chip}>{ORDER_STATUS_LABELS[key]</Chip>)}
        </View>
      </Surface>
      <Divider style={styles.divider} />
      <Surface style={styles.section} elevation={0}>
        <Text style={styles.sectionTitle}>Order Type</Text>
        <View style={styles.chipContainer}>
          {(Object.keys(ORDER_TYPE_LABELS) as Array<OrderType>).map(key => <Chip key={key} selected={localFilters.type?.includes(key)} onPress={() => toggleTypeFilter(key)} style={styles.chip}>{ORDER_TYPE_LABELS[key]</Chip>)}
        </View>
      </Surface>
      <Divider style={styles.divider} />
      <Surface style={styles.section} elevation={0}>
        <Text style={styles.sectionTitle}>Priority</Text>
        <View style={styles.chipContainer}>
          {(Object.keys(PRIORITY_LABELS) as Array<Priority>).map(key => <Chip key={key} selected={localFilters.priority?.includes(key)} onPress={() => togglePriorityFilter(key)} style={styles.chip}>{PRIORITY_LABELS[key]</Chip>)}
        </View>
      </Surface>
      <Divider style={styles.divider} />
      <Surface style={styles.section} elevation={0}>
        <Text style={styles.sectionTitle}>Payment Status</Text>
        <View style={styles.chipContainer}>
          {(['unpaid', 'partial', 'paid'] as Array<PaymentStatus>).map(key => <Chip key={key} selected={localFilters.paymentStatus?.includes(key)} onPress={() => togglePaymentStatusFilter(key)} style={styles.chip}>{key.charAt(0).toUpperCase() + key.slice(1)}</Chip>)}
        </View>
      </Surface>
      <View style={styles.actions}>
        <Button mode="outlined" onPress={handleClear} style={styles.actionButton}>Clear All</Button>
        <Button mode="contained" onPress={handleApply} style={styles.actionButton}>Apply Filters</Button>
      </View>
    </ScrollView>
  );
});

OrderFilters.displayName = 'OrderFilters';

export default OrderFilters;