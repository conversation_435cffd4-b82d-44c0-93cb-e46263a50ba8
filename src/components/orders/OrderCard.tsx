/**
 * Order Card Component
 * Optimized card for displaying order information
 */

import React, { memo } from 'react';
import { View,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
  } from 'react-native';
import { Card, Text, IconButton, Menu, Divider   } from 'react-native-paper';
import { useTheme   } from '../../context/ThemeContext';
import { Order, ORDER_STATUS_LABELS, ORDER_TYPE_LABELS, PRIORITY_LABELS   } from '../../types/order';
import GlobalBadge from '../GlobalBadge';
import LocalIcon from '../LocalIcon';

// Assuming these are defined in your design tokens
const SPACING = { sm: 8, md: 16 };
const BORDER_RADIUS = { lg: 12 };

// Assuming these components exist
const BusinessLocalIcon = (props) => <LocalIcon {...props} />;
const StatusLocalIcon = (props) => <LocalIcon {...props} />;

interface OrderCardProps {
  order: Order;
  onPress: () => void;
  onAction: (action: string) => void;
  style?: ViewStyle;
}

const OrderCard: React.FC<OrderCardProps> = memo(({
  order,
  onPress,
  onAction,
  style,
}) => {
  const { theme  } = useTheme();
  const [menuVisible, setMenuVisible] = React.useState(false);

  const daysUntilDue = React.useMemo(() => {
    if (!order.dueDate) return null;
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const dueDate = new Date(order.dueDate);
    dueDate.setHours(0, 0, 0, 0);
    const diffTime = dueDate.getTime() - today.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }, [order.dueDate]);
  
  // FIXED: Added functions to map status/priority to GlobalBadge variants
  const getStatusVariant = (status: string) => {
    const s = status?.toLowerCase();
    if (s === 'completed' || s === 'delivered') return 'success';
    if (s === 'ready') return 'info';
    if (s === 'cancelled') return 'error';
    if (s === 'on_hold') return 'default';
    if (s?.includes('fitting') || s?.includes('adjustments')) return 'warning';
    return 'secondary'; // for in progress, cutting, etc.
  };

  const getPriorityVariant = (priority: string) => {
      if (priority === 'urgent') return 'error';
      if (priority === 'high') return 'warning';
      return 'default';
  };

  const formatCurrency = (amount: number) => `৳${(amount || 0).toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  const formatDate = (dateString: string) => new Date(dateString).toLocaleDateString('en-GB', { day: '2-digit', month: 'short' });

  const getDueDateStatus = React.useMemo(() => {
    if (daysUntilDue === null) return { label: '', color: theme.colors.outline  };
    if (daysUntilDue < 0) return { label: `${Math.abs(daysUntilDue)} d` overdue`, color: theme.colors.error };
    if (daysUntilDue === 0) return { label: 'Due today', color: '#FF9800'  };
    return { label: `${daysUntilDue} d left`, color: daysUntilDue <= 3 ? '#FF9800' : theme.colors.onSurfaceVariant };
  }, [daysUntilDue, theme.colors]);

  const dueDateStatus = getDueDateStatus;

  return (
    <Card style={[styles.card, style} onPress={onPress} mode="elevated" elevation={1}>
      <Card.Content style={styles.content}>
        <View style={styles.header}>
          <View style={styles.headerLeft}>
            <Text variant="titleMedium" style={styles.orderNumber}>{order.orderNumber}</Text>
            {order.isUrgent && <StatusLocalIcon name="fire" size={16} color={theme.colors.error} style={styles.urgentIcon} />}
          </View>
          <Menu visible={menuVisible} onDismiss={() => setMenuVisible(false)} anchor={<IconButton icon="dots-vertical" size={20} onPress={() => setMenuVisible(true)} />}>
            <Menu.Item onPress={() => { setMenuVisible(false); onAction('edit'); } title="Edit" leadingIcon="pencil" />
            <Menu.Item onPress={() => { setMenuVisible(false); onAction('duplicate'); } title="Duplicate" leadingIcon="content-copy" />
            <Divider />
            <Menu.Item onPress={() => { setMenuVisible(false); onAction('delete'); } title="Archive" leadingIcon="archive" />
          </Menu>
        </View>

        <View style={styles.customerRow}>
          <BusinessLocalIcon name="account" size={16} color={theme.colors.onSurfaceVariant} />
          <Text style={styles.customerName} numberOfLines={1}>{order.customerName}</Text>
          <Text style={styles.customerPhone}>{order.customerPhone}</Text>
        </View>

        <View style={styles.itemsRow}>
          <BusinessLocalIcon name="hanger" size={16} color={theme.colors.onSurfaceVariant} />
          <Text style={styles.itemsText} numberOfLines={1}>
            {order.items.length} item{order.items.length !== 1 ? 's' : ''} • {ORDER_TYPE_LABELS[order.type] || 'Custom'}
          </Text>
        </View>

        {/* FIXED: Using GlobalBadge with 'children' and 'variant' props correctly */}
        <View style={styles.statusRow}>
          <GlobalBadge variant={getStatusVariant(order.status)} size="small" compact>
            {ORDER_STATUS_LABELS[order.status] || order.status}
          </GlobalBadge>
          
          {order.priority !== 'normal' && (
            <GlobalBadge variant={getPriorityVariant(order.priority)} size="small" compact>
              {PRIORITY_LABELS[order.priority] || order.priority}
            </GlobalBadge>
          )}
        </View>

        <View style={styles.bottomRow}>
          <View style={styles.priceSection}>
            <Text style={[styles.totalAmount, { color: theme.colors.primary }}}}>{formatCurrency(order.total)}</Text>
            {(order.balanceAmount > 0) && <Text style={[styles.balanceText, { color: theme.colors.error }}}}>{formatCurrency(order.balanceAmount)} due</Text>}
          </View>
          {order.dueDate && (
            <View style={styles.dateSection}>
              <Text style={[styles.dueDateLabel, { color: theme.colors.onSurfaceVariant }}}}>Due {formatDate(order.dueDate)}</Text>
              <Text style={[styles.dueDateStatus, { color: dueDateStatus.color }}}}>{dueDateStatus.label}</Text>
            </View>
          )}
        </View>
      </Card.Content>
    </Card>
  );
});

OrderCard.displayName = 'OrderCard';

export default OrderCard;

const styles = StyleSheet.create({
  card: { marginVertical: SPACING.sm, borderRadius: BORDER_RADIUS.lg },
  content: { paddingVertical: SPACING.md, paddingHorizontal: SPACING.md },
  header: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: SPACING.md },
  headerLeft: { flexDirection: 'row', alignItems: 'center', flex: 1 },
  orderNumber: { fontWeight: '600' },
  urgentIcon: { marginLeft: SPACING.sm },
  customerRow: { flexDirection: 'row', alignItems: 'center', marginBottom: SPACING.sm },
  customerName: { marginLeft: SPACING.sm, flex: 1, fontWeight: '500' },
  customerPhone: { marginLeft: SPACING.sm },
  itemsRow: { flexDirection: 'row', alignItems: 'center', marginBottom: SPACING.md },
  itemsText: { marginLeft: SPACING.sm, flex: 1 },
  statusRow: { flexDirection: 'row', alignItems: 'center', marginBottom: SPACING.md, gap: SPACING.sm },
  bottomRow: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-end' },
  priceSection: { flex: 1 },
  totalAmount: { fontWeight: '700', fontSize: 18 },
  balanceText: { marginTop: 2, fontSize: 12 },
  dateSection: { alignItems: 'flex-end' },
  dueDateLabel: { marginBottom: 2, fontSize: 12 },
  dueDateStatus: { fontWeight: '500', fontSize: 12 },
});