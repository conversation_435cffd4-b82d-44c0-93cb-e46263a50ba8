/**
 * CustomerSelectionModal - Enhanced customer selection with filter tabs and quick add
 * Features: Man/Women/Child/VIP/Recent filters, search, and quick customer add
 */

import React, { useState, useMemo, useCallback } from 'react';
import { View, StyleSheet, FlatList, Dimensions } from 'react-native';
import { Modal } from 'react-native-paper';
import { Portal } from 'react-native-paper';
import { Text } from 'react-native-paper';
import { Searchbar } from 'react-native-paper';
import { TouchableRipple } from 'react-native-paper';
import { Avatar } from 'react-native-paper';
import { Chip } from 'react-native-paper';
import { IconButton } from 'react-native-paper';
import { Divider } from 'react-native-paper';
import { FAB } from 'react-native-paper';
import LocalIcon from './LocalIcon';
import { useTheme } from '../context/ThemeContext';
import { SPACING, BORDER_RADIUS } from '../theme/designTokens';
import QuickCustomerAddModal from './QuickCustomerAddModal';

const { height: screenHeight } = Dimensions.get('window');

const CustomerSelectionModal = ({
  visible,
  onDismiss,
  customers = [],
  selectedCustomer,
  onCustomerSelect,
  onAddCustomer,
}) => {
  const { theme } = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [activeFilter, setActiveFilter] = useState('All');
  const [quickAddModalVisible, setQuickAddModalVisible] = useState(false);

  // Filter options with icons and colors
  const filterOptions = [
    { id: 'All', label: 'All', icon: 'account-group', color: theme.colors.primary },
    { id: 'Recent', label: 'Recent', icon: 'clock-outline', color: '#4CAF50' },
    { id: 'VIP', label: 'VIP', icon: 'crown', color: '#FF9800' },
    { id: 'Men', label: 'Men', icon: 'account', color: '#2196F3' },
    { id: 'Women', label: 'Women', icon: 'account-outline', color: '#E91E63' },
    { id: 'Child', label: 'Child', icon: 'account-child', color: '#9C27B0' },
  ];

  // Filter customers based on active filter and search query
  const filteredCustomers = useMemo(() => {
    let filtered = customers;

    // Apply category filter
    switch (activeFilter) {
      case 'Recent':
        // Sort by most recent orders or creation date
        filtered = customers
          .filter(customer => customer.lastOrderDate || customer.createdAt)
          .sort((a, b) => {
            const dateA = new Date(a.lastOrderDate || a.createdAt);
            const dateB = new Date(b.lastOrderDate || b.createdAt);
            return dateB - dateA;
          })
          .slice(0, 10); // Show top 10 recent
        break;
      case 'VIP':
        filtered = customers.filter(customer => customer.isVIP);
        break;
      case 'Men':
        filtered = customers.filter(customer =>
          customer.gender === 'Male' || customer.gender === 'M' || customer.category === 'Men'
        );
        break;
      case 'Women':
        filtered = customers.filter(customer =>
          customer.gender === 'Female' || customer.gender === 'F' || customer.category === 'Women'
        );
        break;
      case 'Child':
        filtered = customers.filter(customer =>
          customer.category === 'Child' || customer.age < 18
        );
        break;
      default:
        filtered = customers;
    }

    // Apply search filter
    if (searchQuery.trim()) {
      filtered = filtered.filter(customer =>
        customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        customer.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        customer.phone?.includes(searchQuery)
      );
    }

    return filtered;
  }, [customers, activeFilter, searchQuery]);

  const getCustomerInitials = (name) => {
    return name.split(' ').map(word => word[0]).join('').substring(0, 2).toUpperCase();
  };

  const getCustomerSubtitle = (customer) => {
    const parts = [];
    if (customer.phone) parts.push(customer.phone);
    if (customer.email) parts.push(customer.email);
    return parts.join(' • ');
  };

  const renderFilterChip = (filter) => (
    <Chip
      key={filter.id}
      mode={activeFilter === filter.id ? 'flat' : 'outlined'}
      selected={activeFilter === filter.id}
      onPress={() => setActiveFilter(filter.id)}
      icon={filter.icon}
      style={[
        styles.filterChip,
        {
          backgroundColor: activeFilter === filter.id ? filter.color + '20' : 'transparent',
          borderColor: activeFilter === filter.id ? filter.color : theme.colors.outline,
        }
      ]}
      textStyle={{
        color: activeFilter === filter.id ? filter.color : theme.colors.onSurface,
        fontWeight: activeFilter === filter.id ? '600' : '400',
      }}
    >
      {filter.label}
    </Chip>
  );

  const renderCustomerItem = ({ item: customer }) => (
    <TouchableRipple
      onPress={() => onCustomerSelect(customer)}
      style={[
        styles.customerItem,
        {
          backgroundColor: selectedCustomer?.id === customer.id
            ? theme.colors.primaryContainer
            : 'transparent',
        }
      ]}
    >
      <View style={styles.customerContent}>
        <Avatar.Text
          size={48}
          label={getCustomerInitials(customer.name)}
          style={{
            backgroundColor: selectedCustomer?.id === customer.id
              ? theme.colors.primary
              : theme.colors.primaryContainer,
          }}
        />
        <View style={styles.customerInfo}>
          <View style={styles.customerHeader}>
            <Text variant="titleMedium" style={{
              color: theme.colors.onSurface,
              fontWeight: '600',
              flex: 1,
            }}>
              {customer.name}
            </Text>
            {customer.isVIP && (
              <Chip
                mode="flat"
                compact
                icon="crown"
                style={{
                  backgroundColor: '#FF9800',
                  marginLeft: 8,
                }}
                textStyle={{ color: 'white', fontSize: 10 }}
              >
                VIP
              </Chip>
            )}
          </View>
          <Text variant="bodyMedium" style={{
            color: theme.colors.onSurfaceVariant,
            marginTop: 2,
          }}>
            {getCustomerSubtitle(customer)}
          </Text>
          {customer.lastOrderDate && (
            <Text variant="bodySmall" style={{
              color: theme.colors.onSurfaceVariant,
              marginTop: 2,
              opacity: 0.7,
            }}>
              Last order: {new Date(customer.lastOrderDate).toLocaleDateString()}
            </Text>
          )}
        </View>
        {selectedCustomer?.id === customer.id && (
          <LocalIcon name="check-circle" size={24} color={theme.colors.primary} />
        )}
      </View>
    </TouchableRipple>
  );

  return (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={onDismiss}
        contentContainerStyle={[
          styles.modalContainer,
          { backgroundColor: theme.colors.surface }
        ]}
      >
        {/* Header */}
        <View style={styles.header}>
          <Text variant="headlineSmall" style={{
            color: theme.colors.onSurface,
            fontWeight: '600',
            flex: 1,
          }}>
            Select Customer
          </Text>
          <IconButton
            icon="close"
            size={24}
            onPress={onDismiss}
            iconColor={theme.colors.onSurface}
          />
        </View>

        {/* Search Bar */}
        <Searchbar
          placeholder="Search customers..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={[
            styles.searchbar,
            { backgroundColor: theme.colors.surfaceVariant }
          ]}
          iconColor={theme.colors.onSurfaceVariant}
          placeholderTextColor={theme.colors.onSurfaceVariant}
        />

        {/* Filter Chips */}
        <View style={styles.filtersContainer}>
          <FlatList
            data={filterOptions}
            renderItem={({ item }) => renderFilterChip(item)}
            keyExtractor={(item) => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.filtersContent}
          />
        </View>

        <Divider style={{ marginVertical: SPACING.sm }} />

        {/* Customer List */}
        <View style={styles.listContainer}>
          {filteredCustomers.length > 0 ? (
            <FlatList
              data={filteredCustomers}
              renderItem={renderCustomerItem}
              keyExtractor={(item) => item.id.toString()}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.listContent}
            />
          ) : (
            <View style={styles.emptyState}>
              <LocalIcon name="account-search" size={48} color={theme.colors.onSurfaceVariant} />
              <Text variant="titleMedium" style={{
                color: theme.colors.onSurfaceVariant,
                marginTop: SPACING.md,
                textAlign: 'center',
              }}>
                {searchQuery ? 'No customers found' : `No ${activeFilter.toLowerCase()} customers`}
              </Text>
              <Text variant="bodyMedium" style={{
                color: theme.colors.onSurfaceVariant,
                marginTop: SPACING.sm,
                textAlign: 'center',
                opacity: 0.7,
              }}>
                {searchQuery ? 'Try adjusting your search' : 'Add a new customer to get started'}
              </Text>
            </View>
          )}
        </View>

        {/* Quick Add Customer FAB */}
        <FAB
          icon="plus"
          label="Add Customer"
          onPress={() => setQuickAddModalVisible(true)}
          style={[
            styles.fab,
            { backgroundColor: theme.colors.primary }
          ]}
          color={theme.colors.onPrimary}
        />

        {/* Quick Customer Add Modal */}
        <QuickCustomerAddModal
          visible={quickAddModalVisible}
          onDismiss={() => setQuickAddModalVisible(false)}
          onCustomerAdded={(customer) => {
            setQuickAddModalVisible(false);
            onCustomerSelect(customer);
          }}
        />
      </Modal>
    </Portal>
  );
};

export default CustomerSelectionModal;

const styles = StyleSheet.create({
  modalContainer: {
    margin: SPACING.lg,
    borderRadius: BORDER_RADIUS.xl,
    maxHeight: screenHeight * 0.85,
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.lg,
    paddingBottom: SPACING.md,
  },
  searchbar: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
  },
  filtersContainer: {
    marginBottom: SPACING.sm,
  },
  filtersContent: {
    paddingHorizontal: SPACING.lg,
    gap: SPACING.sm,
  },
  filterChip: {
    marginRight: SPACING.sm,
  },
  listContainer: {
    flex: 1,
    paddingHorizontal: SPACING.lg,
  },
  listContent: {
    paddingBottom: SPACING.xl,
  },
  customerItem: {
    paddingVertical: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
    marginBottom: SPACING.xs,
  },
  customerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.md,
  },
  customerInfo: {
    flex: 1,
    marginLeft: SPACING.md,
  },
  customerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: SPACING.xl,
  },
  fab: {
    position: 'absolute',
    bottom: SPACING.lg,
    right: SPACING.lg,
  },
});