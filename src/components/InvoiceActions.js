import React, { useState, useCallback } from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import { Button, Menu, Divider, ActivityIndicator } from 'react-native-paper';
import { useTheme } from '../context/ThemeContext';
import PDFInvoiceService from '../services/PDFInvoiceService';
import { SPACING } from '../theme/designTokens';

const InvoiceActions = ({ order, customer, style }) => {
  const { theme } = useTheme();
  const [loading, setLoading] = useState(false);
  const [menuVisible, setMenuVisible] = useState(false);

  // FIXED: Corrected useCallback syntax and added dependencies
  const handleGenerateInvoice = useCallback(async (action = 'generate') => {
    if (!order || !customer) {
      Alert.alert('Error', 'Order and customer information are required.');
      return;
    }

    try {
      setLoading(true);
      setMenuVisible(false);

      const options = { order, customer, share: action === 'share', print: action === 'print' };
      const result = await PDFInvoiceService.generateInvoiceForOrder(order.id, customer.id, options);

      if (result.success) {
        let message = 'Invoice generated successfully!';
        if (action === 'share') message = 'Invoice prepared for sharing!';
        else if (action === 'print') message = 'Invoice sent to printer!';
        Alert.alert('Success', message);
      } else {
        Alert.alert('Error', result.error || 'Failed to generate invoice');
      }
    } catch (error) {
      console.error('Error generating invoice:', error);
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [order, customer]);

  // FIXED: Corrected useCallback syntax
  const handleShareInvoice = useCallback(() => {
    handleGenerateInvoice('share');
  }, [handleGenerateInvoice]);

  const handlePrintInvoice = useCallback(() => {
    handleGenerateInvoice('print');
  }, [handleGenerateInvoice]);

  const handleDownloadInvoice = useCallback(() => {
    handleGenerateInvoice('generate');
  }, [handleGenerateInvoice]);

  if (loading) {
    return (
      <View style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color={theme.colors.primary} />
          <Button mode="outlined" disabled style={styles.loadingButton}>
            Generating Invoice...
          </Button>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, style}>
      <View style={styles.buttonContainer}>
        <Button
          mode="contained"
          onPress={handleDownloadInvoice}
          icon="file-pdf-box"
          style={styles.primaryButton}
          contentStyle={styles.buttonContent}>
          Generate Invoice
        </Button>

        <Menu
          visible={menuVisible}
          onDismiss={() => setMenuVisible(false)}
          anchor={
            <Button
              mode="outlined"
              onPress={() => setMenuVisible(true)}
              icon="dots-vertical"
              style={styles.menuButton}
              contentStyle={styles.buttonContent}>
              More
            </Button>
          }
          contentStyle={[styles.menuContent, { backgroundColor: theme.colors.surface }>
          <Menu.Item
            onPress={handleShareInvoice}
            title="Share Invoice"
            leadingIcon="share-variant"
            titleStyle={{ color: theme.colors.onSurface }}
          />
          <Menu.Item
            onPress={handlePrintInvoice}
            title="Print Invoice"
            leadingIcon="printer"
            titleStyle={{ color: theme.colors.onSurface }}
          />
          <Divider />
          <Menu.Item
            onPress={handleDownloadInvoice}
            title="Download PDF"
            leadingIcon="download"
            titleStyle={{ color: theme.colors.onSurface }}
          />
        </Menu>
      </View>
    </View>
  );
};

export default InvoiceActions;

// FIXED: Corrected syntax errors and removed empty style
const styles = StyleSheet.create({
  container: {
    marginVertical: SPACING.sm,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: SPACING.sm,
  },
  primaryButton: {
    flex: 2,
  },
  menuButton: {
    flex: 1,
  },
  buttonContent: {
    paddingVertical: SPACING.xs,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.sm,
  },
  loadingButton: {
    flex: 1,
  },
  menuContent: {
    borderRadius: 8,
    elevation: 8,
  },
});