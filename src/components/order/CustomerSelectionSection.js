/**
 * CustomerSelectionSection - Handles customer selection and display
 */

import React, { memo, useCallback } from 'react';
import { View, StyleSheet   } from 'react-native';
import { Surface   } from 'react-native-paper';
import { Text   } from 'react-native-paper';
import { Button   } from 'react-native-paper';
import { Avatar   } from 'react-native-paper';
import { useTheme   } from '../../context/ThemeContext';
import { SPACING   } from '../../theme/designTokens';

const CustomerSelectionSection = memo(({
  selectedCustomer,
  orderData,
  errors,
  onCustomerPress,
  onInputChange
}) => {
  const { theme  } = useTheme();

  return (
    <Surface style={styles.style6}} elevation={1}>
      <Text variant="titleMedium" style={styles.style4}>
        Customer Information
      </Text>

      {selectedCustomer ? (
        <View style={styles.style2}>
          <View style={styles.style2}>
            <Avatar.Text
              size={48}
              label={selectedCustomer.name.charAt(0).toUpperCase()}
              style={styles.style5}
              labelStyle={{ color: theme.colors.onPrimaryContainer }
            />
            <View style={styles.style2}>
              <Text variant="titleMedium" style={styles.style4}>
                {selectedCustomer.name}
              </Text>
              <Text variant="bodyMedium" style={styles.style3}>
                {selectedCustomer.phone}
              </Text>
              {selectedCustomer.email && (
                <Text variant="bodySmall" style={styles.style3}>
                  {selectedCustomer.email}
                </Text>
              )}
            </View>
          </View>
          <Button
            mode="outlined"
            onPress={onCustomerPress}
            compact
            icon="pencil"
          >
            Change
          </Button>
        </View>
      ) : (
        <View style={styles.style2}>
          <Button
            mode="contained"
            onPress={onCustomerPress}
            icon="account-plus"
            style={styles.style2}>
            Select Customer
          </Button>
          {errors.customerName && (
            <Text style={styles.style1}>
              {errors.customerName}
            </Text>
          )}
        </View>
      )}

      {/* Quick Customer Info (when no customer selected) */}
      {!selectedCustomer && (
        <View style={styles.style2}>
          <Text variant="bodyMedium" style={styles.style3}>
            Or enter customer details manually:
          </Text>
          
          <View style={styles.style2}>
            <TextInput
              mode="outlined"
              label="Customer Name *"
              value={orderData.customerName}
              onChangeText={(value) => onInputChange('customerName', value)}
              error={!!errors.customerName}
              style={styles.style2}
            />
            <TextInput
              mode="outlined"
              label="Phone Number"
              value={orderData.customerPhone}
              onChangeText={(value) => onInputChange('customerPhone', value)}
              error={!!errors.customerPhone}
              keyboardType="phone-pad"
              style={styles.style2}
            />
          </View>
          
          {errors.customerPhone && (
            <Text style={styles.style1}>
              {errors.customerPhone}
            </Text>
          )}
        </View>
      )}
    </Surface>
  );
});



CustomerSelectionSection.displayName = 'CustomerSelectionSection';




export default React.memo(CustomerSelectionSection);

const styles = StyleSheet.create({
  section: {
    margin: SPACING.md,
    padding: SPACING.lg,
    borderRadius: 12,
  },
  sectionTitle: {
    marginBottom: SPACING.md,
    fontWeight: '600',
  },
  selectedCustomer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  customerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  customerDetails: {
    marginLeft: SPACING.md,
    flex: 1,
  },
  noCustomer: {
    alignItems: 'center',
  },
  selectButton: {
    marginBottom: SPACING.sm,
  },
  quickCustomerInfo: {
    marginTop: SPACING.lg,
    paddingTop: SPACING.lg,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  orText: {
    textAlign: 'center',
    marginBottom: SPACING.md,
  },
  inputRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: SPACING.sm,
  },
  input: {
    marginBottom: SPACING.sm,
  },
  halfWidth: {
    flex: 1,
  },
  errorText: {
    fontSize: 12,
    marginTop: 4,
  },
  style1: {
    color: theme.colors.error,
  },
  style2: {
  },
  style3: {
    color: theme.colors.onSurfaceVariant,
  },
  style4: {
    color: theme.colors.onSurface,
  },
  style5: {
    backgroundColor: theme.colors.primaryContainer,
  },
  style6: {
    backgroundColor: theme.colors.surface,
  },
});