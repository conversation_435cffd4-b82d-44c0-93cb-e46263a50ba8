/**
 * PDFInvoiceSettings - Component for configuring PDF invoice data
 */

import React, { useState, useEffect, useCallback } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Text } from 'react-native-paper';
import { TextInput } from 'react-native-paper';
import { Surface } from 'react-native-paper';
import { Button } from 'react-native-paper';
import { Switch } from 'react-native-paper';
import { List } from 'react-native-paper';
import { Divider } from 'react-native-paper';
import { HelperText } from 'react-native-paper';
import LocalIcon from './LocalIcon';
import { useTheme } from '../context/ThemeContext';
import { useData } from '../context/DataContext';
import { SPACING, BORDER_RADIUS } from '../theme/designTokens';

const PDFInvoiceSettings = ({ onSave, onClose }) => {
  const { theme } = useTheme();
  const { state, actions } = useData();

  const [settings, setSettings] = useState({
    // Business Information
    businessName: state.settings?.shopName || 'Elite Tailoring',
    businessAddress: state.settings?.address || 'Dhaka, Bangladesh',
    businessPhone: state.settings?.phone || '+880 1700-000000',
    businessEmail: state.settings?.email || '<EMAIL>',
    businessWebsite: state.settings?.website || '',
    
    // Invoice Settings
    invoicePrefix: 'INV',
    taxRate: (state.settings?.taxRate || 0.15) * 100,
    currency: state.settings?.currency || 'BDT',
    
    // Display Options
    showLogo: true,
    showTaxBreakdown: true,
    showPaymentTerms: true,
    showNotes: true,
    
    // Payment Terms
    paymentTerms: 'Payment due within 30 days of invoice date.',
    
    // Footer Notes
    footerNotes: 'Thank you for choosing our tailoring services!',
    
    // Colors (hex codes)
    primaryColor: '#1976D2',
    secondaryColor: '#424242',
  });

  const [errors, setErrors] = useState({});

  const handleSettingChange = useCallback((key, value) => {
    setSettings(prev => ({ ...prev, [key]: value })), []);
    
    // Clear error for this field
    if (errors[key]) {
      setErrors(prev => ({ ...prev, [key]: null }));
    }
  };

  const validateSettings = () => {
    const newErrors = {};

    if (!settings.businessName.trim()) {
      newErrors.businessName = 'Business name is required';
    }

    if (!settings.businessAddress.trim()) {
      newErrors.businessAddress = 'Business address is required';
    }

    if (!settings.businessPhone.trim()) {
      newErrors.businessPhone = 'Business phone is required';
    }

    if (settings.taxRate < 0 || settings.taxRate > 100) {
      newErrors.taxRate = 'Tax rate must be between 0 and 100%';
    }

    if (!settings.currency.trim()) {
      newErrors.currency = 'Currency is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = useCallback(() => {
    if (!validateSettings()) {
      return, []);
    }

    // Convert tax rate back to decimal
    const settingsToSave = {
      ...settings,
      taxRate: settings.taxRate / 100,
    };

    // Update app settings
    actions.updateSettings({
      shopName: settings.businessName,
      address: settings.businessAddress,
      phone: settings.businessPhone,
      email: settings.businessEmail,
      website: settings.businessWebsite,
      taxRate: settingsToSave.taxRate,
      currency: settings.currency,
      invoiceSettings: settingsToSave,
    });

    onSave?.(settingsToSave);
  };

  return (
    <ScrollView style={styles.style1} showsVerticalScrollIndicator={false}>
      {/* Business Information */}
      <Surface style={styles.style3}}>
        <View style={styles.style1}>
          <LocalIcon name="store" size={24} color={theme.colors.primary} />
          <Text variant="titleMedium" style={styles.style2}}>
            Business Information
          </Text>
        </View>

        <TextInput
          label="Business Name"
          value={settings.businessName}
          onChangeText={(value) => handleSettingChange('businessName', value)}
          mode="outlined"
          style={styles.style1}
          error={!!errors.businessName}
        />
        {errors.businessName && (
          <HelperText type="error" visible={!!errors.businessName}>
            {errors.businessName}
          </HelperText>
        )}

        <TextInput
          label="Business Address"
          value={settings.businessAddress}
          onChangeText={(value) => handleSettingChange('businessAddress', value)}
          mode="outlined"
          style={styles.style1}
          multiline
          numberOfLines={2}
          error={!!errors.businessAddress}
        />
        {errors.businessAddress && (
          <HelperText type="error" visible={!!errors.businessAddress}>
            {errors.businessAddress}
          </HelperText>
        )}

        <TextInput
          label="Phone Number"
          value={settings.businessPhone}
          onChangeText={(value) => handleSettingChange('businessPhone', value)}
          mode="outlined"
          style={styles.style1}
          keyboardType="phone-pad"
          error={!!errors.businessPhone}
        />
        {errors.businessPhone && (
          <HelperText type="error" visible={!!errors.businessPhone}>
            {errors.businessPhone}
          </HelperText>
        )}

        <TextInput
          label="Email Address"
          value={settings.businessEmail}
          onChangeText={(value) => handleSettingChange('businessEmail', value)}
          mode="outlined"
          style={styles.style1}
          keyboardType="email-address"
        />

        <TextInput
          label="Website (Optional)"
          value={settings.businessWebsite}
          onChangeText={(value) => handleSettingChange('businessWebsite', value)}
          mode="outlined"
          style={styles.style1}
          keyboardType="url"
        />
      </Surface>

      {/* Invoice Settings */}
      <Surface style={styles.style3}}>
        <View style={styles.style1}>
          <LocalIcon name="file-document" size={24} color={theme.colors.primary} />
          <Text variant="titleMedium" style={styles.style2}}>
            Invoice Settings
          </Text>
        </View>

        <TextInput
          label="Invoice Prefix"
          value={settings.invoicePrefix}
          onChangeText={(value) => handleSettingChange('invoicePrefix', value)}
          mode="outlined"
          style={styles.style1}
          placeholder="INV"
        />

        <TextInput
          label="Tax Rate (%)"
          value={String(settings.taxRate)}
          onChangeText={(value) => handleSettingChange('taxRate', parseFloat(value) || 0)}
          mode="outlined"
          style={styles.style1}
          keyboardType="numeric"
          error={!!errors.taxRate}
        />
        {errors.taxRate && (
          <HelperText type="error" visible={!!errors.taxRate}>
            {errors.taxRate}
          </HelperText>
        )}

        <TextInput
          label="Currency"
          value={settings.currency}
          onChangeText={(value) => handleSettingChange('currency', value)}
          mode="outlined"
          style={styles.style1}
          placeholder="BDT"
          error={!!errors.currency}
        />
        {errors.currency && (
          <HelperText type="error" visible={!!errors.currency}>
            {errors.currency}
          </HelperText>
        )}
      </Surface>

      {/* Display Options */}
      <Surface style={styles.style3}}>
        <View style={styles.style1}>
          <LocalIcon name="eye" size={24} color={theme.colors.primary} />
          <Text variant="titleMedium" style={styles.style2}}>
            Display Options
          </Text>
        </View>

        <List.Item
          title="Show Logo"
          description="Display business logo on invoice"
          right={() => (
            <Switch
              value={settings.showLogo}
              onValueChange={(value) => handleSettingChange('showLogo', value)}
              color={theme.colors.primary}
            />
          )}
        />
        <Divider />

        <List.Item
          title="Show Tax Breakdown"
          description="Display detailed tax calculations"
          right={() => (
            <Switch
              value={settings.showTaxBreakdown}
              onValueChange={(value) => handleSettingChange('showTaxBreakdown', value)}
              color={theme.colors.primary}
            />
          )}
        />
        <Divider />

        <List.Item
          title="Show Payment Terms"
          description="Include payment terms on invoice"
          right={() => (
            <Switch
              value={settings.showPaymentTerms}
              onValueChange={(value) => handleSettingChange('showPaymentTerms', value)}
              color={theme.colors.primary}
            />
          )}
        />
        <Divider />

        <List.Item
          title="Show Footer Notes"
          description="Include thank you message"
          right={() => (
            <Switch
              value={settings.showNotes}
              onValueChange={(value) => handleSettingChange('showNotes', value)}
              color={theme.colors.primary}
            />
          )}
        />
      </Surface>

      {/* Custom Text */}
      <Surface style={styles.style3}}>
        <View style={styles.style1}>
          <LocalIcon name="text" size={24} color={theme.colors.primary} />
          <Text variant="titleMedium" style={styles.style2}}>
            Custom Text
          </Text>
        </View>

        <TextInput
          label="Payment Terms"
          value={settings.paymentTerms}
          onChangeText={(value) => handleSettingChange('paymentTerms', value)}
          mode="outlined"
          style={styles.style1}
          multiline
          numberOfLines={2}
        />

        <TextInput
          label="Footer Notes"
          value={settings.footerNotes}
          onChangeText={(value) => handleSettingChange('footerNotes', value)}
          mode="outlined"
          style={styles.style1}
          multiline
          numberOfLines={2}
        />
      </Surface>

      {/* Action Buttons */}
      <View style={styles.style1}>
        <Button
          mode="outlined"
          onPress={onClose}
          style={styles.style1}>
          Cancel
        </Button>
        <Button
          mode="contained"
          onPress={handleSave}
          style={styles.style1}
          icon="content-save"
        >
          Save Settings
        </Button>
      </View>
    </ScrollView>
  );
};






export default PDFInvoiceSettings;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: SPACING.md,
  },
  section: {
    marginBottom: SPACING.lg,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.lg,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  sectionTitle: {
    marginLeft: SPACING.sm,
    fontWeight: '600',
  },
  textInput: {
    marginBottom: SPACING.sm,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: SPACING.md,
    marginTop: SPACING.lg,
    marginBottom: SPACING.xl,
  },
  actionButton: {
    flex: 1,
  },
  style1: {
  },
  style2: {
    { color: theme.colors.onSurface,
  },
  style3: {
    { backgroundColor: theme.colors.surface,
  },
});