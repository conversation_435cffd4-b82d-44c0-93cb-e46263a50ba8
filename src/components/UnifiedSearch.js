/**
 * UnifiedSearch - Single search component to replace all search implementations
 */
import React, { useState, useCallback, useEffect, useRef, useMemo } from 'react';
import { View, StyleSheet, TouchableOpacity, Modal, ScrollView } from 'react-native';
// FIXED: Cleaned up and consolidated imports
import { Searchbar, Text, Surface, Chip, IconButton, Portal, Button } from 'react-native-paper';
import { useTheme } from '../context/ThemeContext';
import LocalIcon from './LocalIcon';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { StorageService } from '../services/storageService';
import navigationService from '../services/NavigationService';
import { SPACING, BORDER_RADIUS } from '../theme/designTokens';

const UnifiedSearch = ({
  type = 'global', data = [], searchFields = ['name'], placeholder = 'Search...',
  mode = 'icon', showSuggestions = true, showRecentSearches = true, showFilters = false, filters = [],
  onSearch, onResultSelect, onFilterChange,
  style, iconSize = 24, iconColor,
}) => {
  const { theme } = useTheme();
  const insets = useSafeAreaInsets();
  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  const [recentSearches, setRecentSearches] = useState([]);
  const [selectedFilter, setSelectedFilter] = useState('All');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);
  const searchTimeoutRef = useRef(null);

  useEffect(() => {
    const loadRecent = async () => {
      if (!showRecentSearches) return;
      const recent = await StorageService.get(`recentSearches_${type}`) || [];
      setRecentSearches(recent);
    };
    loadRecent();
  }, [type, showRecentSearches]);

  const saveRecentSearch = useCallback(async (searchQuery) => {
    if (!searchQuery.trim() || !showRecentSearches) return;
    const key = `recentSearches_${type}`;
    const recent = (await StorageService.get(key)) || [];
    const updated = [searchQuery, ...recent.filter(item => item !== searchQuery)].slice(0, 5);
    await StorageService.set(key, updated);
    setRecentSearches(updated);
  }, [type, showRecentSearches]);

  const generateSuggestions = useCallback((searchQuery) => {
    if (!searchQuery.trim() || !showSuggestions) return setSuggestions([]);
    const qLower = searchQuery.toLowerCase();
    const scored = data.map(item => {
        let score = 0;
        searchFields.forEach(field => {
          const val = item[field]?.toString().toLowerCase();
          if (!val) return;
          if (val.startsWith(qLower)) score += 50;
          else if (val.includes(qLower)) score += 25;
        });
        return { ...item, _searchScore: score };
      })
      .filter(item => item._searchScore > 0)
      .sort((a, b) => b._searchScore - a._searchScore)
      .slice(0, 8);
    setSuggestions(scored);
  }, [data, searchFields, showSuggestions]);

  // FIXED: Corrected all useCallback hooks syntax and dependencies
  const handleQueryChange = useCallback((text) => {
    setQuery(text);
    setIsDropdownVisible(true);
    if (searchTimeoutRef.current) clearTimeout(searchTimeoutRef.current);
    searchTimeoutRef.current = setTimeout(() => generateSuggestions(text), 300);
  }, [generateSuggestions]);

  const handleSearch = useCallback(async (searchQuery = query) => {
    if (!searchQuery.trim()) return;
    setIsDropdownVisible(false);
    setIsModalVisible(false);
    await saveRecentSearch(searchQuery);
    onSearch?.(searchQuery, selectedFilter);
  }, [query, selectedFilter, onSearch, saveRecentSearch]);
  
  const handleSuggestionSelect = useCallback((item) => {
    const searchTerm = item.name || item[searchFields[0] || '';
    setQuery(searchTerm);
    setIsDropdownVisible(false);
    setIsModalVisible(false);
    onResultSelect?.(item);
    saveRecentSearch(searchTerm);
    // Optional: Add navigation logic here if needed
  }, [searchFields, onResultSelect, saveRecentSearch]);

  const handleFilterChange = useCallback((filter) => {
    setSelectedFilter(filter);
    onFilterChange?.(filter);
  }, [onFilterChange]);

  const clearRecentSearches = async () => {
    await StorageService.remove(`recentSearches_${type}`);
    setRecentSearches([]);
  };

  const getSearchIcon = (item) => {
    if (item.price !== undefined) return 'package-variant';
    if (item.customerName) return 'clipboard-text';
    if (item.email) return 'account';
    return 'magnify';
  };
  
  // FIXED: Styles moved inside component and memoized for correctness
  const styles = useMemo(() => createStyles(theme, insets), [theme, insets]);

  const DropdownContent = () => (
    <>
      {showRecentSearches && recentSearches.length > 0 && (
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Recent Searches</Text>
            <TouchableOpacity onPress={clearRecentSearches}><Text style={styles.clearButton}>Clear</Text></TouchableOpacity>
          </View>
          {recentSearches.map((search, index) => (
            <TouchableOpacity key={index} style={styles.dropdownItem} onPress={() => { setQuery(search); handleSearch(search); }}>
              <LocalIcon name="history" size={16} color={theme.colors.onSurfaceVariant} />
              <Text style={styles.dropdownText}>{search}</Text>
            </TouchableOpacity>
          ))}
        </View>
      )}
      {suggestions.length > 0 && (
        <View style={styles.section}>
          {recentSearches.length > 0 && <View style={styles.separator} />}
          <View style={styles.sectionHeader}><Text style={styles.sectionTitle}>Suggestions</Text></View>
          {suggestions.map(item => (
            <TouchableOpacity key={item.id || item.name} style={styles.dropdownItem} onPress={() => handleSuggestionSelect(item)}>
              <LocalIcon name={getSearchIcon(item)} size={16} color={theme.colors.onSurfaceVariant} />
              <Text style={styles.dropdownText}>{item.name || item[searchFields[0]</Text>
            </TouchableOpacity>
          ))}
        </View>
      )}
    </>
  );

  const renderSearchBar = () => (
    <View style={[styles.searchBarContainer, style}>
      <Searchbar
        placeholder={placeholder}
        onChangeText={handleQueryChange}
        value={query}
        onSubmitEditing={() => handleSearch()}
        style={styles.searchBar}
        onFocus={() => setIsDropdownVisible(true)}
        onBlur={() => setTimeout(() => setIsDropdownVisible(false), 200)}
      />
      {isDropdownVisible && (suggestions.length > 0 || recentSearches.length > 0) && (
        <Surface style={styles.dropdown} elevation={4}>
          <ScrollView keyboardShouldPersistTaps="handled"><DropdownContent /></ScrollView>
        </Surface>
      )}
    </View>
  );

  const renderSearchModal = () => (
    <Portal>
      <Modal visible={isModalVisible} onDismiss={() => setIsModalVisible(false)} contentContainerStyle={styles.modalContent}>
        <View style={styles.modalHeader}>
          <Text style={styles.modalTitle}>Search {type}</Text>
          <IconButton icon="close" size={24} onPress={() => setIsModalVisible(false)} />
        </View>
        <Searchbar placeholder={placeholder} onChangeText={handleQueryChange} value={query} onSubmitEditing={() => handleSearch()} style={styles.modalSearchBar} autoFocus />
        {showFilters && filters.length > 0 && (
          <View style={styles.filtersContainer}>{filters.map(filter => <Chip key={filter} selected={selectedFilter === filter} onPress={() => handleFilterChange(filter)} style={styles.filterChip}>{filter}</Chip>)}</View>
        )}
        <ScrollView style={styles.modalResultsContainer} keyboardShouldPersistTaps="handled"><DropdownContent /></ScrollView>
        <View style={styles.modalActions}><Button mode="outlined" onPress={() => setIsModalVisible(false)}>Cancel</Button><Button mode="contained" onPress={() => handleSearch()} disabled={!query.trim()}>Search</Button></View>
      </Modal>
    </Portal>
  );

  if (mode === 'bar') return renderSearchBar();
  
  return (
    <>
      <IconButton icon="magnify" size={iconSize} iconColor={iconColor || theme.colors.onSurface} onPress={() => setIsModalVisible(true)} style={style} />
      {renderSearchModal()}
    </>
  );
};

export default UnifiedSearch;

// This function creates the stylesheet object, allowing it to use the theme and insets from props.
const createStyles = (theme, insets) => StyleSheet.create({
  searchBarContainer: { position: 'relative', zIndex: 1 },
  searchBar: { borderRadius: BORDER_RADIUS.lg, elevation: 1 },
  dropdown: { position: 'absolute', top: '100%', left: 0, right: 0, zIndex: 1000, borderRadius: BORDER_RADIUS.lg, marginTop: SPACING.xs, maxHeight: 300, backgroundColor: theme.colors.surface, borderWidth: 1, borderColor: theme.colors.outlineVariant },
  section: { paddingVertical: SPACING.sm },
  sectionHeader: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingHorizontal: SPACING.md, paddingBottom: SPACING.sm },
  sectionTitle: { ...TYPOGRAPHY.labelLarge, color: theme.colors.onSurfaceVariant },
  clearButton: { ...TYPOGRAPHY.labelMedium, color: theme.colors.primary },
  dropdownItem: { flexDirection: 'row', alignItems: 'center', paddingHorizontal: SPACING.md, paddingVertical: SPACING.md },
  dropdownText: { marginLeft: SPACING.md, flex: 1, ...TYPOGRAPHY.bodyLarge, color: theme.colors.onSurface },
  separator: { height: 1, backgroundColor: theme.colors.outlineVariant, marginVertical: SPACING.sm },
  modalContent: { backgroundColor: theme.colors.surface, borderRadius: BORDER_RADIUS.xl, margin: SPACING.md, padding: SPACING.lg, maxHeight: '85%', shadowColor: '#000', elevation: 5 },
  modalHeader: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: SPACING.md },
  modalTitle: { ...TYPOGRAPHY.titleLarge, color: theme.colors.onSurface },
  modalSearchBar: { marginBottom: SPACING.md },
  filtersContainer: { flexDirection: 'row', flexWrap: 'wrap', gap: SPACING.sm, marginBottom: SPACING.md },
  filterChip: {},
  modalResultsContainer: { flex: 1, marginVertical: SPACING.sm },
  modalActions: { flexDirection: 'row', justifyContent: 'flex-end', gap: SPACING.md, paddingTop: SPACING.md, borderTopWidth: 1, borderTopColor: theme.colors.outlineVariant },
});