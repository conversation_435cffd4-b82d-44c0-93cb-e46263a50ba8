import React, { useState, useEffect, useCallback } from 'react';
import { View, StyleSheet, ScrollView   } from 'react-native';
import { Modal   } from 'react-native-paper';
import { Portal   } from 'react-native-paper';
import { Text   } from 'react-native-paper';
import { Button   } from 'react-native-paper';
import { Card   } from 'react-native-paper';
import { Switch   } from 'react-native-paper';
import { Divider   } from 'react-native-paper';
import { TextInput   } from 'react-native-paper';
import { HelperText   } from 'react-native-paper';
import { useTheme   } from '../context/ThemeContext';
import LocalIcon from './LocalIcon';

const PaymentMethodsModal = ({ visible, onDismiss, onSelectMethod }) => {
  const { theme  } = useTheme();
  const [methods, setMethods] = useState({
    cash: { enabled: true, processingFee: 0 },
    card: { enabled: true, processingFee: 2.9 },
    digitalWallet: { enabled: false, processingFee: 2.5 },
    bankTransfer: { enabled: false, processingFee: 1.0 },
    giftCard: { enabled: true, processingFee: 0 }});

  const [errors, setErrors] = useState({});

  const paymentOptions = [
    {
      key: 'cash',
      label: 'Cash',
      icon: 'cash',
      description: 'Physical cash payments'},
    {
      key: 'card',
      label: 'Credit/Debit Card',
      icon: 'credit-card',
      description: 'Visa, Mastercard, American Express'},
    {
      key: 'digitalWallet',
      label: 'Digital Wallet',
      icon: 'wallet',
      description: 'Apple Pay, Google Pay, Samsung Pay'},
    {
      key: 'bankTransfer',
      label: 'Bank Transfer',
      icon: 'bank-transfer',
      description: 'Direct bank transfers'},
    {
      key: 'giftCard',
      label: 'Gift Cards',
      icon: 'gift',
      description: 'Store gift cards and vouchers'}];
  useEffect(() => {
    setErrors({});
  }, [visible]);

  const validateForm = () => {
    const newErrors = {};
    
    Object.keys(methods).forEach(key => {
      const fee = parseFloat(methods[key].processingFee);
      if (methods[key].enabled && (isNaN(fee) || fee < 0 || fee > 10)) {
        newErrors[key] = 'Processing fee must be between 0 and 10%';
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const toggleMethod = (key) => {
    setMethods(prev => ({
      ...prev,
      [key]: {
        ...prev[key],
        enabled: !prev[key].enabled
      }
    }));
  };

  const updateProcessingFee = (key, fee) => {
    setMethods(prev => ({
      ...prev,
      [key]: {
        ...prev[key],
        processingFee: fee
      }
    }));
  };

  const handleSave = useCallback(() => {
    if (validateForm()) {
      onSelectMethod && onSelectMethod(methods);
      onDismiss();
    }
  }, [methods, onSelectMethod, onDismiss, validateForm]);

  const PaymentMethodCard = ({ option }) => (
    <Card style={styles.style1} mode="outlined">
      <Card.Content>
        <View style={styles.style1}>
          <View style={styles.style1}>
            <LocalIcon name={option.icon} size={24} color={theme.colors.primary} />
            <View style={styles.style1}>
              <Text variant="titleMedium">{option.label}</Text>
              <Text variant="bodySmall" style={styles.style8}>
                {option.description}
              </Text>
            </View>
          </View>
          <Switch
            value={methods[option.key].enabled}
            onValueChange={() => toggleMethod(option.key)}
            color={theme.colors.primary}
          />
        </View>

        {methods[option.key].enabled && (
          <>
            <Divider style={styles.style7} />
            <View style={styles.style1}>
              <TextInput
                label="Processing Fee (%)"
                value={methods[option.key].processingFee.toString()}
                onChangeText={(text) => updateProcessingFee(option.key, parseFloat(text) || 0)}
                mode="outlined"
                style={styles.style1}
                keyboardType="decimal-pad"
                error={!!errors[option.key}
                dense
              />
              <HelperText type="error" visible={!!errors[option.key}>
                {errors[option.key}
              </HelperText>
            </View>
          </>
        )}
      </Card.Content>
    </Card>
  );

  const enabledMethods = Object.keys(methods).filter(key => methods[key].enabled);

  return (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={onDismiss}
        contentContainerStyle={[
          styles.modal,
          { backgroundColor: theme.colors.surface }
        }>
        <ScrollView showsVerticalScrollIndicator={false}>
          <Text variant="headlineSmall" style={styles.style6}>
            Payment Methods
          </Text>

          <Card style={styles.style1} mode="contained">
            <Card.Content>
              <Text variant="titleMedium" style={styles.style5}>
                Summary
              </Text>
              <Text variant="bodyMedium">
                {enabledMethods.length} payment method{enabledMethods.length !== 1 ? 's' : ''} enabled
              </Text>
              {enabledMethods.length > 0 && (
                <Text variant="bodySmall" style={styles.style4}>
                  {paymentOptions
                    .filter(option => methods[option.key].enabled)
                    .map(option => option.label)
                    .join(', ')}
                </Text>
              )}
            </Card.Content>
          </Card>

          {paymentOptions.map((option) => (
            <PaymentMethodCard key={option.key} option={option} />
          ))}

          <Card style={styles.style1} mode="outlined">
            <Card.Content>
              <View style={styles.style1}>
                <LocalIcon name="information" size={20} color={theme.colors.primary} />
                <Text variant="titleSmall" style={styles.style3}>
                  Processing Fees
                </Text>
              </View>
              <Text variant="bodySmall" style={styles.style2}>
                Processing fees are automatically calculated and added to transactions. 
                These fees help cover payment processing costs and gateway charges.
              </Text>
            </Card.Content>
          </Card>

          <View style={styles.style1}>
            <Button
              mode="outlined"
              onPress={onDismiss}
              style={styles.style1}>
              Cancel
            </Button>
            <Button
              mode="contained"
              onPress={handleSave}
              style={styles.style1}>
              Save Methods
            </Button>
          </View>
        </ScrollView>
      </Modal>
    </Portal>
  );
};






export default PaymentMethodsModal;

const styles = StyleSheet.create({
  modal: {
    margin: 20,
    padding: 20,
    borderRadius: 16,
    maxHeight: '90%'},
  title: {
    textAlign: 'center',
    marginBottom: 24},
  summaryCard: {
    marginBottom: 16},
  methodCard: {
    marginBottom: 12},
  methodHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'},
  methodInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1},
  methodText: {
    marginLeft: 12,
    flex: 1},
  feeSection: {
    marginTop: 8},
  feeInput: {
    marginBottom: 4},
  infoCard: {
    marginTop: 16,
    marginBottom: 16},
  infoHeader: {
    flexDirection: 'row',
    alignItems: 'center'},
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16},
  button: {
    flex: 1,
    marginHorizontal: 8},
  style1: {
  },
  style2: {
    marginTop: 8},
  style3: {
    marginLeft: 8},
  style4: {
    marginTop: 4},
  style5: {
    marginBottom: 8},
  style6: {
  },
  style7: {
    marginVertical: 12},
  style8: {
  }});