/**
 * UniversalCard - Single unified card component for all use cases
 */

import React, { memo, useMemo, useCallback } from 'react';
import { View, StyleSheet, TouchableOpacity, Image   } from 'react-native';
// FIXED: Cleaned up and consolidated imports
import { Text, Surface, IconButton, Menu, Chip, Button   } from 'react-native-paper';
import { useTheme   } from '../context/ThemeContext';
import LocalIcon from './LocalIcon';
import GlobalBadge from './GlobalBadge';
import { SPACING, BORDER_RADIUS, TYPOGRAPHY   } from '../theme/designTokens';

const UniversalCard = memo(({
  title, subtitle, description, value, price, status, image, icon, iconColor,
  type = 'default', layout = 'default',
  cards = [], columns = 2, showTitle = true, onCardPress,
  onPress, onLongPress,
  menuItems = [], menuVisible, onMenuToggle, onMenuDismiss,
  style, elevation = 1, data, growth,
}) => {
  const { theme  } = useTheme();

  // FIXED: Stylesheet moved inside component and memoized for correctness and performance
  const styles = useMemo(() => createStyles(theme), [theme]);

  // Group type renders multiple cards recursively
  if(type === 'group') {
    return (
      <View style={[styles.groupContainer, style}>
        {showTitle && title && <Text style={styles.groupTitle}>{title}</Text>}
        <View style={[styles.grid, { marginHorizontal: -SPACING.xs }]}}>
          {cards.map((card, index) => {
            const isLastCard = index === cards.length - 1;
            const isOddCount = cards.length % columns !== 0;
            const shouldSpanFullWidth = isOddCount && isLastCard;
            // FIXED: Dynamic width calculation moved here
            const cardWrapperStyle = {
              width: shouldSpanFullWidth ? '100%' : `${100 / columns}`} %`,
              paddingHorizontal: SPACING.xs,
            };
            return (
              <View key={card.key || index} style={[styles.cardWrapper, cardWrapperStyle}>
                <UniversalCard
                  type="stat"
                  title={card.title}
                  value={card.value}
                  icon={card.icon}
                  iconColor={card.iconColor || card.color}
                  onPress={() => onCardPress ? onCardPress(card) : card.onPress?.()}
                />
              </View>
            );
          })}
        </View>
      </View>
    );
  }
  
  const renderContent = () => {
    // Content rendering logic based on 'type'
    switch(type) {
      case 'stat':
        return (
          <View style={styles.statContent}>
            {icon && <View style={[styles.iconContainer, { backgroundColor: `${iconColor || theme.colors.primary}`}]} ]}20`}}><LocalIcon name={icon} size={24} color={iconColor || theme.colors.primary} /></View>}
            <View style={styles.statText}>
              <Text style={styles.statValue}>{title || value}</Text>
              <Text style={styles.statSubtitle}>{subtitle}</Text>
            </View>
          </View>
        );
      case 'order':
        return (
          <View style={styles.orderContent}>
            <View style={styles.orderHeader}>
              <Text style={styles.orderTitle}>Order #{data?.id}</Text>
              {status && <GlobalBadge variant="info">{status}</GlobalBadge>}
            </View>
            <View style={styles.orderMain}>
              <View><Text style={styles.orderCustomer}>{data?.customerName || subtitle}</Text><Text style={styles.orderDate}>{data?.createdAt ? new Date(data.createdAt).toLocaleDateString() : ''}</Text></View>
              <Text style={styles.orderAmount}>৳{data?.total?.toFixed(0) || value}</Text>
            </View>
          </View>
        );
      default: // Default card layout
        return (
          <>
            <View style={styles.cardHeader}>
              {icon && <View style={[styles.iconContainer, { backgroundColor: `${iconColor || theme.colors.primary}`}]} ]}20`}}><LocalIcon name={icon} size={24} color={iconColor || theme.colors.primary} /></View>}
              <View style={styles.cardInfo}>
                <View style={styles.titleRow}>
                  <Text style={styles.title}>{title}</Text>
                  {status && <GlobalBadge variant="default">{status}</GlobalBadge>}
                </View>
                {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
              </View>
            </View>
            {description && <Text style={styles.description}>{description}</Text>}
            {menuItems.length > 0 && (
                <View style={styles.actionButtons}>
                    <Menu visible={menuVisible} onDismiss={onMenuDismiss} anchor={<IconButton icon="dots-vertical" onPress={onMenuToggle} />}>
                        {menuItems.map((item, index) => <Menu.Item key={index} {...item} />)}
                    </Menu>
                </View>
            )}
          </>
        );
    }
  };

  const CardWrapper = onPress ? TouchableOpacity : View;
  const cardProps = onPress ? { onPress, onLongPress, activeOpacity: 0.8 } : {};

  return (
    <CardWrapper {...cardProps}>
      <Surface style={[styles.card, style} elevation={elevation}>
        {image && <Image source={{ uri: image } style={styles.cardImage} />}
        <View style={styles.cardContent}>
          {renderContent()}
        </View>
      </Surface>
    </CardWrapper>
  );
});

export default UniversalCard;

// This function creates the stylesheet object, allowing it to use the theme from props.
const createStyles = (theme) => StyleSheet.create({
  groupContainer: { marginBottom: SPACING.lg },
  groupTitle: { ...TYPOGRAPHY.titleLarge, marginBottom: SPACING.md, color: theme.colors.onBackground },
  grid: { flexDirection: 'row', flexWrap: 'wrap' },
  cardWrapper: { marginBottom: SPACING.sm },
  card: { overflow: 'hidden', borderRadius: BORDER_RADIUS.lg, backgroundColor: theme.colors.surface, borderWidth: 1, borderColor: theme.colors.outlineVariant },
  cardContent: { padding: SPACING.md },
  cardImage: { width: '100%', height: 120, resizeMode: 'cover' },
  // Stat Card Styles
  statContent: { alignItems: 'center', padding: SPACING.sm },
  iconContainer: { width: 48, height: 48, borderRadius: BORDER_RADIUS.full, justifyContent: 'center', alignItems: 'center', marginBottom: SPACING.sm },
  statText: { alignItems: 'center' },
  statValue: { ...TYPOGRAPHY.headlineSmall, color: theme.colors.onSurface },
  statSubtitle: { ...TYPOGRAPHY.bodyMedium, color: theme.colors.onSurfaceVariant },
  // Order Card Styles
  orderContent: { gap: SPACING.sm },
  orderHeader: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' },
  orderTitle: { ...TYPOGRAPHY.titleMedium },
  orderMain: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start' },
  orderCustomer: { ...TYPOGRAPHY.bodyLarge, fontWeight: '600' },
  orderDate: { ...TYPOGRAPHY.bodySmall, color: theme.colors.onSurfaceVariant },
  orderAmount: { ...TYPOGRAPHY.titleLarge, color: theme.colors.primary },
  // Default Card Styles
  cardHeader: { flexDirection: 'row', alignItems: 'center' },
  cardInfo: { flex: 1, marginLeft: SPACING.md },
  titleRow: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' },
  title: { ...TYPOGRAPHY.titleMedium },
  subtitle: { ...TYPOGRAPHY.bodyMedium, color: theme.colors.onSurfaceVariant, marginTop: 2 },
  description: { ...TYPOGRAPHY.bodyMedium, color: theme.colors.onSurfaceVariant, marginTop: SPACING.sm },
  actionButtons: { flexDirection: 'row', justifyContent: 'flex-end', marginTop: SPACING.md },
});