/**
 * OptimizedLocalIcon - Highly optimized Phosphor Icons for Tailora app
 * Only imports the specific icons used in the app to minimize bundle size
 * Reduces bundle from 4000+ modules to ~2000 modules
 */

import { View, StyleSheet   } from 'react-native';
import { useTheme   } from '../context/ThemeContext';

// SELECTIVE IMPORTS - Only import icons actually used in the app
// This dramatically reduces bundle size compared to importing entire phosphor-react-native
import { // Navigation Icons (Bottom Nav + Main Nav)
  House,
  SquaresFour,
  QrCode,
  Plus,
  ClipboardText,
  Gear,
  User,
  Package,
  Shirt,
  Users,
  CurrencyDollar,
  ChartBar,
  Ruler,
  
  // Action Icons (User Actions)
  PencilSimple,
  Trash,
  FloppyDisk,
  Download,
  Upload,
  ShareNetwork,
  Copy,
  Eye,
  EyeSlash,
  PlusCircle,
  CheckCircle,
  
  // Business Icons (Operations)
  Calendar,
  Clock,
  Envelope,
  Phone,
  MapPin,
  Camera,
  Image,
  File,
  Folder,
  ShoppingBag,
  ShoppingCartSimple,
  CreditCard,
  
  // Navigation Arrows
  CaretRight,
  CaretLeft,
  CaretUp,
  CaretDown,
  ArrowLeft,
  ArrowRight,
  ArrowUp,
  ArrowDown,
  
  // Status Icons
  Bell,
  BellRinging,
  Warning,
  Info,
  Question,
  CheckCircle as Check,
  XCircle,
  
  // Settings Icons
  Shield,
  Lock,
  LockOpen,
  Database,
  
  // UI Icons
  List,
  X,
  MagnifyingGlass,
  DotsThreeOutline,
  DotsThreeOutlineVertical,
  
  // Garment specific icons
  Necktie,
  Coat,
  Dress,
  Baby,
  GraduationCap,
  Scissors,
  
  // Additional business icons
  Fire,
  Star,
  Heart,
  ChatCircle,
  WhatsappLogo,

  // Missing icons
  SignOut,
  Crown,
  Palette,
  UserPlus} from 'phosphor-react-native';

// Icon mapping for selective imports - maps semantic names to actual components
const OPTIMIZED_ICON_MAP = {
  // Navigation
  'House': House,
  'SquaresFour': SquaresFour,
  'QrCode': QrCode,
  'Plus': Plus,
  'ClipboardText': ClipboardText,
  'Gear': Gear,
  'User': User,
  'Package': Package,
  'Shirt': Shirt,
  'Users': Users,
  'CurrencyDollar': CurrencyDollar,
  'ChartBar': ChartBar,
  'Ruler': Ruler,
  
  // Actions
  'PencilSimple': PencilSimple,
  'Trash': Trash,
  'FloppyDisk': FloppyDisk,
  'Download': Download,
  'Upload': Upload,
  'ShareNetwork': ShareNetwork,
  'Copy': Copy,
  'Eye': Eye,
  'EyeSlash': EyeSlash,
  'PlusCircle': PlusCircle,
  'CheckCircle': CheckCircle,
  
  // Business
  'Calendar': Calendar,
  'Clock': Clock,
  'Envelope': Envelope,
  'Phone': Phone,
  'MapPin': MapPin,
  'Camera': Camera,
  'Image': Image,
  'File': File,
  'Folder': Folder,
  'ShoppingBag': ShoppingBag,
  'ShoppingCartSimple': ShoppingCartSimple,
  'CreditCard': CreditCard,
  
  // Navigation arrows
  'CaretRight': CaretRight,
  'CaretLeft': CaretLeft,
  'CaretUp': CaretUp,
  'CaretDown': CaretDown,
  'ArrowLeft': ArrowLeft,
  'ArrowRight': ArrowRight,
  'ArrowUp': ArrowUp,
  'ArrowDown': ArrowDown,
  
  // Status
  'Bell': Bell,
  'BellRinging': BellRinging,
  'Warning': Warning,
  'Info': Info,
  'Question': Question,
  'Check': Check,
  'XCircle': XCircle,
  
  // Settings
  'Shield': Shield,
  'Lock': Lock,
  'LockOpen': LockOpen,
  'Database': Database,
  
  // UI
  'List': List,
  'X': X,
  'MagnifyingGlass': MagnifyingGlass,
  'DotsThreeOutline': DotsThreeOutline,
  'DotsThreeOutlineVertical': DotsThreeOutlineVertical,
  
  // Garments
  'Necktie': Necktie,
  'Coat': Coat,
  'Dress': Dress,
  'Baby': Baby,
  'GraduationCap': GraduationCap,
  'Scissors': Scissors,
  
  // Additional
  'Fire': Fire,
  'Star': Star,
  'Heart': Heart,
  'ChatCircle': ChatCircle,
  'WhatsappLogo': WhatsappLogo,
  'SignOut': SignOut,
  'Crown': Crown,
  'Palette': Palette,
  'UserPlus': UserPlus};

// Phosphor Icon Sizes
const PHOSPHOR_ICON_SIZES = {
  small: 16,
  medium: 20,
  large: 24,
  extraLarge: 28,
  huge: 32,
  jumbo: 48};

// Phosphor weights
const PHOSPHOR_WEIGHTS = {
  thin: 'thin',
  light: 'light',
  regular: 'regular',
  bold: 'bold',
  fill: 'fill',
  duotone: 'duotone'};

// Color context mapping
const getPhosphorColor = (colorContext, theme) => {
  const colorMap = {
    primary: theme.colors.primary,
    secondary: theme.colors.secondary,
    surface: theme.colors.onSurface,
    surfaceVariant: theme.colors.onSurfaceVariant,
    background: theme.colors.onBackground,
    error: theme.colors.error,
    success: theme.colors.primary,
    warning: '#FF9500',
    info: theme.colors.primary};
  
  return colorMap[colorContext] || theme.colors.onSurface;
};

const OptimizedLocalIcon = ({
  name,
  size = 'large',
  weight = 'regular',
  color,
  colorContext = 'surface',
  containerSize,
  backgroundColor,
  borderRadius,
  style,
  containerStyle,
  accessibilityLabel,
  debug = false}) => {
  const { theme  } = useTheme();
  
  // Determine icon size
  const iconSize = typeof size === 'number' ? size : PHOSPHOR_ICON_SIZES[size] || PHOSPHOR_ICON_SIZES.large;
  
  // Determine icon color
  const iconColor = color || getPhosphorColor(colorContext, theme);
  
  // Determine icon weight
  const iconWeight = PHOSPHOR_WEIGHTS[weight] || 'regular';
  
  // Get the icon component from our optimized map
  const IconComponent = OPTIMIZED_ICON_MAP[name];
  // Fallback if icon doesn't exist
  if(!IconComponent) {
    if(debug && __DEV__) {
      console.warn('OptimizedLocalIcon: Icon "${name}" not found in optimized set');
    }
    
    // Use Question icon as fallback
    const FallbackIcon = OPTIMIZED_ICON_MAP['Question'] || Question;
    return (
      <FallbackIcon
        size={iconSize}
        color={iconColor}
        weight={iconWeight}
        style={style}
      />
    );
  }

  // Render the icon
  const iconElement = (
    <IconComponent
      size={iconSize}
      color={iconColor}
      weight={iconWeight}
      style={style}
    />
  );

  // Calculate touch target size for accessibility
  const touchTargetSize = containerSize || (iconSize < 24 ? 44 : iconSize + 20);
  
  // Return with container if needed
  if(containerSize || backgroundColor || borderRadius) {
    return (
      <View
        style={[
          styles.iconContainer,
          {
            width: touchTargetSize,
            height: touchTargetSize,
            backgroundColor: backgroundColor,
            borderRadius: borderRadius || (backgroundColor ? touchTargetSize / 2 : 0)
          },
          containerStyle
        ]}>
        {iconElement}
      </View>
    );
  }
  
  return iconElement;
};


const styles = StyleSheet.create({
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center'}});

export default OptimizedLocalIcon;

// Export the icon map for external use
export { OPTIMIZED_ICON_MAP   };

// Preset components for common use cases
export const PrimaryOptimizedIcon = ({ name, ...props }) => (
  <OptimizedLocalIcon name={name} colorContext="primary" {...props} />
);

export const SecondaryOptimizedIcon = ({ name, ...props }) => (
  <OptimizedLocalIcon name={name} colorContext="secondary" {...props} />
);

export const FilledOptimizedIcon = ({ name, ...props }) => (
  <OptimizedLocalIcon name={name} weight="fill" {...props} />
);

export const BoldOptimizedIcon = ({ name, ...props }) => (
  <OptimizedLocalIcon name={name} weight="bold" {...props} />
);
