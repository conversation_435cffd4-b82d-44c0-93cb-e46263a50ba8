import React, { lazy, Suspense } from 'react';
import { View, ActivityIndicator, StyleSheet } from 'react-native';

// Lazy load the NavigationContext component
const NavigationContextComponent = lazy(() => import('../../context/NavigationContext.js'));

// Loading fallback component
const NavigationContextLoading = () => (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" color="#007AFF" />
  </View>
);

// Lazy wrapper component
const NavigationContextLazy = (props) => {
  return (
    <Suspense fallback={<NavigationContextLoading />}>
      <NavigationContextComponent {...props} />
    </Suspense>
  );
};






export default NavigationContextLazy;

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },

});