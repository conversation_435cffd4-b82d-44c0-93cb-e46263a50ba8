import React, { lazy, Suspense } from 'react';
import { View, ActivityIndicator, StyleSheet   } from 'react-native';

// Lazy load the NotificationSettingsScreen component
const NotificationSettingsScreenComponent = lazy(() => import('../../screens/NotificationSettingsScreen.js'));

// Loading fallback component
const NotificationSettingsScreenLoading = () => (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" color="#007AFF" />
  </View>
);

// Lazy wrapper component
const NotificationSettingsScreenLazy = (props) => {
  return (
    <Suspense fallback={<NotificationSettingsScreenLoading />}>
      <NotificationSettingsScreenComponent {...props} />
    </Suspense>
  );
};






export default NotificationSettingsScreenLazy;

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },

});