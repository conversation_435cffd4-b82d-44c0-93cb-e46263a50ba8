import React, { lazy, Suspense } from 'react';
import { View, ActivityIndicator, StyleSheet   } from 'react-native';

// Lazy load the notification component
const notificationComponent = lazy(() => import('../../services/notificationService.js'));

// Loading fallback component
const notificationLoading = () => (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" color="#007AFF" />
  </View>
);

// Lazy wrapper component
const notificationLazy = (props) => {
  return (
    <Suspense fallback={<notificationLoading />}>
      <notificationComponent {...props} />
    </Suspense>
  );
};






export default notificationLazy;

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5'}});