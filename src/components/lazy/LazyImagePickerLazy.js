import React, { lazy, Suspense } from 'react';
import { View, ActivityIndicator, StyleSheet   } from 'react-native';

// Lazy load the LazyImagePicker component
const LazyImagePickerComponent = lazy(() => import('LazyImagePicker.js'));

// Loading fallback component
const LazyImagePickerLoading = () => (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" color="#007AFF" />
  </View>
);

// Lazy wrapper component
const LazyImagePickerLazy = (props) => {
  return (
    <Suspense fallback={<LazyImagePickerLoading />}>
      <LazyImagePickerComponent {...props} />
    </Suspense>
  );
};






export default LazyImagePickerLazy;

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5'}});