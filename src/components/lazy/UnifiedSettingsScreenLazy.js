import React, { lazy, Suspense } from 'react';
import { View, ActivityIndicator, StyleSheet   } from 'react-native';

// Lazy load the UnifiedSettingsScreen component
const UnifiedSettingsScreenComponent = lazy(() => import('../../screens/UnifiedSettingsScreen.js'));

// Loading fallback component
const UnifiedSettingsScreenLoading = () => (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" color="#007AFF" />
  </View>
);

// Lazy wrapper component
const UnifiedSettingsScreenLazy = (props) => {
  return (
    <Suspense fallback={<UnifiedSettingsScreenLoading />}>
      <UnifiedSettingsScreenComponent {...props} />
    </Suspense>
  );
};






export default UnifiedSettingsScreenLazy;

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5'}});