import React, { lazy, Suspense } from 'react';
import { View, ActivityIndicator, StyleSheet } from 'react-native';

// Lazy load the CustomerSelectionModal component
const CustomerSelectionModalComponent = lazy(() => import('../CustomerSelectionModal.js'));

// Loading fallback component
const CustomerSelectionModalLoading = () => (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" color="#007AFF" />
  </View>
);

// Lazy wrapper component
const CustomerSelectionModalLazy = (props) => {
  return (
    <Suspense fallback={<CustomerSelectionModalLoading />}>
      <CustomerSelectionModalComponent {...props} />
    </Suspense>
  );
};






export default CustomerSelectionModalLazy;

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
});