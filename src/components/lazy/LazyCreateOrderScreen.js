import React, { Suspense } from 'react';
import { View, ActivityIndicator, StyleSheet } from 'react-native';
import { Text } from 'react-native-paper';

// Lazy load the heavy component
const CreateOrderScreen = React.lazy(() => import('../../screens/CreateOrderScreen'));

// Loading fallback component
const LoadingFallback = () => (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" />
    <Text style={styles.loadingText}>Loading CreateOrderScreen...</Text>
  </View>
);

// Lazy wrapper component
const LazyCreateOrderScreen = (props) => {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <CreateOrderScreen {...props} />
    </Suspense>
  );
};


const styles = StyleSheet.create({
  loadingText: {
    marginTop: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default LazyCreateOrderScreen;
