import React, { lazy, Suspense } from 'react';
import { View, ActivityIndicator, StyleSheet } from 'react-native';

// Lazy load the PaymentModal component
const PaymentModalComponent = lazy(() => import('../PaymentModal.js'));

// Loading fallback component
const PaymentModalLoading = () => (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" color="#007AFF" />
  </View>
);

// Lazy wrapper component
const PaymentModalLazy = (props) => {
  return (
    <Suspense fallback={<PaymentModalLoading />}>
      <PaymentModalComponent {...props} />
    </Suspense>
  );
};






export default PaymentModalLazy;

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },

});