import React, { lazy, Suspense } from 'react';
import { View, ActivityIndicator, StyleSheet } from 'react-native';

// Lazy load the React component
const ReactComponent = lazy(() => import('../charts/TailoringCharts.js'));

// Loading fallback component
const ReactLoading = () => (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" color="#007AFF" />
  </View>
);

// Lazy wrapper component
const ReactLazy = (props) => {
  return (
    <Suspense fallback={<ReactLoading />}>
      <ReactComponent {...props} />
    </Suspense>
  );
};






export default ReactLazy;

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },

});