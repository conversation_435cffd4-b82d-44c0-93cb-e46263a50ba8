import React, { lazy, Suspense } from 'react';
import { View, ActivityIndicator, StyleSheet } from 'react-native';

// Lazy load the DateRangePicker component
const DateRangePickerComponent = lazy(() => import('../DateRangePicker.js'));

// Loading fallback component
const DateRangePickerLoading = () => (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" color="#007AFF" />
  </View>
);

// Lazy wrapper component
const DateRangePickerLazy = (props) => {
  return (
    <Suspense fallback={<DateRangePickerLoading />}>
      <DateRangePickerComponent {...props} />
    </Suspense>
  );
};






export default DateRangePickerLazy;

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },

});