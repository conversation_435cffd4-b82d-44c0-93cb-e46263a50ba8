import React, { lazy, Suspense } from 'react';
import { View, ActivityIndicator, StyleSheet   } from 'react-native';

// Lazy load the ErrorBoundary component
const ErrorBoundaryComponent = lazy(() => import('../ErrorBoundary.js'));

// Loading fallback component
const ErrorBoundaryLoading = () => (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" color="#007AFF" />
  </View>
);

// Lazy wrapper component
const ErrorBoundaryLazy = (props) => {
  return (
    <Suspense fallback={<ErrorBoundaryLoading />}>
      <ErrorBoundaryComponent {...props} />
    </Suspense>
  );
};






export default ErrorBoundaryLazy;

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
});