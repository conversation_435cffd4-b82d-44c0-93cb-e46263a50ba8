import React, { Suspense } from 'react';
import { View, ActivityIndicator, StyleSheet } from 'react-native';
import { Text } from 'react-native-paper';

// Lazy load the heavy component
const UnifiedInventoryScreen = React.lazy(() => import('../../screens/UnifiedInventoryScreen'));

// Loading fallback component
const LoadingFallback = () => (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" />
    <Text style={styles.loadingText}>Loading UnifiedInventoryScreen...</Text>
  </View>
);

// Lazy wrapper component
const LazyUnifiedInventoryScreen = (props) => {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <UnifiedInventoryScreen {...props} />
    </Suspense>
  );
};


const styles = StyleSheet.create({
  loadingText: {
    marginTop: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default LazyUnifiedInventoryScreen;
