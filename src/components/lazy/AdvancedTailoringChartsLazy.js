import React, { lazy, Suspense } from 'react';
import { View, ActivityIndicator, StyleSheet   } from 'react-native';

// Lazy load the AdvancedTailoringCharts component
const AdvancedTailoringChartsComponent = lazy(() => import('../charts/AdvancedTailoringCharts.js'));

// Loading fallback component
const AdvancedTailoringChartsLoading = () => (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" color="#007AFF" />
  </View>
);

// Lazy wrapper component
const AdvancedTailoringChartsLazy = (props) => {
  return (
    <Suspense fallback={<AdvancedTailoringChartsLoading />}>
      <AdvancedTailoringChartsComponent {...props} />
    </Suspense>
  );
};






export default AdvancedTailoringChartsLazy;

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },

});