import React, { lazy, Suspense } from 'react';
import { View, ActivityIndicator, StyleSheet } from 'react-native';

// Lazy load the LoadingSpinner component
const LoadingSpinnerComponent = lazy(() => import('../LoadingSpinner.js'));

// Loading fallback component
const LoadingSpinnerLoading = () => (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" color="#007AFF" />
  </View>
);

// Lazy wrapper component
const LoadingSpinnerLazy = (props) => {
  return (
    <Suspense fallback={<LoadingSpinnerLoading />}>
      <LoadingSpinnerComponent {...props} />
    </Suspense>
  );
};






export default LoadingSpinnerLazy;

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
});