import React, { lazy, Suspense } from 'react';
import { View, ActivityIndicator, StyleSheet   } from 'react-native';

// Lazy load the UnifiedModal component
const UnifiedModalComponent = lazy(() => import('../UnifiedModal.js'));

// Loading fallback component
const UnifiedModalLoading = () => (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" color="#007AFF" />
  </View>
);

// Lazy wrapper component
const UnifiedModalLazy = (props) => {
  return (
    <Suspense fallback={<UnifiedModalLoading />}>
      <UnifiedModalComponent {...props} />
    </Suspense>
  );
};






export default UnifiedModalLazy;

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
});