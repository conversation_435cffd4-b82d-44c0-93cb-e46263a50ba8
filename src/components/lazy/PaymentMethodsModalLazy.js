import React, { lazy, Suspense } from 'react';
import { View, ActivityIndicator, StyleSheet } from 'react-native';

// Lazy load the PaymentMethodsModal component
const PaymentMethodsModalComponent = lazy(() => import('../PaymentMethodsModal.js'));

// Loading fallback component
const PaymentMethodsModalLoading = () => (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" color="#007AFF" />
  </View>
);

// Lazy wrapper component
const PaymentMethodsModalLazy = (props) => {
  return (
    <Suspense fallback={<PaymentMethodsModalLoading />}>
      <PaymentMethodsModalComponent {...props} />
    </Suspense>
  );
};






export default PaymentMethodsModalLazy;

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },

});