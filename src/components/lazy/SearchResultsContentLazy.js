import React, { lazy, Suspense } from 'react';
import { View, ActivityIndicator, StyleSheet } from 'react-native';

// Lazy load the SearchResultsContent component
const SearchResultsContentComponent = lazy(() => import('../content/SearchResultsContent.js'));

// Loading fallback component
const SearchResultsContentLoading = () => (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" color="#007AFF" />
  </View>
);

// Lazy wrapper component
const SearchResultsContentLazy = (props) => {
  return (
    <Suspense fallback={<SearchResultsContentLoading />}>
      <SearchResultsContentComponent {...props} />
    </Suspense>
  );
};






export default SearchResultsContentLazy;

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },

});