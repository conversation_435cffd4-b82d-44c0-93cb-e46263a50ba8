import React, { lazy, Suspense } from 'react';
import { View, ActivityIndicator, StyleSheet   } from 'react-native';

// Lazy load the OrderDetailsModal component
const OrderDetailsModalComponent = lazy(() => import('../orders/OrderDetailsModal.tsx'));

// Loading fallback component
const OrderDetailsModalLoading = () => (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" color="#007AFF" />
  </View>
);

// Lazy wrapper component
const OrderDetailsModalLazy = (props) => {
  return (
    <Suspense fallback={<OrderDetailsModalLoading />}>
      <OrderDetailsModalComponent {...props} />
    </Suspense>
  );
};






export default OrderDetailsModalLazy;

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
});