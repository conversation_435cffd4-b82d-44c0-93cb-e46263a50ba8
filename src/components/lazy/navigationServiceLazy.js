import React, { lazy, Suspense } from 'react';
import { View, ActivityIndicator, StyleSheet   } from 'react-native';

// Lazy load the navigationService component
const navigationServiceComponent = lazy(() => import('../../services/NavigationService.ts'));

// Loading fallback component
const navigationServiceLoading = () => (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" color="#007AFF" />
  </View>
);

// Lazy wrapper component
const navigationServiceLazy = (props) => {
  return (
    <Suspense fallback={<navigationServiceLoading />}>
      <navigationServiceComponent {...props} />
    </Suspense>
  );
};






export default navigationServiceLazy;

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },

});