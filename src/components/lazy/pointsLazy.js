import React, { lazy, Suspense } from 'react';
import { View, ActivityIndicator, StyleSheet   } from 'react-native';

// Lazy load the points component
const pointsComponent = lazy(() => import('../charts/NativeCharts.js'));

// Loading fallback component
const pointsLoading = () => (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" color="#007AFF" />
  </View>
);

// Lazy wrapper component
const pointsLazy = (props) => {
  return (
    <Suspense fallback={<pointsLoading />}>
      <pointsComponent {...props} />
    </Suspense>
  );
};






export default pointsLazy;

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },

});