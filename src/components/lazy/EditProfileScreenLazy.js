import React, { lazy, Suspense } from 'react';
import { View, ActivityIndicator, StyleSheet   } from 'react-native';

// Lazy load the EditProfileScreen component
const EditProfileScreenComponent = lazy(() => import('../../screens/EditProfileScreen.js'));

// Loading fallback component
const EditProfileScreenLoading = () => (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" color="#007AFF" />
  </View>
);

// Lazy wrapper component
const EditProfileScreenLazy = (props) => {
  return (
    <Suspense fallback={<EditProfileScreenLoading />}>
      <EditProfileScreenComponent {...props} />
    </Suspense>
  );
};






export default EditProfileScreenLazy;

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5'}});