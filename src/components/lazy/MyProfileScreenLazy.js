import React, { lazy, Suspense } from 'react';
import { View, ActivityIndicator, StyleSheet   } from 'react-native';

// Lazy load the MyProfileScreen component
const MyProfileScreenComponent = lazy(() => import('../../screens/ProfileScreen.js'));

// Loading fallback component
const MyProfileScreenLoading = () => (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" color="#007AFF" />
  </View>
);

// Lazy wrapper component
const MyProfileScreenLazy = (props) => {
  return (
    <Suspense fallback={<MyProfileScreenLoading />}>
      <MyProfileScreenComponent {...props} />
    </Suspense>
  );
};






export default MyProfileScreenLazy;

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },

});