import React, { Suspense } from 'react';
import { View, ActivityIndicator, StyleSheet   } from 'react-native';
import { Text   } from 'react-native-paper';

// Lazy load the heavy component
const ProfitLossScreen = React.lazy(() => import('../../screens/ProfitLossScreen'));

// Loading fallback component
const LoadingFallback = () => (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" />
    <Text style={styles.loadingText}>Loading ProfitLossScreen...</Text>
  </View>
);

// Lazy wrapper component
const LazyProfitLossScreen = (props) => {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <ProfitLossScreen {...props} />
    </Suspense>
  );
};


const styles = StyleSheet.create({
  loadingText: {
    marginTop: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default LazyProfitLossScreen;
