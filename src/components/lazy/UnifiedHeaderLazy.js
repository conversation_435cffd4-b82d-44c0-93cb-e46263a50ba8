import React, { lazy, Suspense } from 'react';
import { View, ActivityIndicator, StyleSheet } from 'react-native';

// Lazy load the UnifiedHeader component
const UnifiedHeaderComponent = lazy(() => import('../CommonHeader.js'));

// Loading fallback component
const UnifiedHeaderLoading = () => (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" color="#007AFF" />
  </View>
);

// Lazy wrapper component
const UnifiedHeaderLazy = (props) => {
  return (
    <Suspense fallback={<UnifiedHeaderLoading />}>
      <UnifiedHeaderComponent {...props} />
    </Suspense>
  );
};

export default UnifiedHeaderLazy;

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
});