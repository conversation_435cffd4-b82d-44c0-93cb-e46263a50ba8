import React, { lazy, Suspense } from 'react';
import { View, ActivityIndicator, StyleSheet   } from 'react-native';

// Lazy load the QuickCustomerAddModal component
const QuickCustomerAddModalComponent = lazy(() => import('../QuickCustomerAddModal.js'));

// Loading fallback component
const QuickCustomerAddModalLoading = () => (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" color="#007AFF" />
  </View>
);

// Lazy wrapper component
const QuickCustomerAddModalLazy = (props) => {
  return (
    <Suspense fallback={<QuickCustomerAddModalLoading />}>
      <QuickCustomerAddModalComponent {...props} />
    </Suspense>
  );
};






export default QuickCustomerAddModalLazy;

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },

});