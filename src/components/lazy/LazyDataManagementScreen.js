import React, { Suspense } from 'react';
import { View, ActivityIndicator, StyleSheet   } from 'react-native';
import { Text   } from 'react-native-paper';

// Lazy load the heavy component
const DataManagementScreen = React.lazy(() => import('../../screens/DataManagementScreen'));

// Loading fallback component
const LoadingFallback = () => (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" />
    <Text style={styles.loadingText}>Loading DataManagementScreen...</Text>
  </View>
);

// Lazy wrapper component
const LazyDataManagementScreen = (props) => {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <DataManagementScreen {...props} />
    </Suspense>
  );
};


const styles = StyleSheet.create({
  loadingText: {
    marginTop: 16},
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center'}});

export default LazyDataManagementScreen;
