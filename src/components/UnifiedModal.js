/**
 * UnifiedModal - Consolidated modal system
 * Replaces: ConfirmationModal, PaymentModal, CustomerSelectionModal, QuickCustomerAddModal
 * Features: Dynamic content, type-based configuration, unified styling
 */

import React, { useState, useMemo, useCallback } from 'react';
import { View, StyleSheet, FlatList, Dimensions, KeyboardAvoidingView, Platform } from 'react-native';
import { Modal } from 'react-native-paper';
import { Portal } from 'react-native-paper';
import { Text } from 'react-native-paper';
import { Surface } from 'react-native-paper';
import { TouchableRipple } from 'react-native-paper';
import { Avatar } from 'react-native-paper';
import { Chip } from 'react-native-paper';
import { IconButton } from 'react-native-paper';
import { FAB } from 'react-native-paper';
import { Switch } from 'react-native-paper';
import { RadioButton } from 'react-native-paper';
import { Searchbar } from 'react-native-paper';
import LocalIcon from './LocalIcon';
import { useTheme } from '../context/ThemeContext';
import { SPACING, BORDER_RADIUS } from '../theme/designTokens';
import EnhancedInput from './EnhancedInput';
import EnhancedButton from './EnhancedButton';

const { height: screenHeight, width: screenWidth } = Dimensions.get('window');

// Modal types and configurations
export const MODAL_TYPES = {
  CONFIRMATION: 'confirmation',
  ALERT: 'alert',
  ERROR: 'error',
  SUCCESS: 'success',
  PAYMENT: 'payment',
  CUSTOMER_SELECTION: 'customer_selection',
  CUSTOMER_ADD: 'customer_add',
  FORM: 'form',
  LIST_SELECTION: 'list_selection',
  BOTTOM_SHEET: 'bottom_sheet',
};

export const MODAL_SIZES = {
  SMALL: 'small',
  MEDIUM: 'medium',
  LARGE: 'large',
  FULL: 'full',
};

const UnifiedModal = ({
  visible = false,
  type = MODAL_TYPES.CONFIRMATION,
  size = MODAL_SIZES.MEDIUM,
  title = 'Modal Title',
  subtitle,
  message,
  icon,
  
  // Action buttons
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  onConfirm,
  onCancel,
  onDismiss,
  showCancel = true,
  destructive = false,
  
  // Content props
  children,
  data = [],
  searchable = false,
  
  // Payment modal specific
  order,
  onPaymentRecorded,
  
  // Customer selection specific
  customers = [],
  selectedCustomer,
  onCustomerSelect,
  onAddCustomer,
  
  // Form specific
  fields = [],
  formData = {},
  onFormChange,
  
  // Style overrides
  style,
  contentStyle,
}) => {
  const { theme } = useTheme();
  
  // Local state
  const [searchQuery, setSearchQuery] = useState('');
  const [localFormData, setLocalFormData] = useState(formData);
  const [loading, setLoading] = useState(false);

  // Modal configuration based on type
  const modalConfig = useMemo(() => {
    switch (type) {
      case MODAL_TYPES.ERROR:
        return {
          icon: icon || 'alert-circle',
          iconColor: theme.colors.error,
          confirmText: confirmText || 'OK',
          showCancel: false,
          size: size || MODAL_SIZES.SMALL,
        };

      case MODAL_TYPES.SUCCESS:
        return {
          icon: icon || 'check-circle',
          iconColor: theme.colors.primary,
          confirmText: confirmText || 'OK',
          showCancel: false,
          size: size || MODAL_SIZES.SMALL,
        };

      case MODAL_TYPES.ALERT:
        return {
          icon: icon || 'information',
          iconColor: theme.colors.primary,
          confirmText: confirmText || 'OK',
          showCancel: false,
          size: size || MODAL_SIZES.SMALL,
        };

      case MODAL_TYPES.PAYMENT:
        return {
          icon: icon || 'credit-card',
          iconColor: theme.colors.primary,
          confirmText: confirmText || 'Record Payment',
          showCancel: true,
          size: size || MODAL_SIZES.MEDIUM,
        };

      case MODAL_TYPES.CUSTOMER_SELECTION:
        return {
          icon: icon || 'account-group',
          iconColor: theme.colors.primary,
          confirmText: confirmText || 'Select',
          showCancel: true,
          size: size || MODAL_SIZES.LARGE,
        };

      case MODAL_TYPES.CUSTOMER_ADD:
        return {
          icon: icon || 'account-plus',
          iconColor: theme.colors.primary,
          confirmText: confirmText || 'Add Customer',
          showCancel: true,
          size: size || MODAL_SIZES.MEDIUM,
        };

      case MODAL_TYPES.FORM:
        return {
          icon: icon || 'form-select',
          iconColor: theme.colors.primary,
          confirmText: confirmText || 'Submit',
          showCancel: true,
          size: size || MODAL_SIZES.MEDIUM,
        };

      case MODAL_TYPES.LIST_SELECTION:
        return {
          icon: icon || 'format-list-bulleted',
          iconColor: theme.colors.primary,
          confirmText: confirmText || 'Select',
          showCancel: true,
          size: size || MODAL_SIZES.LARGE,
        };

      case MODAL_TYPES.BOTTOM_SHEET:
        return {
          icon: null,
          iconColor: theme.colors.primary,
          confirmText: confirmText || 'Done',
          showCancel: false,
          size: size || MODAL_SIZES.LARGE,
          isBottomSheet: true,
        };

      case MODAL_TYPES.CONFIRMATION:
      default:
        return {
          icon: icon || 'help-circle',
          iconColor: theme.colors.primary,
          confirmText: confirmText || 'Confirm',
          showCancel: showCancel,
          size: size || MODAL_SIZES.SMALL,
        };
    }
  }, [type, icon, confirmText, showCancel, size, theme]);

  // Size configuration
  const sizeConfig = useMemo(() => {
    switch (modalConfig.size) {
      case MODAL_SIZES.SMALL:
        return {
          width: Math.min(screenWidth * 0.85, 400),
          maxHeight: screenHeight * 0.4,
        };
      case MODAL_SIZES.MEDIUM:
        return {
          width: Math.min(screenWidth * 0.9, 500),
          maxHeight: screenHeight * 0.6,
        };
      case MODAL_SIZES.LARGE:
        return {
          width: Math.min(screenWidth * 0.95, 600),
          maxHeight: screenHeight * 0.8,
        };
      case MODAL_SIZES.FULL:
        return {
          width: screenWidth,
          height: screenHeight,
        };
      default:
        return {
          width: Math.min(screenWidth * 0.9, 500),
          maxHeight: screenHeight * 0.6,
        };
    }
  }, [modalConfig.size]);

  // Handle actions
  const handleConfirm = useCallback(async () => {
    if (onConfirm) {
      setLoading(true);
      try {
        await onConfirm(localFormData);
      } catch (error) {
        console.error('Modal confirm error:', error);
      } finally {
        setLoading(false);
      }
    }
    if (onDismiss) {
      onDismiss();
    }
  }, [onConfirm, localFormData, onDismiss]);

  const handleCancel = useCallback(() => {
    if (onCancel) {
      onCancel();
    }
    if (onDismiss) {
      onDismiss();
    }
  }, [onCancel, onDismiss]);

  // Update form data
  const updateFormData = (key, value) => {
    const newData = { ...localFormData, [key]: value };
    setLocalFormData(newData);
    if (onFormChange) {
      onFormChange(newData);
    }
  };

  // Render content based on type
  const renderContent = () => {
    switch (type) {
      case MODAL_TYPES.PAYMENT:
        return renderPaymentContent();
      case MODAL_TYPES.CUSTOMER_SELECTION:
        return renderCustomerSelectionContent();
      case MODAL_TYPES.CUSTOMER_ADD:
        return renderCustomerAddContent();
      case MODAL_TYPES.FORM:
        return renderFormContent();
      case MODAL_TYPES.LIST_SELECTION:
        return renderListSelectionContent();
      default:
        return renderDefaultContent();
    }
  };

  // Default content (confirmation, alert, etc.)
  const renderDefaultContent = () => (
    <View style={styles.style2}>
      {message && (
        <Text variant="bodyLarge" style={styles.style4}>
          {message}
        </Text>
      )}
      {children}
    </View>
  );

  // Payment modal content
  const renderPaymentContent = () => {
    const [amount, setAmount] = useState('');
    const [paymentMethod, setPaymentMethod] = useState('Cash');
    const paymentMethods = ['Cash', 'Card', 'UPI', 'Bank Transfer', 'Cheque'];

    return (
      <View style={styles.style2}>
        <Text variant="bodyLarge" style={styles.style4}>
          Order #{order?.id} - {order?.customer_name}
        </Text>

        <EnhancedInput
          label="Payment Amount"
          value={amount}
          onChangeText={setAmount}
          keyboardType="numeric"
          style={styles.style2}
        />

        <Text variant="titleMedium" style={styles.style4}>
          Payment Method
        </Text>
        <RadioButton.Group
          onValueChange={setPaymentMethod}
          value={paymentMethod}>
          {paymentMethods.map((method) => (
            <RadioButton.Item key={method} label={method} value={method} />
          ))}
        </RadioButton.Group>
      </View>
    );
  };

  // Customer selection content
  const renderCustomerSelectionContent = () => {
    const [activeFilter, setActiveFilter] = useState('All');
    const filters = ['All', 'Men', 'Women', 'Children', 'VIP', 'Recent'];

    const filteredCustomers = useMemo(() => {
      let filtered = customers;

      if (searchQuery) {
        filtered = filtered.filter(customer =>
          customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          customer.phone?.includes(searchQuery)
        );
      }

      if (activeFilter !== 'All') {
        filtered = filtered.filter(customer => {
          switch (activeFilter) {
            case 'Men':
              return customer.gender === 'Male';
            case 'Women':
              return customer.gender === 'Female';
            case 'Children':
              return customer.age < 18;
            case 'VIP':
              return customer.isVIP;
            case 'Recent':
              return customer.lastOrderDate && new Date(customer.lastOrderDate) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
            default:
              return true;
          }
        });
      }

      return filtered;
    }, [customers, searchQuery, activeFilter]);

    const renderCustomerItem = ({ item: customer }) => (
      <TouchableRipple
        onPress={() => onCustomerSelect && onCustomerSelect(customer)}
        style={styles.style7}>
        <View style={styles.style2}>
          <Avatar.Text
            size={40}
            label={customer.name.split(' ').map(n => n[0]).join('').substring(0, 2)}
            style={styles.style6}
          />
          <View style={styles.style2}>
            <Text variant="titleMedium" style={styles.style4}}>
              {customer.name}
            </Text>
            <Text variant="bodySmall" style={styles.style3}}>
              {customer.phone} • {customer.gender}
            </Text>
          </View>
          {customer.isVIP && (
            <Chip size="small" mode="outlined">VIP</Chip>
          )}
        </View>
      </TouchableRipple>
    );

    return (
      <View style={styles.style2}>
        {searchable && (
          <Searchbar
            placeholder="Search customers..."
            onChangeText={setSearchQuery}
            value={searchQuery}
            style={styles.style2}
          />
        )}

        <View style={styles.style2}>
          {filters.map((filter) => (
            <Chip
              key={filter}
              selected={activeFilter === filter}
              onPress={() => setActiveFilter(filter)}
              style={styles.style2}>
              {filter}
            </Chip>
          ))}
        </View>

        <FlatList
          data={filteredCustomers}
          renderItem={renderCustomerItem}
          keyExtractor={(item) => item.id.toString()}
          style={styles.style2}
          showsVerticalScrollIndicator={false}
        />

        {onAddCustomer && (
          <FAB
            icon="plus"
            onPress={onAddCustomer}
            style={styles.style2}
            size="small"
          />
        )}
      </View>
    );
  };

  // Customer add content
  const renderCustomerAddContent = () => {
    const [customerData, setCustomerData] = useState({
      name: '',
      phone: '',
      email: '',
      gender: 'Male',
      address: '',
    });

    const updateCustomerData = (key, value) => {
      setCustomerData(prev => ({ ...prev, [key]: value }));
    };

    return (
      <View style={styles.style2}>
        <EnhancedInput
          label="Customer Name *"
          value={customerData.name}
          onChangeText={(text) => updateCustomerData('name', text)}
          style={styles.style2}
        />

        <EnhancedInput
          label="Phone Number *"
          value={customerData.phone}
          onChangeText={(text) => updateCustomerData('phone', text)}
          keyboardType="phone-pad"
          style={styles.style2}
        />

        <EnhancedInput
          label="Email Address"
          value={customerData.email}
          onChangeText={(text) => updateCustomerData('email', text)}
          keyboardType="email-address"
          style={styles.style2}
        />

        <Text variant="titleMedium" style={styles.style4}>
          Gender
        </Text>
        <RadioButton.Group
          onValueChange={(value) => updateCustomerData('gender', value)}
          value={customerData.gender}>
          <RadioButton.Item label="Male" value="Male" />
          <RadioButton.Item label="Female" value="Female" />
          <RadioButton.Item label="Other" value="Other" />
        </RadioButton.Group>

        <EnhancedInput
          label="Address"
          value={customerData.address}
          onChangeText={(text) => updateCustomerData('address', text)}
          multiline
          numberOfLines={3}
          style={styles.style2}
        />
      </View>
    );
  };

  // Form content
  const renderFormContent = () => (
    <View style={styles.style2}>
      {fields.map((field, index) => {
        switch (field.type) {
          case 'text':
            return (
              <EnhancedInput
                key={field.key || index}
                label={field.label}
                value={localFormData[field.key] || ''}
                onChangeText={(text) => updateFormData(field.key, text)}
                placeholder={field.placeholder}
                multiline={field.multiline}
                numberOfLines={field.numberOfLines}
                keyboardType={field.keyboardType}
                style={styles.style2}
              />
            );
          case 'switch':
            return (
              <View key={field.key || index} style={styles.style2}>
                <Text variant="titleMedium" style={styles.style4}}>
                  {field.label}
                </Text>
                <Switch
                  value={localFormData[field.key] || false}
                  onValueChange={(value) => updateFormData(field.key, value)}
                />
              </View>
            );
          case 'radio':
            return (
              <View key={field.key || index} style={styles.style2}>
                <Text variant="titleMedium" style={styles.style4}>
                  {field.label}
                </Text>
                <RadioButton.Group
                  onValueChange={(value) => updateFormData(field.key, value)}
                  value={localFormData[field.key] || field.options[0]?.value}>
                  {field.options.map((option) => (
                    <RadioButton.Item
                      key={option.value}
                      label={option.label}
                      value={option.value}
                    />
                  ))}
                </RadioButton.Group>
              </View>
            );
          default:
            return null;
        }
      })}
    </View>
  );

  // List selection content
  const renderListSelectionContent = () => {
    const filteredData = useMemo(() => {
      if (!searchQuery) return data;
      return data.filter(item =>
        item.label?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.title?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }, [data, searchQuery]);

    const renderListItem = ({ item }) => (
      <TouchableRipple
        onPress={() => onConfirm && onConfirm(item)}
        style={styles.style2}>
        <View style={styles.style2}>
          {item.icon && (
            <LocalIcon name={item.icon} size={24} color={theme.colors.onSurface} style={styles.style2} />
          )}
          <View style={styles.style2}>
            <Text variant="titleMedium" style={styles.style4}}>
              {item.label || item.title}
            </Text>
            {item.subtitle && (
              <Text variant="bodySmall" style={styles.style3}}>
                {item.subtitle}
              </Text>
            )}
          </View>
        </View>
      </TouchableRipple>
    );

    return (
      <View style={styles.style2}>
        {searchable && (
          <Searchbar
            placeholder="Search..."
            onChangeText={setSearchQuery}
            value={searchQuery}
            style={styles.style2}
          />
        )}

        <FlatList
          data={filteredData}
          renderItem={renderListItem}
          keyExtractor={(item, index) => item.id?.toString() || index.toString()}
          style={styles.style2}
          showsVerticalScrollIndicator={false}
        />
      </View>
    );
  };

  // Get confirm button color
  const getConfirmButtonColor = () => {
    if (destructive) return theme.colors.error;
    if (type === MODAL_TYPES.ERROR) return theme.colors.error;
    return theme.colors.primary;
  };

  // Dynamic styles that depend on theme
  const dynamicStyles = {
    confirmButton: {
      backgroundColor: getConfirmButtonColor(),
    },
    surface: {
      backgroundColor: theme.colors.surface,
    },
    primaryText: {
      color: theme.colors.onSurface,
    },
    secondaryText: {
      color: theme.colors.onSurfaceVariant,
    },
    iconBackground: {
      backgroundColor: modalConfig.iconColor + '20',
    },
  };

  // Modal presentation style
  const modalStyle = modalConfig.isBottomSheet 
    ? styles.bottomSheetModal 
    : styles.centerModal;

  const containerStyle = modalConfig.isBottomSheet
    ? [styles.bottomSheetContainer, dynamicStyles.surface]
    : [styles.modalContainer, dynamicStyles.surface, sizeConfig];

  return (
    <Portal>
      <Modal
        visible={visible}
        transparent
        animationType={modalConfig.isBottomSheet ? "slide" : "fade"}
        onRequestClose={handleCancel}
        statusBarTranslucent
      >
        <KeyboardAvoidingView 
          style={styles.style2}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          <View style={styles.style2}>
            <Surface 
              style={styles.style2} 
              elevation={modalConfig.isBottomSheet ? 0 : 8}>
              {/* Header */}
              <View style={styles.style2}>
                {/* Icon */}
                {modalConfig.icon && (
                  <View style={styles.style2}>
                    <IconButton
                      icon={modalConfig.icon}
                      size={32}
                      iconColor={modalConfig.iconColor}
                      style={styles.style5}
                    />
                  </View>
                )}

                {/* Title and subtitle */}
                <View style={styles.style2}>
                  <Text variant="headlineSmall" style={styles.style4}>
                    {title}
                  </Text>
                  {subtitle && (
                    <Text variant="bodyMedium" style={styles.style3}>
                      {subtitle}
                    </Text>
                  )}
                </View>

                {/* Close button */}
                {!destructive && type !== MODAL_TYPES.ERROR && (
                  <IconButton
                    icon="close"
                    size={20}
                    onPress={handleCancel}
                    style={styles.style2}
                  />
                )}
              </View>

              {/* Content */}
              <View style={styles.style2}>
                {renderContent()}
              </View>

              {/* Actions */}
              <View style={styles.style2}>
                {modalConfig.showCancel && (
                  <EnhancedButton
                    variant="outline"
                    title={cancelText}
                    onPress={handleCancel}
                    style={styles.style2}
                  />
                )}

                <EnhancedButton
                  variant="primary"
                  title={modalConfig.confirmText}
                  onPress={handleConfirm}
                  style={styles.style1}
                  loading={loading}
                  disabled={loading}
                />
              </View>
            </Surface>
          </View>
        </KeyboardAvoidingView>
      </Modal>
    </Portal>
  );
};

// Basic styles - will be extended in next chunk





export default UnifiedModal;

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  centerModal: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
  },
  bottomSheetModal: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  modalContainer: {
    borderRadius: BORDER_RADIUS.lg,
    maxWidth: '100%',
  },
  bottomSheetContainer: {
    borderTopLeftRadius: BORDER_RADIUS.xl,
    borderTopRightRadius: BORDER_RADIUS.xl,
    paddingBottom: SPACING.xl,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingTop: SPACING.lg,
    paddingBottom: SPACING.md,
  },
  iconContainer: {
    marginRight: SPACING.md,
  },
  headerIcon: {
    borderRadius: BORDER_RADIUS.md,
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontWeight: '600',
  },
  subtitle: {
    marginTop: SPACING.xs,
  },
  closeButton: {
    marginLeft: SPACING.sm,
  },
  content: {
    paddingHorizontal: SPACING.lg,
    paddingBottom: SPACING.md,
  },
  defaultContent: {
    alignItems: 'center',
  },
  message: {
    textAlign: 'center',
    marginBottom: SPACING.md,
  },
  actions: {
    flexDirection: 'row',
    paddingHorizontal: SPACING.lg,
    paddingBottom: SPACING.lg,
    gap: SPACING.md,
  },
  cancelButton: {
    flex: 1,
  },
  confirmButton: {
    flex: 2,
  },
  singleButton: {
    flex: 1,
  },
  paymentContent: {
    minHeight: 200,
  },
  orderInfo: {
    textAlign: 'center',
    marginBottom: SPACING.lg,
    fontWeight: '500',
  },
  sectionTitle: {
    fontWeight: '500',
    marginTop: SPACING.md,
    marginBottom: SPACING.sm,
  },
  input: {
    marginBottom: SPACING.md,
  },
  customerSelectionContent: {
    maxHeight: screenHeight * 0.6,
  },
  searchBar: {
    marginBottom: SPACING.md,
  },
  filterContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: SPACING.md,
    gap: SPACING.xs,
  },
  filterChip: {
    marginRight: SPACING.xs,
    marginBottom: SPACING.xs,
  },
  customerList: {
    flex: 1,
  },
  customerItem: {
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.sm,
    borderRadius: BORDER_RADIUS.md,
    marginBottom: SPACING.xs,
  },
  customerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  customerInfo: {
    flex: 1,
    marginLeft: SPACING.md,
  },
  addCustomerFab: {
    position: 'absolute',
    bottom: SPACING.md,
    right: SPACING.md,
  },
  customerAddContent: {
    minHeight: 300,
  },
  formContent: {
    minHeight: 200,
  },
  switchField: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: SPACING.md,
  },
  radioField: {
    marginBottom: SPACING.md,
  },
  listSelectionContent: {
    maxHeight: screenHeight * 0.6,
  },
  list: {
    flex: 1,
  },
  listItem: {
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.sm,
    borderRadius: BORDER_RADIUS.md,
    marginBottom: SPACING.xs,
  },
  listItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  listItemIcon: {
    marginRight: SPACING.md,
  },
  listItemText: {
    flex: 1,
  },
  style1: {
    flex: 2,
  },
  style2: {
    flex: 1,
  },
  style3: {
    opacity: 0.7,
  },
  style4: {
    fontWeight: '500',
  },
  style5: {
    opacity: 0.2,
  },
  style6: {
    padding: 16,
  },
  style7: {
    backgroundColor: 'transparent',
  },
});