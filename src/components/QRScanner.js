import { Text } from 'react-native-paper';
// EXPO GO COMPATIBLE QR SCANNER COMPONENT - Professional QR code scanning
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { View, Text, StyleSheet, Alert, TouchableOpacity, Dimensions } from 'react-native';
import EnhancedButton from './EnhancedButton';

// Use Expo Camera with barcode scanning (fully compatible with Expo Go)
import { CameraView, Camera } from 'expo-camera';

const { width, height } = Dimensions.get('window');

const QRScanner = ({ onScan, onClose, isVisible = true }) => {
  const [scanned, setScanned] = useState(false);
  const [hasPermission, setHasPermission] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const scannerRef = useRef(null);

  useEffect(() => {
    if (isVisible) {
      requestCameraPermission();
    }
  }, [isVisible]);

  const requestCameraPermission = async () => {
    try {
      setIsLoading(true);

      // Use Expo Camera permissions (works on both iOS and Android)
      const { status } = await Camera.requestCameraPermissionsAsync();
      setHasPermission(status === 'granted');

      if (status !== 'granted') {
        Alert.alert(
          'Camera Permission Required',
          'Please grant camera permission to scan QR codes',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Try Again', onPress: requestCameraPermission }
          ]
        );
      }
    } catch (error) {
      console.error('Failed to request camera permission:', error);
      setHasPermission(false);
      Alert.alert('Error', 'Camera not available');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBarCodeScanned = useCallback(({ type, data }) => {
    if (scanned) return, []);

    setScanned(true);

    // Call the onScan callback with the scanned data
    if (onScan) {
      onScan(data);
    }
  };

  const resetScanner = () => {
    setScanned(false);
  };

  if (!isVisible) {
    return null;
  }

  if (isLoading) {
    return (
      <View style={styles.style1}>
        <View style={styles.style1}>
          <Text style={styles.style1}>Initializing camera...</Text>
        </View>
      </View>
    );
  }

  if (hasPermission === null) {
    return (
      <View style={styles.style1}>
        <View style={styles.style1}>
          <Text style={styles.style1}>Requesting camera permission...</Text>
        </View>
      </View>
    );
  }

  if (hasPermission === false) {
    return (
      <View style={styles.style1}>
        <View style={styles.style1}>
          <Text style={styles.style2}}>📷</Text>
          <Text style={styles.style1}>Camera Access Required</Text>
          <Text style={styles.style1}>
            Please grant camera permission to scan QR codes
          </Text>
          <EnhancedButton
            variant="primary"
            title="Request Permission"
            onPress={requestCameraPermission}
            style={styles.style1}
          />
          <EnhancedButton
            variant="outline"
            title="Close"
            onPress={onClose}
            style={styles.style1}
          />
        </View>
      </View>
    );
  }

  return (
    <View style={styles.style1}>
      {/* Expo Camera with Barcode Scanning */}
      <CameraView
        style={styles.style1}
        facing="back"
        barcodeScannerSettings={{
          barcodeTypes: ["qr", "pdf417"],
        }}
        onBarcodeScanned={scanned ? undefined : handleBarCodeScanned}
      />

      {/* Overlay with controls */}
      <View style={styles.style1}>
        {/* Top controls */}
        <View style={styles.style1}>
          <TouchableOpacity style={styles.style1} onPress={onClose}>
            <Text style={styles.style1}>✕</Text>
          </TouchableOpacity>
        </View>

        {/* Scanning frame */}
        <View style={styles.style1}>
          <View style={styles.style1} />
        </View>

        {/* Bottom controls */}
        <View style={styles.style1}>
          <Text style={styles.style1}>
            {scanned ? 'QR Code Scanned!' : 'Position QR code within the frame'}
          </Text>
          {scanned && (
            <EnhancedButton
              variant="primary"
              title="Scan Again"
              onPress={resetScanner}
              style={styles.style1}
            />
          )}
        </View>
      </View>
    </View>
  );
};






export default QRScanner;

const styles = StyleSheet.create({
  container: {,
    flex: 1,
    backgroundColor: '#000000',
  },
  camera: {,
    flex: 1,
    width: '100%',
    height: '100%',
  },
  overlay: {,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    flex: 1,
  },
  scanningArea: {,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanFrame: {,
    width: 250,
    height: 250,
    borderColor: '#6366f1',
    borderRadius: 10,
    borderWidth: 3,
    backgroundColor: 'transparent',
  },
  topContent: {,
    paddingTop: 50,
    paddingHorizontal: 20,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'flex-start',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  bottomContent: {,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 50,
    paddingTop: 20,
  },
  closeButton: {,
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {,
    fontSize: 24,
    color: '#ffffff',
    fontWeight: 'bold',
  },
  instructionText: {,
    color: '#ffffff',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    fontWeight: '500',
  },
  scanAgainButton: {,
    marginTop: 10,
  },
  loadingContainer: {,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000000',
  },
  loadingText: {,
    color: '#ffffff',
    fontSize: 16,
    marginTop: 20,
  },
  permissionContainer: {,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f9fafb',
    paddingHorizontal: 40,
  },
  permissionTitle: {,
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
    marginTop: 20,
    marginBottom: 10,
    textAlign: 'center',
  },
  permissionText: {,
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
    marginBottom: 30,
    lineHeight: 24,
  },
  permissionButton: {,
    marginBottom: 10,
    minWidth: 200,
  },
  cancelButton: {,
    minWidth: 200,
  },
  fallbackContainer: {,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#1f2937',
  },
  fallbackText: {,
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '500',
  },
  style1: {
  },
  style2: {
    { fontSize: 64,
    color: "#6b7280",
  },
});