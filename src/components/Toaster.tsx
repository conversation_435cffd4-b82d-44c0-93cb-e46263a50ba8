/**
 * Toaster Component - Beautiful toast notifications
 */

import React, { useState, useEffect, useCallback } from 'react';
import { View,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
  TouchableOpacity,
  StatusBar,
  } from 'react-native';
import { Surface   } from 'react-native-paper';
import { useSafeAreaInsets   } from 'react-native-safe-area-context';
import LocalIcon, { NavigationLocalIcon, ActionLocalIcon, BusinessLocalIcon, SettingsLocalIcon, StatusLocalIcon } from './LocalIcon';
import { useTheme   } from '../context/ThemeContext';

const { width: screenWidth  } = Dimensions.get('window');

export interface ToastConfig {
  id?: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
  action?: {
    label: string;
    onPress: () => void;
  };
}

interface ToastItemProps extends ToastConfig {
  onDismiss: (id: string) => void;
}

const ToastItem: React.FC<ToastItemProps> = ({
  id = '',
  type,
  title,
  message,
  duration = 4000,
  action,
  onDismiss,
}) => {
  const { theme  } = useTheme();
  const insets = useSafeAreaInsets();
  const [slideAnim] = useState(new Animated.Value(-100));
  const [opacityAnim] = useState(new Animated.Value(0));

  const getToastConfig = () => {
    switch(type) {
      case 'success':
        return { icon: 'check-circle',
          backgroundColor: '#4CAF50',
          iconColor: '#FFFFFF',
          textColor: '#FFFFFF',
          actionColor: '#FFFFFF',
         };
      case 'error':
        return { icon: 'alert-circle',
          backgroundColor: '#F44336',
          iconColor: '#FFFFFF',
          textColor: '#FFFFFF',
          actionColor: '#FFFFFF',
         };
      case 'warning':
        return { icon: 'alert-triangle',
          backgroundColor: '#FF9800',
          iconColor: '#FFFFFF',
          textColor: '#FFFFFF',
          actionColor: '#FFFFFF',
         };
      case 'info':
      default:
        return { icon: 'info',
          backgroundColor: theme.colors.primary,
          iconColor: '#FFFFFF',
          textColor: '#FFFFFF',
          actionColor: '#FFFFFF',
         };
    }
  };

  const config = getToastConfig();

  useEffect(() => {
    // Slide in animation
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();

    // Auto dismiss
    const timer = setTimeout(() => {
      handleDismiss();
    }, duration);

    return () => clearTimeout(timer);
  }, []);

  const handleDismiss = useCallback(() => {
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: -100,
        duration: 250,
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true,
      }),
    ]).start(() => {
      onDismiss(id);
    });
  }, [id, onDismiss]);

  return (
    <Animated.View
      style={[
        styles.toastContainer,
        {
          top: insets.top + 16,
          transform: [{ translateY: slideAnim },
          opacity: opacityAnim,
        },
      }>
      <Surface
        style={[
          styles.toast,
          {
            backgroundColor: config.backgroundColor,
          },
        }
        elevation={4}>
        <View style={styles.iconContainer}>
          <LocalIcon 
            name={config.icon} 
            size="24" 
            color={config.iconColor}
            style={{}
            containerSize={40}
            backgroundColor="transparent"
            borderRadius={0}
            containerStyle={{}
          />
        </View>

        <View style={styles.content}>
          <Text
            style={[
              styles.title,
              { color: config.textColor },
            }
            numberOfLines={1}>
            {title}
          </Text>
          {message && (
            <Text
              style={[
                styles.message,
                { color: config.textColor },
              }
              numberOfLines={2}>
              {message}
            </Text>
          )}
        </View>

        {action && (
          <TouchableOpacity
            style={[
              styles.actionButton,
              {
                borderColor: config.actionColor,
              },
            }
            onPress={() => {
              action.onPress();
              handleDismiss();
            }>
            <Text style={[
              styles.actionText,
              { color: config.actionColor },
            }>
              {action.label}
            </Text>
          </TouchableOpacity>
        )}

        <TouchableOpacity style={styles.dismissButton} onPress={handleDismiss}>
          <LocalIcon 
            name="x" 
            size="20" 
            color={theme.colors.onSurfaceVariant}
            style={{}
            containerSize={24}
            backgroundColor="transparent"
            borderRadius={0]
            containerStyle={{}
          />
        </TouchableOpacity>
      </Surface>
    </Animated.View>
  );
];

class ToasterService {
  private static toasts: ToastConfig[] = [];
  private static listeners: ((toasts: ToastConfig[]) => void)[] = [];

  static show(config: ToastConfig) {
    const toast: ToastConfig = {
      id: Date.now().toString(),
      ...config,
    };

    this.toasts.unshift(toast);
    this.notifyListeners();

    return toast.id;
  }

  static dismiss(id: string) {
    this.toasts = this.toasts.filter(toast => toast.id !== id);
    this.notifyListeners();
  }

  static dismissAll() {
    this.toasts = [];
    this.notifyListeners();
  }

  static addListener(listener: (toasts: ToastConfig[]) => void) {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  private static notifyListeners() {
    this.listeners.forEach(listener => listener([...this.toasts]));
  }

  // Convenience methods
  static success(title: string, message?: string, action?: ToastConfig['action']) {
    return this.show({ type: 'success', title, message, action });
  }

  static error(title: string, message?: string, action?: ToastConfig['action']) {
    return this.show({ type: 'error', title, message, action });
  }

  static warning(title: string, message?: string, action?: ToastConfig['action']) {
    return this.show({ type: 'warning', title, message, action });
  }

  static info(title: string, message?: string, action?: ToastConfig['action']) {
    return this.show({ type: 'info', title, message, action });
  }
}

const Toaster: React.FC = () => {
  const [toasts, setToasts] = useState<ToastConfig[]>([]);

  useEffect(() => {
    const unsubscribe = ToasterService.addListener(setToasts);
    return unsubscribe;
  }, []);

  return (
    <View style={styles.container} pointerEvents="box-none">
      {toasts.slice(0, 3).map((toast) => (
        <ToastItem
          key={toast.id}
          {...toast}
          onDismiss={ToasterService.dismiss}
        />
      ))}
    </View>
  );
};

export { ToasterService   };
export default Toaster;

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 9999,
  },
  toastContainer: {
    position: 'absolute',
    left: 16,
    right: 16,
    zIndex: 9999,
  },
  toast: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
    minHeight: 64,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  content: {
    flex: 1,
    marginRight: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  message: {
    fontSize: 14,
    lineHeight: 18,
  },
  actionButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    borderWidth: 1,
    marginRight: 8,
  },
  actionText: {
    fontSize: 14,
    fontWeight: '600',
  },
  dismissButton: {
    padding: 4,
  },
});