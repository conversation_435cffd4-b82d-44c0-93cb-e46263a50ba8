/**
 * Line Icons Mapping for Settings Page
 * Using SimpleLineIcons from react-native-vector-icons
 * Provides clean, minimal line-style icons for settings
 */

// Settings-specific Line Icons mapping using MaterialCommunityIcons outline versions
export const SETTINGS_LINE_ICONS = {
  // Appearance & Theme
  theme: 'palette-outline',
  appearance: 'eye-outline',
  display: 'monitor-outline',

  // User & Profile
  profile: 'account-outline',
  account: 'account-outline',
  user: 'account-outline',

  // Management & Admin
  employee: 'account-group-outline',
  management: 'office-building-outline',
  admin: 'shield-account-outline',
  supervisor: 'account-supervisor-outline',

  // Subscription & Business
  subscription: 'diamond-outline',
  crown: 'crown-outline',
  premium: 'star-outline',

  // Data & Storage
  database: 'database-outline',
  backup: 'cloud-upload-outline',
  data: 'folder-outline',
  storage: 'harddisk',

  // Activity & History
  history: 'clock-outline',
  activity: 'chart-line',
  log: 'text-box-outline',

  // Help & Support
  help: 'help-circle-outline',
  support: 'lifebuoy',
  faq: 'frequently-asked-questions',
  information: 'information-outline',
  about: 'information-outline',
  
  // Tools & Development
  tools: 'wrench-outline',
  settings: 'cog-outline',
  optimization: 'speedometer',
  demo: 'view-grid-outline',
  test: 'test-tube',

  // Measurement & Units
  ruler: 'ruler',
  measurement: 'ruler',
  units: 'calculator-variant-outline',

  // Authentication & Security
  logout: 'logout',
  login: 'login',
  security: 'lock-outline',

  // Navigation & UI
  dashboard: 'view-dashboard-outline',
  menu: 'menu',
  close: 'close',
  back: 'arrow-left',
  forward: 'chevron-right',
  
  // Communication
  phone: 'phone',
  email: 'envelope',
  contact: 'envelope-letter',
  
  // Business Operations
  orders: 'bag',
  products: 'handbag',
  customers: 'people',
  financial: 'wallet',
  reports: 'chart',
  
  // System
  refresh: 'refresh',
  sync: 'refresh',
  update: 'cloud-download',
  
  // Media & Files
  camera: 'camera',
  image: 'picture',
  file: 'doc',
  pdf: 'doc',
  
  // Notifications
  notification: 'bell',
  alert: 'exclamation',
  
  // Social & Sharing
  share: 'share',
  social: 'social-facebook',
  
  // Location & Maps
  location: 'location-pin',
  map: 'map',
  
  // Time & Calendar
  calendar: 'calendar',
  time: 'clock',
  schedule: 'calendar'};

// Get Line Icon for settings item
export const getSettingsLineIcon = (iconKey) => {
  return SETTINGS_LINE_ICONS[iconKey] || 'settings';
};

// Settings categories with their respective icons
export const SETTINGS_CATEGORIES = {
  appearance: {
    icon: 'magic-wand',
    items: ['theme', 'display', 'appearance'},
  account: {
    icon: 'user',
    items: ['profile', 'account', 'security'},
  management: {
    icon: 'organization',
    items: ['employee', 'admin', 'supervisor'},
  business: {
    icon: 'diamond',
    items: ['subscription', 'premium', 'crown'},
  data: {
    icon: 'cloud-upload',
    items: ['backup', 'database', 'storage'},
  support: {
    icon: 'question',
    items: ['help', 'faq', 'about', 'contact'},
  tools: {
    icon: 'wrench',
    items: ['optimization', 'demo', 'test', 'measurement']
};

// Icon size mappings for different contexts
export const LINE_ICON_SIZES = {
  small: 16,
  medium: 20,
  large: 24,
  extraLarge: 28,
  huge: 32
};

// Color schemes for line icons
export const LINE_ICON_COLORS = {
  primary: '#6750A4',
  secondary: '#625B71',
  tertiary: '#7D5260',
  success: '#4CAF50',
  warning: '#FF9800',
  error: '#F44336',
  info: '#2196F3',
  neutral: '#9E9E9E'
};

// Get appropriate color for icon based on context
export const getLineIconColor = (context, theme) => {
  // Handle cases where theme might be undefined
  if(!theme || !theme.colors) {
    return LINE_ICON_COLORS[context] || LINE_ICON_COLORS.neutral;
  }
  
  const contextColors = {
    primary: theme.colors.primary || LINE_ICON_COLORS.primary,
    secondary: theme.colors.secondary || LINE_ICON_COLORS.secondary,
    surface: theme.colors.onSurface || LINE_ICON_COLORS.neutral,
    variant: theme.colors.onSurfaceVariant || LINE_ICON_COLORS.neutral,
    success: LINE_ICON_COLORS.success,
    warning: LINE_ICON_COLORS.warning,
    error: LINE_ICON_COLORS.error,
    info: LINE_ICON_COLORS.info
  };
  
  return contextColors[context] || contextColors.surface;
};

// Validate if icon exists in SimpleLineIcons
export const isValidLineIcon = (iconName) => {
  return Object.values(SETTINGS_LINE_ICONS).includes(iconName);
};

// Get fallback icon if requested icon doesn't exist
export const getLineIconWithFallback = (iconKey, fallback = 'settings') => {
  const icon = getSettingsLineIcon(iconKey);
  return isValidLineIcon(icon) ? icon : fallback;
};

export default SETTINGS_LINE_ICONS;
