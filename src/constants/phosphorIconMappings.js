/**
 * Phosphor Icon Mappings for Tailora App
 * Maps semantic icon names to Phosphor icon components
 * Organized by categories for better maintainability
 */

// Navigation & UI Icons
export const NAVIGATION_PHOSPHOR_ICONS = {
  // Bottom Navigation
  home: 'House',
  dashboard: 'SquaresFour',
  scan: 'QrCode',
  plus: 'Plus',
  orders: 'ClipboardText',
  settings: 'Gear',
  profile: 'User',

  // Main Navigation
  products: 'Package',
  garments: 'Shirt',
  customers: 'Users',
  financial: 'CurrencyDollar',
  reports: 'ChartBar',
  measurements: 'Ruler',

  // Secondary Navigation
  help: 'Question',
  about: 'Info',
  contact: 'Phone',
  faq: 'ChatCircleQuestion',
  back: 'ArrowLeft',
  forward: 'ArrowRight',
  up: 'ArrowUp',
  down: 'ArrowDown'};

// Action Icons
export const ACTION_PHOSPHOR_ICONS = {
  // CRUD Operations
  add: 'Plus',
  create: 'PlusCircle',
  edit: 'PencilSimple',
  delete: 'Trash',
  save: 'FloppyDisk',
  cancel: 'X',
  confirm: 'Check',
  
  // File Operations
  download: 'Download',
  upload: 'Upload',
  share: 'ShareNetwork',
  copy: 'Copy',
  cut: 'Scissors',
  paste: 'ClipboardText',
  
  // View Operations
  view: 'Eye',
  hide: 'EyeSlash',
  search: 'MagnifyingGlass',
  filter: 'Funnel',
  sort: 'SortAscending',
  
  // Media Operations
  camera: 'Camera',
  image: 'Image',
  video: 'VideoCamera',
  audio: 'Microphone',
  
  // System Operations
  refresh: 'ArrowClockwise',
  sync: 'ArrowsClockwise',
  update: 'CloudArrowDown',
  backup: 'CloudArrowUp',
  restore: 'ClockCounterClockwise'};

// Status Icons
export const STATUS_PHOSPHOR_ICONS = {
  // Order Status
  pending: 'Clock',
  inProgress: 'CircleNotch',
  completed: 'CheckCircle',
  cancelled: 'XCircle',
  delivered: 'Package',
  
  // Payment Status
  paid: 'CheckCircle',
  unpaid: 'Clock',
  overdue: 'Warning',
  refunded: 'ArrowCounterClockwise',
  
  // General Status
  active: 'CheckCircle',
  inactive: 'Circle',
  success: 'CheckCircle',
  error: 'XCircle',
  warning: 'Warning',
  info: 'Info',
  
  // Connection Status
  online: 'WifiHigh',
  offline: 'WifiSlash',
  synced: 'CheckCircle',
  syncing: 'ArrowsClockwise'};

// Business Icons
export const BUSINESS_PHOSPHOR_ICONS = {
  // Core Business
  order: 'Receipt',
  customer: 'User',
  product: 'Package',
  garment: 'Shirt',
  fabric: 'Scissors',
  measurement: 'Ruler',
  
  // Financial
  payment: 'CurrencyDollar',
  invoice: 'Receipt',
  expense: 'Minus',
  revenue: 'Plus',
  profit: 'TrendUp',
  loss: 'TrendDown',
  
  // Inventory
  stock: 'Stack',
  warehouse: 'Warehouse',
  supplier: 'Truck',
  category: 'Tag',
  
  // Operations
  production: 'Gear',
  quality: 'Medal',
  delivery: 'Package',
  appointment: 'Calendar'};

// UI Component Icons
export const UI_PHOSPHOR_ICONS = {
  // Form Elements
  dropdown: 'CaretDown',
  calendar: 'Calendar',
  time: 'Clock',
  date: 'CalendarBlank',
  
  // Notifications
  notification: 'Bell',
  notificationActive: 'BellRinging',
  alert: 'Warning',
  
  // User Interface
  menu: 'List',
  close: 'X',
  minimize: 'Minus',
  maximize: 'Plus',
  
  // Social/Communication
  email: 'Envelope',
  phone: 'Phone',
  whatsapp: 'WhatsappLogo',
  message: 'ChatCircle',
  
  // Security
  lock: 'Lock',
  unlock: 'LockOpen',
  security: 'Shield',
  password: 'Key',
  
  // Theme & Appearance
  theme: 'Palette',
  light: 'Sun',
  dark: 'Moon',
  auto: 'CircleHalf'};

// Settings Icons
export const SETTINGS_PHOSPHOR_ICONS = {
  // Appearance & Theme
  theme: 'Palette',
  appearance: 'Eye',
  display: 'Monitor',
  
  // User & Profile
  profile: 'User',
  account: 'UserCircle',
  user: 'User',
  
  // Management & Admin
  employee: 'Users',
  management: 'Buildings',
  admin: 'ShieldCheck',
  supervisor: 'UserCheck',
  
  // Subscription & Business
  subscription: 'Crown',
  premium: 'Star',
  crown: 'Crown',
  
  // Data & Storage
  database: 'Database',
  backup: 'CloudArrowUp',
  data: 'Folder',
  storage: 'HardDrives',
  
  // Security & Privacy
  security: 'Shield',
  privacy: 'Lock',
  permissions: 'Key',
  
  // Notifications & Communication
  notifications: 'Bell',
  messaging: 'ChatCircle',
  email: 'Envelope',
  
  // System & Tools
  tools: 'Wrench',
  optimization: 'Gauge',
  demo: 'Play',
  test: 'TestTube',
  
  // Support & Help
  help: 'Question',
  faq: 'ChatCircleQuestion',
  about: 'Info',
  contact: 'AddressBook',
  support: 'Headset',
  
  // Business Operations
  orders: 'ClipboardText',
  products: 'Package',
  customers: 'Users',
  financial: 'CurrencyDollar',
  reports: 'ChartBar',
  
  // System Operations
  refresh: 'ArrowClockwise',
  sync: 'ArrowsClockwise',
  update: 'CloudArrowDown',
  
  // Media & Files
  camera: 'Camera',
  image: 'Image',
  file: 'File',
  pdf: 'FilePdf',
  
  // Location & Maps
  location: 'MapPin',
  map: 'Map',
  
  // Time & Calendar
  calendar: 'Calendar',
  time: 'Clock',
  schedule: 'CalendarCheck'};

// Get Phosphor icon for any category
export const getLocalIcon = (category, name) => {
  const iconMaps = {
    navigation: NAVIGATION_PHOSPHOR_ICONS,
    action: ACTION_PHOSPHOR_ICONS,
    status: STATUS_PHOSPHOR_ICONS,
    business: BUSINESS_PHOSPHOR_ICONS,
    ui: UI_PHOSPHOR_ICONS,
    settings: SETTINGS_PHOSPHOR_ICONS};

  const iconMap = iconMaps[category];
  if(iconMap && iconMap[name]) {
    return iconMap[name];
  }

  // Fallback to searching all categories
  for (const map of Object.values(iconMaps)) {
    if(map[name]) {
      return map[name];
    }
  }

  // Ultimate fallback
  return 'Question';
};

// Migration mapping from Feather/MaterialCommunity to Phosphor
export const MIGRATION_MAP = {
  // Feather to Phosphor
  'search': 'MagnifyingGlass',
  'bell': 'Bell',
  'home': 'House',
  'menu': 'List',
  'settings': 'Gear',
  'plus': 'Plus',
  'edit': 'PencilSimple',
  'trash': 'Trash',
  'save': 'FloppyDisk',
  'download': 'Download',
  'upload': 'Upload',
  'share': 'ShareNetwork',
  'copy': 'Copy',
  'eye': 'Eye',
  'eye-off': 'EyeSlash',
  'user': 'User',
  'users': 'Users',
  'calendar': 'Calendar',
  'clock': 'Clock',
  'mail': 'Envelope',
  'phone': 'Phone',
  'map-pin': 'MapPin',
  'camera': 'Camera',
  'image': 'Image',
  'file': 'File',
  'folder': 'Folder',
  'grid': 'SquaresFour',
  'list': 'List',
  'chevron-right': 'CaretRight',
  'chevron-left': 'CaretLeft',
  'chevron-up': 'CaretUp',
  'chevron-down': 'CaretDown',
  'arrow-right': 'ArrowRight',
  'arrow-left': 'ArrowLeft',
  'check': 'Check',
  'x': 'X',
  'info': 'Info',
  'alert-circle': 'Warning',
  'help-circle': 'Question',
  'star': 'Star',
  'heart': 'Heart',
  'bookmark': 'Bookmark',
  'tag': 'Tag',
  'package': 'Package',
  'shopping-bag': 'ShoppingBag',
  'credit-card': 'CreditCard',
  'dollar-sign': 'CurrencyDollar',
  'trending-up': 'TrendUp',
  'trending-down': 'TrendDown',
  'bar-chart': 'ChartBar',
  'pie-chart': 'ChartPie',
  'activity': 'Activity',
  'wifi': 'WifiHigh',
  'wifi-off': 'WifiSlash',
  'bluetooth': 'BluetoothX',
  'battery': 'Battery',
  'volume-2': 'SpeakerHigh',
  'volume-x': 'SpeakerX',
  'play': 'Play',
  'pause': 'Pause',
  'stop': 'Stop',
  'skip-back': 'SkipBack',
  'skip-forward': 'SkipForward',
  'repeat': 'Repeat',
  'shuffle': 'Shuffle',
  'lock': 'Lock',
  'unlock': 'LockOpen',
  'shield': 'Shield',
  'key': 'Key',
  'globe': 'Globe',
  'link': 'Link',
  'external-link': 'ArrowSquareOut',
  'refresh-cw': 'ArrowClockwise',
  'rotate-ccw': 'ArrowCounterClockwise',
  'maximize': 'ArrowsOut',
  'minimize': 'ArrowsIn',
  'zoom-in': 'MagnifyingGlassPlus',
  'zoom-out': 'MagnifyingGlassMinus',

  // Additional mappings for dashboard quick actions
  'plus-circle': 'PlusCircle',
  'user-plus': 'UserPlus',
  'edit-3': 'PencilSimple',
  'layers': 'Stack',
  'bar-chart-2': 'ChartBar',
  'archive': 'Package',

  // MaterialCommunity to Phosphor mappings for cards
  'account': 'User',
  'account-group': 'Users',
  'account-edit': 'UserGear',
  'hanger': 'Shirt',
  'fire': 'Fire',
  'star': 'Star',
  'heart-outline': 'Heart',
  'share-variant': 'ShareNetwork',
  'cart-plus': 'ShoppingCartSimple',
  'message': 'ChatCircle',
  'calendar': 'Calendar',
  'clock-outline': 'Clock',
  'bell-ring': 'BellRinging',
  'eye': 'Eye',
  'eye-off': 'EyeSlash',
  'content-copy': 'Copy',
  'content-paste': 'ClipboardText',
  'email': 'Envelope',
  'whatsapp': 'WhatsappLogo',
  'lock': 'Lock',
  'lock-open': 'LockOpen',
  'shield-check': 'ShieldCheck',
  'help-circle-outline': 'Question',

  // Navigation specific mappings
  'shopping-bag': 'ShoppingBag',
  'camera': 'Camera',
  'more-horizontal': 'DotsThreeOutline',
  'more-vertical': 'DotsThreeOutlineVertical',

  // Additional common icons
  'clipboard-list-outline': 'ClipboardText',
  'tshirt-crew-outline': 'Shirt',
  'account-group-outline': 'Users',
  'credit-card-outline': 'CreditCard',
  'currency-usd': 'CurrencyDollar',
  'chart-line': 'ChartLine',
  'package-variant': 'Package',
  'scissors-cutting': 'Scissors',
  'ruler': 'Ruler',
  'palette': 'Palette',
  'cog': 'Gear',
  'cog-outline': 'Gear',

  // Additional garment and clothing icons
  'tshirt-crew': 'Shirt',
  'tshirt-crew-outline': 'Shirt',
  'tshirt-v': 'Shirt',
  'tshirt-v-outline': 'Shirt',
  'human-handsdown': 'User',
  'human-male': 'User',
  'tie': 'Necktie',
  'coat-rack': 'Coat',
  'dress': 'Dress',
  'baby': 'Baby',
  'school': 'GraduationCap',
  'check-circle': 'CheckCircle'};

// Get migrated Phosphor icon name
export const getMigratedLocalIcon = (oldIconName) => {
  return MIGRATION_MAP[oldIconName] || 'Question';
};

// Check if icon exists in Phosphor
export const isLocalIcon = (iconName) => {
  // This would need to be updated with actual Phosphor icon list
  // For now, we'll assume all mapped icons exist
  return Object.values(MIGRATION_MAP).includes(iconName) || 
         Object.values({
           ...NAVIGATION_PHOSPHOR_ICONS,
           ...ACTION_PHOSPHOR_ICONS,
           ...STATUS_PHOSPHOR_ICONS,
           ...BUSINESS_PHOSPHOR_ICONS,
           ...UI_PHOSPHOR_ICONS,
           ...SETTINGS_PHOSPHOR_ICONS}).includes(iconName);
};

export default {
  getLocalIcon,
  getMigratedLocalIcon,
  isLocalIcon,
  NAVIGATION_PHOSPHOR_ICONS,
  ACTION_PHOSPHOR_ICONS,
  STATUS_PHOSPHOR_ICONS,
  BUSINESS_PHOSPHOR_ICONS,
  UI_PHOSPHOR_ICONS,
  SETTINGS_PHOSPHOR_ICONS,
  MIGRATION_MAP};
