import React, { createContext, useContext, useState, useEffect } from 'react';
import { Appearance   } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { lightTheme, darkTheme   } from '../theme/theme';

const ThemeContext = createContext();

// Theme modes
export const THEME_MODES = {
  LIGHT: 'light',
  DARK: 'dark',
  SYSTEM: 'system',
};

export const ThemeProvider = ({ children }) => {
  const [themeMode, setThemeMode] = useState(THEME_MODES.SYSTEM);
  const [systemColorScheme, setSystemColorScheme] = useState(Appearance.getColorScheme());
  const [isLoading, setIsLoading] = useState(true);

  // Load theme preference on app start
  useEffect(() => {
    loadThemePreference();
  }, []);

  // Listen to system theme changes
  useEffect(() => {
    const subscription = Appearance.addChangeListener(({ colorScheme }) => {
      setSystemColorScheme(colorScheme);
    });

    return () => subscription?.remove();
  }, []);

  const loadThemePreference = async () => {
    try {
      const savedThemeMode = await AsyncStorage.getItem('themeMode');
      if (savedThemeMode && Object.values(THEME_MODES).includes(savedThemeMode)) {
        setThemeMode(savedThemeMode);
      } else {
        // Migrate from old darkMode setting
        const oldDarkMode = await AsyncStorage.getItem('darkMode');
        if(oldDarkMode !== null) {
          const isDark = JSON.parse(oldDarkMode);
          const newMode = isDark ? THEME_MODES.DARK : THEME_MODES.LIGHT;
          setThemeMode(newMode);
          await AsyncStorage.setItem('themeMode', newMode);
          await AsyncStorage.removeItem('darkMode'); // Clean up old setting
        }
      }
    } catch (error) {
      console.error('Error loading theme preference:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const setTheme = async (mode) => {
    try {
      setThemeMode(mode);
      await AsyncStorage.setItem('themeMode', mode);
    } catch (error) {
      console.error('Error saving theme preference:', error);
    }
  };

  // Determine the actual theme to use
  const getEffectiveTheme = () => {
    if(themeMode === THEME_MODES.SYSTEM) {
      return systemColorScheme === 'dark' ? darkTheme : lightTheme;
    }
    return themeMode === THEME_MODES.DARK ? darkTheme : lightTheme;
  };

  const theme = getEffectiveTheme();
  const isDarkMode = theme === darkTheme;

  const value = {
    isDarkMode,
    theme,
    themeMode,
    systemColorScheme,
    isLoading,
    setTheme,
    // Legacy support
    toggleTheme: () => setTheme(isDarkMode ? THEME_MODES.LIGHT : THEME_MODES.DARK),
    // New theme mode helpers
    isSystemMode: themeMode === THEME_MODES.SYSTEM,
    isLightMode: themeMode === THEME_MODES.LIGHT,
    isDarkModeSet: themeMode === THEME_MODES.DARK,
    THEME_MODES,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if(!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export default ThemeContext;
