import { Button } from 'react-native-paper';
import { StyleSheet } from 'react-native';
/**
 * SubscriptionContext - Subscription management and feature access control
 */

import React, { createContext, useContext, useReducer, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

const SubscriptionContext = createContext();

// Subscription tiers and their features
export const SUBSCRIPTION_TIERS = {
  STARTER: 'starter',
  GROWTH: 'growth',
  PRO: 'pro',
};

// Feature modules for each tier
export const TIER_FEATURES = {
  [SUBSCRIPTION_TIERS.STARTER]: [
    'orders',
    'measurements',
    'customers_basic',
  ],
  [SUBSCRIPTION_TIERS.GROWTH]: [
    'orders',
    'measurements',
    'customers',
    'sales',
    'inventory_basic',
    'reports_basic',
  ],
  [SUBSCRIPTION_TIERS.PRO]: [
    'orders',
    'measurements',
    'customers',
    'sales',
    'inventory',
    'employees',
    'accounting',
    'reports_advanced',
    'analytics',
  ],
};

// Usage limits for each tier
export const TIER_LIMITS = {
  [SUBSCRIPTION_TIERS.STARTER]: {
    orders: 100,
    customers: 200,
    employees: 0,
    storage: 1, // GB
    reports: 5,
  },
  [SUBSCRIPTION_TIERS.GROWTH]: {
    orders: 500,
    customers: 1000,
    employees: 0,
    storage: 5, // GB
    reports: 20,
  },
  [SUBSCRIPTION_TIERS.PRO]: {
    orders: -1, // unlimited
    customers: -1, // unlimited
    employees: -1, // unlimited
    storage: 50, // GB
    reports: -1, // unlimited
  },
};

const initialState = {
  currentTier: SUBSCRIPTION_TIERS.GROWTH, // Default tier
  subscriptionStatus: 'active',
  billingCycle: 'yearly',
  startDate: new Date().toISOString(),
  endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
  autoRenew: true,
  usage: {
    orders: 0,
    customers: 0,
    employees: 0,
    storage: 0,
    reports: 0,
  },
  isLoading: false,
};

const subscriptionReducer = (state, action) => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    
    case 'SET_SUBSCRIPTION':
      return { ...state, ...action.payload };
    
    case 'UPDATE_TIER':
      return { ...state, currentTier: action.payload };
    
    case 'UPDATE_USAGE':
      return {
        ...state,
        usage: { ...state.usage, ...action.payload },
      };
    
    case 'RESET_USAGE':
      return {
        ...state,
        usage: { orders: 0, customers: 0, employees: 0, storage: 0, reports: 0 },
      };
    
    default:
      return state;
  }
};

export const SubscriptionProvider = ({ children }) => {
  const [state, dispatch] = useReducer(subscriptionReducer, initialState);

  // Load subscription data on app start
  useEffect(() => {
    loadSubscriptionData();
  }, []);

  const loadSubscriptionData = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      const subscriptionData = await AsyncStorage.getItem('subscriptionData');
      if (subscriptionData) {
        const parsed = JSON.parse(subscriptionData);
        dispatch({ type: 'SET_SUBSCRIPTION', payload: parsed });
      }
    } catch (error) {
      console.error('Error loading subscription data:', error);
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const saveSubscriptionData = async (data) => {
    try {
      await AsyncStorage.setItem('subscriptionData', JSON.stringify(data));
    } catch (error) {
      console.error('Error saving subscription data:', error);
    }
  };

  // Check if a feature is available in current tier
  const hasFeature = (feature) => {
    const tierFeatures = TIER_FEATURES[state.currentTier] || [];
    return tierFeatures.includes(feature);
  };

  // Check if usage limit is reached
  const isLimitReached = (resource) => {
    const limits = TIER_LIMITS[state.currentTier] || {};
    const limit = limits[resource];
    
    if (limit === -1) return false; // unlimited
    if (limit === undefined) return false; // no limit defined
    
    return state.usage[resource] >= limit;
  };

  // Get remaining usage for a resource
  const getRemainingUsage = (resource) => {
    const limits = TIER_LIMITS[state.currentTier] || {};
    const limit = limits[resource];
    
    if (limit === -1) return -1; // unlimited
    if (limit === undefined) return -1; // no limit defined
    
    return Math.max(0, limit - state.usage[resource]);
  };

  // Get usage percentage for a resource
  const getUsagePercentage = (resource) => {
    const limits = TIER_LIMITS[state.currentTier] || {};
    const limit = limits[resource];
    
    if (limit === -1 || limit === undefined) return 0; // unlimited or no limit
    
    return Math.min((state.usage[resource] / limit) * 100, 100);
  };

  // Update usage for a resource
  const updateUsage = async (resource, increment = 1) => {
    const newUsage = { [resource]: state.usage[resource] + increment };
    dispatch({ type: 'UPDATE_USAGE', payload: newUsage });
    
    const updatedState = { ...state, usage: { ...state.usage, ...newUsage } };
    await saveSubscriptionData(updatedState);
  };

  // Upgrade/downgrade subscription
  const changeTier = async (newTier) => {
    try {
      dispatch({ type: 'UPDATE_TIER', payload: newTier });
      
      const updatedState = { ...state, currentTier: newTier };
      await saveSubscriptionData(updatedState);
      
      return { success: true };
    } catch (error) {
      console.error('Error changing tier:', error);
      return { success: false, error: 'Failed to change subscription tier' };
    }
  };

  // Check if subscription is active
  const isSubscriptionActive = () => {
    if (state.subscriptionStatus !== 'active') return false;
    
    const now = new Date();
    const endDate = new Date(state.endDate);
    
    return now <= endDate;
  };

  // Get subscription info
  const getSubscriptionInfo = () => {
    const tierInfo = {
      [SUBSCRIPTION_TIERS.STARTER]: {
        name: 'Starter',
        price: 2500,
        yearlyPrice: 25000,
        color: '#4CAF50',
      },
      [SUBSCRIPTION_TIERS.GROWTH]: {
        name: 'Growth',
        price: 4500,
        yearlyPrice: 45000,
        color: '#2196F3',
      },
      [SUBSCRIPTION_TIERS.PRO]: {
        name: 'Pro',
        price: 7500,
        yearlyPrice: 75000,
        color: '#9C27B0',
      },
    };

    return {
      ...tierInfo[state.currentTier],
      tier: state.currentTier,
      features: TIER_FEATURES[state.currentTier],
      limits: TIER_LIMITS[state.currentTier],
      usage: state.usage,
      isActive: isSubscriptionActive(),
    };
  };

  // Feature access helpers
  const canAccessOrders = () => hasFeature('orders');
  const canAccessMeasurements = () => hasFeature('measurements');
  const canAccessCustomers = () => hasFeature('customers') || hasFeature('customers_basic');
  const canAccessSales = () => hasFeature('sales');
  const canAccessInventory = () => hasFeature('inventory') || hasFeature('inventory_basic');
  const canAccessEmployees = () => hasFeature('employees');
  const canAccessAccounting = () => hasFeature('accounting');
  const canAccessReports = () => hasFeature('reports_basic') || hasFeature('reports_advanced');
  const canAccessAdvancedReports = () => hasFeature('reports_advanced');
  const canAccessAnalytics = () => hasFeature('analytics');

  // Usage limit helpers
  const canCreateOrder = () => !isLimitReached('orders');
  const canAddCustomer = () => !isLimitReached('customers');
  const canAddEmployee = () => !isLimitReached('employees');
  const canGenerateReport = () => !isLimitReached('reports');

  const value = {
    ...state,
    hasFeature,
    isLimitReached,
    getRemainingUsage,
    getUsagePercentage,
    updateUsage,
    changeTier,
    isSubscriptionActive,
    getSubscriptionInfo,
    
    // Feature access
    canAccessOrders,
    canAccessMeasurements,
    canAccessCustomers,
    canAccessSales,
    canAccessInventory,
    canAccessEmployees,
    canAccessAccounting,
    canAccessReports,
    canAccessAdvancedReports,
    canAccessAnalytics,
    
    // Usage limits
    canCreateOrder,
    canAddCustomer,
    canAddEmployee,
    canGenerateReport,
    
    // Constants
    SUBSCRIPTION_TIERS,
    TIER_FEATURES,
    TIER_LIMITS,
  };

  return (
    <SubscriptionContext.Provider value={value}>
      {children}
    </SubscriptionContext.Provider>
  );
};

export const useSubscription = () => {
  const context = useContext(SubscriptionContext);
  if (!context) {
    throw new Error('useSubscription must be used within a SubscriptionProvider');
  }
  return context;
};

// Higher-order component for subscription-protected features
export const withSubscriptionProtection = (WrappedComponent, requiredFeature) => {
  return (props) => {
    const { hasFeature } = useSubscription();
    
    if (!hasFeature(requiredFeature)) {
      return (
        <View style={styles.style3}>
          <Text style={styles.style2}>
            Feature Not Available
          </Text>
          <Text style={styles.style1}>
            This feature is not included in your current subscription plan.
          </Text>
          <Button
            mode="contained"
            onPress={() => {
              // Navigate to subscription screen

            }}
          >
            Upgrade Plan
          </Button>
        </View>
      );
    }
    
    return <WrappedComponent {...props} />;
  };
};

// Hook for checking subscription features in components
export const useSubscriptionFeatures = () => {
  const subscription = useSubscription();
  
  return {
    hasFeature: subscription.hasFeature,
    canAccessOrders: subscription.canAccessOrders(),
    canAccessMeasurements: subscription.canAccessMeasurements(),
    canAccessCustomers: subscription.canAccessCustomers(),
    canAccessSales: subscription.canAccessSales(),
    canAccessInventory: subscription.canAccessInventory(),
    canAccessEmployees: subscription.canAccessEmployees(),
    canAccessAccounting: subscription.canAccessAccounting(),
    canAccessReports: subscription.canAccessReports(),
    canAccessAdvancedReports: subscription.canAccessAdvancedReports(),
    canAccessAnalytics: subscription.canAccessAnalytics(),
    currentTier: subscription.currentTier,
    subscriptionInfo: subscription.getSubscriptionInfo(),
  };
};


const styles = StyleSheet.create({
  style1: {
    textAlign: 'center',
    marginBottom: 16,
  },
  style2: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  style3: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
});
