import React, { createContext, useContext, useReducer, useEffect, useMemo, useCallback, useState } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import MigrationService from '../services/MigrationService';
import { NotificationService } from '../services/notificationService';
import OrderArchiveService from '../services/OrderArchiveService';
import { DEFAULT_MEASUREMENT_UNIT } from '../config/constants';
import searchService from '../services/SearchService';
import OfflineService from '../services/OfflineService';

// Initial state for Tailor Management System
const initialState = {
  // Core entities for tailoring business
  garments: [], // Clothing items/products
  orders: [], // Tailoring orders
  customers: [], // Customer database
  measurements: [], // Customer measurements
  fabrics: [], // Fabric inventory
  garmentTemplates: [], // Garment templates for orders

  // Tailor-specific settings
  settings: {
    shopName: '<PERSON><PERSON>',
    ownerName: 'Master Tailor',
    email: '<EMAIL>',
    phone: '+880 1700-000000',
    address: 'Dhaka, Bangladesh',
    taxRate: 0.15, // 15% VAT in Bangladesh
    currency: 'BDT',
    notifications: true,
    darkMode: false,
    autoBackup: true,

    // Tailor shop hours
    shopHours: {
      saturday: { open: '09:00', close: '20:00', closed: false },
      sunday: { open: '09:00', close: '20:00', closed: false },
      monday: { open: '09:00', close: '20:00', closed: false },
      tuesday: { open: '09:00', close: '20:00', closed: false },
      wednesday: { open: '09:00', close: '20:00', closed: false },
      thursday: { open: '09:00', close: '20:00', closed: false },
      friday: { open: '14:00', close: '20:00', closed: false }, // Friday afternoon only
    },

    // Payment methods for tailoring
    paymentMethods: {
      cash: { enabled: true, processingFee: 0 },
      bkash: { enabled: true, processingFee: 1.5 },
      nagad: { enabled: true, processingFee: 1.2 },
      rocket: { enabled: false, processingFee: 1.8 },
      bankTransfer: { enabled: true, processingFee: 0 },
      card: { enabled: false, processingFee: 2.5 },
    },

    // Tailoring-specific settings
    defaultFittings: 2, // Number of fittings included
    rushOrderSurcharge: 50, // Percentage surcharge for rush orders
    alterationDiscount: 20, // Discount for alteration-only orders
    loyaltyDiscount: 10, // Discount for loyal customers

    // Measurement units
    measurementUnit: DEFAULT_MEASUREMENT_UNIT, // cm, inches, or feet

    // Order statuses
    orderStatuses: [
      'Order Placed',
      'Measurements Taken',
      'Cutting Started',
      'Stitching In Progress',
      'First Fitting',
      'Adjustments',
      'Final Fitting',
      'Ready for Delivery',
      'Delivered',
      'Completed'
    ],

    // Garment categories
    garmentCategories: [
      'Shirts',
      'Pants',
      'Suits',
      'Blazers',
      'Kurtas',
      'Sherwanis',
      'Punjabis',
      'Formal Wear',
      'Casual Wear',
      'Traditional Wear'
    },

  // ID counters
  nextGarmentId: 1,
  nextOrderId: 1,
  nextCustomerId: 1,
  nextMeasurementId: 1,
  nextFabricId: 1,

  isDataLoaded: false,
};

// Action types for Tailor Management System
const actionTypes = {
  // Garments (clothing items/products)
  ADD_GARMENT: 'ADD_GARMENT',
  UPDATE_GARMENT: 'UPDATE_GARMENT',
  DELETE_GARMENT: 'DELETE_GARMENT',
  UPDATE_GARMENT_STOCK: 'UPDATE_GARMENT_STOCK',

  // Orders (tailoring orders)
  ADD_ORDER: 'ADD_ORDER',
  UPDATE_ORDER: 'UPDATE_ORDER',
  DELETE_ORDER: 'DELETE_ORDER',
  UPDATE_ORDER_STATUS: 'UPDATE_ORDER_STATUS',

  // Customers
  ADD_CUSTOMER: 'ADD_CUSTOMER',
  UPDATE_CUSTOMER: 'UPDATE_CUSTOMER',
  DELETE_CUSTOMER: 'DELETE_CUSTOMER',

  // Measurements
  ADD_MEASUREMENT: 'ADD_MEASUREMENT',
  UPDATE_MEASUREMENT: 'UPDATE_MEASUREMENT',
  DELETE_MEASUREMENT: 'DELETE_MEASUREMENT',

  // Fabrics
  ADD_FABRIC: 'ADD_FABRIC',
  UPDATE_FABRIC: 'UPDATE_FABRIC',
  DELETE_FABRIC: 'DELETE_FABRIC',
  UPDATE_FABRIC_STOCK: 'UPDATE_FABRIC_STOCK',

  // Garment Templates
  ADD_GARMENT_TEMPLATE: 'ADD_GARMENT_TEMPLATE',
  UPDATE_GARMENT_TEMPLATE: 'UPDATE_GARMENT_TEMPLATE',
  DELETE_GARMENT_TEMPLATE: 'DELETE_GARMENT_TEMPLATE',

  // Settings
  UPDATE_SETTINGS: 'UPDATE_SETTINGS',

  // Data
  LOAD_DATA: 'LOAD_DATA',
  SET_DATA_LOADED: 'SET_DATA_LOADED',
  CLEAR_DATA: 'CLEAR_DATA',

  // Legacy support (for backward compatibility)
  ADD_PRODUCT: 'ADD_GARMENT',
  UPDATE_PRODUCT: 'UPDATE_GARMENT',
  DELETE_PRODUCT: 'DELETE_GARMENT',
  UPDATE_STOCK: 'UPDATE_GARMENT_STOCK',
};

// Reducer function for Tailor Management System
const dataReducer = (state, action) => {
  switch (action.type) {
    // Garments (clothing items/products)
    case actionTypes.ADD_GARMENT:
    case actionTypes.ADD_PRODUCT: // Legacy support
      return {
        ...state,
        garments: [...(state.garments || []), action.payload],
        // Don't duplicate in products array - garments is the primary array
      };

    case actionTypes.UPDATE_GARMENT:
    case actionTypes.UPDATE_PRODUCT: // Legacy support
      return {
        ...state,
        garments: (state.garments || []).map(garment =>
          garment.id === action.payload.id ? { ...garment, ...action.payload } : garment
        ),
        // Don't duplicate in products array
      };

    case actionTypes.DELETE_GARMENT:
    case actionTypes.DELETE_PRODUCT: // Legacy support
      return {
        ...state,
        garments: (state.garments || []).filter(garment => garment.id !== action.payload),
        // Don't duplicate in products array
      };

    case actionTypes.UPDATE_GARMENT_STOCK:
    case actionTypes.UPDATE_STOCK: // Legacy support
      return {
        ...state,
        garments: (state.garments || []).map(garment =>
          garment.id === action.payload.id
            ? { ...garment, stock: Math.max(0, garment.stock + action.payload.change) }
            : garment
        ),
        // Don't duplicate in products array
      };

    case actionTypes.ADD_ORDER:
      const newOrderId = String(state.nextOrderId).padStart(3, '0');
      return {
        ...state,
        orders: [...state.orders, { ...action.payload, id: newOrderId }],
        nextOrderId: state.nextOrderId + 1,
      };

    case actionTypes.UPDATE_ORDER:
      return {
        ...state,
        orders: state.orders.map(order =>
          order.id === action.payload.id ? { ...order, ...action.payload } : order
        ),
      };

    case actionTypes.DELETE_ORDER:
      return {
        ...state,
        orders: state.orders.filter(order => order.id !== action.payload),
      };

    case actionTypes.UPDATE_ORDER_STATUS:
      return {
        ...state,
        orders: state.orders.map(order =>
          order.id === action.payload.id ? { ...order, status: action.payload.status } : order
        ),
      };

    case actionTypes.ADD_CUSTOMER:
      return {
        ...state,
        customers: [...state.customers, { ...action.payload, id: state.nextCustomerId }],
        nextCustomerId: state.nextCustomerId + 1,
      };

    case actionTypes.UPDATE_CUSTOMER:
      return {
        ...state,
        customers: state.customers.map(customer =>
          customer.id === action.payload.id ? { ...customer, ...action.payload } : customer
        ),
      };

    case actionTypes.DELETE_CUSTOMER:
      return {
        ...state,
        customers: state.customers.filter(customer => customer.id !== action.payload),
      };

    // Measurements
    case actionTypes.ADD_MEASUREMENT:
      return {
        ...state,
        measurements: [...(state.measurements || []), { ...action.payload, id: state.nextMeasurementId }],
        nextMeasurementId: state.nextMeasurementId + 1,
      };

    case actionTypes.UPDATE_MEASUREMENT:
      return {
        ...state,
        measurements: (state.measurements || []).map(measurement =>
          measurement.id === action.payload.id ? { ...measurement, ...action.payload } : measurement
        ),
      };

    case actionTypes.DELETE_MEASUREMENT:
      return {
        ...state,
        measurements: (state.measurements || []).filter(measurement => measurement.id !== action.payload),
      };

    // Fabrics
    case actionTypes.ADD_FABRIC:
      return {
        ...state,
        fabrics: [...(state.fabrics || []), { ...action.payload, id: state.nextFabricId }],
        nextFabricId: state.nextFabricId + 1,
      };

    case actionTypes.UPDATE_FABRIC:
      return {
        ...state,
        fabrics: (state.fabrics || []).map(fabric =>
          fabric.id === action.payload.id ? { ...fabric, ...action.payload } : fabric
        ),
      };

    case actionTypes.DELETE_FABRIC:
      return {
        ...state,
        fabrics: (state.fabrics || []).filter(fabric => fabric.id !== action.payload),
      };

    case actionTypes.UPDATE_FABRIC_STOCK:
      return {
        ...state,
        fabrics: (state.fabrics || []).map(fabric =>
          fabric.id === action.payload.id
            ? { ...fabric, stock: Math.max(0, fabric.stock + action.payload.change) }
            : fabric
        ),
      };

    // Garment Templates
    case actionTypes.ADD_GARMENT_TEMPLATE:
      return {
        ...state,
        garmentTemplates: [...(state.garmentTemplates || []), action.payload],
      };

    case actionTypes.UPDATE_GARMENT_TEMPLATE:
      return {
        ...state,
        garmentTemplates: (state.garmentTemplates || []).map(template =>
          template.id === action.payload.id ? { ...template, ...action.payload } : template
        ),
      };

    case actionTypes.DELETE_GARMENT_TEMPLATE:
      return {
        ...state,
        garmentTemplates: (state.garmentTemplates || []).filter(template => template.id !== action.payload),
      };

    case actionTypes.UPDATE_SETTINGS:
      const newSettings = { ...state.settings, ...action.payload };
      // Persist profile image separately for better reliability
      if (action.payload.profileImage) {
        AsyncStorage.setItem('profileImage', action.payload.profileImage);
      }
      return {
        ...state,
        settings: newSettings,
      };

    case actionTypes.LOAD_DATA:
      return {
        ...state,
        ...action.payload,
        isDataLoaded: true,
      };

    case actionTypes.SET_DATA_LOADED:
      return {
        ...state,
        isDataLoaded: action.payload,
      };

    case actionTypes.CLEAR_DATA:
      return {
        ...initialState,
        isDataLoaded: false,
      };

    default:
      return state;
  }
};

// Create context
const DataContext = createContext();

// Provider component with performance optimizations
export const DataProvider = ({ children }) => {
  const [state, dispatch] = useReducer(dataReducer, initialState);

  // Load data from AsyncStorage on app start
  useEffect(() => {
    initializeServices();
    loadData();
  }, []);

  // Initialize optimized services
  const initializeServices = useCallback(async () => {
    try {
      // Initialize search service with data providers
      await searchService.initializeHistory();

      // Update search providers with current data
      searchService.updateSearchProvider('customers', async () => state.customers || []);
      searchService.updateSearchProvider('orders', async () => state.orders || []);
      searchService.updateSearchProvider('products', async () => state.garments || []);
      searchService.updateSearchProvider('garments', async () => state.garments || []);
      searchService.updateSearchProvider('fabrics', async () => state.fabrics || []);

    } catch (error) {
      console.error('❌ Error initializing services:', error);
    }
  }, [state]);

  const loadData = useCallback(async () => {
    const startTime = Date.now();
    try {

      // Removed automatic data clearing - preserve user data

      // Initialize MigrationService (handles SQLite setup and migration)
      await MigrationService.initialize();

      // Initialize offline support
      try {
        await OfflineService.initializeOfflineCapabilities();

      } catch (error) {
        console.warn('⚠️ Offline service initialization failed:', error);

      }

      // Data will be loaded from SQLite or start empty

      // Load critical data first, then load secondary data asynchronously
      const [products, orders, customers] = await Promise.all([
        MigrationService.getProducts().catch(() => []),
        MigrationService.getOrders().catch(() => []),
        MigrationService.getCustomers().catch(() => [])
      ]);

      // Load tailoring-specific data - measurements, fabrics, and garment templates now from SQLite with error handling
      let measurements = [];
      let fabrics = [];
      let garmentTemplates = [];
      let savedSettings = null;

      try {
        [measurements, fabrics, garmentTemplates, savedSettings] = await Promise.all([
          MigrationService.getMeasurements().catch(error => {
            // Silently handle SQLite errors in Expo Go
            if (error.message && error.message.includes('executeSql')) {
              // Try AsyncStorage fallback for Expo Go
              return AsyncStorage.getItem('tailorMeasurements')
                .then(data => data ? JSON.parse(data) : [])
                .catch(() => []);
            }
            console.error('Error getting measurements:', error);
            return [];
          }),
          MigrationService.getFabrics().catch(error => {
            // Silently handle SQLite errors in Expo Go
            if (error.message && error.message.includes('executeSql')) {
              // Try AsyncStorage fallback for Expo Go
              return AsyncStorage.getItem('tailorFabrics')
                .then(data => data ? JSON.parse(data) : [])
                .catch(() => []);
            }
            console.error('Error getting fabrics:', error);
            return [];
          }),
          MigrationService.getGarmentTemplates().catch(error => {
            // Silently handle SQLite errors in Expo Go
            if (error.message && error.message.includes('executeSql')) {
              // Try AsyncStorage fallback for Expo Go
              return AsyncStorage.getItem('garmentTemplates')
                .then(data => data ? JSON.parse(data) : [])
                .catch(() => []);
            }
            console.error('Error getting garment templates:', error);
            return [];
          }),
          AsyncStorage.getItem('tailorSettings')
        ]);
      } catch (error) {
        console.error('Error loading tailor-specific data:', error);
        measurements = [];
        fabrics = [];
        garmentTemplates = [];
        savedSettings = null;
      }

      let settings = savedSettings ?
        { ...initialState.settings, ...JSON.parse(savedSettings) } :
        initialState.settings;

      // Load profile image separately for better reliability
      const profileImageData = await AsyncStorage.getItem('profileImage');
      if (profileImageData) {
        settings.profileImage = profileImageData;
      }

      // Check for orders to archive before finalizing data
      let finalOrders = orders;
      try {
        const archivedOrderIds = await OrderArchiveService.checkAndArchiveOrders(orders);
        if (archivedOrderIds.length > 0) {
          finalOrders = orders.filter(order => !archivedOrderIds.includes(order.id));

        }
      } catch (error) {
        console.error('Error checking for orders to archive:', error);
        // Continue with original orders if archiving fails
      }

      console.log('Performance info:', MigrationService.getPerformanceInfo());

      // Calculate next IDs for all entities with safe handling
      const nextGarmentId = products.length > 0 ? Math.max(...products.map(p => parseInt(p.id) || 0)) + 1 : 1;
      const nextOrderId = finalOrders.length > 0 ? Math.max(...finalOrders.map(o => parseInt(o.id) || 0)) + 1 : 1;
      const nextCustomerId = customers.length > 0 ? Math.max(...customers.map(c => parseInt(c.id) || 0)) + 1 : 1;
      const nextMeasurementId = measurements.length > 0 ? Math.max(...measurements.map(m => parseInt(m.id) || 0)) + 1 : 1;
      const nextFabricId = fabrics.length > 0 ? Math.max(...fabrics.map(f => parseInt(f.id) || 0)) + 1 : 1;

      const loadedData = {
        // Core tailoring entities
        garments: products, // Use products as garments for now
        orders: finalOrders,
        customers,
        measurements,
        fabrics,
        garmentTemplates,
        settings,

        // ID counters
        nextGarmentId,
        nextOrderId,
        nextCustomerId,
        nextMeasurementId,
        nextFabricId,

        // Legacy support - products should reference garments, not duplicate
        products: products, // Keep reference to same array for backward compatibility
        nextProductId: nextGarmentId,
      };

      dispatch({ type: actionTypes.LOAD_DATA, payload: loadedData });

      // Track performance metrics
      const loadTime = Date.now() - startTime;

    } catch (error) {
      console.error('❌ Error loading data:', error);
      // Fallback to empty state on error
      dispatch({ type: actionTypes.SET_DATA_LOADED, payload: true });
    }
  }, []);

  const saveData = useCallback(async () => {
    // Only save if data has been loaded to prevent overwriting with empty state
    if (!state.isDataLoaded) {
      return;
    }

    try {
      // Save tailoring-specific data to AsyncStorage (measurements and fabrics now in SQLite)
      await Promise.all([
        AsyncStorage.setItem('tailorSettings', JSON.stringify(state.settings || {}))
      ]);

      // Note: Garments, orders, customers, measurements, and fabrics are automatically saved by MigrationService
      // when using the action creators below.

    } catch (error) {
      console.error('Error saving tailor data:', error);
      // Don't crash the app on save errors
    }
  }, [state.appointments, state.settings, state.isDataLoaded]);

  // Simple debounced save - moved after saveData definition
  const debouncedSave = useCallback(() => {
    const timeoutId = setTimeout(() => {
      saveData();
    }, 300);
    return () => clearTimeout(timeoutId);
  }, [saveData]);

  useEffect(() => {
    debouncedSave();
  }, [state.garments, state.products, state.orders, state.customers, state.measurements, state.fabrics, state.settings, debouncedSave]);

  // Check for notifications when data changes
  useEffect(() => {
    if (!state.isLoading && state.isDataLoaded) {
      // Check for tailor-specific notifications
      NotificationService.checkTailorNotifications(state);
    }
  }, [state.fabrics, state.appointments, state.orders, state.customers, state.isLoading, state.isDataLoaded]);

  // Clear all data function
  const clearAllData = useCallback(async () => {
    try {
      // Clear AsyncStorage
      await AsyncStorage.multiRemove([
        'tailorMeasurements',
        'tailorFabrics',
        'tailorAppointments',
        'tailorSettings'
      ]);

      // Clear SQLite data
      await MigrationService.clearAllData();

      // Reset state
      dispatch({ type: actionTypes.CLEAR_DATA });

      return true;
    } catch (error) {
      console.error('Error clearing data:', error);
      return false;
    }
  }, []);

  // Memoized action creators for performance with SQLite optimization
  const actions = useMemo(() => ({
    // Clear all data
    clearAllData,

    // Products - now with SQLite backend
    addProduct: async (product) => {
      const savedProduct = await MigrationService.saveProduct({
        ...product,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
      dispatch({ type: actionTypes.ADD_PRODUCT, payload: savedProduct });
      return savedProduct;
    },

    updateProduct: async (product) => {
      const updatedProduct = await MigrationService.saveProduct({
        ...product,
        updatedAt: new Date().toISOString()
      });
      dispatch({ type: actionTypes.UPDATE_PRODUCT, payload: updatedProduct });
      return updatedProduct;
    },

    deleteProduct: async (id) => {
      await MigrationService.deleteProduct(id);
      dispatch({ type: actionTypes.DELETE_PRODUCT, payload: id });
    },

    updateStock: async (id, change) => {
      // Get current product, update stock, then save
      const products = await MigrationService.getProducts();
      const product = products.find(p => p.id === id);
      if (product) {
        const updatedProduct = {
          ...product,
          stock: Math.max(0, product.stock + change),
          updatedAt: new Date().toISOString()
        };
        await MigrationService.saveProduct(updatedProduct);
        dispatch({ type: actionTypes.UPDATE_STOCK, payload: { id, change } });
      }
    },

    // Orders - now with SQLite backend
    addOrder: async (order) => {
      const orderWithId = {
        ...order,
        id: order.id || String(state.nextOrderId).padStart(3, '0'),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      const savedOrder = await MigrationService.saveOrder(orderWithId);
      dispatch({ type: actionTypes.ADD_ORDER, payload: savedOrder });
      return savedOrder;
    },

    updateOrder: async (order) => {
      const updatedOrder = await MigrationService.saveOrder({
        ...order,
        updatedAt: new Date().toISOString()
      });
      dispatch({ type: actionTypes.UPDATE_ORDER, payload: updatedOrder });
      return updatedOrder;
    },

    deleteOrder: async (id) => {
      await MigrationService.deleteOrder(id);
      dispatch({ type: actionTypes.DELETE_ORDER, payload: id });
    },

    updateOrderStatus: async (id, status) => {
      const orders = await MigrationService.getOrders();
      const order = orders.find(o => o.id === id);
      if (order) {
        const oldStatus = order.status;
        const updatedOrder = {
          ...order,
          status,
          updatedAt: new Date().toISOString()
        };
        await MigrationService.saveOrder(updatedOrder);
        dispatch({ type: actionTypes.UPDATE_ORDER_STATUS, payload: { id, status } });

        // Create notification for status change
        if (oldStatus !== status) {
          NotificationService.createOrderStatusNotification(updatedOrder, oldStatus, status);
        }
      }
    },

    // Customers - now with SQLite backend
    addCustomer: async (customer) => {
      const savedCustomer = await MigrationService.saveCustomer({
        ...customer,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
      dispatch({ type: actionTypes.ADD_CUSTOMER, payload: savedCustomer });
      return savedCustomer;
    },

    updateCustomer: async (customer) => {
      const updatedCustomer = await MigrationService.saveCustomer({
        ...customer,
        updatedAt: new Date().toISOString()
      });
      dispatch({ type: actionTypes.UPDATE_CUSTOMER, payload: updatedCustomer });
      return updatedCustomer;
    },

    deleteCustomer: async (id) => {
      await MigrationService.deleteCustomer(id);
      dispatch({ type: actionTypes.DELETE_CUSTOMER, payload: id });
    },

    // Measurements - now with SQLite backend
    addMeasurement: async (measurement) => {
      const savedMeasurement = await MigrationService.saveMeasurement({
        ...measurement,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
      dispatch({ type: actionTypes.ADD_MEASUREMENT, payload: savedMeasurement });
      return savedMeasurement;
    },

    updateMeasurement: async (measurement) => {
      const updatedMeasurement = await MigrationService.saveMeasurement({
        ...measurement,
        updatedAt: new Date().toISOString()
      });
      dispatch({ type: actionTypes.UPDATE_MEASUREMENT, payload: updatedMeasurement });
      return updatedMeasurement;
    },

    deleteMeasurement: async (id) => {
      await MigrationService.deleteMeasurement(id);
      dispatch({ type: actionTypes.DELETE_MEASUREMENT, payload: id });
    },

    // Fabrics - now with SQLite backend
    addFabric: async (fabric) => {
      const savedFabric = await MigrationService.saveFabric({
        ...fabric,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
      dispatch({ type: actionTypes.ADD_FABRIC, payload: savedFabric });
      return savedFabric;
    },

    updateFabric: async (fabric) => {
      const updatedFabric = await MigrationService.saveFabric({
        ...fabric,
        updatedAt: new Date().toISOString()
      });
      dispatch({ type: actionTypes.UPDATE_FABRIC, payload: updatedFabric });
      return updatedFabric;
    },

    deleteFabric: async (id) => {
      await MigrationService.deleteFabric(id);
      dispatch({ type: actionTypes.DELETE_FABRIC, payload: id });
    },

    updateFabricStock: async (id, change) => {
      await MigrationService.updateFabricStock(id, change);
      dispatch({ type: actionTypes.UPDATE_FABRIC_STOCK, payload: { id, change } });
    },

    // Garment Templates
    addGarmentTemplate: async (template) => {
      const savedTemplate = await MigrationService.saveGarmentTemplate({
        ...template,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
      dispatch({ type: actionTypes.ADD_GARMENT_TEMPLATE, payload: savedTemplate });
      return savedTemplate;
    },

    updateGarmentTemplate: async (template) => {
      const updatedTemplate = await MigrationService.saveGarmentTemplate({
        ...template,
        updatedAt: new Date().toISOString()
      });
      dispatch({ type: actionTypes.UPDATE_GARMENT_TEMPLATE, payload: updatedTemplate });
      return updatedTemplate;
    },

    deleteGarmentTemplate: async (id) => {
      await MigrationService.deleteGarmentTemplate(id);
      dispatch({ type: actionTypes.DELETE_GARMENT_TEMPLATE, payload: id });
    },

    // Garment aliases (for cleaner API)
    addGarment: async (garment) => {
      return await actions.addProduct(garment);
    },

    updateGarment: async (garment) => {
      return await actions.updateProduct(garment);
    },

    deleteGarment: async (id) => {
      return await actions.deleteProduct(id);
    },

    updateGarmentStock: async (id, change) => {
      return await actions.updateStock(id, change);
    },

    // Settings
    updateSettings: (settings) => dispatch({ type: actionTypes.UPDATE_SETTINGS, payload: settings }),

    // Data Management

    importData: (importedData) => {
      try {
        // Validate imported data with support for both new and legacy formats
        const validatedData = {
          customers: Array.isArray(importedData.customers) ? importedData.customers : [],
          orders: Array.isArray(importedData.orders) ? importedData.orders : [],
          garments: Array.isArray(importedData.garments) ? importedData.garments : [],
          products: Array.isArray(importedData.products) ? importedData.products : [], // Legacy support
          measurements: Array.isArray(importedData.measurements) ? importedData.measurements : [],
          fabrics: Array.isArray(importedData.fabrics) ? importedData.fabrics : [],
          settings: importedData.settings || state.settings,
        };

        // Merge garments and products for backward compatibility
        if (validatedData.products.length > 0 && validatedData.garments.length === 0) {
          validatedData.garments = validatedData.products;
        }

        // Update next IDs based on imported data (with safe fallbacks)
        const nextProductId = validatedData.products.length > 0 ? Math.max(...validatedData.products.map(p => p.id || 0)) + 1 : 1;
        const nextGarmentId = validatedData.garments.length > 0 ? Math.max(...validatedData.garments.map(g => g.id || 0)) + 1 : 1;
        const nextOrderId = validatedData.orders.length > 0 ? Math.max(...validatedData.orders.map(o => parseInt(o.id) || 0)) + 1 : 1;
        const nextCustomerId = validatedData.customers.length > 0 ? Math.max(...validatedData.customers.map(c => c.id || 0)) + 1 : 1;
        const nextMeasurementId = validatedData.measurements.length > 0 ? Math.max(...validatedData.measurements.map(m => m.id || 0)) + 1 : 1;
        const nextFabricId = validatedData.fabrics.length > 0 ? Math.max(...validatedData.fabrics.map(f => f.id || 0)) + 1 : 1;

        dispatch({
          type: actionTypes.LOAD_DATA,
          payload: {
            ...validatedData,
            nextProductId,
            nextGarmentId,
            nextOrderId,
            nextCustomerId,
            nextMeasurementId,
            nextFabricId,
          }
        });

      } catch (error) {
        console.error('Error importing data:', error);
        throw error; // Re-throw to handle in UI
      }
    },

    exportData: () => {
      return {
        // Core entities
        customers: state.customers,
        orders: state.orders,
        garments: state.garments,
        products: state.products, // Keep for backward compatibility
        measurements: state.measurements,
        fabrics: state.fabrics,

        // Settings and configuration
        settings: state.settings,

        // Metadata
        exportDate: new Date().toISOString(),
        version: '1.0.0',
        appName: 'Elite Tailoring Management',
      };
    },

    clearData: async () => {
      try {
        // Clear SQLite data
        await MigrationService.clearAllData();

        // Clear AsyncStorage including tailor-specific data
        await AsyncStorage.multiRemove([
          'tailorData',
          'tailorSettings',
          'tailorMeasurements',
          'tailorFabrics',
          'recentSearches_global',
          'recentSearches_products',
          'recentSearches_orders',
          'recentSearches_customers'
        ]);

        dispatch({ type: actionTypes.CLEAR_DATA });

      } catch (error) {
        console.error('Error clearing tailor data:', error);
      }
    },

    // Force save data immediately (for critical operations)
    forceSave: async () => {
      await saveData();
    },

    // Simple monitoring methods
    getPerformanceScore: () => {
      return 100; // Always return good performance
    },

    getHealthStatus: () => {
      return { status: 'healthy', score: 100 };
    },

    generateHealthReport: () => {
      return { status: 'healthy', score: 100, message: 'All systems operational' };
    },

    getOverallAppScore: () => {
      return {
        overallScore: 100,
        breakdown: {
          performance: 100,
          health: 100,
        },
        grade: 'A+',
        status: 'Excellent',
      };
    },

  }), [dispatch, state.products, state.garments, state.orders, state.customers, state.measurements, state.fabrics, state.settings, saveData, loadData]);

  return (
    <DataContext.Provider value={{ state, actions }>
      {children}
    </DataContext.Provider>
  );
};

// Custom hook to use the context
export const useData = () => {
  const context = useContext(DataContext);
  if (!context) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
};

export default DataContext;
