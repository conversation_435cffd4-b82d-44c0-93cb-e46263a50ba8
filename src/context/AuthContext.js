/**
 * AuthContext - Authentication and role management
 * Handles login, logout, role-based access control
 */

import React, { createContext, useContext, useReducer, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

const AuthContext = createContext();

// User roles for tailor management
export const ROLES = {
  ADMIN: 'admin',
  USER: 'user',
  // Tailor-specific roles
  MASTER_TAILOR: 'master_tailor',
  CUTTER: 'cutter',
  STITCHER: 'stitcher',
  FINISHER: 'finisher',
  RECEPTIONIST: 'receptionist',
  HELPER: 'helper'};

// Permissions for each role
export const PERMISSIONS = {
  [ROLES.ADMIN]: [
    'view_dashboard',
    'view_products',
    'view_customers',
    'view_orders',
    'view_reports',
    'view_financial',
    'manage_employees',
    'manage_settings',
    'create_products',
    'edit_products',
    'delete_products',
    'create_customers',
    'edit_customers',
    'delete_customers',
    'create_orders',
    'edit_orders',
    'delete_orders'],
  [ROLES.USER]: [
    'view_dashboard',
    'view_products',
    'view_customers',
    'view_orders',
    'manage_settings',
    'create_products',
    'edit_products',
    'create_customers',
    'edit_customers',
    'create_orders',
    'edit_orders']};

const initialState = {
  user: null,
  isAuthenticated: false,
  isLoading: true,
  employees: []};

const authReducer = (state, action) => {
  switch(action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload  };
    case 'LOGIN_SUCCESS':
      return { ...state,
        user: action.payload,
        isAuthenticated: true,
        isLoading: false};
    case 'LOGOUT':
      return { ...state,
        user: null,
        isAuthenticated: false,
        isLoading: false};
    case 'SET_EMPLOYEES':
      return { ...state, employees: action.payload  };
    case 'ADD_EMPLOYEE':
      return { ...state,
        employees: [action.payload, ...state.employees]};
    case 'UPDATE_EMPLOYEE':
      return { ...state,
        employees: state.employees.map(emp =>
          emp.id === action.payload.id ? action.payload : emp
        )};
    case 'DELETE_EMPLOYEE':
      return { ...state,
        employees: state.employees.filter(emp => emp.id !== action.payload)};
    default:
      return state;
  }
};

export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Load user from storage on app start
  useEffect(() => {
    loadUserFromStorage();
  }, []);

  const loadUserFromStorage = async () => {
    try {
      const userData = await AsyncStorage.getItem('user');
      const employeesData = await AsyncStorage.getItem('employees');

      if(userData) {
        const user = JSON.parse(userData);
        dispatch({ type: 'LOGIN_SUCCESS', payload: user });
      }

      if(employeesData) {
        const employees = JSON.parse(employeesData);
        dispatch({ type: 'SET_EMPLOYEES', payload: employees });
      }
    } catch (error) {
      console.error('Failed to load user from storage:', error);
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const saveUserToStorage = async (user) => {
    try {
      await AsyncStorage.setItem('user', JSON.stringify(user));
    } catch (error) {
      console.error('Failed to save user to storage:', error);
    }
  };

  const saveEmployeesToStorage = async (employees) => {
    try {
      await AsyncStorage.setItem('employees', JSON.stringify(employees));
    } catch (error) {
      console.error('Failed to save employees to storage:', error);
    }
  };

  const login = async (credentials) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });

      // Simulate API call - replace with real authentication
      const { email, phone, password  } = credentials;

      // Check if it's the default admin
      if ((email === '<EMAIL>' || phone === '+1234567890') && password === 'admin123') {
        const adminUser = {
          id: 'admin_001',
          name: 'Admin User',
          email: '<EMAIL>',
          phone: '+1234567890',
          role: ROLES.ADMIN,
          avatar: null,
          createdAt: new Date().toISOString()};

        await saveUserToStorage(adminUser);
        dispatch({ type: 'LOGIN_SUCCESS', payload: adminUser });
        return { success: true, user: adminUser  };
      }

      // Check against employees database
      const employeesData = await AsyncStorage.getItem('employees');
      const employees = employeesData ? JSON.parse(employeesData) : [];
      const employee = employees.find(emp =>
        (emp.email === email || emp.phone === phone) && emp.password === password
      );

      if(employee) {
        const user = { ...employee };
        delete user.password; // Don't store password in user object

        await saveUserToStorage(user);
        dispatch({ type: 'LOGIN_SUCCESS', payload: user });
        return { success: true, user  };
      }

      return { success: false, error: 'Invalid credentials'  };
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, error: 'Login failed'  };
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const logout = async () => {
    try {
      await AsyncStorage.removeItem('user');
      dispatch({ type: 'LOGOUT' });
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const hasPermission = (permission) => {
    if (!state.user) return false;
    const userPermissions = PERMISSIONS[state.user.role] || [];
    return userPermissions.includes(permission);
  };

  const isAdmin = () => {
    return state.user?.role === ROLES.ADMIN;
  };

  const isUser = () => {
    return state.user?.role === ROLES.USER;
  };

  // Employee management (Admin only)
  const addEmployee = async (employeeData) => {
    if (!isAdmin()) {
      throw new Error('Unauthorized: Admin access required');
    }

    try {
      const newEmployee = {
        ...employeeData,
        id: `emp_${Date.now()}`,`
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()};

      const updatedEmployees = [newEmployee, ...state.employees];
      await saveEmployeesToStorage(updatedEmployees);
      dispatch({ type: 'ADD_EMPLOYEE', payload: newEmployee });

      return { success: true, employee: newEmployee  };
    } catch (error) {
      console.error('Add employee error:', error);
      return { success: false, error: 'Failed to add employee'  };
    }
  };

  const updateEmployee = async (employeeId, updates) => {
    if (!isAdmin()) {
      throw new Error('Unauthorized: Admin access required');
    }

    try {
      const updatedEmployee = {
        ...state.employees.find(emp => emp.id === employeeId),
        ...updates,
        updatedAt: new Date().toISOString()};

      const updatedEmployees = state.employees.map(emp =>
        emp.id === employeeId ? updatedEmployee : emp
      );

      await saveEmployeesToStorage(updatedEmployees);
      dispatch({ type: 'UPDATE_EMPLOYEE', payload: updatedEmployee });

      return { success: true, employee: updatedEmployee  };
    } catch (error) {
      console.error('Update employee error:', error);
      return { success: false, error: 'Failed to update employee'  };
    }
  };

  const deleteEmployee = async (employeeId) => {
    if (!isAdmin()) {
      throw new Error('Unauthorized: Admin access required');
    }

    try {
      const updatedEmployees = state.employees.filter(emp => emp.id !== employeeId);
      await saveEmployeesToStorage(updatedEmployees);
      dispatch({ type: 'DELETE_EMPLOYEE', payload: employeeId });

      return { success: true  };
    } catch (error) {
      console.error('Delete employee error:', error);
      return { success: false, error: 'Failed to delete employee'  };
    }
  };

  const value = {
    ...state,
    login,
    logout,
    hasPermission,
    isAdmin,
    isUser,
    addEmployee,
    updateEmployee,
    deleteEmployee,
    ROLES,
    PERMISSIONS};

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if(!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
