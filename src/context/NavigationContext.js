/**
 * NavigationContext - Unified navigation state management
 * Provides navigation state, history, and actions across the app
 */

import React, { createContext, useContext, useReducer, useEffect, useCallback } from 'react';
import navigationService from '../services/NavigationService';

// Navigation state structure
const initialState = {
  currentRoute: 'Dashboard',
  currentTab: 'Dashboard',
  isModalOpen: false,
  modalStack: [],
  routeHistory: [],
  navigationReady: false,
  breadcrumbs: [],
  quickActionsVisible: false,
  searchActive: false,
  searchQuery: '',
  notifications: {
    count: 3,
    visible: false,
  },
  user: {
    profileVisible: false,
  }
};

// Action types
const NAVIGATION_ACTIONS = {
  SET_CURRENT_ROUTE: 'SET_CURRENT_ROUTE',
  SET_CURRENT_TAB: 'SET_CURRENT_TAB',
  OPEN_MODAL: 'OPEN_MODAL',
  CLOSE_MODAL: 'CLOSE_MODAL',
  SET_NAVIGATION_READY: 'SET_NAVIGATION_READY',
  ADD_TO_HISTORY: 'ADD_TO_HISTORY',
  UPDATE_BREADCRUMBS: 'UPDATE_BREADCRUMBS',
  TOGGLE_QUICK_ACTIONS: 'TOGGLE_QUICK_ACTIONS',
  SET_SEARCH_ACTIVE: 'SET_SEARCH_ACTIVE',
  SET_SEARCH_QUERY: 'SET_SEARCH_QUERY',
  TOGGLE_NOTIFICATIONS: 'TOGGLE_NOTIFICATIONS',
  TOGGLE_PROFILE: 'TOGGLE_PROFILE',
  RESET_NAVIGATION: 'RESET_NAVIGATION',
};

// Navigation reducer
const navigationReducer = (state, action) => {
  switch(action.type) {
    case NAVIGATION_ACTIONS.SET_CURRENT_ROUTE:
      return { ...state,
        currentRoute: action.payload,
        breadcrumbs: generateBreadcrumbs(action.payload),
     };

    case NAVIGATION_ACTIONS.SET_CURRENT_TAB:
      return { ...state,
        currentTab: action.payload,
     };

    case NAVIGATION_ACTIONS.OPEN_MODAL:
      return { ...state,
        isModalOpen: true,
        modalStack: [...state.modalStack, action.payload],
     };

    case NAVIGATION_ACTIONS.CLOSE_MODAL:
      const newModalStack = state.modalStack.slice(0, -1);
      return { ...state,
        isModalOpen: newModalStack.length > 0,
        modalStack: newModalStack,
     };

    case NAVIGATION_ACTIONS.SET_NAVIGATION_READY:
      return { ...state,
        navigationReady: action.payload,
     };

    case NAVIGATION_ACTIONS.ADD_TO_HISTORY:
      return { ...state,
        routeHistory: [...state.routeHistory.slice(-9), action.payload], // Keep last 10
     };

    case NAVIGATION_ACTIONS.UPDATE_BREADCRUMBS:
      return { ...state,
        breadcrumbs: action.payload,
     };

    case NAVIGATION_ACTIONS.TOGGLE_QUICK_ACTIONS:
      return { ...state,
        quickActionsVisible: action.payload ?? !state.quickActionsVisible,
     };

    case NAVIGATION_ACTIONS.SET_SEARCH_ACTIVE:
      return { ...state,
        searchActive: action.payload,
        searchQuery: action.payload ? state.searchQuery : '',
     };

    case NAVIGATION_ACTIONS.SET_SEARCH_QUERY:
      return { ...state,
        searchQuery: action.payload,
     };

    case NAVIGATION_ACTIONS.TOGGLE_NOTIFICATIONS:
      return {
        ...state,
        notifications: {
          ...state.notifications,
          visible: action.payload ?? !state.notifications.visible,
        },
      };

    case NAVIGATION_ACTIONS.TOGGLE_PROFILE:
      return {
        ...state,
        user: {
          ...state.user,
          profileVisible: action.payload ?? !state.user.profileVisible,
        },
      };

    case NAVIGATION_ACTIONS.RESET_NAVIGATION:
      return { ...initialState,
        navigationReady: state.navigationReady,
     };

    default:
      return state;
  }
};

// Helper function to generate breadcrumbs
const generateBreadcrumbs = (routeName) => {
  const breadcrumbMap = {
    'Dashboard': [{ label: 'Dashboard', route: 'Dashboard' }],
    'Scan': [{ label: 'Scan', route: 'Scan' }],
    'Orders': [{ label: 'Orders', route: 'Orders' }],
    'Settings': [{ label: 'Settings', route: 'Settings' }],
    'SettingsMain': [{ label: 'Settings', route: 'Settings' }],
    'MyProfile': [{ label: 'My Profile', route: 'MyProfile' }],
    'Products': [{ label: 'Products', route: 'Products' }],
    'AddProduct': [{ label: 'Add Product', route: 'AddProduct' }],
    'AddOrder': [{ label: 'Create Order', route: 'AddOrder' }],
    'ImportData': [
      { label: 'Settings', route: 'Settings' },
      { label: 'Import Data', route: 'ImportData' }
    ],
    'ActivityLog': [
      { label: 'Settings', route: 'Settings' },
      { label: 'Activity Log', route: 'ActivityLog' }
    ],
  };

  return breadcrumbMap[routeName] || [{ label: routeName, route: routeName }];
};

// Create context
const NavigationContext = createContext();

// Navigation provider component
export const NavigationProvider = ({ children }) => {
  const [state, dispatch] = useReducer(navigationReducer, initialState);

  // Navigation actions
  const actions = {
    // Core navigation
    setCurrentRoute: useCallback((route) => {
      dispatch({ type: NAVIGATION_ACTIONS.SET_CURRENT_ROUTE, payload: route });
      dispatch({ type: NAVIGATION_ACTIONS.ADD_TO_HISTORY, payload: { route, timestamp: Date.now() } });
    }, []),

    setCurrentTab: useCallback((tab) => {
      dispatch({ type: NAVIGATION_ACTIONS.SET_CURRENT_TAB, payload: tab });
    }, []),

    // Modal management
    openModal: useCallback((modalName, params) => {
      dispatch({ type: NAVIGATION_ACTIONS.OPEN_MODAL, payload: { modalName, params } });
      navigationService.openModal(modalName, params);
    }, []),

    closeModal: useCallback(() => {
      dispatch({ type: NAVIGATION_ACTIONS.CLOSE_MODAL });
      navigationService.closeModal();
    }, []),

    // Navigation utilities
    setNavigationReady: useCallback((ready) => {
      dispatch({ type: NAVIGATION_ACTIONS.SET_NAVIGATION_READY, payload: ready });
    }, []),

    // UI state management
    toggleQuickActions: useCallback((visible) => {
      dispatch({ type: NAVIGATION_ACTIONS.TOGGLE_QUICK_ACTIONS, payload: visible });
    }, []),

    setSearchActive: useCallback((active) => {
      dispatch({ type: NAVIGATION_ACTIONS.SET_SEARCH_ACTIVE, payload: active });
    }, []),

    setSearchQuery: useCallback((query) => {
      dispatch({ type: NAVIGATION_ACTIONS.SET_SEARCH_QUERY, payload: query });
    }, []),

    toggleNotifications: useCallback((visible) => {
      dispatch({ type: NAVIGATION_ACTIONS.TOGGLE_NOTIFICATIONS, payload: visible });
    }, []),

    toggleProfile: useCallback((visible) => {
      dispatch({ type: NAVIGATION_ACTIONS.TOGGLE_PROFILE, payload: visible });
    }, []),

    // Quick navigation methods
    navigateToTab: useCallback((tabName) => {
      navigationService.navigateToTab(tabName);
    }, []),

    openSettings: useCallback((settingsScreen) => {
      navigationService.openSettings(settingsScreen);
    }, []),

    openQuickAction: useCallback((action, params) => {
      navigationService.openQuickAction(action, params);
      dispatch({ type: NAVIGATION_ACTIONS.TOGGLE_QUICK_ACTIONS, payload: false });
    }, []),

    // Reset navigation
    resetNavigation: useCallback(() => {
      dispatch({ type: NAVIGATION_ACTIONS.RESET_NAVIGATION });
      navigationService.reset({
        index: 0,
        routes: [{ name: 'Main', params: { screen: 'Dashboard' } }],
      });
    }, []),
  };

  // Listen to navigation state changes
  useEffect(() => {
    const listener = (navigationState) => {
      if(navigationState) {
        const currentRoute = navigationService.getCurrentRouteName();
        if(currentRoute && currentRoute !== state.currentRoute) {
          dispatch({ type: NAVIGATION_ACTIONS.SET_CURRENT_ROUTE, payload: currentRoute });

          // Update current tab based on route
          const tab = navigationService.getTabFromRoute(currentRoute);
          if(tab !== state.currentTab) {
            dispatch({ type: NAVIGATION_ACTIONS.SET_CURRENT_TAB, payload: tab });
          }
        }
      }
    };

    navigationService.addNavigationListener(listener);
    return () => navigationService.removeNavigationListener(listener);
  }, [state.currentRoute, state.currentTab]);

  const value = {
    state,
    actions,
    // Convenience getters
    currentRoute: state.currentRoute,
    currentTab: state.currentTab,
    isModalOpen: state.isModalOpen,
    navigationReady: state.navigationReady,
    breadcrumbs: state.breadcrumbs,
    quickActionsVisible: state.quickActionsVisible,
    searchActive: state.searchActive,
    searchQuery: state.searchQuery,
    notifications: state.notifications,
    user: state.user,
  };

  return (
    <NavigationContext.Provider value={value}>
      {children}
    </NavigationContext.Provider>
  );
};

// Custom hook to use navigation context
export const useNavigationContext = () => {
  const context = useContext(NavigationContext);
  if(!context) {
    throw new Error('useNavigationContext must be used within a NavigationProvider');
  }
  return context;
};

// Keep the old export for backward compatibility but mark as deprecated
// Temporarily disabled to isolate the issue
/*
export const useNavigation = () => {
  console.warn('useNavigation from NavigationContext is deprecated. Use useNavigationContext instead.');
  return useNavigationContext();
};
*/

export default NavigationContext;
