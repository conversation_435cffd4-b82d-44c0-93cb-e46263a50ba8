import React, { createContext, useContext, useReducer, useCallback, useEffect } from 'react';
import { FinancialService   } from '../services/financialService';

// Initial state
const initialState = {
  expenses: [],
  reconciliations: [],
  profitLossData: null,
  paymentAnalytics: null,
  taxSummary: null,
  loading: false,
  error: null,
  lastUpdated: null,
};

// Action types
const ActionTypes = {
  SET_LOADING: 'SET_LOADING',
  SET_ERROR: 'SET_ERROR',
  SET_EXPENSES: 'SET_EXPENSES',
  ADD_EXPENSE: 'ADD_EXPENSE',
  UPDATE_EXPENSE: 'UPDATE_EXPENSE',
  DELETE_EXPENSE: 'DELETE_EXPENSE',
  SET_RECONCILIATIONS: 'SET_RECONCILIATIONS',
  ADD_RECONCILIATION: 'ADD_RECONCILIATION',
  SET_PROFIT_LOSS: 'SET_PROFIT_LOSS',
  SET_PAYMENT_ANALYTICS: 'SET_PAYMENT_ANALYTICS',
  SET_TAX_SUMMARY: 'SET_TAX_SUMMARY',
  CLEAR_DATA: 'CLEAR_DATA',
};

// Reducer
const financialReducer = (state, action) => {
  switch(action.type) {
    case ActionTypes.SET_LOADING:
      return { ...state, loading: action.payload  };

    case ActionTypes.SET_ERROR:
      return { ...state, error: action.payload, loading: false  };

    case ActionTypes.SET_EXPENSES:
      return { ...state,
        expenses: action.payload,
        loading: false,
        error: null,
        lastUpdated: new Date().toISOString(),
       };

    case ActionTypes.ADD_EXPENSE:
      return { ...state,
        expenses: [action.payload, ...state.expenses],
        lastUpdated: new Date().toISOString(),
       };

    case ActionTypes.UPDATE_EXPENSE:
      return { ...state,
        expenses: state.expenses.map(expense =>
          expense.id === action.payload.id ? action.payload : expense
        ),
        lastUpdated: new Date().toISOString(),
       };

    case ActionTypes.DELETE_EXPENSE:
      return { ...state,
        expenses: state.expenses.filter(expense => expense.id !== action.payload),
        lastUpdated: new Date().toISOString(),
       };

    case ActionTypes.SET_RECONCILIATIONS:
      return { ...state,
        reconciliations: action.payload,
        loading: false,
        error: null,
        lastUpdated: new Date().toISOString(),
       };

    case ActionTypes.ADD_RECONCILIATION:
      return { ...state,
        reconciliations: [action.payload, ...state.reconciliations],
        lastUpdated: new Date().toISOString(),
       };

    case ActionTypes.SET_PROFIT_LOSS:
      return { ...state,
        profitLossData: action.payload,
        loading: false,
        error: null,
        lastUpdated: new Date().toISOString(),
       };

    case ActionTypes.SET_PAYMENT_ANALYTICS:
      return { ...state,
        paymentAnalytics: action.payload,
        loading: false,
        error: null,
        lastUpdated: new Date().toISOString(),
       };

    case ActionTypes.SET_TAX_SUMMARY:
      return { ...state,
        taxSummary: action.payload,
        loading: false,
        error: null,
        lastUpdated: new Date().toISOString(),
       };

    case ActionTypes.CLEAR_DATA:
      return { ...initialState  };

    default:
      return state;
  }
};

// Context
const FinancialContext = createContext();

// Provider component
export const FinancialProvider = ({ children }) => {
  const [state, dispatch] = useReducer(financialReducer, initialState);

  // Expense management
  const loadExpenses = useCallback(async (filters = {}) => {
    try {
      dispatch({ type: ActionTypes.SET_LOADING, payload: true });
      const expenses = await FinancialService.getExpenses(filters);

      // Validate expenses array
      if (Array.isArray(expenses)) {
        dispatch({ type: ActionTypes.SET_EXPENSES, payload: expenses });
      } else {
        console.warn('Invalid expenses data received, using empty array');
        dispatch({ type: ActionTypes.SET_EXPENSES, payload: []);
      }
    } catch (error) {
      console.error('Failed to load expenses:', error);
      dispatch({ type: ActionTypes.SET_ERROR, payload: error.message || 'Failed to load expenses' });
    }
  }, []);

  const addExpense = useCallback(async (expenseData) => {
    try {
      dispatch({ type: ActionTypes.SET_LOADING, payload: true });
      const newExpense = await FinancialService.addExpense(expenseData);
      dispatch({ type: ActionTypes.ADD_EXPENSE, payload: newExpense });
      return newExpense;
    } catch (error) {
      console.error('Failed to add expense:', error);
      dispatch({ type: ActionTypes.SET_ERROR, payload: error.message });
      throw error;
    }
  }, []);

  const updateExpense = useCallback(async (id, updates) => {
    try {
      const updatedExpense = await FinancialService.updateExpense(id, updates);
      dispatch({ type: ActionTypes.UPDATE_EXPENSE, payload: updatedExpense });
      return updatedExpense;
    } catch (error) {
      console.error('Failed to update expense:', error);
      dispatch({ type: ActionTypes.SET_ERROR, payload: error.message });
      throw error;
    }
  }, []);

  const deleteExpense = useCallback(async (id) => {
    try {
      await FinancialService.deleteExpense(id);
      dispatch({ type: ActionTypes.DELETE_EXPENSE, payload: id });
    } catch (error) {
      console.error('Failed to delete expense:', error);
      dispatch({ type: ActionTypes.SET_ERROR, payload: error.message });
      throw error;
    }
  }, []);

  // Cash reconciliation
  const loadReconciliations = useCallback(async (filters = {}) => {
    try {
      dispatch({ type: ActionTypes.SET_LOADING, payload: true });
      const reconciliations = await FinancialService.getCashReconciliations(filters);
      dispatch({ type: ActionTypes.SET_RECONCILIATIONS, payload: reconciliations });
    } catch (error) {
      console.error('Failed to load reconciliations:', error);
      dispatch({ type: ActionTypes.SET_ERROR, payload: error.message });
    }
  }, []);

  const performReconciliation = useCallback(async (reconciliationData) => {
    try {
      dispatch({ type: ActionTypes.SET_LOADING, payload: true });
      const newReconciliation = await FinancialService.performCashReconciliation(reconciliationData);
      dispatch({ type: ActionTypes.ADD_RECONCILIATION, payload: newReconciliation });
      return newReconciliation;
    } catch (error) {
      console.error('Failed to perform reconciliation:', error);
      dispatch({ type: ActionTypes.SET_ERROR, payload: error.message });
      throw error;
    }
  }, []);

  const calculateDailyCashExpected = useCallback(async (date) => {
    try {
      return await FinancialService.calculateDailyCashExpected(date);
    } catch (error) {
      console.error('Failed to calculate daily cash expected:', error);
      throw error;
    }
  }, []);

  // Financial reports
  const generateProfitLossStatement = useCallback(async (startDate, endDate) => {
    try {
      dispatch({ type: ActionTypes.SET_LOADING, payload: true });
      const profitLoss = await FinancialService.generateProfitLossStatement(startDate, endDate);
      dispatch({ type: ActionTypes.SET_PROFIT_LOSS, payload: profitLoss });
      return profitLoss;
    } catch (error) {
      console.error('Failed to generate P&L statement:', error);
      dispatch({ type: ActionTypes.SET_ERROR, payload: error.message });
      throw error;
    }
  }, []);

  const getPaymentMethodAnalytics = useCallback(async (startDate, endDate) => {
    try {
      dispatch({ type: ActionTypes.SET_LOADING, payload: true });
      const analytics = await FinancialService.getPaymentMethodAnalytics(startDate, endDate);
      dispatch({ type: ActionTypes.SET_PAYMENT_ANALYTICS, payload: analytics });
      return analytics;
    } catch (error) {
      console.error('Failed to get payment analytics:', error);
      dispatch({ type: ActionTypes.SET_ERROR, payload: error.message });
      throw error;
    }
  }, []);

  const getTaxSummary = useCallback(async (startDate, endDate) => {
    try {
      dispatch({ type: ActionTypes.SET_LOADING, payload: true });
      const taxSummary = await FinancialService.getTaxSummary(startDate, endDate);
      dispatch({ type: ActionTypes.SET_TAX_SUMMARY, payload: taxSummary });
      return taxSummary;
    } catch (error) {
      console.error('Failed to get tax summary:', error);
      dispatch({ type: ActionTypes.SET_ERROR, payload: error.message });
      throw error;
    }
  }, []);

  // Utility functions
  const clearError = useCallback(() => {
    dispatch({ type: ActionTypes.SET_ERROR, payload: null });
  }, []);

  const clearData = useCallback(() => {
    dispatch({ type: ActionTypes.CLEAR_DATA });
  }, []);

  // Calculate derived data
  const getDerivedData = useCallback(() => {
    const totalExpenses = state.expenses.reduce((sum, expense) => sum + expense.amount, 0);
    const expensesByCategory = state.expenses.reduce((acc, expense) => {
      acc[expense.category] = (acc[expense.category] || 0) + expense.amount;
      return acc;
    }, {});

    const recentReconciliations = state.reconciliations.slice(0, 5);
    const reconciliationTrend = state.reconciliations.map(r => ({
      date: r.date,
      difference: r.difference,
      status: r.status,
    }));

    return { totalExpenses,
      expensesByCategory,
      recentReconciliations,
      reconciliationTrend,
     };
  }, [state.expenses, state.reconciliations]);

  // Load initial data only once on mount
  useEffect(() => {
    let mounted = true;

    const loadInitialData = async () => {
      if(mounted) {
        await Promise.all([
          loadExpenses(),
          loadReconciliations()
        ]);
      }
    };

    loadInitialData();

    return () => {
      mounted = false;
    };
  }, []); // Empty dependency array - only run once

  const value = {
    // State
    ...state,
    derivedData: getDerivedData(),

    // Expense management
    loadExpenses,
    addExpense,
    updateExpense,
    deleteExpense,

    // Cash reconciliation
    loadReconciliations,
    performReconciliation,
    calculateDailyCashExpected,

    // Financial reports
    generateProfitLossStatement,
    getPaymentMethodAnalytics,
    getTaxSummary,

    // Utilities
    clearError,
    clearData,
  };

  return (
    <FinancialContext.Provider value={value}>
      {children}
    </FinancialContext.Provider>
  );
};

// Hook to use financial context
export const useFinancial = () => {
  const context = useContext(FinancialContext);
  if(!context) {
    throw new Error('useFinancial must be used within a FinancialProvider');
  }
  return context;
};

export default FinancialContext;
