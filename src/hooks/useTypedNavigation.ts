/**
 * Typed navigation hooks for better TypeScript support
 */

import { useNavigation, useRoute, NavigationProp, RouteProp   } from '@react-navigation/native';
import { useState   } from 'react';
import type { RootStackParamList, TabParamList } from '../types/navigation';

// Type-safe navigation hook
export function useTypedNavigation() {
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  
  const navigate = <K extends keyof RootStackParamList>(
    screen: K,
    params?: RootStackParamList[K]
  ) => {
    if(params !== undefined) {
      (navigation.navigate as any)(screen, params);
    } else {
      (navigation.navigate as any)(screen);
    }
  };

  return { ...navigation,
    navigate};
}

// Type-safe route hook
export function useTypedRoute<T extends keyof RootStackParamList>() {
  return useRoute<RouteProp<RootStackParamList, T>>();
}

// Tab navigation hook
export function useTabNavigation() {
  const navigation = useNavigation<NavigationProp<TabParamList>>();
  
  const navigateToTab = <K extends keyof TabParamList>(
    screen: K,
    params?: TabParamList[K]
  ) => {
    if(params !== undefined) {
      (navigation.navigate as any)(screen, params);
    } else {
      (navigation.navigate as any)(screen);
    }
  };

  return { ...navigation,
    navigateToTab};
}

// Route hook for tab screens
export function useTabRoute<T extends keyof TabParamList>() {
  return useRoute<RouteProp<TabParamList, T>>();
}

// Combined navigation hook that provides both stack and tab navigation
export function useCombinedNavigation() {
  const stackNavigation = useNavigation<NavigationProp<RootStackParamList>>();
  const tabNavigation = useNavigation<NavigationProp<TabParamList>>();
  
  return {
    stack: stackNavigation,
    tab: tabNavigation,
    // Convenience methods
    navigateToTab: (screen: keyof TabParamList, params?: TabParamList[keyof TabParamList]) => {
      (tabNavigation.navigate as any)(screen, params);
    },
    navigateToScreen: <T extends keyof RootStackParamList>(,
      screen: T, 
      params?: RootStackParamList[T]
    ) => {
      if(params) {
        (stackNavigation.navigate as any)(screen, params);
      } else {
        (stackNavigation.navigate as any)(screen);
      }
    },
    goBack: () => stackNavigation.goBack(),
    canGoBack: () => stackNavigation.canGoBack()};
}

// Navigation guard hook for protected screens
export function useNavigationGuard() {
  const navigation = useTypedNavigation();
  
  const navigateWithAuth = <T extends keyof RootStackParamList>(
    screen: T,
    params?: RootStackParamList[T],
    requiresAuth: boolean = true
  ) => {
    if(requiresAuth) {
      // Add authentication check logic here
      // For now, just navigate directly
      if(params) {
        navigation.navigate(screen, params);
      } else {
        navigation.navigate(screen as any);
      }
    } else {
      if(params) {
        navigation.navigate(screen, params);
      } else {
        navigation.navigate(screen as any);
      }
    }
  };
  
  return { navigation,
    navigateWithAuth};
}

// Mock permissions hook (replace with actual implementation)
export function usePermissions() {
  const [isAuthenticated, setIsAuthenticated] = useState(true);
  const [userRole, setUserRole] = useState<'admin' | 'manager' | 'user'>('admin');

  // Mock role hierarchy
  const roleHierarchy: Record<string, string[]> = {
    admin: ['admin', 'manager', 'user'],
    manager: ['manager', 'user'],
    user: ['user'];
  // Mock permission mapping
  const permissionMap: Record<string, string[]> = {
    'analytics:read': ['admin', 'manager'],
    'orders:write': ['admin', 'manager'],
    'products:write': ['admin', 'manager'],
    'customers:write': ['admin', 'manager'],
    'settings:write': ['admin'],
    'reports:read': ['admin', 'manager'],
    'dashboard:read': ['admin', 'manager', 'user']};

  const hasPermission = (permission: string): boolean => {
    if (!isAuthenticated) return false;
    
    const allowedRoles = permissionMap[permission] || [];
    return allowedRoles.includes(userRole);
  };

  const hasRole = (role: string): boolean => {
    if (!isAuthenticated) return false;
    
    const userRoles = roleHierarchy[userRole] || [];
    return userRoles.includes(role);
  };

  return { isAuthenticated,
    userRole,
    hasPermission,
    hasRole,
    setUserRole: (role: 'admin' | 'manager' | 'user') => setUserRole(role),
    setIsAuthenticated};
}

// Permission-based navigation hook
export function usePermissionNavigation() {
  const navigation = useTypedNavigation();
  const { hasPermission  } = usePermissions();

  const navigateWithPermission = <T extends keyof RootStackParamList>(
    screen: T,
    requiredPermissions: string[],
    params?: RootStackParamList[T],
    fallbackScreen?: keyof RootStackParamList
  ) => {
    const hasAccess = requiredPermissions.every(permission => hasPermission(permission));
    
    if(hasAccess) {
      navigation.navigate(screen, params);
    } else if(fallbackScreen) {
      navigation.navigate(fallbackScreen);
    } else {
      // Show access denied message or navigate to login
      console.warn('Access denied for screen: ${String(screen)}');
    }
  };

  return { ...navigation,
    navigateWithPermission,
    hasPermission};
}

// Deep link navigation hook
export function useDeepLinkNavigation() {
  const navigation = useTypedNavigation();
  
  const navigateToOrder = (orderId: string) => {
    navigation.navigate('OrderDetails', { orderId });
  };
  
  const navigateToCustomer = (customerId: string) => {
    navigation.navigate('CustomerDetails', { customerId });
  };
  
  const navigateToProduct = (productId: string) => {
    navigation.navigate('AddProduct', undefined);
  };
  
  const navigateToProfile = (userId?: string) => {
    navigation.navigate('MyProfile');
  };
  
  return { navigateToOrder,
    navigateToCustomer,
    navigateToProduct,
    navigateToProfile
 };
}

// Navigation state hook
export function useNavigationState() {
  const navigation = useNavigation();
  
  const getCurrentRoute = () => {
    const state = navigation.getState();
    return state?.routes?.[state.index];
  };
  
  const canGoBack = () => {
    return navigation.canGoBack();
  };
  
  const getRouteHistory = () => {
    const state = navigation.getState();
    return state?.routes || [];
  };
  
  return { getCurrentRoute,
    canGoBack,
    getRouteHistory
 };
}