/**
 * Modern Orders Hook
 * Optimized state management for orders
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
// FIXED: Imported all necessary types for type safety
import { Order, OrderFilters, OrderSortOptions, OrderStats, OrderStatus, PaymentMethod, OrderFormData } from '../types/order';
import { OrderService } from '../services/OrderService';

interface UseOrdersResult {
  orders: Order[];
  filteredOrders: Order[];
  stats: OrderStats;
  loading: boolean;
  refreshing: boolean;
  loadOrders: () => Promise<void>;
  refreshOrders: () => Promise<void>;
  updateFilters: (filters: Partial<OrderFilters>) => void;
  updateSort: (sort: OrderSortOptions) => void;
  filters: OrderFilters;
  sort: OrderSortOptions;
  overdueOrders: Order[];
  dueSoonOrders: Order[];
}

export const useOrders = (initialFilters: OrderFilters = {}): UseOrdersResult => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [filters, setFilters] = useState<OrderFilters>(initialFilters);
  const [sort, setSort] = useState<OrderSortOptions>({ field: 'createdAt', direction: 'desc' });

  const loadOrders = useCallback(async () => {
    setLoading(true);
    try {
      const loadedOrders = await OrderService.getAllOrders();
      setOrders(loadedOrders);
    } catch (error) {
      console.error('Error loading orders:', error);
      setOrders([]);
    } finally {
      setLoading(false);
    }
  }, []);

  const refreshOrders = useCallback(async () => {
    setRefreshing(true);
    try {
      const loadedOrders = await OrderService.getAllOrders();
      setOrders(loadedOrders);
    } catch (error) {
      console.error('Error refreshing orders:', error);
    } finally {
      setRefreshing(false);
    }
  }, []);

  const updateFilters = useCallback((newFilters: Partial<OrderFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  const updateSort = useCallback((newSort: OrderSortOptions) => {
    setSort(newSort);
  }, []);

  useEffect(() => {
    loadOrders();
  }, [loadOrders]);

  const filteredOrders = useMemo(() => {
    if (!orders.length) return [];
    try {
      // Assuming 'includeArchived' is a potential filter property
      const activeOrders = 'includeArchived' in filters && filters.includeArchived
        ? orders 
        : orders.filter(order => !order.isArchived);
      return OrderService.filterAndSortOrders(activeOrders, filters, sort);
    } catch (error) {
      console.error('Error filtering orders:', error);
      return orders.filter(order => !order.isArchived);
    }
  }, [orders, filters, sort]);

  const stats = useMemo(() => {
    try {
      return OrderService.calculateStats(orders.filter(o => !o.isArchived));
    } catch (error) {
      console.error('Error calculating stats:', error);
      return { total: 0, byStatus: {}, byType: {}, byPriority: {}, revenue: { total: 0, paid: 0, pending: 0 }, averageOrderValue: 0, completionRate: 0 } as OrderStats;
    }
  }, [orders]);

  const overdueOrders = useMemo(() => OrderService.getOverdueOrders(orders), [orders]);
  const dueSoonOrders = useMemo(() => OrderService.getOrdersDueSoon(orders), [orders]);

  return {
    orders, filteredOrders, stats, loading, refreshing, loadOrders,
    refreshOrders, updateFilters, updateSort, filters, sort,
    overdueOrders, dueSoonOrders,
  };
};

/**
 * Hook for single order management
 */
export const useOrder = (orderId: string | undefined | null) => {
  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(true);

  const loadOrder = useCallback(async () => {
    if (!orderId) {
      setOrder(null);
      setLoading(false);
      return;
    }
    setLoading(true);
    try {
      const loadedOrder = await OrderService.getOrderById(orderId);
      setOrder(loadedOrder);
    } catch (error) {
      console.error(`Error loading order ${orderId}:`, error);
      setOrder(null);
    } finally {
      setLoading(false);
    }
  }, [orderId]);

  const updateOrder = useCallback(async (updates: Partial<Order>) => {
    if (!orderId) throw new Error("Order ID is missing");
    const updatedOrder = await OrderService.updateOrder(orderId, updates);
    setOrder(updatedOrder);
    return updatedOrder;
  }, [orderId]);

  // FIXED: Parameter 'newStatus' is now strongly typed
  const updateStatus = useCallback(async (newStatus: OrderStatus, notes?: string) => {
    if (!orderId) throw new Error("Order ID is missing");
    const updatedOrder = await OrderService.updateOrderStatus(orderId, newStatus, notes);
    setOrder(updatedOrder);
    return updatedOrder;
  }, [orderId]);

  // FIXED: Parameter 'method' is now strongly typed
  const addPayment = useCallback(async (amount: number, method: PaymentMethod, notes?: string) => {
    if (!orderId) throw new Error("Order ID is missing");
    const updatedOrder = await OrderService.addPayment(orderId, amount, method, notes);
    setOrder(updatedOrder);
    return updatedOrder;
  }, [orderId]);

  useEffect(() => {
    loadOrder();
  }, [loadOrder]);

  return { order, loading, loadOrder, updateOrder, updateStatus, addPayment };
};

/**
 * Hook for order form management
 */
export const useOrderForm = () => {
  const [submitting, setSubmitting] = useState(false);

  // FIXED: Parameter 'formData' is now strongly typed
  const createOrder = useCallback(async (formData: OrderFormData) => {
    setSubmitting(true);
    try {
      const newOrder = await OrderService.createOrder(formData);
      return newOrder;
    } catch (error) {
      // Error is re-thrown to be handled by the component's try-catch block
      throw error;
    } finally {
      setSubmitting(false);
    }
  }, []);

  return { submitting, createOrder };
};