/**
 * useGlobalSortFilter - Global sorting and filtering system
 * Replaces all hardcoded filtering/sorting logic across the app
 */

import { useMemo, useCallback, useState   } from 'react';

// Global filter configurations for different data types
export const GLOBAL_FILTER_CONFIGS = {
  orders: {
    filters: [
      { value: 'all', label: 'All Orders' },
      { value: 'pending', label: 'Pending' },
      { value: 'in_progress', label: 'In Progress' },
      { value: 'fitting', label: 'Fitting' },
      { value: 'ready', label: 'Ready' },
      { value: 'delivered', label: 'Delivered' },
      { value: 'completed', label: 'Completed' },
      { value: 'cancelled', label: 'Cancelled' },
    ],
    sortOptions: [
      { value: 'date_desc', label: 'Latest First', icon: 'sort-calendar-descending' },
      { value: 'date_asc', label: 'Oldest First', icon: 'sort-calendar-ascending' },
      { value: 'customerName_asc', label: 'Customer A-Z', icon: 'sort-alphabetical-ascending' },
      { value: 'customerName_desc', label: 'Customer Z-A', icon: 'sort-alphabetical-descending' },
      { value: 'total_desc', label: 'Highest Value', icon: 'sort-numeric-descending' },
      { value: 'total_asc', label: 'Lowest Value', icon: 'sort-numeric-ascending' },
      { value: 'status_asc', label: 'Status A-Z', icon: 'sort-variant' },
    ],
    defaultSort: 'date_desc',
    defaultFilter: 'all',
  },

  customers: {
    filters: [
      { value: 'all', label: 'All Customers' },
      { value: 'active', label: 'Active' },
      { value: 'inactive', label: 'Inactive' },
      { value: 'vip', label: 'VIP' },
      { value: 'new', label: 'New' },
    ],
    sortOptions: [
      { value: 'name_asc', label: 'Name A-Z', icon: 'sort-alphabetical-ascending' },
      { value: 'name_desc', label: 'Name Z-A', icon: 'sort-alphabetical-descending' },
      { value: 'totalSpent_desc', label: 'Highest Spent', icon: 'sort-numeric-descending' },
      { value: 'totalSpent_asc', label: 'Lowest Spent', icon: 'sort-numeric-ascending' },
      { value: 'totalOrders_desc', label: 'Most Orders', icon: 'sort-numeric-descending' },
      { value: 'createdAt_desc', label: 'Newest First', icon: 'sort-calendar-descending' },
      { value: 'lastOrderDate_desc', label: 'Recent Activity', icon: 'sort-clock-descending' },
    ],
    defaultSort: 'name_asc',
    defaultFilter: 'all',
  },

  products: {
    filters: [
      { value: 'all', label: 'All Products' },
      { value: 'shirts', label: 'Shirts' },
      { value: 'pants', label: 'Pants' },
      { value: 'suits', label: 'Suits' },
      { value: 'blazers', label: 'Blazers' },
      { value: 'traditional', label: 'Traditional' },
      { value: 'other', label: 'Other' },
    ],
    sortOptions: [
      { value: 'name_asc', label: 'Name A-Z', icon: 'sort-alphabetical-ascending' },
      { value: 'name_desc', label: 'Name Z-A', icon: 'sort-alphabetical-descending' },
      { value: 'price_desc', label: 'Highest Price', icon: 'sort-numeric-descending' },
      { value: 'price_asc', label: 'Lowest Price', icon: 'sort-numeric-ascending' },
      { value: 'stock_desc', label: 'Most Stock', icon: 'sort-numeric-descending' },
      { value: 'stock_asc', label: 'Least Stock', icon: 'sort-numeric-ascending' },
      { value: 'category_asc', label: 'Category A-Z', icon: 'sort-variant' },
    ],
    defaultSort: 'name_asc',
    defaultFilter: 'all',
  },

  fabrics: {
    filters: [
      { value: 'all', label: 'All Fabrics' },
      { value: 'cotton', label: 'Cotton' },
      { value: 'silk', label: 'Silk' },
      { value: 'wool', label: 'Wool' },
      { value: 'linen', label: 'Linen' },
      { value: 'polyester', label: 'Polyester' },
      { value: 'blend', label: 'Blend' },
    ],
    sortOptions: [
      { value: 'name_asc', label: 'Name A-Z', icon: 'sort-alphabetical-ascending' },
      { value: 'pricePerMeter_desc', label: 'Highest Price', icon: 'sort-numeric-descending' },
      { value: 'pricePerMeter_asc', label: 'Lowest Price', icon: 'sort-numeric-ascending' },
      { value: 'stock_desc', label: 'Most Stock', icon: 'sort-numeric-descending' },
      { value: 'stock_asc', label: 'Least Stock', icon: 'sort-numeric-ascending' },
      { value: 'type_asc', label: 'Type A-Z', icon: 'sort-variant' },
      { value: 'createdAt_desc', label: 'Newest First', icon: 'sort-calendar-descending' },
    ],
    defaultSort: 'name_asc',
    defaultFilter: 'all',
  },

  measurements: {
    filters: [
      { value: 'all', label: 'All Types' },
      { value: 'shirt', label: 'Shirt' },
      { value: 'pant', label: 'Pant' },
      { value: 'suit', label: 'Suit' },
      { value: 'dress', label: 'Dress' },
      { value: 'blazer', label: 'Blazer' },
      { value: 'kurta', label: 'Kurta' },
    ],
    sortOptions: [
      { value: 'createdAt_desc', label: 'Latest First', icon: 'sort-calendar-descending' },
      { value: 'createdAt_asc', label: 'Oldest First', icon: 'sort-calendar-ascending' },
      { value: 'customerName_asc', label: 'Customer A-Z', icon: 'sort-alphabetical-ascending' },
      { value: 'garmentType_asc', label: 'Garment Type', icon: 'sort-variant' },
    ],
    defaultSort: 'createdAt_desc',
    defaultFilter: 'all',
  },

  inventory: {
    filters: [
      { value: 'all', label: 'All Items' },
      { value: 'fabrics', label: 'Fabrics' },
      { value: 'products', label: 'Products' },
      { value: 'garments', label: 'Garments' },
      { value: 'consumables', label: 'Consumables' },
    ],
    sortOptions: [
      { value: 'name_asc', label: 'Name A-Z', icon: 'sort-alphabetical-ascending' },
      { value: 'category_asc', label: 'Category', icon: 'sort-variant' },
      { value: 'displayPrice_desc', label: 'Highest Price', icon: 'sort-numeric-descending' },
      { value: 'stock_desc', label: 'Most Stock', icon: 'sort-numeric-descending' },
      { value: 'stock_asc', label: 'Least Stock', icon: 'sort-numeric-ascending' },
      { value: 'createdAt_desc', label: 'Newest First', icon: 'sort-calendar-descending' },
    ],
    defaultSort: 'name_asc',
    defaultFilter: 'all',
  },
};

// Global sorting function
export const applySorting = (data, sortValue) => {
  if (!sortValue || !data?.length) return data;

  const [sortKey, direction] = sortValue.split('_');
  const isDesc = direction === 'desc';

  return [...data].sort((a, b) => {
    let aValue = a[sortKey];
    let bValue = b[sortKey];

    // Handle special cases
    if(sortKey === 'customerName') {
      aValue = a.customerName || a.customer || '';
      bValue = b.customerName || b.customer || '';
    }

    // Handle different data types
    if(typeof aValue === 'string') {
      aValue = aValue.toLowerCase();
      bValue = bValue.toLowerCase();
    }

    // Handle dates
    if (sortKey.includes('Date') || sortKey === 'date' || sortKey === 'createdAt') {
      aValue = new Date(aValue).getTime();
      bValue = new Date(bValue).getTime();
    }

    // Handle numbers
    if(sortKey === 'price' || sortKey === 'total' || sortKey === 'stock' || 
        sortKey === 'totalSpent' || sortKey === 'totalOrders' || sortKey === 'pricePerMeter') {
      aValue = parseFloat(aValue) || 0;
      bValue = parseFloat(bValue) || 0;
    }

    // Sort logic
    if(isDesc) {
      return aValue < bValue ? 1 : aValue > bValue ? -1 : 0;
    } else {
      return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
    }
  });
};

// Global filtering function
export const applyFiltering = (data, filterValue, dataType, searchQuery = '') => {
  if (!data?.length) return data;

  let filtered = [...data];

  // Apply search filter first
  if (searchQuery && typeof searchQuery === 'string' && searchQuery.trim()) {
    const query = searchQuery.toLowerCase();
    filtered = filtered.filter(item => {
      switch(dataType) {
        case 'orders':
          return (
            (item.customerName || item.customer || '').toLowerCase().includes(query) ||
            item.id?.toString().toLowerCase().includes(query) ||
            item.status?.toLowerCase().includes(query)
          );
        case 'customers':
          return (
            item.name?.toLowerCase().includes(query) ||
            item.email?.toLowerCase().includes(query) ||
            item.phone?.includes(query)
          );
        case 'products':
        case 'fabrics':
        case 'inventory':
          return (
            item.name?.toLowerCase().includes(query) ||
            item.category?.toLowerCase().includes(query) ||
            item.type?.toLowerCase().includes(query) ||
            item.color?.toLowerCase().includes(query)
          );
        case 'measurements':
          return (
            item.customerName?.toLowerCase().includes(query) ||
            item.garmentType?.toLowerCase().includes(query) ||
            item.notes?.toLowerCase().includes(query)
          );
        default:
          return true;
      }
    });
  }

  // Apply category/status filter
  if(filterValue && filterValue !== 'all') {
    filtered = filtered.filter(item => {
      switch(dataType) {
        case 'orders':
          return item.status?.toLowerCase() === filterValue.toLowerCase();
        case 'customers':
          if (filterValue === 'vip') return item.isVIP || item.totalSpent > 50000;
          if (filterValue === 'active') return item.isActive !== false;
          if (filterValue === 'inactive') return item.isActive === false;
          if (filterValue === 'new') return item.totalOrders === 0;
          return true;
        case 'products':
        case 'fabrics':
          return item.category?.toLowerCase() === filterValue.toLowerCase() ||
                 item.type?.toLowerCase() === filterValue.toLowerCase();
        case 'measurements':
          return item.garmentType?.toLowerCase() === filterValue.toLowerCase();
        case 'inventory':
          return item.category?.toLowerCase() === filterValue.toLowerCase();
        default:
          return true;
      }
    });
  }

  return filtered;
};

// Main hook
export const useGlobalSortFilter = (dataType, data = [], initialSearch = '') => {
  const config = GLOBAL_FILTER_CONFIGS[dataType];

  const [searchQuery, setSearchQuery] = useState(initialSearch || '');
  const [selectedFilter, setSelectedFilter] = useState(config?.defaultFilter || 'all');
  const [selectedSort, setSelectedSort] = useState(config?.defaultSort || 'name_asc');

  // Memoized filtered and sorted data
  const processedData = useMemo(() => {
    let result = applyFiltering(data, selectedFilter, dataType, searchQuery);
    result = applySorting(result, selectedSort);
    return result;
  }, [data, selectedFilter, selectedSort, searchQuery, dataType]);

  // Action handlers
  const handleFilterChange = useCallback((filter) => {
    setSelectedFilter(filter);
  }, []);

  const handleSortChange = useCallback((sort) => {
    setSelectedSort(sort);
  }, []);

  const handleSearchChange = useCallback((query) => {
    setSearchQuery(query);
  }, []);

  const resetFilters = useCallback(() => {
    setSelectedFilter(config?.defaultFilter || 'all');
    setSelectedSort(config?.defaultSort || 'name_asc');
    setSearchQuery('');
  }, [config]);

  return { // Data
    data: processedData,
    
    // Current state
    searchQuery,
    selectedFilter,
    selectedSort,
    
    // Configuration
    filters: config?.filters || [],
    sortOptions: config?.sortOptions || [],
    
    // Actions
    handleFilterChange,
    handleSortChange,
    handleSearchChange,
    resetFilters,
    
    // Utilities
    totalCount: data?.length || 0,
    filteredCount: processedData?.length || 0,
 };
};

export default useGlobalSortFilter;
