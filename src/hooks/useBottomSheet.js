/**
 * useBottomSheet - Custom hook for managing bottom sheet state and actions
 * Provides convenient methods for controlling bottom sheets
 */

import { useRef, useState, useCallback   } from 'react';

export const useBottomSheet = (initialIndex = -1) => {
  const bottomSheetRef = useRef(null);
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const [isOpen, setIsOpen] = useState(initialIndex > -1);

  // Handle sheet changes
  const handleSheetChange = useCallback((index) => {
    setCurrentIndex(index);
    setIsOpen(index > -1);
  }, []);

  // Expand to specific index
  const expand = useCallback((index = 0) => {
    bottomSheetRef.current?.expand(index);
  }, []);

  // Collapse to specific index
  const collapse = useCallback((index = 0) => {
    bottomSheetRef.current?.collapse(index);
  }, []);

  // Close the bottom sheet
  const close = useCallback(() => {
    bottomSheetRef.current?.close();
  }, []);

  // Snap to specific index
  const snapToIndex = useCallback((index) => {
    bottomSheetRef.current?.snapToIndex(index);
  }, []);

  // Snap to position (percentage)
  const snapToPosition = useCallback((position) => {
    bottomSheetRef.current?.snapToPosition(position);
  }, []);

  // Toggle between closed and first snap point
  const toggle = useCallback(() => {
    if(isOpen) {
      close();
    } else {
      expand(0);
    }
  }, [isOpen, close, expand]);

  // Force close (useful for cleanup)
  const forceClose = useCallback(() => {
    bottomSheetRef.current?.forceClose();
  }, []);

  return { // Ref to pass to BottomSheet component
    bottomSheetRef,
    
    // State
    currentIndex,
    isOpen,
    
    // Actions
    expand,
    collapse,
    close,
    snapToIndex,
    snapToPosition,
    toggle,
    forceClose,
    
    // Handler to pass to BottomSheet onChange
    handleSheetChange,
 };
};

// Hook for managing multiple bottom sheets
export const useMultipleBottomSheets = (sheetConfigs = []) => {
  const sheets = sheetConfigs.reduce((acc, config) => {
    acc[config.name] = useBottomSheet(config.initialIndex);
    return acc;
  }, {});

  // Close all sheets
  const closeAll = useCallback(() => {
    Object.values(sheets).forEach(sheet => sheet.close());
  }, [sheets]);

  // Get currently open sheets
  const getOpenSheets = useCallback(() => {
    return Object.entries(sheets)
      .filter(([_, sheet]) => sheet.isOpen)
      .map(([name, _]) => name);
  }, [sheets]);

  // Check if any sheet is open
  const hasOpenSheet = useCallback(() => {
    return Object.values(sheets).some(sheet => sheet.isOpen);
  }, [sheets]);

  return { sheets,
    closeAll,
    getOpenSheets,
    hasOpenSheet,
 };
};

// Hook for bottom sheet with form state
export const useBottomSheetForm = (initialIndex = -1, initialFormData = {}) => {
  const bottomSheet = useBottomSheet(initialIndex);
  const [formData, setFormData] = useState(initialFormData);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState({});

  // Update form field
  const updateField = useCallback((field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when field is updated
    if(errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  }, [errors]);

  // Reset form
  const resetForm = useCallback(() => {
    setFormData(initialFormData);
    setErrors({});
    setIsSubmitting(false);
  }, [initialFormData]);

  // Submit form
  const submitForm = useCallback(async (onSubmit, validation) => {
    setIsSubmitting(true);
    setErrors({});

    try {
      // Run validation if provided
      if(validation) {
        const validationErrors = validation(formData);
        if (Object.keys(validationErrors).length > 0) {
          setErrors(validationErrors);
          setIsSubmitting(false);
          return false;
        }
      }

      // Submit form
      await onSubmit(formData);
      
      // Close sheet and reset form on success
      bottomSheet.close();
      resetForm();
      
      return true;
    } catch (error) {
      console.error('Form submission error:', error);
      setErrors({ submit: error.message || 'Submission failed' });
      return false;
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, bottomSheet, resetForm]);

  // Close and reset
  const closeAndReset = useCallback(() => {
    bottomSheet.close();
    resetForm();
  }, [bottomSheet, resetForm]);

  return { ...bottomSheet,
    formData,
    isSubmitting,
    errors,
    updateField,
    resetForm,
    submitForm,
    closeAndReset,
 };
};

// Hook for bottom sheet with async data loading
export const useBottomSheetData = (initialIndex = -1, dataLoader) => {
  const bottomSheet = useBottomSheet(initialIndex);
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Load data when sheet opens
  const loadData = useCallback(async (params) => {
    if (!dataLoader) return;

    setLoading(true);
    setError(null);

    try {
      const result = await dataLoader(params);
      setData(result);
    } catch (err) {
      setError(err.message || 'Failed to load data');
      console.error('Data loading error:', err);
    } finally {
      setLoading(false);
    }
  }, [dataLoader]);

  // Expand and load data
  const expandWithData = useCallback(async (index = 0, params) => {
    bottomSheet.expand(index);
    await loadData(params);
  }, [bottomSheet, loadData]);

  // Refresh data
  const refreshData = useCallback(async (params) => {
    await loadData(params);
  }, [loadData]);

  // Close and clear data
  const closeAndClear = useCallback(() => {
    bottomSheet.close();
    setData(null);
    setError(null);
  }, [bottomSheet]);

  return { ...bottomSheet,
    data,
    loading,
    error,
    loadData,
    expandWithData,
    refreshData,
    closeAndClear,
 };
};

export default useBottomSheet;
