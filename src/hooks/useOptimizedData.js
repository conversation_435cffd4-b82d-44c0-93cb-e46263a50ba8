/**
 * Optimized Data Hook
 * React hook for accessing optimized data service with caching and lazy loading
 */

import { useState, useEffect, useCallback, useRef   } from 'react';
import optimizedDataService from '../services/OptimizedDataService';
import performanceService from '../services/PerformanceService';

/**
 * Hook for accessing optimized data with caching and performance monitoring
 */
export const useOptimizedData = (type, options = {}) => {
  const { autoFetch = true,
    useCache = true,
    transform = true,
    refreshInterval = null
   } = options;

  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);
  
  const unsubscribeRef = useRef(null);
  const refreshIntervalRef = useRef(null);

  // Subscribe to data changes
  useEffect(() => {
    const handleDataChange = useCallback((newData) => {
      setData(newData), []);
      setLastUpdated(Date.now());
      setError(null);
    };

    unsubscribeRef.current = optimizedDataService.subscribe(type, handleDataChange);

    return () => {
      if(unsubscribeRef.current) {
        unsubscribeRef.current();
      }
    };
  }, [type]);

  // Auto-fetch data on mount
  useEffect(() => {
    if(autoFetch) {
      fetchData();
    }
  }, [type, autoFetch]);

  // Set up refresh interval
  useEffect(() => {
    if(refreshInterval && refreshInterval > 0) {
      refreshIntervalRef.current = setInterval(() => {
        fetchData(true);
      }, refreshInterval);

      return () => {
        if(refreshIntervalRef.current) {
          clearInterval(refreshIntervalRef.current);
        }
      };
    }
  }, [refreshInterval]);

  // Fetch data function
  const fetchData = useCallback(async (forceRefresh = false) => {
    const timer = performanceService.startTimer(`fetch_${type} , 'data_fetch');
    
    try {
      setLoading(true);
      setError(null);

      const result = await optimizedDataService.getData(type, {
        forceRefresh,
        useCache,
        transform
      });

      setData(result);
      setLastUpdated(Date.now());
      
      timer.end();
      return result;
    } catch (err) {
      setError(err);
      console.error(`Failed to fetch ${type}:`, err);
      timer.end();
      throw err;
    } finally {
      setLoading(false);
    }
  }, [type, useCache, transform]);

  // Add item function
  const addItem = useCallback(async (item) => {
    const timer = performanceService.startTimer(`add_${type} , 'data_operation');
    
    try {
      const success = await optimizedDataService.setItem(type, item);
      timer.end();
      return success;
    } catch (err) {
      timer.end();
      throw err;
    }
  }, [type]);

  // Update item function
  const updateItem = useCallback(async (item) => {
    const timer = performanceService.startTimer(`update_${type} ,` 'data_operation');
    
    try {
      const success = await optimizedDataService.setItem(type, item);
      timer.end();
      return success;
    } catch (err) {
      timer.end();
      throw err;
    }
  }, [type]);

  // Remove item function
  const removeItem = useCallback(async (itemId) => {
    const timer = performanceService.startTimer(`remove_${type} ,` 'data_operation');
    
    try {
      const success = await optimizedDataService.removeItem(type, itemId);
      timer.end();
      return success;
    } catch (err) {
      timer.end();
      throw err;
    }
  }, [type]);

  // Refresh function
  const refresh = useCallback(() => {
    return fetchData(true);
  }, [fetchData]);

  return { data,
    loading,
    error,
    lastUpdated,
    fetchData,
    addItem,
    updateItem,
    removeItem,
    refresh
   };
};

/**
 * Hook for accessing multiple data types
 */
export const useMultipleData = (types, options = {}) => {
  const [dataMap, setDataMap] = useState(new Map());
  const [loadingMap, setLoadingMap] = useState(new Map());
  const [errorMap, setErrorMap] = useState(new Map());

  const updateDataMap = useCallback((type, data) => {
    setDataMap(prev => new Map(prev.set(type, data)));
  }, []);

  const updateLoadingMap = useCallback((type, loading) => {
    setLoadingMap(prev => new Map(prev.set(type, loading)));
  }, []);

  const updateErrorMap = useCallback((type, error) => {
    setErrorMap(prev => new Map(prev.set(type, error)));
  }, []);

  // Initialize maps
  useEffect(() => {
    types.forEach(type => {
      updateDataMap(type, []);
      updateLoadingMap(type, false);
      updateErrorMap(type, null);
    });
  }, [types]);

  // Fetch all data types
  const fetchAll = useCallback(async (forceRefresh = false) => {
    const promises = types.map(async (type) => {
      updateLoadingMap(type, true);
      updateErrorMap(type, null);

      try {
        const data = await optimizedDataService.getData(type, {
          forceRefresh,
          ...options
        });
        updateDataMap(type, data);
        return { type, data, success: true  };
      } catch (error) {
        updateErrorMap(type, error);
        return { type, error, success: false  };
      } finally {
        updateLoadingMap(type, false);
      }
    });

    return Promise.allSettled(promises);
  }, [types, options]);

  // Auto-fetch on mount
  useEffect(() => {
    if(options.autoFetch !== false) {
      fetchAll();
    }
  }, [fetchAll]);

  return { dataMap,
    loadingMap,
    errorMap,
    fetchAll,
    isLoading: Array.from(loadingMap.values()).some(Boolean),
    hasErrors: Array.from(errorMap.values()).some(Boolean)
   };
};

/**
 * Hook for paginated data
 */
export const usePaginatedData = (type, options = {}) => {
  const { pageSize = 20,
    autoFetch = true,
    ...otherOptions
   } = options;

  const [allData, setAllData] = useState([]);
  const [displayData, setDisplayData] = useState([]);
  const [currentPage, setCurrentPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(false);

  const { data, loading: dataLoading, error, fetchData  } = useOptimizedData(type, {
    autoFetch,
    ...otherOptions
  });

  // Update all data when data changes
  useEffect(() => {
    setAllData(data);
    setCurrentPage(0);
    setDisplayData(data.slice(0, pageSize));
    setHasMore(data.length > pageSize);
  }, [data, pageSize]);

  // Load more data
  const loadMore = useCallback(() => {
    if (loading || !hasMore) return;

    setLoading(true);
    
    setTimeout(() => {
      const nextPage = currentPage + 1;
      const startIndex = nextPage * pageSize;
      const endIndex = startIndex + pageSize;
      const newItems = allData.slice(startIndex, endIndex);
      
      setDisplayData(prev => [...prev, ...newItems]);
      setCurrentPage(nextPage);
      setHasMore(endIndex < allData.length);
      setLoading(false);
    }, 100); // Small delay to prevent rapid calls
  }, [allData, currentPage, pageSize, hasMore, loading]);

  // Reset pagination
  const reset = useCallback(() => {
    setCurrentPage(0);
    setDisplayData(allData.slice(0, pageSize));
    setHasMore(allData.length > pageSize);
  }, [allData, pageSize]);

  return { data: displayData,
    allData,
    loading: dataLoading || loading,
    error,
    hasMore,
    currentPage,
    totalPages: Math.ceil(allData.length / pageSize),
    loadMore,
    reset,
    refresh: fetchData
   };
};

/**
 * Hook for filtered data
 */
export const useFilteredData = (type, filterFunction, options = {}) => {
  const [filteredData, setFilteredData] = useState([]);
  const [filterLoading, setFilterLoading] = useState(false);

  const { data, loading, error, ...otherMethods  } = useOptimizedData(type, options);

  // Apply filter when data or filter function changes
  useEffect(() => {
    if(!filterFunction) {
      setFilteredData(data);
      return;
    }

    setFilterLoading(true);
    
    // Use setTimeout to prevent blocking UI
    setTimeout(() => {
      try {
        const filtered = data.filter(filterFunction);
        setFilteredData(filtered);
      } catch (err) {
        console.error('Filter function error:', err);
        setFilteredData(data);
      } finally {
        setFilterLoading(false);
      }
    }, 0);
  }, [data, filterFunction]);

  return { data: filteredData,
    originalData: data,
    loading: loading || filterLoading,
    error,
    ...otherMethods
   };
};

/**
 * Hook for sorted data
 */
export const useSortedData = (type, sortFunction, options = {}) => {
  const [sortedData, setSortedData] = useState([]);
  const [sortLoading, setSortLoading] = useState(false);

  const { data, loading, error, ...otherMethods  } = useOptimizedData(type, options);

  // Apply sort when data or sort function changes
  useEffect(() => {
    if(!sortFunction) {
      setSortedData(data);
      return;
    }

    setSortLoading(true);
    
    // Use setTimeout to prevent blocking UI
    setTimeout(() => {
      try {
        const sorted = [...data].sort(sortFunction);
        setSortedData(sorted);
      } catch (err) {
        console.error('Sort function error:', err);
        setSortedData(data);
      } finally {
        setSortLoading(false);
      }
    }, 0);
  }, [data, sortFunction]);

  return { data: sortedData,
    originalData: data,
    loading: loading || sortLoading,
    error,
    ...otherMethods
   };
};

/**
 * Hook for cached search results
 */
export const useCachedSearch = (searchFunction, dependencies = []) => {
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  const cacheRef = useRef(new Map());
  const lastSearchRef = useRef('');

  const search = useCallback(async (query) => {
    if (!query || query.trim().length === 0) {
      setResults([]);
      return;
    }

    const cacheKey = query.toLowerCase().trim();
    
    // Check cache first
    if (cacheRef.current.has(cacheKey)) {
      setResults(cacheRef.current.get(cacheKey));
      return;
    }

    if(lastSearchRef.current === cacheKey) {
      return; // Prevent duplicate searches
    }

    lastSearchRef.current = cacheKey;
    setLoading(true);
    setError(null);

    const timer = performanceService.startTimer('search', 'search');

    try {
      const searchResults = await searchFunction(query);
      
      // Cache results
      cacheRef.current.set(cacheKey, searchResults);
      
      // Limit cache size
      if(cacheRef.current.size > 50) {
        const firstKey = cacheRef.current.keys().next().value;
        cacheRef.current.delete(firstKey);
      }
      
      setResults(searchResults);
      timer.end();
    } catch (err) {
      setError(err);
      console.error('Search error:', err);
      timer.end();
    } finally {
      setLoading(false);
      lastSearchRef.current = '';
    }
  }, [searchFunction, ...dependencies]);

  const clearCache = useCallback(() => {
    cacheRef.current.clear();
  }, []);

  return { results,
    loading,
    error,
    search,
    clearCache
   };
};

export default useOptimizedData;
