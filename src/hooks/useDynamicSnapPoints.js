import { useMemo, useState } from 'react';
import { Dimensions } from 'react-native';

const { height: screenHeight } = Dimensions.get('window');

/**
 * Google Maps style bottom sheet hook
 * Implements the exact behavior of Google Maps bottom sheets:
 * 1. Small peek height initially
 * 2. Drag to expand to show more content
 * 3. Scrolling only starts after reaching maximum expansion
 * 4. Smooth transitions between states
 */
export const useGoogleMapsBottomSheet = ({
  peekHeight = 120,        // Initial peek height (like Google Maps)
  headerHeight = 80,       // Header section height
  footerHeight = 0,        // Footer section height (if any)
  contentHeight = 0,       // Estimated content height
  maxHeight = 90,          // Maximum height percentage
  enableScrolling = true,  // Whether to enable scrolling
}) => {
  const [currentIndex, setCurrentIndex] = useState(-1);

  return useMemo(() => {
    // Calculate the optimal content height
    const totalContentHeight = headerHeight + contentHeight + footerHeight;

    // Convert to screen percentages
    const peekPercentage = Math.min((peekHeight / screenHeight) * 100, 25);
    const contentPercentage = Math.min((totalContentHeight / screenHeight) * 100, maxHeight);

    // Google Maps style snap points:
    // 1. Peek height (shows preview)
    // 2. Content height (shows most/all content)
    // 3. Full height (if content is very large)
    let snapPoints = [];

    if (contentPercentage <= 50) {
      // Small to medium content: peek + optimal height
      snapPoints = [
        `${Math.max(peekPercentage, 15)}%`,  // Peek
        `${Math.min(contentPercentage + 10, maxHeight)}%`  // Optimal
      ];
    } else {
      // Large content: peek + medium + full
      snapPoints = [
        `${Math.max(peekPercentage, 15)}%`,  // Peek
        `${Math.min(60, maxHeight - 15)}%`,  // Medium
        `${maxHeight}%`                      // Full
      ];
    }

    // Determine when scrolling should be enabled
    const shouldEnableScrolling = enableScrolling && (contentPercentage > maxHeight - 10);

    return {
      snapPoints,
      enableScrolling: shouldEnableScrolling,
      peekHeight,
      contentHeight: totalContentHeight,
      currentIndex,
      setCurrentIndex,
      // Google Maps style behavior flags
      enablePanDownToClose: true,
      enableOverDrag: false,
      enableHandlePanningGesture: true,
    };
  }, [
    peekHeight,
    headerHeight,
    footerHeight,
    contentHeight,
    maxHeight,
    enableScrolling,
    currentIndex,
  ]);
};

/**
 * Enhanced hook that provides Google Maps behavior with content-aware calculations
 */
export const useDynamicSnapPoints = ({
  headerHeight = 80,
  footerHeight = 0,
  contentHeight = 0,
  minHeight = 15,
  maxHeight = 90,
  enableScrolling = true,
  padding = 40,
  itemCount = 0,
  itemHeight = 60,
  peekHeight = 120,
  initialSnapIndex = 0, // Which snap point to open initially
}) => {
  // Calculate content height if not provided
  const calculatedContentHeight = contentHeight || (itemCount * itemHeight);

  // Use Google Maps style behavior
  const result = useGoogleMapsBottomSheet({
    peekHeight,
    headerHeight,
    footerHeight,
    contentHeight: calculatedContentHeight + padding,
    maxHeight,
    enableScrolling,
  });

  // Determine optimal initial snap index based on content
  const optimalInitialIndex = useMemo(() => {
    if (initialSnapIndex !== undefined) return initialSnapIndex;

    // For small content, open at optimal size (last snap point)
    if (result.snapPoints.length === 1) return 0;

    // For medium content, open at peek first
    if (result.snapPoints.length === 2) return 0;

    // For large content, open at peek first
    return 0;
  }, [initialSnapIndex, result.snapPoints.length]);

  return {
    ...result,
    initialSnapIndex: optimalInitialIndex,
  };
};

/**
 * Google Maps style configurations for different bottom sheet types
 */
export const BOTTOM_SHEET_CONFIGS = {
  // Financial reports with charts and tables (Google Maps style)
  FINANCIAL_REPORT: {
    peekHeight: 140,      // Show title + summary preview
    headerHeight: 80,
    footerHeight: 0,
    maxHeight: 90,
    enableScrolling: true,
    padding: 60,
  },

  // Forms with inputs and buttons (Google Maps style)
  FORM: {
    peekHeight: 120,      // Show title + first input preview
    headerHeight: 80,
    footerHeight: 80,     // Space for action buttons
    maxHeight: 85,
    enableScrolling: true,
    padding: 40,
  },

  // Simple content display (Google Maps style)
  CONTENT: {
    peekHeight: 100,      // Minimal peek
    headerHeight: 80,
    footerHeight: 0,
    maxHeight: 75,
    enableScrolling: true,
    padding: 40,
  },

  // Quick actions or menu (Google Maps style)
  ACTIONS: {
    peekHeight: 80,       // Very small peek
    headerHeight: 60,
    footerHeight: 0,
    maxHeight: 60,        // Actions don't need full screen
    enableScrolling: false,
    padding: 20,
  },

  // Customer/product details (Google Maps style)
  DETAILS: {
    peekHeight: 130,      // Show name + key info preview
    headerHeight: 80,
    footerHeight: 0,
    maxHeight: 80,
    enableScrolling: true,
    padding: 50,
  },
};

/**
 * Helper function to estimate content height based on data
 */
export const estimateContentHeight = (data, type = 'default') => {
  if (!data) return 200;

  switch (type) {
    case 'financial_report':
      // Estimate based on number of sections, charts, and tables
      const sections = 3; // Summary, charts, tables
      const charts = 2; // Pie chart, bar chart
      const tableRows = Math.min(data.length || 10, 15);
      return (sections * 100) + (charts * 250) + (tableRows * 50) + 100;

    case 'form':
      // Estimate based on number of form fields
      const fields = Object.keys(data).length || 5;
      return fields * 80 + 100;

    case 'list':
      // Estimate based on number of list items
      const items = Array.isArray(data) ? data.length : 5;
      return Math.min(items * 60, 800) + 50;

    case 'details':
      // Estimate based on number of detail sections
      const detailSections = Object.keys(data).length || 4;
      return detailSections * 80 + 150;

    default:
      return 300;
  }
};
