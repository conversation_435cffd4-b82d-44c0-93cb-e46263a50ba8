import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, ActivityIndicator, StyleSheet } from 'react-native';

/**
 * Custom hook for lazy loading components with loading states
 * @param {Function} importFunction - Dynamic import function
 * @param {Object} options - Configuration options
 * @returns {Object} - { Component, loading, error }
 */
export const useLazyLoading = (importFunction, options = {}) => {
  const [Component, setComponent] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  const {
    delay = 0,
    timeout = 10000,
    retries = 3,
    onLoad,
    onError
  } = options;
  
  useEffect(() => {
    let timeoutId;
    let retryCount = 0;
    
    const loadComponent = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Add artificial delay if specified
        if (delay > 0) {
          await new Promise(resolve => setTimeout(resolve, delay));
        }
        
        // Set timeout for loading
        const timeoutPromise = new Promise((_, reject) => {
          timeoutId = setTimeout(() => reject(new Error('Loading timeout')), timeout);
        });
        
        const componentModule = await Promise.race([
          importFunction(),
          timeoutPromise
        ]);
        
        clearTimeout(timeoutId);
        
        const LoadedComponent = componentModule.default || componentModule;
        setComponent(() => LoadedComponent);
        setLoading(false);
        
        if (onLoad) onLoad(LoadedComponent);
        
      } catch (err) {
        clearTimeout(timeoutId);
        
        if (retryCount < retries) {
          retryCount++;
          console.warn('Lazy loading failed, retrying (' + retryCount + '/' + retries + '):', err);
          setTimeout(loadComponent, 1000 * retryCount); // Exponential backoff
        } else {
          setError(err);
          setLoading(false);
          if (onError) onError(err);
        }
      }
    };
    
    loadComponent();
    
    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [importFunction, delay, timeout, retries, onLoad, onError]);
  
  return { Component, loading, error };
};

/**
 * Higher-order component for lazy loading
 * @param {Function} importFunction - Dynamic import function
 * @param {Object} options - Configuration options
 * @returns {React.Component} - Lazy loaded component
 */
export const withLazyLoading = (importFunction, options = {}) => {
  return (props) => {
    const { Component, loading, error } = useLazyLoading(importFunction, options);
    
    if (error) {
      const ErrorComponent = options.ErrorComponent || DefaultErrorComponent;
      return <ErrorComponent error={error} retry={() => window.location.reload()} />;
    }
    
    if (loading || !Component) {
      const LoadingComponent = options.LoadingComponent || DefaultLoadingComponent;
      return <LoadingComponent />;
    }
    
    return <Component {...props} />;
  };
};

// Default components
const DefaultLoadingComponent = () => {
  return (
    <View style={styles.loadingContainer}>
      <ActivityIndicator size="large" color="#007AFF" />
    </View>
  );
};

const DefaultErrorComponent = ({ error, retry }) => {
  return (
    <View style={styles.errorContainer}>
      <Text style={styles.errorTitle}>Failed to load</Text>
      <Text style={styles.errorMessage}>{error.message}</Text>
      <TouchableOpacity style={styles.retryButton} onPress={retry}>
        <Text style={styles.retryButtonText}>Retry</Text>
      </TouchableOpacity>
    </View>
  );
};


const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorTitle: {
    fontSize: 18,
    marginBottom: 10,
    textAlign: 'center',
  },
  errorMessage: {
    fontSize: 14,
    color: '#666',
    marginBottom: 20,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: '#007AFF',
    padding: 10,
    borderRadius: 5,
  },
  retryButtonText: {
    color: 'white',
  },
});
