/**
 * useUnifiedNavigation - Unified navigation hook
 * Provides all navigation methods and state in one convenient hook
 */

import { useCallback   } from 'react';
import { useNavigationContext   } from '../context/NavigationContext';
import navigationService from '../services/NavigationService';

export const useUnifiedNavigation = () => {
  const rnNavigation = useRNNavigation();
  const navigationContext = useNavigationContext();

  // Core navigation methods
  const navigate = useCallback((name, params) => {
    navigationService.navigate(name, params);
    navigationContext.actions.setCurrentRoute(name);
  }, [navigationContext.actions]);

  const goBack = useCallback(() => {
    navigationService.goBack();
  }, []);

  const reset = useCallback((state) => {
    navigationService.reset(state);
    navigationContext.actions.resetNavigation();
  }, [navigationContext.actions]);

  // Tab navigation
  const navigateToTab = useCallback((tabName) => {
    navigationContext.actions.navigateToTab(tabName);
  }, [navigationContext.actions]);

  const switchTab = useCallback((tabName) => {
    navigateToTab(tabName);
  }, [navigateToTab]);

  // Modal navigation
  const openModal = useCallback((modalName, params) => {
    navigationContext.actions.openModal(modalName, params);
  }, [navigationContext.actions]);

  const closeModal = useCallback(() => {
    navigationContext.actions.closeModal();
  }, [navigationContext.actions]);

  // Settings navigation
  const openSettings = useCallback((settingsScreen) => {
    navigationContext.actions.openSettings(settingsScreen);
  }, [navigationContext.actions]);

  const openProfile = useCallback(() => {
    openSettings('Profile');
  }, [openSettings]);



  // Quick actions
  const openQuickActions = useCallback(() => {
    navigationContext.actions.toggleQuickActions(true);
  }, [navigationContext.actions]);

  const closeQuickActions = useCallback(() => {
    navigationContext.actions.toggleQuickActions(false);
  }, [navigationContext.actions]);

  const executeQuickAction = useCallback((action, params) => {
    navigationContext.actions.openQuickAction(action, params);
  }, [navigationContext.actions]);

  // Search functionality
  const activateSearch = useCallback(() => {
    navigationContext.actions.setSearchActive(true);
  }, [navigationContext.actions]);

  const deactivateSearch = useCallback(() => {
    navigationContext.actions.setSearchActive(false);
  }, [navigationContext.actions]);

  const updateSearchQuery = useCallback((query) => {
    navigationContext.actions.setSearchQuery(query);
  }, [navigationContext.actions]);

  // Notifications
  const openNotifications = useCallback(() => {
    navigationContext.actions.toggleNotifications(true);
  }, [navigationContext.actions]);

  const closeNotifications = useCallback(() => {
    navigationContext.actions.toggleNotifications(false);
  }, [navigationContext.actions]);

  // Profile menu
  const openProfileMenu = useCallback(() => {
    navigationContext.actions.toggleProfile(true);
  }, [navigationContext.actions]);

  const closeProfileMenu = useCallback(() => {
    navigationContext.actions.toggleProfile(false);
  }, [navigationContext.actions]);

  // Specific screen navigation
  const goToDashboard = useCallback(() => {
    navigateToTab('Dashboard');
  }, [navigateToTab]);

  const goToScan = useCallback(() => {
    navigateToTab('Scan');
  }, [navigateToTab]);

  const goToOrders = useCallback(() => {
    navigateToTab('Orders');
  }, [navigateToTab]);

  const goToProducts = useCallback((mode = 'view', productId = null) => {
    openModal('Products', { mode, productId });
  }, [openModal]);

  // Utility methods
  const getCurrentRoute = useCallback(() => {
    return navigationService.getCurrentRouteName();
  }, []);

  const getCurrentTab = useCallback(() => {
    return navigationContext.currentTab;
  }, [navigationContext.currentTab]);

  const isCurrentRoute = useCallback((routeName) => {
    return navigationContext.currentRoute === routeName;
  }, [navigationContext.currentRoute]);

  const isCurrentTab = useCallback((tabName) => {
    return navigationContext.currentTab === tabName;
  }, [navigationContext.currentTab]);

  const canGoBack = useCallback(() => {
    return rnNavigation.canGoBack();
  }, [rnNavigation]);

  const getNavigationHistory = useCallback(() => {
    return navigationService.getRouteHistory();
  }, []);

  const getBreadcrumbs = useCallback(() => {
    return navigationContext.breadcrumbs;
  }, [navigationContext.breadcrumbs]);

  // Analytics
  const getNavigationAnalytics = useCallback(() => {
    return navigationService.getNavigationAnalytics();
  }, []);

  // Deep linking
  const buildDeepLink = useCallback((routeName, params) => {
    return navigationService.buildDeepLink(routeName, params);
  }, []);

  // State getters
  const isModalOpen = navigationContext.isModalOpen;
  const isQuickActionsVisible = navigationContext.quickActionsVisible;
  const isSearchActive = navigationContext.searchActive;
  const searchQuery = navigationContext.searchQuery;
  const isNotificationsVisible = navigationContext.notifications.visible;
  const notificationCount = navigationContext.notifications.count;
  const isProfileMenuVisible = navigationContext.user.profileVisible;
  const isNavigationReady = navigationContext.navigationReady;

  return { // Core navigation
    navigate,
    goBack,
    reset,

    // Tab navigation
    navigateToTab,
    switchTab,
    goToDashboard,
    goToScan,
    goToOrders,

    // Modal navigation
    openModal,
    closeModal,
    goToProducts,

    // Settings navigation
    openSettings,
    openProfile,

    // Quick actions
    openQuickActions,
    closeQuickActions,
    executeQuickAction,

    // Search
    activateSearch,
    deactivateSearch,
    updateSearchQuery,

    // Notifications
    openNotifications,
    closeNotifications,

    // Profile menu
    openProfileMenu,
    closeProfileMenu,

    // Utilities
    getCurrentRoute,
    getCurrentTab,
    isCurrentRoute,
    isCurrentTab,
    canGoBack,
    getNavigationHistory,
    getBreadcrumbs,
    getNavigationAnalytics,
    buildDeepLink,

    // State
    currentRoute: navigationContext.currentRoute,
    currentTab: navigationContext.currentTab,
    isModalOpen,
    isQuickActionsVisible,
    isSearchActive,
    searchQuery,
    isNotificationsVisible,
    notificationCount,
    isProfileMenuVisible,
    isNavigationReady,
    breadcrumbs: navigationContext.breadcrumbs,

    // Raw navigation objects (for advanced use)
    rnNavigation,
    navigationContext,
    navigationService,
 };
};

export default React.memo(useUnifiedNavigation);
