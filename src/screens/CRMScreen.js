/**
 * CRMScreen - Customer Relationship Management
 * Advanced customer analytics and relationship tracking
 */

import React, { useState, useRef, useCallback, useMemo } from 'react';
import { FlatList, View, StyleSheet, Alert, RefreshControl, TouchableOpacity, ScrollView } from 'react-native';
import { Text } from 'react-native-paper';
import { Surface } from 'react-native-paper';
import { IconButton } from 'react-native-paper';
import { Menu } from 'react-native-paper';
import { Chip } from 'react-native-paper';
import { FAB } from 'react-native-paper';
import { Searchbar } from 'react-native-paper';
import { Card } from 'react-native-paper';
import LocalIcon, { NavigationLocalIcon, ActionLocalIcon, BusinessLocalIcon, SettingsLocalIcon, StatusLocalIcon } from '../components/LocalIcon';
import { useData } from '../context/DataContext';
import { useTheme } from '../context/ThemeContext';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import UnifiedFilterChips from '../components/UnifiedFilterChips';
import UnifiedEmptyState from '../components/UnifiedEmptyState';
import UnifiedSortMenu from '../components/UnifiedSortMenu';
import StatCardGroup from '../components/StatCardGroup';
import GlobalBadge from '../components/GlobalBadge';
import navigationService from '../services/NavigationService';
import { SPACING, BORDER_RADIUS } from '../theme/designTokens';

const CRMScreen = ({ navigation, route }) => {
  const { theme } = useTheme();
  const { state, actions } = useData();
  const insets = useSafeAreaInsets();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('All');
  const [menuVisible, setMenuVisible] = useState({});
  const [refreshing, setRefreshing] = useState(false);
  const [selectedSort, setSelectedSort] = useState({ key: 'name', direction: 'asc' });

  // Handle route params for customer selection mode
  const selectMode = route?.params?.selectMode || false;
  const onCustomerSelect = useCallback(route?.params?.onCustomerSelect, []);

  const filters = ['All', 'Active', 'New', 'VIP', 'High Value'];

  const sortOptions = [
    { key: 'name', label: 'Name' },
    { key: 'totalSpent', label: 'Total Spent' },
    { key: 'totalOrders', label: 'Total Orders' },
    { key: 'createdAt', label: 'Date Added' },
    { key: 'lastOrderDate', label: 'Last Order' },
  ];

  // Enhanced filtering for CRM
  const filteredCustomers = useMemo(() => {
    let filtered = state.customers || [];

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(customer =>
        customer.name?.toLowerCase().includes(query) ||
        customer.email?.toLowerCase().includes(query) ||
        customer.phone?.includes(query)
      );
    }

    // Apply category filter
    if (selectedFilter !== 'All') {
      filtered = filtered.filter(customer => {
        switch (selectedFilter) {
          case 'Active':
            return customer.isActive !== false;
          case 'New':
            const oneMonthAgo = new Date();
            oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
            return new Date(customer.createdAt) > oneMonthAgo;
          case 'VIP':
            return customer.isVIP || customer.totalSpent > 5000;
          case 'High Value':
            return customer.totalSpent > 2000;
          default:
            return true;
        }
      });
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue, bValue;

      switch (selectedSort.key) {
        case 'name':
          aValue = a.name?.toLowerCase() || '';
          bValue = b.name?.toLowerCase() || '';
          break;
        case 'totalSpent':
          aValue = a.totalSpent || 0;
          bValue = b.totalSpent || 0;
          break;
        case 'totalOrders':
          aValue = a.totalOrders || 0;
          bValue = b.totalOrders || 0;
          break;
        case 'createdAt':
          aValue = new Date(a.createdAt || 0);
          bValue = new Date(b.createdAt || 0);
          break;
        case 'lastOrderDate':
          const aOrders = (state.orders || []).filter(order => order.customerId === a.id);
          const bOrders = (state.orders || []).filter(order => order.customerId === b.id);
          aValue = aOrders.length > 0 ? new Date(Math.max(...aOrders.map(o => new Date(o.createdAt)))) : new Date(0);
          bValue = bOrders.length > 0 ? new Date(Math.max(...bOrders.map(o => new Date(o.createdAt)))) : new Date(0);
          break;
        default:
          aValue = a.name?.toLowerCase() || '';
          bValue = b.name?.toLowerCase() || '';
      }

      if (selectedSort.direction === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    return filtered;
  }, [state.customers, state.orders, searchQuery, selectedFilter, selectedSort]);

  // CRM Statistics
  const crmStats = useMemo(() => {
    const customers = state.customers || [];
    const orders = state.orders || [];
    
    const totalCustomers = customers.length;
    const activeCustomers = customers.filter(c => c.isActive !== false).length;
    const vipCustomers = customers.filter(c => c.isVIP || c.totalSpent > 5000).length;
    const totalRevenue = customers.reduce((sum, c) => sum + (c.totalSpent || 0), 0);
    const avgOrderValue = totalRevenue / Math.max(orders.length, 1);

    return [
      {
        title: 'Total Customers',
        value: totalCustomers.toString(),
        icon: 'account-group',
        color: theme.colors.primary,
        trend: '+12%'
      },
      {
        title: 'Active Customers',
        value: activeCustomers.toString(),
        icon: 'account-check',
        color: theme.colors.secondary,
        trend: '+8%'
      },
      {
        title: 'VIP Customers',
        value: vipCustomers.toString(),
        icon: 'crown',
        color: theme.colors.tertiary,
        trend: '+15%'
      },
      {
        title: 'Avg Order Value',
        value: `৳${avgOrderValue.toFixed(0)}`,
        icon: 'currency-bdt',
        color: theme.colors.primary,
        trend: '+5%'
      }
    ];
  }, [state.customers, state.orders, theme.colors]);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await actions.loadData();
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setRefreshing(false);
    }
  }, [actions]);

  const handleEditCustomer = useCallback((customer) => {
    if (selectMode && onCustomerSelect) {
      onCustomerSelect(customer);
      return;
    }

    try {
      navigationService.navigate('AddCustomer', { customer });
    } catch (error) {
      console.error('Failed to navigate to edit customer:', error);
    }
  }, [selectMode, onCustomerSelect]);

  const handleDeleteCustomer = useCallback((customer) => {
    Alert.alert(
      'Delete Customer',
      `Are you sure you want to delete "${customer.name}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => actions.deleteCustomer(customer.id),
        },
      ]
    );
  }, [actions]);

  const handleViewOrders = useCallback((customer) => {
    try {
      navigationService.navigate('Orders', {
        filterByCustomer: customer.id,
        customerName: customer.name
      });
    } catch (error) {
      console.error('Failed to navigate to customer orders:', error);
    }
  }, []);

  const handleViewDetails = useCallback((customer) => {
    try {
      navigationService.navigate('CustomerDetails', { customer });
    } catch (error) {
      console.error('Failed to navigate to customer details:', error);
    }
  }, []);

  const toggleMenu = (customerId) => {
    setMenuVisible(prev => ({
      ...prev,
      [customerId]: !prev[customerId]
    }));
  };

  const closeMenu = (customerId) => {
    setMenuVisible(prev => ({
      ...prev,
      [customerId]: false
    }));
  };

  const getCustomerInitials = (name) => {
    return name.split(' ').map(word => word[0]).join('').substring(0, 2).toUpperCase();
  };

  const getCustomerStatus = (customer) => {
    const orders = (state.orders || []).filter(order => 
      order.customerId === customer.id || order.customerName === customer.name
    );
    
    if (customer.isVIP || customer.totalSpent > 5000) {
      return { status: 'VIP', color: theme.colors.tertiary };
    } else if (orders.length === 0) {
      return { status: 'New', color: theme.colors.primary };
    } else if (customer.isActive === false) {
      return { status: 'Inactive', color: theme.colors.outline };
    } else {
      return { status: 'Active', color: theme.colors.secondary };
    }
  };

  const renderCustomerCard = useCallback(({ item: customer }) => {
    const statusInfo = getCustomerStatus(customer);
    const orders = (state.orders || []).filter(order =>
      order.customerId === customer.id || order.customerName === customer.name
    );
    const lastOrder = orders.length > 0 ? orders.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))[0] : null;

    return (
      <Surface style={styles.customerCard} elevation={1}>
        <TouchableOpacity onPress={() => handleViewDetails(customer)}>
          <View style={styles.customerContent}>
            <View style={styles.customerHeader}>
              <View style={styles.customerInfo}>
                <View style={styles.customerNameRow}>
                  <Text variant="titleMedium" style={styles.customerName}>
                    {customer.name}
                  </Text>
                  <GlobalBadge
                    variant={statusInfo.status === 'VIP' ? 'success' : statusInfo.status === 'New' ? 'info' : statusInfo.status === 'Inactive' ? 'error' : 'secondary'}
                    size="medium"
                  >
                    {statusInfo.status}
                  </GlobalBadge>
                </View>

                <Text variant="bodySmall" style={styles.customerContact}>
                  {customer.phone}
                </Text>
                <Text variant="bodySmall" style={styles.customerContact}>
                  {customer.email}
                </Text>

                <View style={styles.customerBottomRow}>
                  <View style={styles.customerLeftInfo}>
                    <View style={styles.customerPriceAndBadge}>
                      <Text variant="titleSmall" style={styles.customerPrice}>
                        ৳{customer.totalSpent || 0}
                      </Text>
                      {lastOrder && (
                        <Text variant="bodySmall" style={styles.customerLastOrder}>
                          Last: {lastOrder.garmentType || 'Order'} • {new Date(lastOrder.createdAt).toLocaleDateString()}
                        </Text>
                      )}
                    </View>
                    <Text variant="bodySmall" style={styles.customerOrders}>
                      {customer.totalOrders || 0} orders
                    </Text>
                  </View>
                </View>
              </View>
            </View>

            {/* Measurement History Timeline */}
            {customer.measurements && customer.measurements.length > 0 && (
              <View style={styles.measurementTimeline}>
                <Text variant="bodySmall" style={styles.customerMeasurements}>
                  📏 {customer.measurements.length} measurement{customer.measurements.length > 1 ? 's' : ''} • Last: {new Date(customer.measurements[0]?.createdAt || customer.measurements[0]?.measurementDate).toLocaleDateString()}
                </Text>
              </View>
            )}
          </View>
        </TouchableOpacity>
      </Surface>
    );
  }, [theme, handleEditCustomer, handleDeleteCustomer, handleViewOrders, handleViewDetails, menuVisible, toggleMenu, closeMenu, state.orders]);

  return (
    <View style={styles.style7}>
      {/* Search Bar */}
      <Surface
        style={styles.style6}
        elevation={2}
      >
        <Searchbar
          placeholder="Search customers by name, email, or phone"
          value={searchQuery}
          onChangeText={setSearchQuery}
          style={styles.searchBar}
          inputStyle={styles.searchInput}
          iconColor={theme.colors.onSurfaceVariant}
          placeholderTextColor={theme.colors.onSurfaceVariant}
          elevation={0}
        />
      </Surface>

      {/* Filter and Sort Controls */}
      <View style={styles.style5}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filtersContainer}>
          {filters.map((filter) => (
            <Chip
              key={filter}
              mode={selectedFilter === filter ? 'filled' : 'outlined'}
              onPress={() => setSelectedFilter(filter)}
              style={styles.filterChip}
              textStyle={{
                color: selectedFilter === filter ? theme.colors.onPrimary : theme.colors.onSurface,
              }}
            >
              {filter}
            </Chip>
          ))}
        </ScrollView>

        <Menu
          visible={menuVisible.sort}
          onDismiss={() => setMenuVisible({ ...menuVisible, sort: false })}
          anchor={
            <IconButton
              icon="sort"
              mode="outlined"
              onPress={() => setMenuVisible({ ...menuVisible, sort: true })}
              style={styles.sortButton}
            />
          }
        >
          {sortOptions.map((option) => (
            <Menu.Item
              key={option.key}
              onPress={() => {
                setSelectedSort(prev => ({
                  key: option.key,
                  direction: prev.key === option.key && prev.direction === 'asc' ? 'desc' : 'asc'
                }));
                setMenuVisible({ ...menuVisible, sort: false });
              }}
              title={`${option.label} ${selectedSort.key === option.key ? (selectedSort.direction === 'asc' ? '↑' : '↓') : ''}`}
            />
          ))}
        </Menu>
      </View>

      <FlatList
        data={filteredCustomers}
        renderItem={renderCustomerCard}
        keyExtractor={(item) => item.id?.toString() || item.name}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
        ListHeaderComponent={
          <View>
            {/* CRM Statistics */}
            <StatCardGroup
              stats={crmStats}
              columns={2}
              style={styles.style2}
            />

          </View>
        }
        ListEmptyComponent={
          <UnifiedEmptyState
            icon="account-group"
            title="No Customers Found"
            subtitle={searchQuery ? "Try adjusting your search terms" : "Start by adding your first customer"}
            actionLabel="Add Customer"
            onActionPress={() => navigationService.navigate('AddCustomer')}
          />
        }
      />

      {/* Add New Customer FAB */}
      <FAB
        icon="account-plus"
        label="Add Customer"
        onPress={() => navigation.navigate('AddCustomer')}
        style={styles.style1}
      />
    </View>
  );
};

export default CRMScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  searchBar: {
    backgroundColor: 'rgba(0,0,0,0.05)',
    borderRadius: 12,
  },
  searchInput: {
    fontSize: 16,
  },
  fab: {
    position: 'absolute',
    right: 16,
    borderRadius: 16,
  },
  listContainer: {
    padding: 16,
    paddingBottom: 100,
  },
  controlsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 8,
    gap: 8,
  },
  filtersContainer: {
    flex: 1,
  },
  filterChip: {
    marginRight: 4,
  },
  sortButton: {
    margin: 0,
  },
  customerCard: {
    marginBottom: 8,
    borderRadius: 16,
    overflow: 'hidden',
  },
  customerContent: {
    padding: 16,
  },
  customerHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  customerInfo: {
    flex: 1,
    marginLeft: 16,
  },
  customerNameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  customerPriceAndBadge: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  customerMetrics: {
    marginTop: 4,
  },
  measurementTimeline: {
    marginTop: 4,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    flex: 1,
    borderRadius: 8,
  },
  statusBadge: {
    paddingHorizontal: 4,
    paddingVertical: 1,
    borderRadius: 12,
    borderWidth: 1,
    alignSelf: 'flex-start',
  },
  statusBadgeText: {
    fontSize: 8,
    fontWeight: '600',
    textTransform: 'uppercase',
  },
  customerBottomRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
  },
  customerLeftInfo: {
    flex: 1,
  },
  style1: {
    position: 'absolute',
    right: 16,
    bottom: 32,
  },
  style2: {
    marginBottom: 16,
  },
  style3: {
    margin: 0,
  },
  style4: {
    marginRight: 4,
  },
  style5: {
    flex: 1,
  },
  style6: {
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  style7: {
    flex: 1,
  },
  style8: {
    fontWeight: '500',
  },
  style9: {
    marginTop: 2,
  },
  customerName: {
    fontWeight: '600',
  },
  customerContact: {
    opacity: 0.7,
  },
  customerPrice: {
    color: '#FFFFFF',
    fontWeight: '700',
  },
  customerLastOrder: {
    marginTop: 2,
  },
  customerOrders: {
    fontWeight: '500',
  },
  customerMeasurements: {
    fontWeight: '500',
  },
  customerCard: {
    marginBottom: 8,
    borderRadius: 12,
    overflow: 'hidden',
  },
  customerContent: {
    padding: 16,
  },
  customerHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  customerInfo: {
    flex: 1,
    marginLeft: 16,
  },
  customerNameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  customerBottomRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
  },
  customerLeftInfo: {
    flex: 1,
  },
  customerPriceAndBadge: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});