import React, { useState, useCallback, useMemo } from 'react';
// FIXED: Removed invalid semicolon from import
import { View, ScrollView, StyleSheet, Alert } from 'react-native';
import { Surface, Text, Button, List, Switch } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import LocalIcon from '../components/LocalIcon';
import { useTheme } from '../context/ThemeContext';
import { useData } from '../context/DataContext';
import { useAuth } from '../context/AuthContext';
import { useSubscription } from '../context/SubscriptionContext';
import ImagePicker from '../components/ImagePicker';
import CompactThemeSelector from '../components/CompactThemeSelector';
import ProtectedRoute from '../components/ProtectedRoute';
import { SPACING, BORDER_RADIUS, TYPOGRAPHY } from '../theme/designTokens';
import navigationService from '../services/NavigationService';
import CommonHeader from '../components/CommonHeader';

// FIXED: Extracted ProfileHeader into its own memoized component for performance and clarity
const ProfileHeader = React.memo(({ user, settings, subscriptionInfo, onImageSelected }) => {
  const { theme } = useTheme();
  const navigation = useNavigation();
  const { currentTier, subscriptionStatus, endDate, usage } = subscriptionInfo;
  
  const planName = currentTier === 'growth' ? 'Growth Plan' : currentTier === 'pro' ? 'Pro Plan' : 'Starter Plan';
  const planColor = currentTier === 'growth' ? '#2196F3' : currentTier === 'pro' ? '#9C27B0' : '#4CAF50';
  const renewalDate = new Date(endDate || Date.now()).toLocaleDateString();

  const styles = useMemo(() => createStyles(theme, planColor), [theme, planColor]);

  return (
    <Surface style={styles.profileCard} elevation={1}>
      <View style={styles.profileContent}>
        <View style={styles.profileHeader}>
          <View style={styles.profileMain}>
            <View style={styles.avatarContainer}>
              <ImagePicker
                onImageSelected={onImageSelected}
                currentImage={user?.avatar || settings?.profileImage}
                size={64}
                borderRadius={32}
              />
            </View>
            <View style={styles.profileInfo}>
              <Text style={styles.profileName}>{user?.name || 'User'}</Text>
              <Text style={styles.profileContact}>{user?.email || settings?.email || 'No email'}</Text>
              <Text style={styles.profileContact}>{user?.phone || settings?.phone || 'No phone'}</Text>
            </View>
          </View>
          <Button mode="outlined" onPress={() => navigation.navigate('EditProfile')} icon="pencil" compact>Edit</Button>
        </View>

        <View style={styles.subscriptionDivider} />

        <TouchableOpacity style={styles.subscriptionContainer} onPress={() => navigationService.navigate('Subscription')}>
          <View style={styles.subscriptionHeader}>
            <View style={styles.subscriptionIcon}>
              <LocalIcon name="award" size={20} color={planColor} />
            </View>
            <View style={styles.subscriptionInfo}>
              <Text style={styles.subscriptionTitle}>{planName}</Text>
              <Text style={styles.subscriptionStatus}>{subscriptionStatus === 'active' ? 'Active' : 'Inactive'} • Renews {renewalDate}</Text>
            </View>
            <LocalIcon name="chevron-right" size={20} color={theme.colors.onSurfaceVariant} />
          </View>
          <View style={styles.subscriptionStats}>
            <View style={styles.subscriptionStat}><Text style={styles.statLabel}>Orders</Text><Text style={styles.statValue}>{usage?.orders || 0}/{subscriptionInfo.limits.orders ?? '∞'}</Text></View>
            <View style={styles.subscriptionStat}><Text style={styles.statLabel}>Customers</Text><Text style={styles.statValue}>{usage?.customers || 0}/{subscriptionInfo.limits.customers ?? '∞'}</Text></View>
            <View style={styles.subscriptionStat}><Text style={styles.statLabel}>Storage</Text><Text style={styles.statValue}>{usage?.storage || 0}GB/{subscriptionInfo.limits.storage ?? '∞'}GB</Text></View>
          </View>
        </TouchableOpacity>
      </View>
    </Surface>
  );
});

const MyProfileScreen = () => {
  const { theme } = useTheme();
  const { state, actions } = useData();
  const { user, isAdmin, logout } = useAuth();
  const subscriptionInfo = useSubscription();
  const navigation = useNavigation();

  const [notifications, setNotifications] = useState(state?.settings?.notifications ?? true);
  const [autoBackup, setAutoBackup] = useState(state?.settings?.autoBackup ?? true);

  const handleSettingChange = useCallback(async (setting, value) => {
    if (setting === 'notifications') setNotifications(value);
    else if (setting === 'autoBackup') setAutoBackup(value);
    actions.updateSettings({ ...state.settings, [setting]: value });
  }, [actions, state.settings]);

  const handleProfileImageSelected = useCallback((imageUri) => {
    actions.updateUser({ ...user, avatar: imageUri });
  }, [actions, user]);

  const handleLogout = useCallback(() => {
    Alert.alert('Logout', 'Are you sure you want to logout?', [
      { text: 'Cancel', style: 'cancel' },
      { text: 'Logout', style: 'destructive', onPress: logout }
    ]);
  }, [logout]);

  const navigateTo = (screen) => navigationService.navigate(screen);
  
  const styles = useMemo(() => createStyles(theme, theme.colors.primary), [theme]);

  const menuItems = {
    business: [
      { title: 'Subscription', icon: 'award', screen: 'Subscription' },
      { title: 'Payment Methods', icon: 'credit-card', screen: 'PaymentMethods' },
      { title: 'Tax Settings', icon: 'receipt', screen: 'TaxSettings' },
    ],
    profile: [
      { title: 'Change Password', icon: 'lock', screen: 'SecuritySettings' },
    ],
    admin: [
      { title: 'Admin Settings', icon: 'settings', screen: 'AdminSettings' },
      { title: 'Employee Management', icon: 'users', screen: 'EmployeeManagement' },
      { title: 'Data Management', icon: 'database', screen: 'DataManagement' },
    ],
    support: [
      { title: 'Help & FAQ', icon: 'help-circle', screen: 'HelpFAQ' },
      { title: 'Contact Support', icon: 'mail', screen: 'ContactSupport' },
      { title: 'About', icon: 'info', screen: 'About' },
    ]
  };

  const renderSection = (title, items) => (
    <Surface style={styles.sectionCard} elevation={1}>
      <Text style={styles.sectionTitle}>{title}</Text>
      {items.map(item => (
        <List.Item
          key={item.title}
          title={item.title}
          left={() => <List.Icon icon={item.icon} color={theme.colors.primary} />}
          right={() => <List.Icon icon="chevron-right" />}
          onPress={() => navigateTo(item.screen)}
        />
      ))}
    </Surface>
  );

  return (
    <View style={styles.container}>
      <CommonHeader title="My Profile" />
      <ScrollView contentContainerStyle={styles.content} showsVerticalScrollIndicator={false}>
        <ProfileHeader user={user} settings={state.settings} subscriptionInfo={subscriptionInfo} onImageSelected={handleProfileImageSelected} />
        <Surface style={styles.sectionCard} elevation={1}>
          <Text style={styles.sectionTitle}>Appearance</Text>
          <CompactThemeSelector />
        </Surface>
        <ProtectedRoute role="admin">
          {renderSection("Business Settings", menuItems.business)}
        </ProtectedRoute>
        <Surface style={styles.sectionCard} elevation={1}>
          <Text style={styles.sectionTitle}>Profile & Security</Text>
          {renderSection("", menuItems.profile)}
          <List.Item title="Notifications" left={() => <List.Icon icon="bell" color={theme.colors.primary} />} right={() => <Switch value={notifications} onValueChange={v => handleSettingChange('notifications', v)} />} />
          <List.Item title="Auto Backup" left={() => <List.Icon icon="cloud-upload" color={theme.colors.primary} />} right={() => <Switch value={autoBackup} onValueChange={v => handleSettingChange('autoBackup', v)} />} />
        </Surface>
        <ProtectedRoute role="admin">
          {renderSection("Administration", menuItems.admin)}
        </ProtectedRoute>
        {renderSection("Support", menuItems.support)}
        <Button mode="outlined" onPress={handleLogout} style={styles.logoutButton} labelStyle={{ color: theme.colors.error }} icon="log-out">Logout</Button>
        <View style={styles.footer}><Text style={styles.footerText}>Elite Tailoring Management v2.1.0</Text></View>
      </ScrollView>
    </View>
  );
};

// Moved stylesheet creation to a function to be used by both components
const createStyles = (theme, planColor) => StyleSheet.create({
  container: { flex: 1, backgroundColor: theme.colors.background },
  content: { padding: SPACING.md, paddingBottom: SPACING.xl * 2 },
  profileCard: { marginBottom: SPACING.lg, borderRadius: BORDER_RADIUS.xl, backgroundColor: theme.colors.surface },
  profileContent: { padding: SPACING.lg },
  profileHeader: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' },
  profileMain: { flexDirection: 'row', alignItems: 'center', flex: 1 },
  avatarContainer: { marginRight: SPACING.md },
  profileInfo: { flex: 1 },
  profileName: { ...TYPOGRAPHY.titleLarge },
  profileContact: { ...TYPOGRAPHY.bodyMedium, color: theme.colors.onSurfaceVariant },
  subscriptionDivider: { height: 1, backgroundColor: theme.colors.outlineVariant, marginVertical: SPACING.md },
  subscriptionContainer: { paddingVertical: SPACING.sm },
  subscriptionHeader: { flexDirection: 'row', alignItems: 'center' },
  subscriptionIcon: { width: 32, height: 32, borderRadius: 16, justifyContent: 'center', alignItems: 'center', marginRight: SPACING.sm, backgroundColor: planColor + '20' },
  subscriptionInfo: { flex: 1 },
  subscriptionTitle: { ...TYPOGRAPHY.titleSmall, color: planColor },
  subscriptionStatus: { ...TYPOGRAPHY.bodySmall, color: theme.colors.onSurfaceVariant },
  subscriptionStats: { flexDirection: 'row', justifyContent: 'space-around', paddingTop: SPACING.md, marginTop: SPACING.md, borderTopWidth: 1, borderTopColor: theme.colors.outlineVariant },
  subscriptionStat: { alignItems: 'center' },
  statLabel: { ...TYPOGRAPHY.labelSmall, color: theme.colors.onSurfaceVariant },
  statValue: { ...TYPOGRAPHY.bodyMedium, fontWeight: '600' },
  sectionCard: { marginBottom: SPACING.lg, borderRadius: BORDER_RADIUS.xl, backgroundColor: theme.colors.surface },
  sectionTitle: { ...TYPOGRAPHY.titleMedium, paddingHorizontal: SPACING.lg, paddingTop: SPACING.lg, paddingBottom: SPACING.sm },
  logoutButton: { margin: SPACING.lg, borderColor: theme.colors.error, borderWidth: 1 },
  footer: { paddingVertical: SPACING.lg, alignItems: 'center' },
  footerText: { ...TYPOGRAPHY.bodySmall, color: theme.colors.onSurfaceVariant },
});

export default MyProfileScreen;