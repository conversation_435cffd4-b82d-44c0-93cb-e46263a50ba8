import React, { useState, useCallback } from 'react';
import { ScrollView, View, StyleSheet, Alert   } from 'react-native';
import { Text   } from 'react-native-paper';
import { TextInput   } from 'react-native-paper';
import { Button   } from 'react-native-paper';
import { Surface   } from 'react-native-paper';
import { Switch   } from 'react-native-paper';
import LocalIcon from '../components/LocalIcon';
import { useData   } from '../context/DataContext';
import { useTheme   } from '../context/ThemeContext';
import { useSafeAreaInsets   } from 'react-native-safe-area-context';
import { Appbar   } from 'react-native-paper';

const AddCustomerScreen = ({ route, navigation }) => {
  const { theme  } = useTheme();
  const { actions  } = useData();
  const insets = useSafeAreaInsets();

  // Get customer from route params for editing
  const editingCustomer = route?.params?.customer;
  const isEditing = !!editingCustomer;

  // Get search query and return navigation info
  const searchQuery = route?.params?.searchQuery || '';
  const returnTo = route?.params?.returnTo;

  // Customer form state with search query pre-filling
  const getInitialName = () => {
    if (editingCustomer?.name) return editingCustomer.name;
    if (searchQuery && !/^\d+$/.test(searchQuery)) return searchQuery; // If not numeric, assume it's a name
    return '';
  };

  const getInitialPhone = () => {
    if (editingCustomer?.phone) return editingCustomer.phone;
    if (searchQuery && /^\d+$/.test(searchQuery)) return searchQuery; // If numeric, assume it's a phone
    return '';
  };

  const [customerData, setCustomerData] = useState({
    name: getInitialName(),
    email: editingCustomer?.email || '',
    phone: getInitialPhone(),
    address: editingCustomer?.address || '',
    notes: editingCustomer?.notes || '',
    isVIP: editingCustomer?.isVIP || false,

    // Enhanced fields
    birthday: editingCustomer?.birthday || '',
    anniversary: editingCustomer?.anniversary || '',
    preferences: editingCustomer?.preferences || {
      preferredFabrics: [],
      preferredColors: [],
      specialInstructions: '',
      communicationMethod: 'phone',
      reminderPreferences: {
        birthday: true,
        anniversary: true,
        appointments: true,
        payments: true,
      },
    },
  });

  const [errors, setErrors] = useState({});

  const handleInputChange = useCallback((field, value) => {
    setCustomerData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if(errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  }, [errors]);

  const validateForm = () => {
    const newErrors = {};

    if (!customerData.name.trim()) {
      newErrors.name = 'Customer name is required';
    }

    if (!customerData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    } else if (!/^\+?[\d\s\-\(\)]+$/.test(customerData.phone)) {
      newErrors.phone = 'Please enter a valid phone number';
    }

    if (customerData.email && !/\S+@\S+\.\S+/.test(customerData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = useCallback(async () => {
    if (!validateForm()) {
      Alert.alert('Validation Error', 'Please fix the errors and try again.');
      return;
    }

    try {
      const customerToSave = {
        name: customerData.name,
        phone: customerData.phone,
        email: customerData.email,
        address: customerData.address,
      };

      if(isEditing) {
        // Update existing customer
        await actions.updateCustomer({
          ...customerToSave,
          id: editingCustomer.id
        });

        Alert.alert(
          'Success',
          'Customer updated successfully!',
          [
            {
              text: 'Done',
              onPress: () => navigation.goBack(),
              style: 'default'
            }
          ]
        );
      } else {
        // Add new customer
        const savedCustomer = await actions.addCustomer(customerToSave);

        // Handle different navigation flows based on returnTo parameter
        if(returnTo === 'AddOrder') {
          // Navigate back to AddOrder with the saved customer
          navigation.navigate('AddOrder', { newCustomer: savedCustomer });
        } else {
          // Standard flow with alert options
          Alert.alert(
            'Success',
            'Customer added successfully!',
            [
              {
                text: 'Add Another',
                onPress: () => {
                  setCustomerData({
                    name: '',
                    email: '',
                    phone: '',
                    address: '',
                    notes: '',
                    isVIP: false,
                    birthday: '',
                    anniversary: '',
                    preferences: {
                      preferredFabrics: [],
                      preferredColors: [],
                      specialInstructions: '',
                      communicationMethod: 'phone',
                      reminderPreferences: {
                        birthday: true,
                        anniversary: true,
                        appointments: true,
                        payments: true,
                      },
                    },
                  });
                }
              },
              {
                text: 'Done',
                onPress: () => navigation.goBack(),
                style: 'default'
              }
            ]
          );
        }
      }
    } catch (error) {
      Alert.alert('Error', `Failed to ${isEditing ? 'update' : 'add'} customer. Please try again.`);
    }
  }, [customerData, isEditing, editingCustomer, actions, navigation, returnTo]);

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }}}}>
      {/* Small Appbar */}
      <Appbar.Header
        style={[
          styles.appbar,
          {
            backgroundColor: theme.colors.surface,
            paddingTop: insets.top,
            height: 56 + insets.top,
          }
        }
        elevation={2}>
        <Appbar.BackAction onPress={() => navigation.goBack()} />
        <Appbar.Content
          title={isEditing ? "Edit Customer" : "Add Customer"}
          titleStyle={styles.appbarTitle}
        />
      </Appbar.Header>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Basic Information */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }}}]} elevation={1}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }}}}>
            Basic Information
          </Text>

          <TextInput
            label="Customer Name *"
            value={customerData.name}
            onChangeText={(text) => handleInputChange('name', text)}
            mode="outlined"
            style={styles.input}
            error={!!errors.name}
            left={<TextInput.Icon icon="account" />}
          />
          {errors.name && (
            <Text variant="bodySmall" style={[styles.errorText, { color: theme.colors.error }}}}>
              {errors.name}
            </Text>
          )}

          <TextInput
            label="Phone Number *"
            value={customerData.phone}
            onChangeText={(text) => handleInputChange('phone', text)}
            mode="outlined"
            style={styles.input}
            error={!!errors.phone}
            keyboardType="phone-pad"
            left={<TextInput.Icon icon="phone" />}
          />
          {errors.phone && (
            <Text variant="bodySmall" style={[styles.errorText, { color: theme.colors.error }}}}>
              {errors.phone}
            </Text>
          )}

          <TextInput
            label="Email Address"
            value={customerData.email}
            onChangeText={(text) => handleInputChange('email', text)}
            mode="outlined"
            style={styles.input}
            error={!!errors.email}
            keyboardType="email-address"
            autoCapitalize="none"
            left={<TextInput.Icon icon="email" />}
          />
          {errors.email && (
            <Text variant="bodySmall" style={[styles.errorText, { color: theme.colors.error }}}}>
              {errors.email}
            </Text>
          )}

          <TextInput
            label="Address"
            value={customerData.address}
            onChangeText={(text) => handleInputChange('address', text)}
            mode="outlined"
            style={styles.input}
            multiline
            numberOfLines={2}
            left={<TextInput.Icon icon="map-marker" />}
          />
        </Surface>

        {/* Customer Settings */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }}}]} elevation={1}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }}}}>
            Customer Settings
          </Text>

          <View style={styles.switchRow}>
            <View style={styles.switchLabel}>
              <LocalIcon name="crown" size={20} color={theme.colors.primary} />
              <Text variant="bodyLarge" style={[styles.switchLabelText, { color: theme.colors.onSurface, marginLeft: 8 }}}}>
                VIP Customer
              </Text>
            </View>
            <Switch
              value={customerData.isVIP}
              onValueChange={(value) => handleInputChange('isVIP', value)}
            />
          </View>

          <TextInput
            label="Notes"
            value={customerData.notes}
            onChangeText={(text) => handleInputChange('notes', text)}
            mode="outlined"
            style={styles.input}
            multiline
            numberOfLines={3}
            placeholder="Special preferences, allergies, etc."
            left={<TextInput.Icon icon="note-text" />}
          />
        </Surface>

        {/* Action Buttons */}
        <View style={styles.buttonContainer}>
          <Button
            mode="outlined"
            onPress={() => navigation.goBack()}
            style={styles.button}>
            Cancel
          </Button>
          <Button
            mode="contained"
            onPress={handleSave}
            style={styles.button}
            icon="check"
          >
            {isEditing ? 'Update Customer' : 'Save Customer'}
          </Button>
        </View>
      </ScrollView>
    </View>
  );
};

export default AddCustomerScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  appbar: {
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  appbarTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  section: {
    padding: 16,
    marginVertical: 8,
    borderRadius: 12,
  },
  card: {
    marginVertical: 8,
    borderRadius: 12,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: 16,
  },
  subsectionTitle: {
    fontWeight: '500',
    marginBottom: 8,
    marginTop: 8,
  },
  input: {
    marginBottom: 8,
  },
  errorText: {
    marginBottom: 8,
    marginLeft: 4,
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  switchLabel: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  switchLabelText: {},
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 8,
  },
  tag: {
    marginBottom: 4,
  },
  selectedTag: {
    marginBottom: 4,
  },
  customTagRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 16,
  },
  tagInput: {
    flex: 1,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
    marginVertical: 24,
    paddingBottom: 32,
  },
  button: {
    flex: 1,
  },
  cancelButton: {
    marginRight: 6,
  },
  saveButton: {
    marginLeft: 6,
  },
  communicationMethods: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 16,
  },
  methodChip: {
    marginBottom: 4,
  },
  measurementsList: {
    marginTop: 8,
  },
  measurementItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  measurementInfo: {
    flex: 1,
  },
  orderStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 16,
  },
  statItem: {
    alignItems: 'center',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  avatarSection: {
    alignItems: 'center',
    paddingVertical: 8,
  },
});