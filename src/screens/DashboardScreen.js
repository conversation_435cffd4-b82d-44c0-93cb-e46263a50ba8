import { Text, Button   } from 'react-native-paper';
import React, { useState, useMemo, useCallback } from 'react';
import { ScrollView, View, StyleSheet, RefreshControl   } from 'react-native';
import { useSafeAreaInsets   } from 'react-native-safe-area-context';
import { Surface,
  TouchableRipple,
  } from '../utils/OptimizedPaperImports';

import { useData   } from '../context/DataContext';
import { useTheme   } from '../context/ThemeContext';
import { useFinancial   } from '../context/FinancialContext';
import { useAuth   } from '../context/AuthContext';

import CommonHeader from '../components/CommonHeader';
import navigationService from '../services/NavigationService';
import UniversalCard from '../components/UniversalCard';
import StatCardGroup from '../components/StatCardGroup';
import AnalyticsService from '../services/AnalyticsService';
import { SPACING, BORDER_RADIUS, COMPONENT_SIZES, TYPOGRAPHY   } from '../theme/designTokens';
import { formatCurrency   } from '../utils/numberUtils';

import backgroundProcessOptimizer from '../services/BackgroundProcessOptimizer';

import LocalIcon, { NavigationLocalIcon, ActionLocalIcon, BusinessLocalIcon, SettingsLocalIcon, StatusLocalIcon } from '../components/LocalIcon';

const DashboardScreen = ({ navigation, navigateToTab }) => {
  const { theme  } = useTheme();
  const { state  } = useData();
  const { profitLossData, derivedData  } = useFinancial();
  const { hasPermission  } = useAuth();
  const insets = useSafeAreaInsets();

  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Create styles with theme
  const styles = useMemo(() => createStyles(theme), [theme]);

  // Register dashboard background processes
  React.useEffect(() => {
    backgroundProcessOptimizer.registerProcess('dashboardRefresh', {
      interval: 300000, // 5 minutes (reduced from 1 minute)
      priority: 'low',
      canThrottle: true,
      activeOnly: true,
      description: 'Dashboard data refresh',
      callback: () => {
        // Auto-refresh dashboard data
        if(!refreshing) {

        }
      }
    });

    return () => {
      backgroundProcessOptimizer.stopProcess('dashboardRefresh');
    };
  }, [refreshing]);

  // Memoize today's date to avoid recalculating
  const today = useMemo(() => new Date().toISOString().split('T')[0], []);

  // Simplified refresh functionality
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      // Simple refresh simulation
      await new Promise(resolve => setTimeout(resolve, 500));

    } catch (error) {

    } finally {
      setRefreshing(false);
    }
  }, []);

  // Ultra-fast analytics - minimal calculations for instant loading
  const analyticsData = useMemo(() => {
    // Always return fast, basic calculations
    const ordersCount = state.orders?.length || 0;
    const customersCount = state.customers?.length || 0;
    const garmentsCount = state.garments?.length || state.products?.length || 0;
    const measurementsCount = state.measurements?.length || 0;
    const fabricsCount = state.fabrics?.length || 0;

    // Simple revenue calculation without heavy processing
    const totalRevenue = state.orders?.reduce((sum, order) => {
      return sum + (parseFloat(order.totalAmount) || parseFloat(order.total) || 0);
    }, 0) || 0;

    const pendingOrders = state.orders?.filter(order =>
      order.status === 'pending' || order.status === 'in_progress'
    ).length || 0;

    return {
      sales: {
        todaysRevenue: totalRevenue * 0.1, // Estimate today's revenue
        monthRevenue: totalRevenue
      },
      orders: {
        total: ordersCount,
        pending: pendingOrders
      },
      inventory: {
        totalGarments: garmentsCount,
        totalMeasurements: measurementsCount,
        totalFabrics: fabricsCount
      },
      customers: {
        total: customersCount,
        newThisMonth: Math.floor(customersCount * 0.1) // Estimate new customers
      },
      overview: {
        completionRate: ordersCount > 0 ? ((ordersCount - pendingOrders) / ordersCount) * 100 : 0
      }
    };
  }, [state.orders?.length, state.customers?.length, state.garments?.length, state.products?.length]);

  // Dashboard stats derived from analytics
  const dashboardStats = useMemo(() => {
    return { todaysSales: analyticsData.sales.todaysRevenue,
      totalOrders: analyticsData.orders.total,
      activeOrders: analyticsData.orders.pending,
      totalGarments: analyticsData.inventory.totalGarments,
      totalCustomers: analyticsData.customers.total,
      totalMeasurements: analyticsData.inventory.totalMeasurements,
      recentMeasurements: analyticsData.customers.newThisMonth, // Using new customers as proxy for recent activity
      totalFabrics: analyticsData.inventory.totalFabrics,
      monthlyRevenue: analyticsData.sales.monthRevenue,
      completionRate: analyticsData.overview.completionRate,
     };
  }, [analyticsData]);

  // Enhanced navigation handlers with proper data views
  const handleStatCardPress = useCallback((type) => {
    switch(type) {
      case 'sales':
        if (hasPermission('view_financial')) {

          try {
            // Navigate to Reports screen
            navigationService.navigate('Reports');
          } catch (error) {
            console.error('Failed to navigate to Reports:', error);
          }
        }
        break;
      case 'orders':
        navigateToTab('Orders');
        break;
      case 'garments':
        navigateToTab('Garments');
        break;
      case 'products': // Legacy support

        try {
          navigationService.navigate('Products');
        } catch (error) {
          console.error('Failed to navigate to Products:', error);
          // Fallback to garments tab
          navigateToTab('Garments');
        }
        break;
      case 'customers':
        navigateToTab('CRM');
        break;
      case 'measurements':
        navigateToTab('Measurements');
        break;
      case 'inventory':
        try {
          navigationService.navigate('UnifiedInventory');
        } catch (error) {
          console.error('Failed to navigate to UnifiedInventory:', error);
        }
        break;
      case 'financial':
        if (hasPermission('view_financial')) {

          try {
            navigationService.navigate('ProfitLoss');
          } catch (error) {
            console.error('Failed to navigate to ProfitLoss:', error);
          }
        }
        break;
      case 'reports':
        if (hasPermission('view_reports')) {

          try {
            navigationService.navigate('Reports');
          } catch (error) {
            console.error('Failed to navigate to Reports:', error);
          }
        }
        break;
      case 'revenue':
      case 'week-sales':
      case 'profit':
      case 'expenses':
      case 'tax':
        if (hasPermission('view_financial')) {

          try {
            navigationService.navigate('FinancialReports');
          } catch (error) {
            console.error('Failed to navigate to FinancialReports:', error);
          }
        }
        break;
    }
  }, [navigateToTab, hasPermission]);

  const dashboardCards = useMemo(() => {
    // Enhanced Tailor Management System Dashboard Cards
    const baseCards = [
      {
        key: 'active-orders',
        title: 'Active Orders',
        value: dashboardStats.activeOrders.toString(),
        icon: 'ClipboardText', // Phosphor icon
        iconColor: theme.colors.primary,
        onPress: () => handleStatCardPress('orders')
      },
      {
        key: 'pending-orders',
        title: 'Pending Orders',
        value: dashboardStats.activeOrders.toString(), // Same as active for now
        icon: 'Clock', // Phosphor icon
        iconColor: '#FF9800', // Orange for pending
        onPress: () => handleStatCardPress('orders')
      },
      {
        key: 'customers',
        title: 'Total Customers',
        value: dashboardStats.totalCustomers.toString(),
        icon: 'Users', // Phosphor icon
        iconColor: theme.colors.primary,
        onPress: () => handleStatCardPress('customers')
      },
      // Measurements card removed from dashboard
      {
        key: 'inventory',
        title: 'Inventory',
        value: (dashboardStats.totalFabrics || 0).toString(),
        icon: 'Package', // Phosphor icon
        iconColor: theme.colors.tertiary,
        onPress: () => handleStatCardPress('inventory')
      },
      {
        key: 'top-product',
        title: 'Top Product',
        value: 'Shirt', // Default or calculate from data
        icon: 'Star', // Phosphor icon
        iconColor: '#F59E0B', // Amber for top product
        onPress: () => handleStatCardPress('products')
      },
    ];

    // Add financial cards only for users with permission
    if (hasPermission('view_financial')) {
      // Add financial cards at the beginning
      baseCards.unshift(
        {
          key: 'todays-sales',
          title: 'Today\'s Sales',
          value: formatCurrency(dashboardStats.todaysSales || 0),
          icon: 'CurrencyDollar', // Phosphor icon
          iconColor: '#10B981', // Green for sales
          onPress: () => handleStatCardPress('sales')
        },
        {
          key: 'total-revenue',
          title: 'Total Revenue',
          value: formatCurrency(dashboardStats.monthlyRevenue || 0),
          icon: 'TrendUp', // Phosphor icon
          iconColor: '#3B82F6', // Blue for revenue
          onPress: () => handleStatCardPress('revenue')
        },
        // Week Sales and Gross Profit cards removed from dashboard
        {
          key: 'total-expenses',
          title: 'Total Expenses',
          value: formatCurrency((profitLossData?.totalExpenses || 0)),
          icon: 'Receipt', // Phosphor icon
          iconColor: '#EF4444', // Red for expenses
          onPress: () => handleStatCardPress('expenses')
        }
        // Total Tax card removed from dashboard
      );
    }

    // Reports card removed - advanced analytics moved to dedicated screens

    return baseCards;
  }, [dashboardStats, theme.colors, profitLossData, derivedData, hasPermission, handleStatCardPress]);

  // Simplified recent orders
  const recentOrders = useMemo(() =>
    state.orders
      .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
      .slice(0, 2),
    [state.orders]
  );

  const handleOrderPress = useCallback((order) => {

    // Navigate to order details
    navigationService.navigate('OrderDetails', { order });
  }, []);

  const handleQuickAction = useCallback((actionId) => {

    switch(actionId) {
      case 'add-garment':
        try {
          navigationService.navigate('AddProduct', {
            title: 'Add Garment',
            isGarment: true
          });
        } catch (error) {

          navigateToTab('Garments');
        }
        break;
      case 'add-order':
        try {
          navigationService.navigate('CreateOrder');
        } catch (error) {

          navigateToTab('Orders');
        }
        break;
      case 'add-customer':
        try {
          navigationService.navigate('AddCustomer');
        } catch (error) {

          navigateToTab('CRM');
        }
        break;
      case 'add-measurement':
        try {
          navigationService.navigate('AddMeasurement');
        } catch (error) {

          navigateToTab('Measurements');
        }
        break;
      case 'add-fabric':
        try {
          navigationService.navigate('AddFabric');
        } catch (error) {

          navigationService.navigate('UnifiedInventory');
        }
        break;
      case 'garment-templates':
        try {
          navigationService.navigate('GarmentTemplates');
        } catch (error) {

          navigateToTab('Garments');
        }
        break;
      case 'reports':
        try {
          navigationService.navigate('Reports');
        } catch (error) {

        }
        break;
      case 'business-analytics':
        console.log('Navigate to Reports (Analytics)');
        try {
          navigationService.navigate('Reports');
        } catch (error) {

          try {
            navigationService.navigate('FinancialReports');
          } catch (fallbackError) {

          }
        }
        break;
      case 'payment-analytics':
        console.log('Navigate to Financial Reports (Payment Analytics)');
        try {
          navigationService.navigate('FinancialReports');
        } catch (error) {

        }
        break;
      case 'profit-loss':
        try {
          navigationService.navigate('ProfitLoss');
        } catch (error) {

        }
        break;
      case 'accounting':
        try {
          navigationService.navigate('Accounting');
        } catch (error) {

        }
        break;
      case 'sales-analytics':
        try {
          navigationService.navigate('Sales');
        } catch (error) {

        }
        break;
      case 'tax-summary':
        try {
          navigationService.navigate('TaxSummary');
        } catch (error) {

        }
        break;
      case 'cash-reconciliation':
        try {
          navigationService.navigate('CashReconciliation');
        } catch (error) {

        }
        break;
      case 'inventory':
        try {
          navigationService.navigate('UnifiedInventory');
        } catch (error) {

        }
        break;
      default:

    }
  }, [navigateToTab]);

  return (
    <View style={styles.screenContainer}>
      <CommonHeader
        title="Home"
        subtitle="Welcome back!"
        searchPlaceholder="Search orders, products, customers..."
        searchType="global"
        searchData={[
          ...state.orders,
          ...(state.garments || state.products || []),
          ...state.customers,
          ...(state.measurements || []),
          ...(state.fabrics || [])
        }
        searchFields={["name", "customerName", "customer", "title", "description"}
        onSearchChange={setSearchQuery}
        onSearchResult={(item) => {

          // Handle navigation based on item type
          if(item.customerName || item.customer) {
            // It's an order

          } else if(item.price) {
            // It's a product

          } else {
            // It's a customer

          }
        }}
        onNotificationPress={() => {

          try {
            navigationService.navigate('Notifications');
          } catch (error) {
            console.error('Failed to navigate to Notifications:', error);
          }
        }}
        onProfilePress={() => {

          try {
            navigationService.navigate('MyProfile');
          } catch (error) {
            console.error('Failed to navigate to MyProfile:', error);
          }
        }}
      />

      <ScrollView
        style={styles.quickActionContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary}
            tintColor={theme.colors.primary}
          />
        }
      >
        <View style={styles.quickActionContainer}>
          <>
              <StatCardGroup
                cards={dashboardCards}
                columns={2}
                showTitle={false}
              />

              {/* Quick Actions Section - 4x2 Grid */}
              <View style={styles.quickActionContainer}>
                <View style={styles.quickActionContainer}>
                  <Text style={styles.sectionTitleText}>
                    Quick Actions
                  </Text>
                </View>
                <View style={styles.quickActionContainer}>
                  {[
                    {
                      id: 'add-garment',
                      title: 'Add Garment',
                      icon: 'Shirt', // Phosphor icon
                      iconType: 'phosphor',
                      color: theme.colors.onSurfaceVariant,
                      backgroundColor: theme.colors.surfaceVariant,
                    },
                    {
                      id: 'add-order',
                      title: 'Add Order',
                      icon: 'plus-circle', // Will migrate to PlusCircle
                      iconType: 'unified',
                      color: theme.colors.onSurfaceVariant,
                      backgroundColor: theme.colors.surfaceVariant,
                    },
                    {
                      id: 'add-customer',
                      title: 'Add Customer',
                      icon: 'UserPlus', // Phosphor icon
                      iconType: 'phosphor',
                      color: theme.colors.onSurfaceVariant,
                      backgroundColor: theme.colors.surfaceVariant,
                    },
                    {
                      id: 'add-measurement',
                      title: 'Measurement',
                      icon: 'Ruler', // Phosphor icon
                      iconType: 'phosphor',
                      color: theme.colors.onSurfaceVariant,
                      backgroundColor: theme.colors.surfaceVariant,
                    },
                    {
                      id: 'add-fabric',
                      title: 'Add Fabric',
                      icon: 'Scissors', // Phosphor icon for fabric/cutting
                      iconType: 'phosphor',
                      color: theme.colors.onSurfaceVariant,
                      backgroundColor: theme.colors.surfaceVariant,
                    },
                    {
                      id: 'garment-templates',
                      title: 'Templates',
                      icon: 'grid', // Will migrate to SquaresFour
                      iconType: 'unified',
                      color: theme.colors.onSurfaceVariant,
                      backgroundColor: theme.colors.surfaceVariant,
                    },
                    {
                      id: 'reports',
                      title: 'Reports',
                      icon: 'ChartBar', // Phosphor icon
                      iconType: 'phosphor',
                      color: theme.colors.onSurfaceVariant,
                      backgroundColor: theme.colors.surfaceVariant,
                    },
                    // Analytics and Payments removed from quick actions
                    {
                      id: 'profit-loss',
                      title: 'P&L',
                      icon: 'ChartLineUp', // Phosphor icon
                      iconType: 'phosphor',
                      color: theme.colors.onSurfaceVariant,
                      backgroundColor: theme.colors.surfaceVariant,
                    },
                    {
                      id: 'accounting',
                      title: 'Accounting',
                      icon: 'Calculator', // Phosphor icon
                      iconType: 'phosphor',
                      color: theme.colors.onSurfaceVariant,
                      backgroundColor: theme.colors.surfaceVariant,
                    },
                    {
                      id: 'sales-analytics',
                      title: 'Sales',
                      icon: 'ChartPie', // Phosphor icon
                      iconType: 'phosphor',
                      color: theme.colors.onSurfaceVariant,
                      backgroundColor: theme.colors.surfaceVariant,
                    },
                    {
                      id: 'tax-summary',
                      title: 'Tax',
                      icon: 'Receipt', // Phosphor icon
                      iconType: 'phosphor',
                      color: theme.colors.onSurfaceVariant,
                      backgroundColor: theme.colors.surfaceVariant,
                    },
                    {
                      id: 'cash-reconciliation',
                      title: 'Cash',
                      icon: 'Money', // Phosphor icon
                      iconType: 'phosphor',
                      color: theme.colors.onSurfaceVariant,
                      backgroundColor: theme.colors.surfaceVariant,
                    }
                    // Inventory removed from quick actions
                  ].map((action) => (
                    <TouchableRipple
                      key={action.id}
                      onPress={() => handleQuickAction(action.id)}
                      style={styles.quickActionTitleText}
                      borderless
                    >
                      <View style={styles.quickActionContainer}>
                        {action.iconType === 'phosphor' ? (
                          <BusinessLocalIcon
                            name={action.icon}
                            size={20}
                            color={action.color}
                            library="phosphor"
                          />
                        ) : (
                          <ActionLocalIcon
                            name={action.icon}
                            size={20}
                            color={action.color}
                          />
                        )}
                        <Text style={styles.sectionTitleText}>
                          {action.title}
                        </Text>
                      </View>
                    </TouchableRipple>
                  ))}
                </View>
              </View>

              {/* Only show Recent Orders section if there are orders */}
              {recentOrders && recentOrders.length > 0 && (
                <View style={styles.quickActionContainer}>
                  <View style={styles.quickActionContainer}>
                    <Text style={styles.quickActionTitleText}>
                      Recent Orders
                    </Text>
                    <Button
                      mode="text"
                      onPress={() => navigateToTab('Orders')}
                      textColor={theme.colors.primary}
                      compact
                    >
                      View All
                    </Button>
                  </View>
                  <View style={styles.quickActionContainer}>
                    {recentOrders.map((order, index) => (
                      <UniversalCard
                        key={order.id}
                        type="order"
                        data={order}
                        status={order.status}
                        statusColor={
                          order.status === 'Completed' ? theme.colors.tertiary :
                          order.status === 'In Progress' ? theme.colors.secondary :
                          order.status === 'Pending' ? theme.colors.primary :
                          theme.colors.error
                        }
                        description={order.items?.map(item => `${item.name}`}  x${item.quantity} ).join(',` ') || ''}
                        onPress={() => handleOrderPress(order)}
                        elevation={1}
                      />
                    ))}
                  </View>
                </View>
              )}
          </>
        </View>
      </ScrollView>
    </View>
  );
};






const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flex: 1,
  },
  content: {
    padding: SPACING.lg,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: SPACING.md,
    marginHorizontal: -SPACING.xs / 2,
  },
  statCardWrapper: {
    width: '50%',
    paddingHorizontal: SPACING.xs / 2,
    marginBottom: SPACING.xs,
  },
  sectionTitle: {
    fontWeight: '700',
    marginBottom: SPACING.sm,
  },
  financialSection: {
    marginBottom: SPACING.lg,
  },
  financialCardsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.md,
  },
  financialCardWrapper: {
    width: '48%',
  },
  financialIconContainer: {
    width: COMPONENT_SIZES.icon.xxxl,
    height: COMPONENT_SIZES.icon.xxxl,
    borderRadius: BORDER_RADIUS.md,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.sm + 2,
  },
  reportsCardWrapper: {
    width: '100%',
  },
  reportsCard: {
    borderRadius: BORDER_RADIUS.xl,
    padding: SPACING.lg,
    borderWidth: 1,
  },
  reportsCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  reportsCardText: {
    flex: 1,
    marginLeft: 4,
  },
  ordersSection: {
    marginBottom: SPACING.lg,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  ordersList: {
    gap: SPACING.sm,
  },
  quickOrderCard: {
    borderRadius: BORDER_RADIUS.xl,
    marginBottom: SPACING.lg,
    overflow: 'hidden',
  },
  subscriptionCard: {
    borderRadius: BORDER_RADIUS.xl,
    padding: SPACING.lg,
    marginBottom: SPACING.lg,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  subscriptionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  subscriptionIcon: {
    width: 40,
    height: 40,
    borderRadius: BORDER_RADIUS.md,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  subscriptionInfo: {
    flex: 1,
  },
  subscriptionTitle: {
    fontWeight: '600',
  },
  subscriptionButton: {
    minWidth: 80,
  },
  subscriptionStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: SPACING.md,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.05)',
  },
  subscriptionStat: {
    flex: 1,
    alignItems: 'center',
  },
  quickActionsSection: {
    marginBottom: SPACING.lg,
  },
  quickActionsContainer: {
    paddingHorizontal: SPACING.sm,
    gap: SPACING.md,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: SPACING.sm,
  },
  quickActionGridItem: {
    width: '23%', // 4 columns with gaps
    height: 70,
    borderRadius: BORDER_RADIUS.lg,
    overflow: 'hidden',
  },
  quickActionItem: {
    width: 100,
    height: 80,
    borderRadius: BORDER_RADIUS.lg,
    marginRight: SPACING.sm,
    marginVertical: 4, // 4px vertical padding
    overflow: 'hidden',
  },
  quickActionContent: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.sm,
  },
  quickActionTitle: {
    fontSize: 10,
    fontWeight: '600',
    textAlign: 'center',
    marginTop: SPACING.xs,
    lineHeight: 12,
  },
  sectionTitleText: {
    color: theme.colors.onBackground,
    fontSize: TYPOGRAPHY.sectionTitle.fontSize,
    fontWeight: TYPOGRAPHY.sectionTitle.fontWeight,
    lineHeight: TYPOGRAPHY.sectionTitle.lineHeight * TYPOGRAPHY.sectionTitle.fontSize,
  },
  quickActionTitleText: {
    color: theme.colors.onSurface,
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
  quickActionContainer: {
    borderRadius: BORDER_RADIUS.lg,
    overflow: 'hidden',
  },
  screenContainer: {
    backgroundColor: theme.colors.background,
    flex: 1,
  },
});

export default DashboardScreen;