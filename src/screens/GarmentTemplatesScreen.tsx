/**
 * <PERSON><PERSON><PERSON> Templates Management Screen
 * Admin interface for creating and managing garment templates
 */

import React, { useState, useCallback, useMemo } from 'react';
import { View, StyleSheet, FlatList, Alert   } from 'react-native';
import { Text   } from 'react-native-paper';
import { FAB   } from 'react-native-paper';
import { Surface   } from 'react-native-paper';
import { IconButton   } from 'react-native-paper';
import { Chip   } from 'react-native-paper';
import { Searchbar   } from 'react-native-paper';
import { Menu   } from 'react-native-paper';
import { Button   } from 'react-native-paper';
import { useSafeAreaInsets   } from 'react-native-safe-area-context';
import LocalIcon from '../components/LocalIcon';
import { useTheme   } from '../context/ThemeContext';
import { useData   } from '../context/DataContext';
import CommonHeader from '../components/CommonHeader';

const GarmentTemplatesScreen = ({ navigation }) => {
  const { theme  } = useTheme();
  const insets = useSafeAreaInsets();
  const { state: dataState, actions  } = useData();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [menuVisible, setMenuVisible] = useState(false);

  const garmentTemplates = useMemo(() => dataState.garmentTemplates || [], [dataState.garmentTemplates]);
  const categories = useMemo(() => ['All', 'Men', 'Women', 'Unisex', 'Children'], []);

  const filteredTemplates = useMemo(() => {
    let filtered = garmentTemplates;

    if(selectedCategory !== 'All') {
      filtered = filtered.filter(template => template.category === selectedCategory);
    }

    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      filtered = filtered.filter(template =>
        template.name.toLowerCase().includes(query) ||
        template.category.toLowerCase().includes(query)
      );
    }

    return filtered.sort((a, b) => a.name.localeCompare(b.name));
  }, [garmentTemplates, selectedCategory, searchQuery]);

  const handleAddTemplate = useCallback(() => {
    navigation.navigate('AddGarmentTemplate');
  }, [navigation]);

  const handleEditTemplate = useCallback((template) => {
    navigation.navigate('AddGarmentTemplate', { template, isEditing: true });
  }, [navigation]);

  const handleDeleteTemplate = useCallback((template) => {
    Alert.alert(
      'Delete Template',
      `Are you sure you want to delete "${template.name}"? This action cannot be undone.`,
      [
        { text: ('Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () ) => {
            try {
              await actions.deleteGarmentTemplate(template.id);
              Alert.alert('Success', 'Template deleted successfully');
            } catch (error) {
              Alert.alert('Error', 'Failed to delete template');
            }
          }
        }
      ]
    );
  }, [actions]);

  const renderTemplateItem = useCallback(({ item: (template }) ) => (
    <Surface style={styles.templateCard} elevation={1}>
      <View style={styles.templateHeader}>
        <View style={styles.templateInfo}>
          <Text variant="titleMedium" style={styles.templateName}>
            {template.name}
          </Text>
          <View style={styles.templateMeta}>
            <Chip
              mode="outlined"
              compact
              style={styles.categoryChip}
              textStyle={{ color: theme.colors.primary }>
              {template.category}
            </Chip>
            <Text variant="bodyMedium" style={styles.priceText}>
              ৳{template.basePrice?.toFixed(0) || '0'}
            </Text>
          </View>
        </View>
        
        <View style={styles.actionButtons}>
          <IconButton
            icon="pencil"
            size={20}
            onPress={() => handleEditTemplate(template)}
            iconColor={theme.colors.primary}
          />
          <IconButton
            icon="delete"
            size={20}
            onPress={() => handleDeleteTemplate(template)}
            iconColor={theme.colors.error}
          />
        </View>
      </View>

      <View style={styles.fieldsSection}>
        <Text variant="bodySmall" style={styles.fieldsLabel}>
          Measurement Fields ({template.measurementFields?.length || 0}):
        </Text>
        <View style={styles.fieldsContainer}>
          {template.measurementFields?.slice(0, 4).map((field, index) => (
            <Chip
              key={index}
              mode="outlined"
              compact
              style={styles.fieldChip}
              textStyle={{ fontSize: 11 }>
              {field.label}
            </Chip>
          ))}
          {template.measurementFields?.length > 4 && (
            <Text variant="bodySmall" style={styles.fieldsLabel}>
              +{template.measurementFields.length - 4} more
            </Text>
          )}
        </View>
      </View>
    </Surface>
  ), [theme, handleEditTemplate, handleDeleteTemplate]);

  const keyExtractor = useCallback((item) => item.id.toString(), []);

  return (
    <View style={styles.container}>
      <CommonHeader
        title="Garment Templates"
        subtitle={`${filteredTemplates.length} ` templates`}
        showBackButton={true}
        onBackPress={() => navigation.goBack()}
        rightComponent={
          <Menu
            visible={menuVisible}
            onDismiss={() => setMenuVisible(false)}
            anchor={
              <IconButton
                icon="dots-vertical"
                onPress={() => setMenuVisible(true)}
                iconColor={theme.colors.onSurface}
              />
            }
          >
            <Menu.Item
              onPress={() => {
                setMenuVisible(false);
                handleAddTemplate();
              }
              title="Add Template"
              leadingIcon="plus"
            />
            <Menu.Item
              onPress={() => {
                setMenuVisible(false);
                // Add bulk import functionality
              }
              title="Import Templates"
              leadingIcon="upload"
            />
          </Menu>
        }
      />

      <View style={styles.content}>
        {/* Search and Filters */}
        <View style={styles.searchSection}>
          <Searchbar
            placeholder="Search templates..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            style={styles.searchBar}
            inputStyle={{ color: theme.colors.onSurfaceVariant }
            iconColor={theme.colors.onSurfaceVariant}
          />

          <View style={styles.categoriesContainer}>
            {categories.map((category) => (
              <Chip
                key={category}
                selected={selectedCategory === category}
                onPress={() => setSelectedCategory(category)}
                style={styles.categoryFilterChip}
                textStyle={{
                  color: selectedCategory === category 
                    ? theme.colors.onPrimary 
                    : theme.colors.onSurfaceVariant
                }>
                {category}
              </Chip>
            ))}
          </View>
        </View>

        {/* Templates List */}
        <FlatList
          data={filteredTemplates}
          renderItem={renderTemplateItem}
          keyExtractor={keyExtractor}
          style={styles.listContainer}
          contentContainerStyle={[
            styles.listContent,
            { paddingBottom: insets.bottom + 80 }
          }
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={
            <View style={styles.emptyState}>
              <LocalIcon name="tshirt-crew" size={64} color={theme.colors.onSurfaceVariant} />
              <Text variant="headlineSmall" style={styles.emptyTitle}>
                No Templates Found
              </Text>
              <Text variant="bodyMedium" style={styles.emptySubtitle}>
                {searchQuery || selectedCategory !== 'All' 
                  ? 'Try adjusting your search or filters'
                  : 'Create your first garment template to get started'
                }
              </Text>
              {!searchQuery && selectedCategory === 'All' && (
                <Button
                  mode="contained"
                  onPress={handleAddTemplate}
                  style={styles.createButton}
                  icon="plus"
                >
                  Create Template
                </Button>
              )}
            </View>
          }
        />
      </View>

      {/* FAB */}
      <FAB
        icon="plus"
        style={styles.fab}
        onPress={handleAddTemplate}
        label="Add Template"
      />
    </View>
  );
};






export default GarmentTemplatesScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  searchSection: {
    marginBottom: 16,
  },
  searchBar: {
    marginBottom: 12,
    elevation: 0,
  },
  categoriesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  categoryFilterChip: {
    marginRight: 8,
    marginBottom: 8,
  },
  listContainer: {
    flex: 1,
  },
  templateCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
  },
  templateHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  templateInfo: {
    flex: 1,
  },
  templateName: {
    fontWeight: '600',
    marginBottom: 8,
  },
  templateMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryChip: {
    height: 28,
    marginRight: 12,
  },
  priceText: {
    fontWeight: '600',
    fontSize: 16,
  },
  actionButtons: {
    flexDirection: 'row',
  },
  fieldsSection: {
    marginTop: 8,
  },
  fieldsLabel: {
    color: '#666',
    marginBottom: 8,
  },
  fieldsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  fieldChip: {
    marginRight: 8,
    marginBottom: 4,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyTitle: {
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtitle: {
    color: '#666',
    marginBottom: 16,
    textAlign: 'center',
  },
  createButton: {
    marginTop: 16,
  },
  fab: {
    position: 'absolute',
    right: 16,
    bottom: 16,
  },
  listContent: {
    paddingBottom: 16,
  },
});