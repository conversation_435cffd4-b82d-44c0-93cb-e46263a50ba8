/**
 * EmployeeAttendanceScreen - Track employee attendance and work hours
 */

import React, { useState, useMemo, useCallback } from 'react';
import { View, StyleSheet, Alert, FlatList, ScrollView   } from 'react-native';
import { Text, Surface, Button, Chip, FAB, Avatar, SegmentedButtons   } from 'react-native-paper';
import LocalIcon from '../components/LocalIcon';
import { useAuth, ROLES   } from '../context/AuthContext';
import { useTheme   } from '../context/ThemeContext';
import CommonHeader from '../components/CommonHeader';
import StatCardGroup from '../components/StatCardGroup';
import navigationService from '../services/NavigationService';
import { SPACING, BORDER_RADIUS   } from '../theme/designTokens';

const EmployeeAttendanceScreen = () => {
  const { theme  } = useTheme();
  const { user, employees  } = useAuth(); // Assuming useAuth provides the current user
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [viewMode, setViewMode] = useState('today');

  // Mock attendance data - in a real app, this would come from a service
  const [attendanceData, setAttendanceData] = useState([
    { id: '1', employeeId: 'emp_1', employeeName: 'Ahmed Hassan', role: 'master_tailor', date: '2025-07-12', checkIn: '09:00', checkOut: '18:05', status: 'present', workHours: 9.08, overtime: 1.08, notes: '' },
    { id: '2', employeeId: 'emp_2', employeeName: 'Fatima Khan', role: 'stitcher', date: '2025-07-12', checkIn: '09:15', checkOut: null, status: 'present', workHours: 0, overtime: 0, notes: '' },
  ]);

  const attendanceStats = useMemo(() => {
    const today = new Date().toISOString().split('T')[0];
    const todayAttendance = attendanceData.filter(record => record.date === today);
    const totalEmployees = employees.length;
    const presentToday = todayAttendance.filter(record => record.status === 'present').length;
    const absentToday = totalEmployees - presentToday;
    const lateToday = todayAttendance.filter(record => record.checkIn && record.checkIn > '09:00').length;
    return { totalEmployees, presentToday, absentToday, lateToday  };
  }, [employees, attendanceData]);

  const handleCheckIn = useCallback((employee) => {
    // ... check-in logic ...
    Alert.alert('Checked In', `${employee.name}`}  has been checked in.`);
  }, [attendanceData]);

  const handleCheckOut = useCallback((employee) => {
    // ... check-out logic ...
    Alert.alert('Checked Out', `${employee.name}`}  has been checked out.`);
  }, [attendanceData]);

  const getRoleColor = (role) => ({
    [ROLES.ADMIN]: '#FF5722', [ROLES.MASTER_TAILOR]: '#9C27B0', [ROLES.CUTTER]: '#2196F3',
    [ROLES.STITCHER]: '#4CAF50', [ROLES.FINISHER]: '#FF9800', [ROLES.RECEPTIONIST]: '#E91E63',
    [ROLES.HELPER]: '#607D8B',
  }[role] || theme.colors.primary);

  const getStatusColor = (status) => ({
    present: '#4CAF50', absent: '#F44336', late: '#FF9800', leave: '#9E9E9E',
  }[status] || theme.colors.onSurfaceVariant);

  // FIXED: Styles moved inside component and memoized for performance
  const styles = useMemo(() => StyleSheet.create({
    container: { flex: 1, backgroundColor: theme.colors.background },
    content: { flex: 1, paddingHorizontal: 16, paddingBottom: 100 },
    section: { padding: 16, marginBottom: 16, borderRadius: BORDER_RADIUS.lg, backgroundColor: theme.colors.surface },
    sectionHeader: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 },
    sectionTitle: { fontWeight: '600', color: theme.colors.onSurface },
    employeeCard: { padding: 16, marginBottom: 12, borderRadius: BORDER_RADIUS.lg, backgroundColor: theme.colors.surface },
    employeeHeader: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start' },
    employeeInfo: { flexDirection: 'row', flex: 1, alignItems: 'center' },
    employeeDetails: { marginLeft: 12, flex: 1 },
    employeeName: { color: theme.colors.onSurface, fontWeight: '600' },
    employeeRole: { color: theme.colors.onSurfaceVariant },
    timeInfo: { marginTop: 4, flexDirection: 'row', gap: SPACING.md },
    timeText: { color: theme.colors.onSurfaceVariant, fontSize: 12 },
    workHoursText: { color: theme.colors.primary, fontSize: 12, fontWeight: '600' },
    employeeActions: { alignItems: 'flex-end', justifyContent: 'space-between' },
    statusChip: { marginBottom: 8 },
    actionButtons: { flexDirection: 'row', gap: 8, marginTop: SPACING.sm },
    emptyState: { alignItems: 'center', paddingVertical: 48 },
    emptyStateText: { color: theme.colors.onSurfaceVariant, marginTop: 16 },
    fab: { position: 'absolute', margin: 16, right: 0, bottom: 0, backgroundColor: theme.colors.primary },
  }), [theme]);

  const renderEmployeeCard = ({ item: employee }) => {
    const today = new Date().toISOString().split('T')[0];
    const todayRecord = attendanceData.find(record => record.employeeId === employee.id && record.date === today);
    const status = todayRecord?.status || 'absent';
    const statusColor = getStatusColor(status);
    
    // FIXED: Dynamic styles are calculated here, not in the stylesheet
    const avatarStyle = { backgroundColor: getRoleColor(employee.role) };
    const statusChipStyle = { backgroundColor: statusColor + '20' };

    return (
      <Surface style={styles.employeeCard} elevation={1}>
        <View style={styles.employeeHeader}>
          <View style={styles.employeeInfo}>
            <Avatar.Text size={48} label={employee.name.split(' ').map(n => n[0]).join('')} style={avatarStyle} />
            <View style={styles.employeeDetails}>
              <Text variant="titleMedium" style={styles.employeeName}>{employee.name}</Text>
              <Text variant="bodySmall" style={styles.employeeRole}>{employee.role.replace('_', ' ').toUpperCase()}</Text>
              {todayRecord && (
                <View style={styles.timeInfo}>
                  <Text style={styles.timeText}>In: {todayRecord.checkIn}</Text>
                  {todayRecord.checkOut && <Text style={styles.timeText}>Out: {todayRecord.checkOut}</Text>}
                  {todayRecord.workHours > 0 && <Text style={styles.workHoursText}>{todayRecord.workHours}h</Text>}
                </View>
              )}
            </View>
          </View>
          <View style={styles.employeeActions}>
            <Chip icon={todayRecord ? 'check-circle' : 'clock-outline'} style={[styles.statusChip, statusChipStyle} textStyle={{ color: statusColor } compact>
              {status.charAt(0).toUpperCase() + status.slice(1)}
            </Chip>
            <View style={styles.actionButtons}>
              <Button mode="outlined" onPress={() => handleCheckIn(employee)} disabled={!!todayRecord} compact>In</Button>
              <Button mode="contained" onPress={() => handleCheckOut(employee)} disabled={!todayRecord || !!todayRecord?.checkOut} compact>Out</Button>
            </View>
          </View>
        </View>
      </Surface>
    );
  };

  return (
    <View style={styles.container}>
      <CommonHeader
        title="Attendance"
        subtitle="Track daily attendance"
        showSearch={false}
        showBackButton={true}
      />
      <ScrollView contentContainerStyle={styles.content} showsVerticalScrollIndicator={false}>
        <StatCardGroup
          cards={[
            { key: 'total_employees', title: 'Total Staff', value: attendanceStats.totalEmployees.toString(), icon: 'account-group' },
            { key: 'present_today', title: 'Present', value: attendanceStats.presentToday.toString(), icon: 'check-circle', iconColor: '#4CAF50' },
            { key: 'absent_today', title: 'Absent', value: attendanceStats.absentToday.toString(), icon: 'close-circle', iconColor: '#F44336' },
            { key: 'late_today', title: 'Late', value: attendanceStats.lateToday.toString(), icon: 'clock-alert', iconColor: '#FF9800' },
          }
          columns={4}
        />
        <Surface style={styles.section} elevation={1}>
          <SegmentedButtons
            value={viewMode}
            onValueChange={setViewMode}
            buttons={[
              { value: 'today', label: 'Today', icon: 'calendar-today' },
              { value: 'week', label: 'This Week', icon: 'calendar-week' },
              { value: 'month', label: 'This Month', icon: 'calendar-month' },
            }
          />
        </Surface>
        <View style={styles.sectionHeader}>
            <Text variant="titleMedium" style={styles.sectionTitle}>Employee Status</Text>
        </View>
        {employees.length > 0 ? (
          <FlatList
            data={employees}
            renderItem={renderEmployeeCard}
            keyExtractor={(item) => item.id}
            scrollEnabled={false}
          />
        ) : (
          <View style={styles.emptyState}>
            <LocalIcon name="account-off" size={48} color={theme.colors.onSurfaceVariant} />
            <Text style={styles.emptyStateText}>Add employees to start tracking attendance.</Text>
          </View>
        )}
      </ScrollView>
      {user.role === ROLES.ADMIN && (
        <FAB icon="account-plus" style={styles.fab} onPress={() => navigationService.navigate('AddEmployee')} label="Add Employee" />
      )}
    </View>
  );
};

export default EmployeeAttendanceScreen;