/**
 * ProfitLossScreen - Dedicated Profit & Loss Statement Screen
 * Converted from bottomsheet to full screen for better analytics experience
 */

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { View, ScrollView, StyleSheet, Alert } from 'react-native';
import { Text, Card, Button, Surface, Divider, SegmentedButtons } from 'react-native-paper';
import LocalIcon from '../components/LocalIcon';
import { useTheme } from '../context/ThemeContext';
import { useData } from '../context/DataContext';
import { useFinancial } from '../context/FinancialContext';
import CommonHeader from '../components/CommonHeader';
import { SPACING, BORDER_RADIUS } from '../theme/designTokens';
import navigationService from '../services/NavigationService';
import AnalyticsService from '../services/AnalyticsService';

const ProfitLossScreen = () => {
  const { theme } = useTheme();
  const { state } = useData();
  const { expenses } = useFinancial();
  const [selectedPeriod, setSelectedPeriod] = useState('month');

  // Calculate real profit/loss data using AnalyticsService
  const profitLossData = useMemo(() => {
    return AnalyticsService.calculateProfitLoss(state.orders || [], expenses || [], selectedPeriod);
  }, [state.orders, expenses, selectedPeriod]);

  const formatCurrency = (amount) => {
    return `৳${amount.toLocaleString('en-BD', { minimumFractionDigits: 2 })}`;
  };

  const exportToPDF = () => {
    Alert.alert('Export', 'P&L Statement exported successfully!');
  };

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    periodSelector: {
      margin: SPACING.md,
      padding: SPACING.md,
      borderRadius: BORDER_RADIUS.md,
      backgroundColor: theme.colors.surface,
    },
    content: {
      flex: 1,
      padding: SPACING.md,
    },
    metricsRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: SPACING.lg,
      gap: SPACING.md,
    },
    metricCard: {
      flex: 1,
      borderRadius: BORDER_RADIUS.md,
    },
    metricContent: {
      alignItems: 'center',
      paddingVertical: SPACING.lg,
    },
    section: {
      marginBottom: SPACING.md,
      borderRadius: BORDER_RADIUS.md,
      backgroundColor: theme.colors.surface,
    },
    sectionHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: SPACING.md,
    },
    itemRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: SPACING.xs,
    },
    totalRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: SPACING.sm,
    },
    divider: {
      marginVertical: SPACING.sm,
    },
    exportContainer: {
      marginTop: SPACING.lg,
      marginBottom: SPACING.xl,
    },
    exportButton: {
      borderRadius: BORDER_RADIUS.md,
    },
    profitCard: {
      backgroundColor: theme.colors.tertiary + '15',
    },
    expenseCard: {
      backgroundColor: theme.colors.error + '15',
    },
    summaryCard: {
      backgroundColor: theme.colors.primaryContainer,
    },
    sectionTitle: {
      color: theme.colors.onSurface,
      marginLeft: 8,
    },
    itemLabel: {
      color: theme.colors.onSurface,
      textTransform: 'capitalize',
    },
    totalLabel: {
      color: theme.colors.onSurface,
      fontWeight: '600',
    },
    profitText: {
      color: theme.colors.onSurface,
      marginTop: 8,
    },
    summaryLabel: {
      color: theme.colors.onPrimaryContainer,
    },
    summaryTitle: {
      color: theme.colors.onPrimaryContainer,
      marginLeft: 8,
    },
  }), [theme]);

  const getDynamicStyles = useMemo(() => ({
    netProfitText: {
      color: profitLossData.profit.net >= 0 ? theme.colors.tertiary : theme.colors.error,
      fontWeight: '700',
      marginTop: 4,
    },
    expenseText: {
      color: theme.colors.error,
      fontWeight: '700',
      marginTop: 4,
    },
    netProfitAmount: {
      color: profitLossData.profit.net >= 0 ? '#4CAF50' : '#F44336',
      fontWeight: '700',
    },
    grossProfitAmount: {
      color: profitLossData.profit.gross >= 0 ? '#4CAF50' : '#F44336',
      fontWeight: '500',
    },
    operatingProfitAmount: {
      color: profitLossData.profit.operating >= 0 ? '#4CAF50' : '#F44336',
      fontWeight: '500',
    },
    summaryNetProfit: {
      color: theme.colors.onPrimaryContainer,
      fontWeight: '700',
    },
  }), [profitLossData, theme]);

  const renderSection = (title, icon, data, isExpense = false) => (
    <Card style={styles.section}>
      <Card.Content>
        <View style={styles.sectionHeader}>
          <LocalIcon name={icon} size={24} color={theme.colors.primary} />
          <Text variant="titleMedium" style={styles.sectionTitle}>
            {title}
          </Text>
        </View>
        
        {Object.entries(data).map(([key, value]) => {
          if (key === 'total') return null;
          return (
            <View key={key} style={styles.itemRow}>
              <Text variant="bodyMedium" style={styles.itemLabel}>
                {key.replace(/([A-Z])/g, ' $1').trim()}
              </Text>
              <Text variant="bodyMedium" style={{
                color: isExpense ? '#F44336' : '#4CAF50',
                fontWeight: '500',
              }>
                {formatCurrency(value)}
              </Text>
            </View>
          );
        })}
        
        <Divider style={styles.divider} />
        <View style={styles.totalRow}>
          <Text variant="titleMedium" style={styles.totalLabel}>
            Total {title}
          </Text>
          <Text variant="titleMedium" style={{
            color: isExpense ? '#F44336' : '#4CAF50',
            fontWeight: '600',
          }>
            {formatCurrency(data.total)}
          </Text>
        </View>
      </Card.Content>
    </Card>
  );

  return (
    <View style={styles.container}>
      <CommonHeader
        title="Profit & Loss Statement"
        subtitle="Financial performance overview"
        onNotificationPress={() => console.log('Notifications')}
        onProfilePress={() => navigationService.navigate('MyProfile')}
      />

      {/* Period Selection */}
      <Surface style={styles.periodSelector} elevation={1}>
        <SegmentedButtons
          value={selectedPeriod}
          onValueChange={setSelectedPeriod}
          buttons={[
            { value: 'week', label: 'Week' },
            { value: 'month', label: 'Month' },
            { value: 'quarter', label: 'Quarter' },
            { value: 'year', label: 'Year' },
          }
        />
      </Surface>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Key Metrics Cards */}
        <View style={styles.metricsRow}>
          <Card style={[styles.metricCard, styles.profitCard}>
            <Card.Content style={styles.metricContent}>
              <LocalIcon name="trending-up" size={32} color={theme.colors.tertiary} />
              <Text variant="bodySmall" style={styles.profitText}>
                Net Profit
              </Text>
              <Text variant="headlineSmall" style={getDynamicStyles.netProfitText}>
                {formatCurrency(profitLossData.profit.net)}
              </Text>
            </Card.Content>
          </Card>

          <Card style={[styles.metricCard, styles.expenseCard}>
            <Card.Content style={styles.metricContent}>
              <LocalIcon name="trending-down" size={32} color={theme.colors.error} />
              <Text variant="bodySmall" style={styles.profitText}>
                Total Expenses
              </Text>
              <Text variant="headlineSmall" style={getDynamicStyles.expenseText}>
                {formatCurrency(profitLossData.expenses.total)}
              </Text>
            </Card.Content>
          </Card>
        </View>

        {/* Revenue Section */}
        {renderSection('Revenue', 'trending-up', profitLossData.revenue)}

        {/* Expenses Section */}
        {renderSection('Expenses', 'trending-down', profitLossData.expenses, true)}

        {/* Profit Summary */}
        <Card style={styles.summaryCard}>
          <Card.Content>
            <View style={styles.sectionHeader}>
              <LocalIcon name="chart-line" size={24} color={theme.colors.primary} />
              <Text variant="titleMedium" style={styles.summaryTitle}>
                Profit Summary
              </Text>
            </View>
            
            <View style={styles.itemRow}>
              <Text variant="bodyMedium" style={styles.summaryLabel}>
                Gross Profit
              </Text>
              <Text variant="bodyMedium" style={getDynamicStyles.grossProfitAmount}>
                {formatCurrency(profitLossData.profit.gross)}
              </Text>
            </View>
            
            <View style={styles.itemRow}>
              <Text variant="bodyMedium" style={styles.summaryLabel}>
                Operating Profit
              </Text>
              <Text variant="bodyMedium" style={getDynamicStyles.operatingProfitAmount}>
                {formatCurrency(profitLossData.profit.operating)}
              </Text>
            </View>

            <Divider style={styles.divider} />
            <View style={styles.totalRow}>
              <Text variant="titleLarge" style={styles.summaryNetProfit}>
                Net Profit
              </Text>
              <Text variant="titleLarge" style={getDynamicStyles.netProfitAmount}>
                {formatCurrency(profitLossData.profit.net)}
              </Text>
            </View>
          </Card.Content>
        </Card>

        {/* Export Button */}
        <View style={styles.exportContainer}>
          <Button
            mode="contained"
            onPress={exportToPDF}
            icon="download"
            style={styles.exportButton}>
            Export Statement
          </Button>
        </View>
      </ScrollView>
    </View>
  );
};

export default ProfitLossScreen;