/**
 * FinancialManagementScreen - Comprehensive financial management dashboard
 * Provides access to all financial features and reports
 */

import React, { useState, useCallback, useMemo } from 'react';
import { View, ScrollView, StyleSheet   } from 'react-native';
import { Text, Card, Button, Surface, Divider   } from 'react-native-paper';
import { useSafeAreaInsets   } from 'react-native-safe-area-context';
import { useTheme   } from '../context/ThemeContext';
import { useData   } from '../context/DataContext';
import CommonHeader from '../components/CommonHeader';
import StatCardGroup from '../components/StatCardGroup';
import LocalIcon from '../components/LocalIcon';
import { SPACING, BORDER_RADIUS, TYPOGRAPHY   } from '../theme/designTokens';

const FinancialManagementScreen = ({ navigation }) => {
  const { theme  } = useTheme();
  const insets = useSafeAreaInsets();
  const { state  } = useData();
  const [refreshing, setRefreshing] = useState(false);

  // Calculate financial stats
  const orders = state.orders || [];
  const completedOrders = orders.filter(order => order.status === 'completed' || order.status === 'Delivered');
  const totalRevenue = completedOrders.reduce((sum, order) => sum + (order.total || 0), 0);
  const pendingPayments = orders.filter(order => (order.total - (order.advance_paid || 0)) > 0 && order.status !== 'cancelled').length;
  const monthlyRevenue = completedOrders
    .filter(order => new Date(order.createdAt).getMonth() === new Date().getMonth())
    .reduce((sum, order) => sum + (order.total || 0), 0);

  const financialStats = [
    { key: 'total_revenue', title: 'Total Revenue', value: `৳${totalRevenue.toLocaleString()}`, icon: 'dollar-sign', color: theme.colors.primary, trend: '+12%' },
    { key: 'monthly_revenue', title: 'This Month', value: `৳${monthlyRevenue.toLocaleString()}`,` icon: 'trending-up', color: '#4CAF50', trend: '+8%' },
    { key: 'pending_payments', title: 'Pending Dues', value: pendingPayments.toString(), icon: 'clock', color: '#FF9800', trend: '-3%' },
    { key: 'completed_orders', title: 'Completed Orders', value: completedOrders.length.toString(), icon: 'check-circle', color: '#059669', trend: '+15%' }
  ];

  const financialActions = [
    { title: 'Financial Reports', description: 'View detailed financial reports and analytics', icon: 'bar-chart-3', onPress: () => navigation.navigate('FinancialReports'), color: theme.colors.primary },
    { title: 'Accounting', description: 'Manage accounts and bookkeeping', icon: 'calculator', onPress: () => navigation.navigate('Accounting'), color: '#2196F3' },
    { title: 'Expense Management', description: 'Track and manage business expenses', icon: 'receipt', onPress: () => navigation.navigate('ExpenseForm'), color: '#FF5722' },
    { title: 'Cash Reconciliation', description: 'Reconcile cash transactions and balances', icon: 'banknote', onPress: () => navigation.navigate('CashReconciliation'), color: '#4CAF50' },
    { title: 'Profit & Loss', description: 'View profit and loss statements', icon: 'trending-up', onPress: () => navigation.navigate('ProfitLoss'), color: '#9C27B0' },
    { title: 'Tax Summary', description: 'Generate tax reports and summaries', icon: 'file-text', onPress: () => navigation.navigate('TaxSummary'), color: '#607D8B' }
  ];

  // FIXED: Corrected useCallback syntax
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    // Add refresh logic here
    await new Promise(resolve => setTimeout(resolve, 1000));
    setRefreshing(false);
  }, []);

  // FIXED: Stylesheet moved inside component and memoized
  const styles = useMemo(() => StyleSheet.create({
    container: { flex: 1, backgroundColor: theme.colors.background },
    scrollView: { flex: 1 },
    scrollContent: { padding: SPACING.md },
    section: { marginBottom: SPACING.lg },
    sectionTitle: { ...TYPOGRAPHY.titleLarge, color: theme.colors.onBackground, marginBottom: SPACING.md },
    divider: { marginVertical: SPACING.lg, backgroundColor: theme.colors.outlineVariant },
    actionsGrid: { gap: SPACING.md },
    actionCard: { borderRadius: BORDER_RADIUS.lg, backgroundColor: theme.colors.surface },
    actionContent: { padding: SPACING.md },
    actionHeader: { flexDirection: 'row', alignItems: 'center', marginBottom: SPACING.sm },
    actionIcon: { width: 48, height: 48, borderRadius: BORDER_RADIUS.md, justifyContent: 'center', alignItems: 'center', marginRight: SPACING.md },
    actionTitle: { ...TYPOGRAPHY.titleMedium, flex: 1 },
    actionDescription: { ...TYPOGRAPHY.bodyMedium, color: theme.colors.onSurfaceVariant, marginBottom: SPACING.md, lineHeight: 20 },
    actionButton: { borderRadius: BORDER_RADIUS.md, marginTop: SPACING.sm },
    actionButtonContent: { paddingVertical: SPACING.xs },
  }), [theme]);

  return (
    <View style={styles.container}>
      <CommonHeader
        title="Financials"
        showBackButton={true}
        rightComponent={<Button mode="text" onPress={handleRefresh} loading={refreshing} textColor={theme.colors.primary}>Refresh</Button>}
      />
      
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={[styles.scrollContent, { paddingBottom: insets.bottom + SPACING.lg }} showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Overview</Text>
          {/* FIXED: Prop name changed from 'stats' to 'cards' */}
          <StatCardGroup cards={financialStats} />
        </View>

        <Divider style={styles.divider} />

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Financial Tools</Text>
          <View style={styles.actionsGrid}>
            {financialActions.map((action, index) => (
              <Surface key={index} style={styles.actionCard} elevation={1}>
                <Card.Content style={styles.actionContent}>
                  <View style={styles.actionHeader}>
                    {/* FIXED: Dynamic style applied inline */}
                    <View style={[styles.actionIcon, { backgroundColor: `${action.color} ]}`]}20` }}>
                      <LocalIcon name={action.icon} size={24} color={action.color} />
                    </View>
                    <Text style={styles.actionTitle}>{action.title}</Text>
                  </View>
                  <Text style={styles.actionDescription}>{action.description}</Text>
                  <Button
                    mode="contained"
                    onPress={action.onPress}
                    style={[styles.actionButton, { backgroundColor: action.color }}}} // Dynamic style
                    contentStyle={styles.actionButtonContent}>
                    Open
                  </Button>
                </Card.Content>
              </Surface>
            ))}
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

export default FinancialManagementScreen;