/**
 * UnifiedSettingsScreen - Consolidated settings with tabbed interface
 * Replaces: AdminSettingsScreen, BusinessSettingsScreen, SecuritySettingsScreen, 
 * ThemeSettingsScreen, NotificationSettingsScreen, PDFInvoiceSettingsScreen
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { View, ScrollView, StyleSheet, Alert, KeyboardAvoidingView, Platform   } from 'react-native';
// FIXED: Added all necessary imports from react-native-paper
import { Text, Surface, TouchableRipple, Divider, Switch, Button, TextInput, RadioButton, Card   } from 'react-native-paper';
import LocalIcon from '../components/LocalIcon';
import { useSafeAreaInsets   } from 'react-native-safe-area-context';
import { useTheme   } from '../context/ThemeContext';
import { useData   } from '../context/DataContext';
import { useAuth   } from '../context/AuthContext';
import CommonHeader from '../components/CommonHeader';
import ProtectedRoute from '../components/ProtectedRoute';
import { SPACING, BORDER_RADIUS   } from '../theme/designTokens';
import { DEFAULT_MEASUREMENT_UNIT   } from '../config/constants';
import AsyncStorage from '@react-native-async-storage/async-storage';
import PDFInvoiceService from '../services/PDFInvoiceService';
import EnhancedInput from '../components/EnhancedInput';
import EnhancedButton from '../components/EnhancedButton';
import CompactMeasurementUnitSelector from '../components/CompactMeasurementUnitSelector';

const SETTINGS_TABS = [
  { value: 'general', label: 'General', icon: 'cog' },
  { value: 'business', label: 'Business', icon: 'store' },
  { value: 'appearance', label: 'Theme', icon: 'palette' },
  { value: 'security', label: 'Security', icon: 'shield-lock' },
  { value: 'notifications', label: 'Alerts', icon: 'bell' },
  { value: 'admin', label: 'Admin', icon: 'account-supervisor' }];
const UnifiedSettingsScreen = ({ navigation }) => {
  const { theme, isDarkMode, toggleTheme  } = useTheme();
  const { state, actions  } = useData();
  const { isAdmin  } = useAuth();
  
  const [activeTab, setActiveTab] = useState('general');
  const [settings, setSettings] = useState({
    measurementUnit: state.settings?.measurementUnit || DEFAULT_MEASUREMENT_UNIT,
    currency: state.settings?.currency || 'BDT',
    taxRate: state.settings?.taxRate || 0.15,
    defaultFittings: state.settings?.defaultFittings || 2,
    rushOrderSurcharge: state.settings?.rushOrderSurcharge || 50,
    alterationDiscount: state.settings?.alterationDiscount || 20,
    loyaltyDiscount: state.settings?.loyaltyDiscount || 10,
    autoBackup: state.settings?.autoBackup || true,
    businessName: 'Tailora', tagline: 'Premium Tailoring Services', address: '123 Fashion Street, Textile City',
    phone: '+91 98765 43210', email: '<EMAIL>', website: 'www.tailora.com', gst: 'GST123456789',
    currentPassword: '', newPassword: '', confirmPassword: '',
    notifications: state.settings?.notifications || true,
    orderNotifications: true, paymentNotifications: true, reminderNotifications: true});
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const loadAllSettings = async () => {
      try {
        const businessInfo = await AsyncStorage.getItem('businessInfo');
        if (businessInfo) setSettings(prev => ({ ...prev, ...JSON.parse(businessInfo) }));
      } catch (error) { console.error('Failed to load settings:', error); }
    };
    loadAllSettings();
  }, []);

  // FIXED: Corrected useCallback syntax and dependencies
  const handleSave = useCallback(async () => {
    setLoading(true);
    try {
      await actions.updateSettings(settings);
      const businessInfo = { name: settings.businessName, tagline: settings.tagline, address: settings.address, phone: settings.phone, email: settings.email, website: settings.website, gst: settings.gst };
      await AsyncStorage.setItem('businessInfo', JSON.stringify(businessInfo));
      PDFInvoiceService.updateBusinessInfo(businessInfo);
      Alert.alert('Success', 'Settings saved successfully!');
    } catch (error) {
      console.error('Failed to save settings:', error);
      Alert.alert('Error', 'Failed to save settings. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [actions, settings]);

  const updateSetting = (key, value) => setSettings(prev => ({ ...prev, [key]: value }));

  const getAvailableTabs = () => SETTINGS_TABS.filter(tab => tab.value !== 'admin' || isAdmin);

  // FIXED: Stylesheet moved inside component and memoized
  const styles = useMemo(() => StyleSheet.create({
      container: { flex: 1, backgroundColor: theme.colors.background },
      content: { flex: 1, padding: SPACING.lg },
      section: { marginBottom: SPACING.lg },
      sectionTitle: { ...TYPOGRAPHY.titleLarge, marginBottom: SPACING.md, color: theme.colors.onSurface },
      settingsCard: { marginBottom: SPACING.lg, backgroundColor: theme.colors.surface },
      settingItem: { marginBottom: SPACING.md },
      switchRow: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', paddingVertical: SPACING.sm },
      switchTextContainer: { flex: 1, marginRight: SPACING.md },
      settingTitle: { ...TYPOGRAPHY.titleMedium, color: theme.colors.onSurface },
      settingDescription: { ...TYPOGRAPHY.bodyMedium, color: theme.colors.onSurfaceVariant, marginTop: SPACING.xs },
      input: { marginBottom: SPACING.md },
      divider: { marginVertical: SPACING.md },
      radioRow: { flexDirection: 'row', justifyContent: 'space-around', marginTop: SPACING.sm },
      actionContainer: { padding: SPACING.md, borderTopWidth: 1, borderTopColor: theme.colors.outlineVariant, backgroundColor: theme.colors.surface },
      buttonRow: { flexDirection: 'row', gap: SPACING.md },
      flexButton: { flex: 1 },
      tabContainer: { backgroundColor: theme.colors.surface, elevation: 2 },
      tabScrollContent: { paddingHorizontal: SPACING.sm, paddingVertical: SPACING.xs },
      tab: { flexDirection: 'row', alignItems: 'center', marginHorizontal: SPACING.xs, borderRadius: BORDER_RADIUS.lg, paddingHorizontal: SPACING.md, paddingVertical: SPACING.sm },
      tabLabel: { marginLeft: SPACING.sm, ...TYPOGRAPHY.labelLarge }}), [theme]);

  const renderGeneralSettings = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>General Settings</Text>
      <Card style={styles.settingsCard}>
        <Card.Content>
          <View style={styles.switchRow}><View style={styles.switchTextContainer}><Text style={styles.settingTitle}>Measurement Unit</Text><Text style={styles.settingDescription}>Default unit for measurements</Text></View><CompactMeasurementUnitSelector selectedUnit={settings.measurementUnit} onUnitChange={unit => updateSetting('measurementUnit', unit)} /></View>
          <Divider style={styles.divider} />
          <View style={styles.settingItem}><Text style={styles.settingTitle}>Currency</Text><RadioButton.Group onValueChange={value => updateSetting('currency', value)} value={settings.currency}><View style={styles.radioRow}><RadioButton.Item label="BDT (৳)" value="BDT" /><RadioButton.Item label="USD ($)" value="USD" /><RadioButton.Item label="EUR (€)" value="EUR" /></View></RadioButton.Group></View>
          <Divider style={styles.divider} />
          <View style={styles.settingItem}><Text style={styles.settingTitle}>Tax Rate (%)</Text><TextInput mode="outlined" value={String(settings.taxRate)} onChangeText={text => updateSetting('taxRate', parseFloat(text) || 0)} keyboardType="numeric" style={styles.input} /></View>
          <Divider style={styles.divider} />
          <View style={styles.switchRow}><View style={styles.switchTextContainer}><Text style={styles.settingTitle}>Auto Backup</Text><Text style={styles.settingDescription}>Automatically backup data daily</Text></View><Switch value={settings.autoBackup} onValueChange={value => updateSetting('autoBackup', value)} /></View>
        </Card.Content>
      </Card>
    </View>
  );

  const renderBusinessSettings = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Business Information</Text>
      <Card style={styles.settingsCard}>
        <Card.Content>
          <EnhancedInput label="Business Name *" value={settings.businessName} onChangeText={text => updateSetting('businessName', text)} error={errors.businessName} style={styles.input} />
          <EnhancedInput label="Tagline" value={settings.tagline} onChangeText={text => updateSetting('tagline', text)} style={styles.input} />
          <EnhancedInput label="Address *" value={settings.address} onChangeText={text => updateSetting('address', text)} multiline numberOfLines={3} error={errors.address} style={styles.input} />
          <EnhancedInput label="Phone Number *" value={settings.phone} onChangeText={text => updateSetting('phone', text)} keyboardType="phone-pad" error={errors.phone} style={styles.input} />
          <EnhancedInput label="Email Address *" value={settings.email} onChangeText={text => updateSetting('email', text)} keyboardType="email-address" autoCapitalize="none" error={errors.email} style={styles.input} />
          <EnhancedInput label="Website" value={settings.website} onChangeText={text => updateSetting('website', text)} keyboardType="url" autoCapitalize="none" style={styles.input} />
          <EnhancedInput label="GST Number" value={settings.gst} onChangeText={text => updateSetting('gst', text)} style={styles.input} />
        </Card.Content>
      </Card>
    </View>
  );

  const renderAppearanceSettings = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Appearance & Theme</Text>
      <Card style={styles.settingsCard}>
        <Card.Content>
          <View style={styles.switchRow}><View style={styles.switchTextContainer}><Text style={styles.settingTitle}>Dark Mode</Text><Text style={styles.settingDescription}>Use dark theme for better visibility in low light</Text></View><Switch value={isDarkMode} onValueChange={toggleTheme} /></View>
        </Card.Content>
      </Card>
    </View>
  );

  const renderSecuritySettings = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Security & Privacy</Text>
      <Card style={styles.settingsCard}>
        <Card.Content>
          <Text style={styles.settingTitle}>Change Password</Text>
          <EnhancedInput label="Current Password" value={settings.currentPassword} onChangeText={text => updateSetting('currentPassword', text)} secureTextEntry error={errors.currentPassword} style={styles.input} />
          <EnhancedInput label="New Password" value={settings.newPassword} onChangeText={text => updateSetting('newPassword', text)} secureTextEntry error={errors.newPassword} style={styles.input} />
          <EnhancedInput label="Confirm New Password" value={settings.confirmPassword} onChangeText={text => updateSetting('confirmPassword', text)} secureTextEntry error={errors.confirmPassword} style={styles.input} />
          <Button mode="outlined" onPress={() => Alert.alert('Password Changed', 'Your password has been updated.')} style={{ marginTop: SPACING.md }>Update Password</Button>
        </Card.Content>
      </Card>
    </View>
  );

  const renderNotificationSettings = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Notifications & Alerts</Text>
      <Card style={styles.settingsCard}>
        <Card.Content>
          <View style={styles.switchRow}><View style={styles.switchTextContainer}><Text style={styles.settingTitle}>All Notifications</Text><Text style={styles.settingDescription}>Enable or disable all notifications</Text></View><Switch value={settings.notifications} onValueChange={value => updateSetting('notifications', value)} /></View>
          <Divider style={styles.divider} />
          <View style={styles.switchRow}><View style={styles.switchTextContainer}><Text style={styles.settingTitle}>Order Notifications</Text><Text style={styles.settingDescription}>New orders, status updates, delivery reminders</Text></View><Switch value={settings.orderNotifications} onValueChange={value => updateSetting('orderNotifications', value)} disabled={!settings.notifications} /></View>
          <Divider style={styles.divider} />
          <View style={styles.switchRow}><View style={styles.switchTextContainer}><Text style={styles.settingTitle}>Payment Notifications</Text><Text style={styles.settingDescription}>Payment received, pending payments</Text></View><Switch value={settings.paymentNotifications} onValueChange={value => updateSetting('paymentNotifications', value)} disabled={!settings.notifications} /></View>
          <Divider style={styles.divider} />
          <View style={styles.switchRow}><View style={styles.switchTextContainer}><Text style={styles.settingTitle}>Reminder Notifications</Text><Text style={styles.settingDescription}>Appointment reminders, follow-ups</Text></View><Switch value={settings.reminderNotifications} onValueChange={value => updateSetting('reminderNotifications', value)} disabled={!settings.notifications} /></View>
        </Card.Content>
      </Card>
    </View>
  );

  const renderAdminSettings = () => (
    <ProtectedRoute role="admin" showAccessDenied>
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Admin Configuration</Text>
        <Card style={styles.settingsCard}>
          <Card.Content>
            <Text style={styles.settingTitle}>Business Rules</Text>
            <EnhancedInput label="Default Fittings" value={String(settings.defaultFittings)} onChangeText={text => updateSetting('defaultFittings', parseInt(text) || 2)} keyboardType="numeric" style={styles.input} />
            <EnhancedInput label="Rush Order Surcharge (%)" value={String(settings.rushOrderSurcharge)} onChangeText={text => updateSetting('rushOrderSurcharge', parseFloat(text) || 50)} keyboardType="numeric" style={styles.input} />
            <EnhancedInput label="Alteration Discount (%)" value={String(settings.alterationDiscount)} onChangeText={text => updateSetting('alterationDiscount', parseFloat(text) || 20)} keyboardType="numeric" style={styles.input} />
            <EnhancedInput label="Loyalty Discount (%)" value={String(settings.loyaltyDiscount)} onChangeText={text => updateSetting('loyaltyDiscount', parseFloat(text) || 10)} keyboardType="numeric" style={styles.input} />
          </Card.Content>
        </Card>
      </View>
    </ProtectedRoute>
  );

  const renderTabContent = () => {
    switch(activeTab) {
      case 'general': return renderGeneralSettings(); case 'business': return renderBusinessSettings();
      case 'appearance': return renderAppearanceSettings(); case 'security': return renderSecuritySettings();
      case 'notifications': return renderNotificationSettings(); case 'admin': return renderAdminSettings();
      default: return renderGeneralSettings();
    }
  };

  return (
    <KeyboardAvoidingView style={styles.container} behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
      <CommonHeader title="Settings" subtitle="Manage your preferences" showBackButton onBackPress={() => navigation.goBack()} />
      <Surface style={styles.tabContainer} elevation={2}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.tabScrollContent}>
          {getAvailableTabs().map(tab => {
            const isTabActive = activeTab === tab.value;
            return (
              <TouchableRipple key={tab.value} onPress={() => setActiveTab(tab.value)} style={[styles.tab, { backgroundColor: isTabActive ? theme.colors.primaryContainer : 'transparent' }}}}>
                <>
                  <LocalIcon name={tab.icon} size={20} color={isTabActive ? theme.colors.primary : theme.colors.onSurfaceVariant} />
                  <Text style={[styles.tabLabel, { color: isTabActive ? theme.colors.primary : theme.colors.onSurfaceVariant }}}}>{tab.label}</Text>
                </>
              </TouchableRipple>
            );
          })}
        </ScrollView>
      </Surface>
      <ScrollView contentContainerStyle={styles.content} showsVerticalScrollIndicator={false}>
        {renderTabContent()}
      </ScrollView>
      <Surface style={styles.actionContainer} elevation={4}>
        <View style={styles.buttonRow}>
          <EnhancedButton variant="outline" title="Cancel" onPress={() => navigation.goBack()} style={styles.flexButton} />
          <EnhancedButton variant="primary" title="Save Changes" onPress={handleSave} style={styles.flexButton} disabled={loading} loading={loading} />
        </View>
      </Surface>
    </KeyboardAvoidingView>
  );
};

export default UnifiedSettingsScreen;