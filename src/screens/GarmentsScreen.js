/**
 * GarmentsScreen - Manage clothing items and garments for tailoring business
 * Adapted from ProductsScreen for tailor management system
 */

import React, { useState, useMemo, useCallback } from 'react';
import { View, ScrollView, StyleSheet, Alert, RefreshControl } from 'react-native';
import { Text, Searchbar, Chip, FAB, IconButton, Menu } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { useData } from '../context/DataContext';
import { useTheme } from '../context/ThemeContext';
import { SPACING } from '../theme/designTokens';
import UniversalCard from '../components/UniversalCard';
import StatCardGroup from '../components/StatCardGroup';
import UnifiedEmptyState from '../components/UnifiedEmptyState';
import CommonHeader from '../components/CommonHeader';
import navigationService from '../services/NavigationService';

const GarmentsScreen = () => {
  const navigation = useNavigation();
  const { state, actions } = useData();
  const { theme } = useTheme();

  const garments = state.garments || state.products || [];

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('All');
  const [menuVisible, setMenuVisible] = useState({});
  const [refreshing, setRefreshing] = useState(false);
  const [selectedSort, setSelectedSort] = useState({ key: 'createdAt', direction: 'desc' });

  const filters = ['All', 'Shirts', 'Pants', 'Suits', 'Dresses', 'Accessories', 'Low Stock'];
  const sortOptions = [
    { key: 'name', label: 'Name' }, { key: 'category', label: 'Category' },
    { key: 'price', label: 'Price' }, { key: 'stock', label: 'Stock' },
    { key: 'createdAt', label: 'Date Added' },
  ];

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    // In a real app, this would refetch data, e.g., await actions.fetchGarments();
    await new Promise(resolve => setTimeout(resolve, 1000));
    setRefreshing(false);
  }, []);

  const filteredGarments = useMemo(() => {
    let filtered = garments.filter(garment => {
      const searchLower = searchQuery.toLowerCase();
      const matchesSearch = garment.name?.toLowerCase().includes(searchLower) ||
                           garment.description?.toLowerCase().includes(searchLower) ||
                           garment.category?.toLowerCase().includes(searchLower);
      if (selectedFilter === 'All') return matchesSearch;
      if (selectedFilter === 'Low Stock') return matchesSearch && (garment.stock || 0) <= 5;
      return matchesSearch && garment.category === selectedFilter;
    });

    return [...filtered].sort((a, b) => {
      let aValue = a[selectedSort.key];
      let bValue = b[selectedSort.key];
      if (['price', 'stock'].includes(selectedSort.key)) {
        aValue = parseFloat(aValue) || 0;
        bValue = parseFloat(bValue) || 0;
      } else if (selectedSort.key === 'createdAt') {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      } else {
        aValue = String(aValue || '').toLowerCase();
        bValue = String(bValue || '').toLowerCase();
      }
      if (selectedSort.direction === 'asc') return aValue > bValue ? 1 : -1;
      return aValue < bValue ? 1 : -1;
    });
  }, [garments, searchQuery, selectedFilter, selectedSort]);

  const stats = useMemo(() => ({
    totalGarments: garments.length,
    totalStock: garments.reduce((sum, g) => sum + (g.stock || 0), 0),
    lowStockItems: garments.filter(g => (g.stock || 0) <= 5).length,
  }), [garments]);

  const handleDeleteGarment = useCallback((garment) => {
    Alert.alert('Delete Garment', `Are you sure you want to delete "${garment.name}"?`, [
      { text: 'Cancel', style: 'cancel' },
      { text: 'Delete', style: 'destructive', onPress: async () => {
          try {
            await actions.deleteProduct(garment.id); // Assuming deleteProduct handles both
          } catch (error) {
            console.error('Error deleting garment:', error);
            Alert.alert('Error', 'Failed to delete garment.');
          }
      }},
    ]);
  }, [actions]);

  const handleEditGarment = useCallback((garment) => {
    navigationService.navigate('AddProduct', { garment, isEditing: true, title: 'Edit Garment' });
  }, []);

  return (
    // FIXED: Using the correct 'container' style
    <View style={styles.container}>
      <CommonHeader title="Garments" subtitle={`${filteredGarments.length} items`} />
      
      {/* Search Bar is outside ScrollView for fixed position */}
      <View style={styles.searchSection}>
        <Searchbar
          placeholder="Search garments..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchbar}
        />
      </View>

      {/* Filter and Sort are also fixed */}
      <View style={styles.filterSortContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterScrollView}>
          {filters.map(filter => (
            <Chip
              key={filter}
              selected={selectedFilter === filter}
              onPress={() => setSelectedFilter(filter)}
              style={styles.filterChip}
            >
              {filter}
            </Chip>
          ))}
        </ScrollView>
        <Menu
          visible={!!menuVisible.sort}
          onDismiss={() => setMenuVisible(prev => ({ ...prev, sort: false }))}
          anchor={<IconButton icon="sort" size={24} onPress={() => setMenuVisible(prev => ({ ...prev, sort: true }))} style={styles.sortButton} />}
        >
          {sortOptions.map(option => (
            <Menu.Item
              key={option.key}
              onPress={() => {
                setSelectedSort(prev => ({ key: option.key, direction: prev.key === option.key && prev.direction === 'asc' ? 'desc' : 'asc' }));
                setMenuVisible(prev => ({ ...prev, sort: false }));
              }}
              title={`${option.label} ${selectedSort.key === option.key ? (selectedSort.direction === 'asc' ? '↑' : '↓') : ''}`}
            />
          ))}
        </Menu>
      </View>

      <ScrollView
        // FIXED: Using the correct 'content' style
        contentContainerStyle={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={handleRefresh} colors={[theme.colors.primary]} />}
      >
        <StatCardGroup
          cards={[
            { key: 'garments', title: 'Total Garments', value: stats.totalGarments.toString(), icon: 'tshirt-crew' },
            { key: 'stock', title: 'Total Stock', value: stats.totalStock.toString(), icon: 'package-variant' },
            { key: 'low-stock', title: 'Low Stock', value: stats.lowStockItems.toString(), icon: 'alert-circle', iconColor: stats.lowStockItems > 0 ? theme.colors.error : theme.colors.tertiary },
          ]}
        />
        <Text variant="titleMedium" style={styles.sectionTitle}>
          All Garments
        </Text>
        {filteredGarments.length === 0 ? (
          <UnifiedEmptyState
            icon="hanger"
            title={searchQuery || selectedFilter !== 'All' ? 'No Garments Found' : 'No Garments Yet'}
            subtitle={searchQuery || selectedFilter !== 'All' ? 'Try adjusting your search or filters.' : 'Add your first garment to get started.'}
          />
        ) : (
          <View style={styles.garmentsList}>
            {filteredGarments.map((garment) => (
              <UniversalCard
                key={garment.id}
                type="product"
                data={garment}
                onPress={() => handleEditGarment(garment)}
                onDelete={() => handleDeleteGarment(garment)}
                style={styles.cardStyle}
              />
            ))}
          </View>
        )}
      </ScrollView>

      <FAB
        icon="plus"
        label="Add Garment"
        onPress={() => navigationService.navigate('AddProduct', { title: 'Add Garment', isGarment: true })}
        style={styles.fullWidthFab}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5', // Should use theme.colors.background
  },
  content: {
    paddingHorizontal: SPACING.md,
    paddingBottom: 100, // For FAB
  },
  searchSection: {
    paddingHorizontal: SPACING.md,
    paddingTop: SPACING.md,
    paddingBottom: SPACING.sm,
  },
  searchbar: {
    elevation: 2,
    borderRadius: 12,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: SPACING.md,
    marginTop: SPACING.lg,
    paddingHorizontal: SPACING.xs, // Align with cards
  },
  filterSortContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.md,
    marginBottom: SPACING.sm,
  },
  filterScrollView: {
    flex: 1,
    marginRight: SPACING.sm,
  },
  filterChip: {
    marginRight: SPACING.sm,
  },
  sortButton: {
    margin: 0,
    backgroundColor: '#FFFFFF', // Should use theme.colors.surface
    borderRadius: 12,
  },
  garmentsList: {
    gap: SPACING.md,
  },
  fullWidthFab: {
    position: 'absolute',
    left: SPACING.md,
    right: SPACING.md,
    bottom: SPACING.md,
    borderRadius: 16,
  },
  cardStyle: {
    // No margin needed if using gap in container
  },
});

export default GarmentsScreen;