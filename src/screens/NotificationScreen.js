/**
 * NotificationScreen - Fully functional notifications display
 */

import React, { useState, useEffect, useCallback } from 'react';
import { View, ScrollView, StyleSheet, RefreshControl, TouchableOpacity   } from 'react-native';
import { Text   } from 'react-native-paper';
import { Button   } from 'react-native-paper';
import { Surface   } from 'react-native-paper';
import { IconButton   } from 'react-native-paper';
import { Chip   } from 'react-native-paper';
import { Badge   } from 'react-native-paper';
import LocalIcon from '../components/LocalIcon';
import { useTheme   } from '../context/ThemeContext';
import { useData   } from '../context/DataContext';
import CommonHeader from '../components/CommonHeader';
import { SPACING, BORDER_RADIUS   } from '../theme/designTokens';
import navigationService from '../services/NavigationService';
import { NotificationService, useNotifications   } from '../services/notificationService';

const NotificationScreen = ({ navigation }) => {
  const { theme  } = useTheme();
  const { state  } = useData();
  const { notifications, unreadCount, markAsRead, markAllAsRead  } = useNotifications();
  
  const [refreshing, setRefreshing] = useState(false);
  const [filter, setFilter] = useState('all'); // all, unread, orders, customers, system
  const [selectedNotifications, setSelectedNotifications] = useState(new Set());
  const [selectionMode, setSelectionMode] = useState(false);

  // Filter notifications to show only order and low stock related
  const filteredNotifications = notifications.filter(notification => {
    // Only show order and low stock notifications
    const isOrderRelated = notification.type?.includes('order') || notification.type?.includes('ORDER');
    const isStockRelated = notification.type?.includes('stock') || notification.type?.includes('STOCK') ||
                          notification.title?.toLowerCase().includes('stock') ||
                          notification.message?.toLowerCase().includes('stock');

    // Base filter: only order and stock notifications
    if (!isOrderRelated && !isStockRelated) return false;

    // Apply secondary filter
    if (filter === 'unread') return !notification.read;
    if (filter === 'orders') return isOrderRelated;
    if (filter === 'stock') return isStockRelated;
    return true; // 'all' case
  });

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      // Trigger notification check
      await NotificationService.checkTailorNotifications(state);

    } catch (error) {
      console.error('Failed to refresh notifications:', error);
    } finally {
      setRefreshing(false);
    }
  }, [state]);

  const handleNotificationPress = useCallback((notification) => {
    if(selectionMode) {
      const newSelected = new Set(selectedNotifications);
      if (newSelected.has(notification.id)) {
        newSelected.delete(notification.id);
      } else {
        newSelected.add(notification.id);
      }
      setSelectedNotifications(newSelected);
      return;
    }

    // Mark as read
    if(!notification.read) {
      markAsRead(notification.id);
    }

    // Handle notification action
    if(notification.data?.action) {
      notification.data.action();
    } else {
      // Default navigation based on notification type
      handleNotificationNavigation(notification);
    }
  }, [selectionMode, selectedNotifications, markAsRead]);

  const handleNotificationNavigation = useCallback((notification) => {
    const { type, data  } = notification;
    
    try {
      if (type?.includes('order') || type?.includes('ORDER')) {
        if(data?.orderId) {
          navigationService.navigate('OrderDetails', { orderId: data.orderId });
        } else {
          navigationService.navigate('Orders');
        }
      } else if (type?.includes('customer') || type?.includes('CUSTOMER')) {
        if(data?.customerId) {
          navigationService.navigate('CustomerDetails', { customerId: data.customerId });
        } else {
          navigationService.navigate('Customers');
        }
      } else if (type?.includes('appointment') || type?.includes('APPOINTMENT')) {
        navigationService.navigate('Appointments');
      } else if (type?.includes('payment') || type?.includes('PAYMENT')) {
        navigationService.navigate('PaymentAnalytics');
      } else {

      }
    } catch (error) {
      console.error('Navigation error:', error);
    }
  }, []);

  const handleLongPress = useCallback((notification) => {
    setSelectionMode(true);
    setSelectedNotifications(new Set([notification.id]));
  }, []);

  const handleMarkSelectedAsRead = useCallback(() => {
    selectedNotifications.forEach(id => markAsRead(id));
    setSelectedNotifications(new Set());
    setSelectionMode(false);
  }, [selectedNotifications, markAsRead]);

  const handleDeleteSelected = useCallback(async () => {
    try {
      for(const id of selectedNotifications) {
        await NotificationService.deleteNotification(id);
      }
      setSelectedNotifications(new Set());
      setSelectionMode(false);
    } catch (error) {
      console.error('Failed to delete notifications:', error);
    }
  }, [selectedNotifications]);

  const getNotificationIcon = (type) => {
    if (type?.includes('order') || type?.includes('ORDER')) return 'clipboard-list';
    if (type?.includes('customer') || type?.includes('CUSTOMER')) return 'account';
    if (type?.includes('appointment') || type?.includes('APPOINTMENT')) return 'calendar';
    if (type?.includes('payment') || type?.includes('PAYMENT')) return 'credit-card';
    if (type?.includes('system') || type?.includes('SYSTEM')) return 'cog';
    return 'bell';
  };

  const getNotificationColor = (type, priority) => {
    if (priority === 'HIGH' || priority === 'URGENT') return theme.colors.error;
    if (type?.includes('order')) return theme.colors.primary;
    if (type?.includes('customer')) return theme.colors.secondary;
    if (type?.includes('payment')) return '#4CAF50';
    return theme.colors.onSurfaceVariant;
  };

  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / ********);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}`} m ago`;
    if (diffHours < 24) return `${diffHours}`} h ago`;
    if (diffDays < 7) return `${diffDays}`} d ago`;
    return date.toLocaleDateString();
  };

  const renderNotificationItem = (notification) => {
    const isSelected = selectedNotifications.has(notification.id);
    const iconName = getNotificationIcon(notification.type);
    const iconColor = getNotificationColor(notification.type, notification.priority);

    return (
      <TouchableOpacity
        key={notification.id}
        onPress={() => handleNotificationPress(notification)}
        onLongPress={() => handleLongPress(notification)}
        style={[
          styles.notificationItem,
          {
            backgroundColor: notification.read 
              ? theme.colors.surface 
              : theme.colors.primaryContainer,
            borderColor: isSelected ? theme.colors.primary : theme.colors.outline,
            borderWidth: isSelected ? 2 : 1,
          }
        }>
        <View style={styles.notificationContent}>
          <View style={styles.notificationHeader}>
            <View style={styles.notificationIcon}>
              <LocalIcon name={iconName} size={24} color={iconColor} />
              {!notification.read && (
                <View style={[styles.unreadDot, { backgroundColor: theme.colors.primary }]}/>
              )}
            </View>
            
            <View style={styles.notificationInfo}>
              <Text 
                variant="bodyLarge" 
                style={[styles.notificationTitle, { 
                  color: theme.colors.onSurface,
                  fontWeight: notification.read ? '400' : '600',
                }]}numberOfLines={1}>
                {notification.title}
              </Text>
              <Text 
                variant="bodyMedium" 
                style={[styles.notificationMessage, { color: theme.colors.onSurfaceVariant }]}numberOfLines={2}>
                {notification.message}
              </Text>
            </View>

            <View style={styles.notificationMeta}>
              <Text variant="bodySmall" style={[styles.notificationTime, { color: theme.colors.onSurfaceVariant }]}>
                {formatTimestamp(notification.timestamp)}
              </Text>
              {notification.priority === 'HIGH' || notification.priority === 'URGENT' ? (
                <Chip
                  mode="flat"
                  compact
                  style={[styles.priorityChip, { backgroundColor: theme.colors.errorContainer, marginTop: 4 }]}textStyle={{ color: theme.colors.onErrorContainer, fontSize: 10 }>
                  {notification.priority}
                </Chip>
              ) : null}
            </View>
          </View>

          {/* Split Buttons for Order Details */}
          {(notification.type?.includes('order') || notification.type?.includes('ORDER')) && notification.data?.orderId && (
            <View style={styles.splitButtonContainer}>
              <Button
                mode="outlined"
                compact
                onPress={() => {
                  if (!notification.read) markAsRead(notification.id);
                  navigationService.navigate('OrderDetails', { orderId: notification.data.orderId });
                }}
                style={styles.splitButton}
                contentStyle={styles.splitButtonContent}>
                View Order
              </Button>
              <Button
                mode="contained"
                compact
                onPress={() => {
                  if (!notification.read) markAsRead(notification.id);
                  navigationService.navigate('Orders');
                }
                style={styles.splitButton}
                contentStyle={styles.splitButtonContent}>
                All Orders
              </Button>
            </View>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <CommonHeader
        title="Notifications"
        subtitle={unreadCount > 0 ? `${unreadCount}`}  unread` : 'All caught up!'}
        showBack
        onBack={() => navigation.goBack()}
        rightComponent={
          <View style={styles.headerActions}>
            {unreadCount > 0 && (
              <IconButton
                icon="check-all"
                size={24}
                onPress={markAllAsRead}
                iconColor={theme.colors.primary}
              />
            )}
            <IconButton
              icon="cog"
              size={24}
              onPress={() => navigationService.navigate('NotificationSettings')}
              iconColor={theme.colors.onSurface}
            />
          </View>
        }
      />

      {/* Filter Chips */}
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={styles.filterContainer}
        contentContainerStyle={styles.filterContent}>
        {[
          {
            key: 'all',
            label: 'All',
            count: notifications.filter(n =>
              (n.type?.includes('order') || n.type?.includes('ORDER')) ||
              (n.type?.includes('stock') || n.type?.includes('STOCK') ||
               n.title?.toLowerCase().includes('stock') ||
               n.message?.toLowerCase().includes('stock'))
            ).length
          },
          {
            key: 'unread',
            label: 'Unread',
            count: notifications.filter(n =>
              !n.read && (
                (n.type?.includes('order') || n.type?.includes('ORDER')) ||
                (n.type?.includes('stock') || n.type?.includes('STOCK') ||
                 n.title?.toLowerCase().includes('stock') ||
                 n.message?.toLowerCase().includes('stock'))
              )
            ).length
          },
          {
            key: 'orders',
            label: 'Orders',
            count: notifications.filter(n => n.type?.includes('order') || n.type?.includes('ORDER')).length
          },
          {
            key: 'stock',
            label: 'Low Stock',
            count: notifications.filter(n =>
              n.type?.includes('stock') || n.type?.includes('STOCK') ||
              n.title?.toLowerCase().includes('stock') ||
              n.message?.toLowerCase().includes('stock')
            ).length
          },
        ].map(filterOption => (
          <View key={filterOption.key} style={styles.filterChipContainer}>
            <Chip
              mode={filter === filterOption.key ? 'flat' : 'outlined'}
              selected={filter === filterOption.key}
              onPress={() => setFilter(filterOption.key)}
              style={[
                styles.filterChip,
                {
                  backgroundColor: filter === filterOption.key
                    ? theme.colors.primaryContainer
                    : theme.colors.surface,
                }
              }
              textStyle={{
                color: filter === filterOption.key
                  ? theme.colors.onPrimaryContainer
                  : theme.colors.onSurface
              }>
              {filterOption.label}
            </Chip>
            {filterOption.count > 0 && (
              <Badge
                size={16}
                style={[
                  styles.filterBadge,
                  {
                    backgroundColor: filterOption.key === 'unread'
                      ? theme.colors.error
                      : theme.colors.primary,
                  }
                }>
                {filterOption.count}
              </Badge>
            )}
          </View>
        ))}
      </ScrollView>

      {/* Selection Mode Actions */}
      {selectionMode && (
        <Surface style={[styles.selectionActions, { backgroundColor: theme.colors.surface }]}elevation={2}>
          <Text variant="bodyMedium" style={[styles.selectionText, { color: theme.colors.onSurface }]}>
            {selectedNotifications.size} selected
          </Text>
          <View style={styles.selectionButtons}>
            <Button mode="text" onPress={handleMarkSelectedAsRead}>
              Mark Read
            </Button>
            <Button mode="text" onPress={handleDeleteSelected} textColor={theme.colors.error}>
              Delete
            </Button>
            <Button mode="text" onPress={() => {
              setSelectionMode(false);
              setSelectedNotifications(new Set());
            }>
              Cancel
            </Button>
          </View>
        </Surface>
      )}

      {/* Notifications List */}
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary}
            tintColor={theme.colors.primary}
          />
        }
        showsVerticalScrollIndicator={false}>
        {filteredNotifications.length > 0 ? (
          <View style={styles.notificationsList}>
            {filteredNotifications.map(renderNotificationItem)}
          </View>
        ) : (
          <View style={styles.emptyState}>
            <LocalIcon name="bell-outline" size={64} color={theme.colors.onSurfaceVariant} />
            <Text variant="headlineSmall" style={[styles.emptyTitle, { color: theme.colors.onSurface, marginTop: 16 }]}>
              No notifications
            </Text>
            <Text variant="bodyMedium" style={[styles.emptyMessage, { color: theme.colors.onSurfaceVariant, textAlign: 'center', marginTop: 8 }]}>
              {filter === 'all' 
                ? "You're all caught up! New notifications will appear here."
                : `No ${filter} notifications found.`
              }
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

export default NotificationScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  filterContainer: {
    maxHeight: 60,
  },
  filterContent: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    gap: SPACING.xs,
  },
  filterChipContainer: {
    position: 'relative',
    marginRight: SPACING.xs,
  },
  filterChip: {
    marginRight: 0,
  },
  filterBadge: {
    position: 'absolute',
    top: -6,
    right: -6,
    minWidth: 16,
    height: 16,
  },
  selectionActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
  },
  selectionText: {},
  selectionButtons: {
    flexDirection: 'row',
    gap: SPACING.xs,
  },
  content: {
    flex: 1,
    padding: SPACING.md,
  },
  notificationsList: {
    gap: SPACING.sm,
  },
  notificationItem: {
    borderRadius: BORDER_RADIUS.md,
    padding: SPACING.md,
    marginBottom: SPACING.xs,
  },
  notificationContent: {
    flex: 1,
  },
  notificationHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  notificationIcon: {
    position: 'relative',
    marginRight: SPACING.md,
    marginTop: 2,
  },
  unreadDot: {
    position: 'absolute',
    top: -2,
    right: -2,
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  notificationInfo: {
    flex: 1,
    marginRight: SPACING.sm,
  },
  notificationTitle: {},
  notificationMessage: {},
  notificationTime: {},
  notificationMeta: {
    alignItems: 'flex-end',
    minWidth: 80,
  },
  priorityChip: {},
  splitButtonContainer: {
    flexDirection: 'row',
    marginTop: SPACING.sm,
    gap: SPACING.xs,
  },
  splitButton: {
    flex: 1,
    borderRadius: BORDER_RADIUS.sm,
  },
  splitButtonLeft: {
    marginRight: SPACING.xs / 2,
  },
  splitButtonRight: {
    marginLeft: SPACING.xs / 2,
  },
  splitButtonContent: {
    height: 32,
    paddingHorizontal: SPACING.sm,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: SPACING.xxxl,
  },
  emptyTitle: {},
  emptyMessage: {},
});