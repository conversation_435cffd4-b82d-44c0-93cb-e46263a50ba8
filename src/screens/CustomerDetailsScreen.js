/**
 * CustomerDetailsScreen - Detailed customer view with comprehensive information
 * Part of the CRM module for enhanced customer relationship management
 */

import React, { useState, useEffect, useCallback } from 'react';
import { View, ScrollView, StyleSheet, Linking   } from 'react-native';
import { Text   } from 'react-native-paper';
import { Card   } from 'react-native-paper';
import { Button   } from 'react-native-paper';
import { Surface   } from 'react-native-paper';
import { Chip   } from 'react-native-paper';
import { IconButton   } from 'react-native-paper';
import { FAB   } from 'react-native-paper';
import LocalIcon from '../components/LocalIcon';
import { useTheme   } from '../context/ThemeContext';
import { useData   } from '../context/DataContext';
import CommonHeader from '../components/CommonHeader';
import { SPACING, BORDER_RADIUS   } from '../theme/designTokens';
import navigationService from '../services/NavigationService';

const CustomerDetailsScreen = ({ route }) => {
  const { theme  } = useTheme();
  const { state, actions  } = useData();
  const customer = route?.params?.customer;
  const [customerOrders, setCustomerOrders] = useState([]);
  const [customerMeasurements, setCustomerMeasurements] = useState([]);

  useEffect(() => {
    if(customer) {
      // Get customer orders
      const orders = (state.orders || []).filter(order =>
        order.customerId === customer.id ||
        order.customerName === customer.name ||
        order.customer === customer.name
      );
      setCustomerOrders(orders);

      // Get customer measurements
      const measurements = (state.measurements || []).filter(measurement =>
        measurement.customerId === customer.id
      );
      setCustomerMeasurements(measurements);
    }
  }, [customer, state.orders, state.measurements]);

  if(!customer) {
    return (
      <View style={styles.container}>
        <CommonHeader
          title="Customer Details"
          subtitle="Customer not found"
          showBackButton={true}
          onBackPress={() => navigationService.goBack()}
        />
        <View style={styles.errorContainer}>
          <LocalIcon name="account-alert" size={64} color={theme.colors.onSurfaceVariant} />
          <Text variant="titleMedium" style={styles.errorText}>
            Customer not found
          </Text>
        </View>
      </View>
    );
  }

  const getCustomerStatus = () => {
    const lastOrderDate = customerOrders.length > 0 ?
      Math.max(...customerOrders.map(o => new Date(o.createdAt || o.date).getTime())) : 0;
    const daysSinceLastOrder = (Date.now() - lastOrderDate) / (1000 * 60 * 60 * 24);

    if (customerOrders.length === 0) return { status: 'new', color: '#2196F3'  };
    if (daysSinceLastOrder <= 30) return { status: 'active', color: '#4CAF50'  };
    if (daysSinceLastOrder <= 90) return { status: 'inactive', color: '#FF9800'  };
    return { status: 'dormant', color: '#F44336'  };
  };

  const getCustomerType = () => {
    const totalValue = customerOrders.reduce((sum, order) => sum + (order.total || 0), 0);

    if (customerOrders.length >= 10 || totalValue >= 50000) return 'VIP';
    if (customerOrders.length >= 5 || totalValue >= 20000) return 'Premium';
    if (customerOrders.length >= 2) return 'Regular';
    return 'New';
  };

  const getTotalValue = () => {
    return customerOrders.reduce((sum, order) => sum + (order.total || 0), 0);
  };

  const handleCall = useCallback(() => {
    if(customer.phone) {
      Linking.openURL(`tel:${customer.phone}`);
    }
  }, [customer.phone]);

  const handleWhatsApp = useCallback(() => {
    if(customer.phone) {
      const message = 'Hello! This is regarding your tailor order.';
      Linking.openURL(`whatsapp://send?phone=${customer.phone}&text=${encodeURIComponent(message)}`);`
    }
  }, [customer.phone]);

  const handleEmail = useCallback(() => {
    if(customer.email) {
      Linking.openURL(`mailto:${customer.email}`);
    }
  }, [customer.email]);

  const handleEditCustomer = useCallback(() => {
    navigationService.navigate('AddCustomer', { customer });
  }, [customer]);

  const handleNewOrder = useCallback(() => {
    navigationService.navigate('CreateOrder', { customer: customer });
  }, [customer]);

  const handleNewMeasurement = useCallback(() => {
    navigationService.navigate('AddMeasurement', { customerId: customer.id });
  }, [customer.id]);

  const handleViewOrder = useCallback((order) => {
    navigationService.navigate('OrderDetails', { order });
  }, []);

  const handleViewMeasurement = useCallback((measurement) => {
    navigationService.navigate('MeasurementDetails', { measurement });
  }, []);

  const statusInfo = getCustomerStatus();
  const customerType = getCustomerType();
  const totalValue = getTotalValue();

  return (
    <View style={styles.container}>
      <CommonHeader
        title={customer.name}
        subtitle={`${customerType}`}` Customer`}
        showBackButton={true}
        onBackPress={() => navigationService.goBack()}
        rightAction={
          <IconButton
            icon="pencil"
            size={24}
            onPress={handleEditCustomer}
            iconColor={theme.colors.onSurface}
          />
        }
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Customer Header */}
        <Card style={styles.headerCard}>
          <Card.Content>
            <View style={styles.customerHeader}>
              <View style={styles.customerInfo}>
                <Text variant="headlineSmall" style={styles.customerName}>
                  {customer.name}
                </Text>
                <View style={styles.customerMeta}>
                  <Chip
                    mode="outlined"
                    textStyle={{ fontSize: 12 }
                    style={styles.customerTypeChip}>
                    {customerType}
                  </Chip>
                  <View style={styles.statusIndicator}>
                    <LocalIcon name="circle" size={10} color={statusInfo.color} />
                    <Text variant="bodyMedium" style={[styles.statusText, { color: statusInfo.color }]}>
                      {statusInfo.status}
                    </Text>
                  </View>
                </View>
                <Text variant="bodyMedium" style={styles.customerSince}>
                  Customer since {new Date(customer.createdAt || Date.now()).getFullYear()}
                </Text>
              </View>
            </View>

            {/* Quick Actions */}
            <View style={styles.quickActions}>
              {customer.phone && (
                <>
                  <Button
                    mode="outlined"
                    icon="phone"
                    onPress={handleCall}
                    style={styles.actionButton}
                    compact
                  >
                    Call
                  </Button>
                  <Button
                    mode="outlined"
                    icon="whatsapp"
                    onPress={handleWhatsApp}
                    style={styles.actionButton}
                    compact
                  >
                    WhatsApp
                  </Button>
                </>
              )}
              {customer.email && (
                <Button
                  mode="outlined"
                  icon="email"
                  onPress={handleEmail}
                  style={styles.actionButton}
                  compact
                >
                  Email
                </Button>
              )}
            </View>
          </Card.Content>
        </Card>

        {/* Customer Statistics */}
        <Card style={styles.statsCard}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Customer Statistics
            </Text>
            <View style={styles.statsGrid}>
              <View style={styles.statItem}>
                <Text variant="headlineMedium" style={styles.primaryStat}>
                  {customerOrders.length}
                </Text>
                <Text variant="bodySmall" style={styles.statLabel}>
                  Total Orders
                </Text>
              </View>
              <View style={styles.statItem}>
                <Text variant="headlineMedium" style={styles.secondaryStat}>
                  ৳{totalValue.toLocaleString()}
                </Text>
                <Text variant="bodySmall" style={styles.statLabel}>
                  Total Value
                </Text>
              </View>
              <View style={styles.statItem}>
                <Text variant="headlineMedium" style={styles.tertiaryStat}>
                  {customerMeasurements.length}
                </Text>
                <Text variant="bodySmall" style={styles.statLabel}>
                  Measurements
                </Text>
              </View>
            </View>
          </Card.Content>
        </Card>

        {/* Contact Information */}
        <Card style={styles.infoCard}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Contact Information
            </Text>

            {customer.phone && (
              <View style={styles.infoRow}>
                <LocalIcon name="phone" size={20} color={theme.colors.primary} />
                <Text variant="bodyLarge" style={styles.infoText}>
                  {customer.phone}
                </Text>
              </View>
            )}

            {customer.email && (
              <View style={styles.infoRow}>
                <LocalIcon name="email" size={20} color={theme.colors.primary} />
                <Text variant="bodyLarge" style={styles.infoText}>
                  {customer.email}
                </Text>
              </View>
            )}

            {customer.address && (
              <View style={styles.infoRow}>
                <LocalIcon name="map-marker" size={20} color={theme.colors.primary} />
                <Text variant="bodyLarge" style={styles.infoText}>
                  {customer.address}
                </Text>
              </View>
            )}

            {customer.notes && (
              <View style={styles.infoRow}>
                <LocalIcon name="note-text" size={20} color={theme.colors.primary} />
                <Text variant="bodyLarge" style={styles.infoText}>
                  {customer.notes}
                </Text>
              </View>
            )}
          </Card.Content>
        </Card>

        {/* Recent Orders */}
        <Card style={styles.ordersCard}>
          <Card.Content>
            <View style={styles.sectionHeader}>
              <Text variant="titleMedium" style={styles.customerName}>
                Recent Orders ({customerOrders.length})
              </Text>
              <Button
                mode="outlined"
                icon="plus"
                onPress={handleNewOrder}
                compact
              >
                New Order
              </Button>
            </View>

            {customerOrders.length > 0 ? (
              <View style={styles.ordersList}>
                {customerOrders.slice(0, 5).map((order) => (
                  <Surface
                    key={order.id}
                    style={[styles.orderItem, { backgroundColor: theme.colors.surfaceVariant }]}elevation={1}>
                    <View style={styles.orderInfo}>
                      <Text variant="bodyLarge" style={styles.customerName}>
                        Order #{order.id?.slice(-6) || 'N/A'}
                      </Text>
                      <Text variant="bodyMedium" style={styles.statLabel}>
                        {new Date(order.createdAt || order.date).toLocaleDateString()}
                      </Text>
                      <Text variant="bodyMedium" style={styles.orderValue}>
                        ৳{(order.total || 0).toLocaleString()}
                      </Text>
                    </View>
                    <View style={styles.orderActions}>
                      <Chip
                        mode="outlined"
                        textStyle={{ fontSize: 11 }
                        style={styles.statusChip}>
                        {order.status || 'Pending'}
                      </Chip>
                      <IconButton
                        icon="chevron-right"
                        size={20}
                        onPress={() => handleViewOrder(order)}
                      />
                    </View>
                  </Surface>
                ))}
                {customerOrders.length > 5 && (
                  <Button
                    mode="text"
                    onPress={() => navigationService.navigate('Orders', { customerId: customer.id })}
                    style={styles.viewAllButton}>
                    View All Orders ({customerOrders.length})
                  </Button>
                )}
              </View>
            ) : (
              <View style={styles.emptyState}>
                <LocalIcon name="clipboard-list" size={48} color={theme.colors.onSurfaceVariant} />
                <Text variant="bodyMedium" style={styles.emptyText}>
                  No orders yet
                </Text>
              </View>
            )}
          </Card.Content>
        </Card>

        {/* Measurements */}
        <Card style={styles.measurementsCard}>
          <Card.Content>
            <View style={styles.sectionHeader}>
              <Text variant="titleMedium" style={styles.customerName}>
                Measurements ({customerMeasurements.length})
              </Text>
              <Button
                mode="outlined"
                icon="ruler"
                onPress={handleNewMeasurement}
                compact
              >
                Add
              </Button>
            </View>

            {customerMeasurements.length > 0 ? (
              <View style={styles.measurementsList}>
                {customerMeasurements.slice(0, 3).map((measurement) => (
                  <Surface
                    key={measurement.id}
                    style={[styles.measurementItem, { backgroundColor: theme.colors.surfaceVariant }]}elevation={1}>
                    <View style={styles.measurementInfo}>
                      <Text variant="bodyLarge" style={styles.customerName}>
                        {measurement.garmentType || 'Unknown'}
                      </Text>
                      <Text variant="bodyMedium" style={styles.statLabel}>
                        {new Date(measurement.createdAt || measurement.measurementDate).toLocaleDateString()}
                      </Text>
                      <Text variant="bodySmall" style={styles.statLabel}>
                        By: {measurement.takenBy || 'Unknown'}
                      </Text>
                    </View>
                    <IconButton
                      icon="chevron-right"
                      size={20}
                      onPress={() => handleViewMeasurement(measurement)}
                    />
                  </Surface>
                ))}
                {customerMeasurements.length > 3 && (
                  <Button
                    mode="text"
                    onPress={() => navigationService.navigate('CustomerMeasurements', {
                      customerId: customer.id,
                      customer: customer
                    })}
                    style={styles.viewAllButton}>
                    View All Measurements ({customerMeasurements.length})
                  </Button>
                )}
              </View>
            ) : (
              <View style={styles.emptyState}>
                <LocalIcon name="ruler" size={48} color={theme.colors.onSurfaceVariant} />
                <Text variant="bodyMedium" style={styles.emptyText}>
                  No measurements recorded
                </Text>
              </View>
            )}
          </Card.Content>
        </Card>

        {/* Bottom padding */}
        <View style={styles.bottomPadding} />
      </ScrollView>

      {/* Floating Action Button */}
      <FAB
        icon="pencil"
        style={[styles.fab, { backgroundColor: theme.colors.primary }]}onPress={handleEditCustomer}
      />
    </View>
  );
};

export default CustomerDetailsScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: SPACING.md,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.xl,
  },
  errorText: {
    marginTop: 16,
  },
  headerCard: {
    marginBottom: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
  },
  customerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  customerInfo: {
    marginLeft: SPACING.md,
    flex: 1,
  },
  customerName: {
    fontWeight: '600',
  },
  customerMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: SPACING.xs,
  },
  customerTypeChip: {
    height: 28,
    marginRight: 8,
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    marginLeft: 4,
    textTransform: 'capitalize',
  },
  customerSince: {
    marginTop: 4,
  },
  quickActions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.sm,
  },
  actionButton: {
    marginRight: SPACING.sm,
    marginBottom: SPACING.sm,
  },
  statsCard: {
    marginBottom: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statLabel: {},
  primaryStat: {
    fontWeight: '700',
  },
  secondaryStat: {
    fontWeight: '700',
  },
  tertiaryStat: {
    fontWeight: '700',
  },
  infoCard: {
    marginBottom: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  infoText: {
    marginLeft: 12,
  },
  sectionTitle: {
    marginBottom: 16,
    fontWeight: '600',
  },
  ordersCard: {
    marginBottom: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
  },
  measurementsCard: {
    marginBottom: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  ordersList: {
    marginTop: SPACING.sm,
  },
  measurementsList: {
    marginTop: SPACING.sm,
  },
  orderItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.md,
    marginBottom: SPACING.sm,
    borderRadius: BORDER_RADIUS.sm,
  },
  measurementItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.md,
    marginBottom: SPACING.sm,
    borderRadius: BORDER_RADIUS.sm,
  },
  orderInfo: {
    flex: 1,
  },
  measurementInfo: {
    flex: 1,
  },
  orderActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  orderValue: {
    fontWeight: '600',
  },
  statusChip: {
    height: 24,
  },
  viewAllButton: {
    marginTop: 8,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: SPACING.xl,
  },
  emptyText: {
    marginTop: 8,
  },
  bottomPadding: {
    height: 100,
  },
  fab: {
    position: 'absolute',
    margin: SPACING.lg,
    right: 0,
    bottom: 0,
  },
});