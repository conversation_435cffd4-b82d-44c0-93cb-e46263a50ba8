/**
 * QRScannerScreen - QR Code Scanner for Order Data
 */

import React, { useState, useEffect, useCallback } from 'react';
import { View, StyleSheet, Alert, Dimensions } from 'react-native';
import { Text } from 'react-native-paper';
import { Button } from 'react-native-paper';
import { Surface } from 'react-native-paper';
import { IconButton } from 'react-native-paper';
import LocalIcon from '../components/LocalIcon';
import QRScanner from '../components/QRScanner';

// Production QR Scanner - Using react-native-qrcode-scanner
import { useTheme } from '../context/ThemeContext';
import { useData } from '../context/DataContext';
import CommonHeader from '../components/CommonHeader';
import { SPACING, BORDER_RADIUS } from '../theme/designTokens';
import QRCodeService from '../services/QRCodeService';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const QRScannerScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { state } = useData();
  const [scanned, setScanned] = useState(false);
  const [scanning, setScanning] = useState(true);
  const [scannedData, setScannedData] = useState(null);
  const [flashOn, setFlashOn] = useState(false);
  const [hasPermission, setHasPermission] = useState(true); // Production scanner handles permissions

  const handleBarCodeScanned = useCallback((data) => {
    if (scanned) return;
    
    setScanned(true);
    setScanning(false);

    // Check if it's a Tailora order QR code
    if (QRCodeService.isTailoraOrderQR(data)) {
      const orderResult = QRCodeService.getOrderFromQR(data, state.orders || []);
      
      if (orderResult.success) {
        setScannedData(orderResult);
        showOrderDetails(orderResult);
      } else {
        Alert.alert('Invalid QR Code', orderResult.error || 'Could not read order data');
        resetScanner();
      }
    } else {
      // Try to handle as generic QR code
      QRCodeService.handleScannedQR(data, navigation, state);
      resetScanner();
    }
  };

  const showOrderDetails = (orderResult) => {
    const { qrData, existingOrder, orderExists, orderInfo } = orderResult;
    
    Alert.alert(
      'Order QR Code Scanned',
      `Order #${orderInfo.id}\n` +
      `Customer: ${orderInfo.customer}\n` +
      `Total: ৳${orderInfo.total}\n` +
      `Status: ${orderInfo.status}\n` +
      `${orderExists ? 'Found in your records' : 'Not found in current data'}`,
      [
        {
          text: 'Scan Again',
          style: 'cancel',
          onPress: resetScanner,
        },
        ...(orderExists ? [{
          text: 'View Order',
          onPress: () => {
            navigation.navigate('OrderDetails', { 
              order: existingOrder,
              orderId: existingOrder.id 
            });
          },
        }] : []),
        {
          text: 'Show Details',
          onPress: () => showDetailedOrderInfo(orderResult),
        },
      ]
    );
  };

  const showDetailedOrderInfo = (orderResult) => {
    const { orderInfo, qrData } = orderResult;
    
    navigation.navigate('QROrderDetails', {
      orderInfo,
      qrData,
      scannedAt: new Date().toISOString(),
    });
  };

  const resetScanner = () => {
    setScanned(false);
    setScanning(true);
    setScannedData(null);
  };

  const toggleFlash = () => {
    setFlashOn(!flashOn);
  };

  if (hasPermission === null) {
    return (
      <View style={styles.style5}>
        <CommonHeader
          title="QR Scanner"
          showBack
          onBack={() => navigation.goBack()}
        />
        <View style={styles.style2}>
          <Text variant="bodyLarge" style={styles.style9}}>
            Requesting camera permission...
          </Text>
        </View>
      </View>
    );
  }

  if (hasPermission === false) {
    return (
      <View style={styles.style5}>
        <CommonHeader
          title="QR Scanner"
          showBack
          onBack={() => navigation.goBack()}
        />
        <View style={styles.style2}>
          <LocalIcon name="camera" size={64} color={theme.colors.onSurfaceVariant} />
          <Text variant="headlineSmall" style={styles.style8}}>
            Camera Permission Required
          </Text>
          <Text variant="bodyMedium" style={styles.style7}}>
            Please grant camera permission to scan QR codes
          </Text>
          <Button
            mode="contained"
            onPress={() => setHasPermission(true)}
            style={styles.style6}}
          >
            Continue
          </Button>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.style5}>
      <CommonHeader
        title="QR Scanner"
        subtitle="Scan order QR codes"
        showBack
        onBack={() => navigation.goBack()}
        rightComponent={
          <IconButton
            icon={flashOn ? "flash" : "flash-off"}
            size={24}
            onPress={toggleFlash}
            iconColor={theme.colors.onSurface}
          />
        }
      />

      <View style={styles.style2}>
        {scanning && (
          <QRScanner
            onScan={handleBarCodeScanned}
            onClose={() => setScanning(false)}
            isVisible={scanning}
          />
        )}
      </View>

      {/* Help Section */}
      <Surface style={styles.style4} elevation={1}>
        <Text variant="titleMedium" style={styles.style3}}>
          How to use QR Scanner
        </Text>
        <View style={styles.style2}>
          <LocalIcon name="camera" size={20} color={theme.colors.primary} />
          <Text variant="bodySmall" style={styles.style1}}>
            Scan order QR codes from invoices to view order details
          </Text>
        </View>
        <View style={styles.style2}>
          <LocalIcon name="check-circle" size={20} color={theme.colors.primary} />
          <Text variant="bodySmall" style={styles.style1}}>
            Automatically detects Tailora order QR codes
          </Text>
        </View>
        <View style={styles.style2}>
          <LocalIcon name="eye" size={20} color={theme.colors.primary} />
          <Text variant="bodySmall" style={styles.style1}}>
            View complete order information and status
          </Text>
        </View>
      </Surface>
    </View>
  );
};






export default QRScannerScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.lg,
  },
  scannerContainer: {
    flex: 1,
    position: 'relative',
  },
  scanner: {
    ...StyleSheet.absoluteFillObject,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scannerFrame: {
    width: 250,
    height: 250,
    position: 'relative',
  },
  corner: {
    position: 'absolute',
    width: 30,
    height: 30,
    borderWidth: 4,
  },
  topLeft: {
    top: 0,
    left: 0,
    borderRightWidth: 0,
    borderBottomWidth: 0,
  },
  topRight: {
    top: 0,
    right: 0,
    borderLeftWidth: 0,
    borderBottomWidth: 0,
  },
  bottomLeft: {
    bottom: 0,
    left: 0,
    borderRightWidth: 0,
    borderTopWidth: 0,
  },
  bottomRight: {
    bottom: 0,
    right: 0,
    borderLeftWidth: 0,
    borderTopWidth: 0,
  },
  instructionsCard: {
    position: 'absolute',
    bottom: 120,
    left: SPACING.lg,
    right: SPACING.lg,
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
  },
  actionButtons: {
    position: 'absolute',
    bottom: 60,
    left: SPACING.lg,
    right: SPACING.lg,
  },
  scanAgainButton: {
    borderRadius: BORDER_RADIUS.md,
  },
  helpSection: {
    margin: SPACING.md,
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
  },
  helpItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  style1: {
    { color: theme.colors.onSurfaceVariant,
    marginLeft: 8,
    flex: 1,
  },
  style2: {
  },
  style3: {
    { color: theme.colors.onSurface,
    marginBottom: 8,
  },
  style4: {
    { backgroundColor: theme.colors.surface,
  },
  style5: {
    { backgroundColor: theme.colors.background,
  },
  style6: {
    { marginTop: 24,
  },
  style7: {
    { color: theme.colors.onSurfaceVariant,
    textAlign: 'center',
    marginTop: 8,
  },
  style8: {
    { color: theme.colors.onSurface,
    marginTop: 16,
  },
  style9: {
    { color: theme.colors.onSurface,
  },
});