import React, { useState, useCallback, useEffect, useMemo } from 'react';
// FIXED: Removed invalid semicolon from import
import { View,
  StyleSheet,
  ScrollView,
  Alert,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  } from 'react-native';
import { Text, TextInput, Button, Card, Chip, IconButton, Surface, DataTable, ProgressBar   } from 'react-native-paper';
// FIXED: Corrected useTheme import to use the custom context
import { useTheme } from '../context/ThemeContext';
import { useNavigation   } from '@react-navigation/native';
import { useFinancial   } from '../context/FinancialContext';
import { NativePieChart   } from '../components/charts/NativeCharts';

const { width: screenWidth  } = Dimensions.get('window');

const SPACING = { xs: 4, sm: 8, md: 16, lg: 24, xl: 32 };
const BORDER_RADIUS = { sm: 4, md: 8, lg: 12, xl: 16 };

const CashReconciliationScreen = () => {
  const { theme  } = useTheme();
  const navigation = useNavigation();
  const { getFinancialSummary, getCashFlow, expenses, revenue  } = useFinancial();

  const [reconciliationData, setReconciliationData] = useState({
    openingBalance: '', closingBalance: '', expectedBalance: '', actualBalance: '', discrepancy: 0,
  });
  const [cashFlowData, setCashFlowData] = useState([]);
  const [summaryData, setSummaryData] = useState({
    totalIncome: 0, totalExpenses: 0, netCashFlow: 0, reconciliationStatus: 'pending',
  });
  const [isReconciling, setIsReconciling] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState('today');

  const loadReconciliationData = useCallback(async () => {
    try {
      const summary = await getFinancialSummary(selectedPeriod);
      const cashFlow = await getCashFlow(selectedPeriod);
      setSummaryData(summary);
      setCashFlowData(cashFlow);
      const expected = parseFloat(reconciliationData.openingBalance || 0) + summary.netCashFlow;
      setReconciliationData(prev => ({ ...prev, expectedBalance: expected.toFixed(2) }));
    } catch (error) {
      console.error('Error loading reconciliation data:', error);
    }
  }, [selectedPeriod, reconciliationData.openingBalance, getFinancialSummary, getCashFlow]);

  useEffect(() => {
    loadReconciliationData();
  }, [loadReconciliationData]);

  useEffect(() => {
    const expected = parseFloat(reconciliationData.expectedBalance || 0);
    const actual = parseFloat(reconciliationData.actualBalance || 0);
    setReconciliationData(prev => ({ ...prev, discrepancy: actual - expected }));
  }, [reconciliationData.expectedBalance, reconciliationData.actualBalance]);

  const handleReconciliation = useCallback(async () => {
    if(!reconciliationData.actualBalance) {
      Alert.alert('Error', 'Please enter the actual cash balance');
      return;
    }
    setIsReconciling(true);
    try {
      const discrepancy = Math.abs(reconciliationData.discrepancy);
      const tolerance = 5.00;
      if(discrepancy <= tolerance) {
        Alert.alert('Reconciliation Complete', `Cash reconciliation successful!\nDiscrepancy: $${discrepancy.toFixed(2)}`, [{ text: 'OK', onPress: () => navigation.goBack() }]);
      } else {
        Alert.alert('Discrepancy Found', `Significant discrepancy detected: $${discrepancy.toFixed(2)}`,` [{ text: 'Review' }, { text: 'Force Reconcile', style: 'destructive', onPress: () => navigation.goBack() }]);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to complete reconciliation');
    } finally {
      setIsReconciling(false);
    }
  }, [reconciliationData, navigation]);

  const updateField = useCallback((field, value) => {
    setReconciliationData(prev => ({ ...prev, [field]: value }));
  }, []);

  const getDiscrepancyColor = () => {
    const discrepancy = Math.abs(reconciliationData.discrepancy);
    if (discrepancy <= 5) return theme.colors.primary;
    if (discrepancy <= 20) return '#FF9800';
    return theme.colors.error;
  };
  
  const getDiscrepancyStatus = () => {
    const discrepancy = Math.abs(reconciliationData.discrepancy);
    if (discrepancy <= 5) return 'Within Tolerance';
    if (discrepancy <= 20) return 'Minor Discrepancy';
    return 'Major Discrepancy';
  };

  // FIXED: Stylesheet moved inside component and memoized
  const styles = useMemo(() => StyleSheet.create({
    container: { flex: 1, backgroundColor: theme.colors.background },
    header: { backgroundColor: theme.colors.surface },
    headerContent: { flexDirection: 'row', alignItems: 'center', paddingHorizontal: SPACING.sm, paddingVertical: SPACING.sm, borderBottomWidth: 1, borderBottomColor: theme.colors.outlineVariant },
    headerTitle: { flex: 1, textAlign: 'center', fontWeight: 'bold', fontSize: 20, color: theme.colors.onSurface },
    headerSpacer: { width: 48 },
    content: { flex: 1, padding: SPACING.md },
    section: { marginBottom: SPACING.lg, borderRadius: BORDER_RADIUS.lg, backgroundColor: theme.colors.surface, padding: SPACING.lg },
    sectionTitle: { fontWeight: '600', marginBottom: SPACING.md, color: theme.colors.onSurface, fontSize: 18 },
    periodButtons: { flexDirection: 'row', gap: SPACING.sm, justifyContent: 'center' },
    periodChip: { flex: 1 },
    summaryRow: { flexDirection: 'row', justifyContent: 'space-around', marginBottom: SPACING.lg },
    summaryItem: { alignItems: 'center', flex: 1 },
    summaryLabel: { color: theme.colors.onSurfaceVariant },
    summaryValue: { fontWeight: 'bold' },
    incomeText: { color: '#4CAF50', fontWeight: 'bold' },
    expenseText: { color: '#F44336', fontWeight: 'bold' },
    chartContainer: { alignItems: 'center', marginTop: SPACING.md },
    input: { marginBottom: SPACING.md },
    discrepancyCard: { padding: SPACING.md, borderRadius: BORDER_RADIUS.md, marginTop: SPACING.sm },
    discrepancyContent: { alignItems: 'center' },
    discrepancyTitle: { fontWeight: 'bold' },
    discrepancyStatus: {},
    discrepancyProgress: { width: '100%', marginTop: SPACING.md, borderRadius: BORDER_RADIUS.sm },
    submitSection: { padding: SPACING.lg, borderTopLeftRadius: BORDER_RADIUS.xl, borderTopRightRadius: BORDER_RADIUS.xl, borderTopWidth: 1, borderTopColor: theme.colors.outlineVariant, backgroundColor: theme.colors.surface },
    submitButtonContent: { paddingVertical: SPACING.sm },
    bottomSpacer: { height: SPACING.xl },
  }), [theme]);

  return (
    <KeyboardAvoidingView style={styles.container} behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
      <Surface style={styles.header} elevation={2}>
        <View style={styles.headerContent}>
          <IconButton icon="arrow-left" size={24} onPress={() => navigation.goBack()} />
          <Text style={styles.headerTitle}>Cash Reconciliation</Text>
          <View style={styles.headerSpacer} />
        </View>
      </Surface>
      <ScrollView contentContainerStyle={styles.content} showsVerticalScrollIndicator={false}>
        <Card style={styles.section}>
          <Card.Content>
            <Text style={styles.sectionTitle}>📅 Reconciliation Period</Text>
            <View style={styles.periodButtons}>
              {['today', 'week', 'month'].map(p => <Chip key={p} mode={selectedPeriod === p ? 'flat' : 'outlined'} selected={selectedPeriod === p} onPress={() => setSelectedPeriod(p)} style={styles.periodChip}>{p.charAt(0).toUpperCase() + p.slice(1)}</Chip>)}
            </View>
          </Card.Content>
        </Card>
        <Card style={styles.section}>
          <Card.Content>
            <Text style={styles.sectionTitle}>💰 Cash Flow Summary</Text>
            <View style={styles.summaryRow}>
              <View style={styles.summaryItem}><Text style={styles.summaryLabel}>Income</Text><Text style={[styles.summaryValue, styles.incomeText}>${summaryData.totalIncome.toFixed(2)}</Text></View>
              <View style={styles.summaryItem}><Text style={styles.summaryLabel}>Expenses</Text><Text style={[styles.summaryValue, styles.expenseText}>${summaryData.totalExpenses.toFixed(2)}</Text></View>
              <View style={styles.summaryItem}><Text style={styles.summaryLabel}>Net Flow</Text><Text style={[styles.summaryValue, { color: summaryData.netCashFlow >= 0 ? '#4CAF50' : '#F44336' }}}}>${summaryData.netCashFlow.toFixed(2)}</Text></View>
            </View>
            {summaryData.totalIncome > 0 && (
              <View style={styles.chartContainer}>
                <NativePieChart data={[{ name: 'Income', value: summaryData.totalIncome, color: '#4CAF50' }, { name: 'Expenses', value: summaryData.totalExpenses, color: '#F44336' }} width={screenWidth - 80} height={200} showLegend />
              </View>
            )}
          </Card.Content>
        </Card>
        <Card style={styles.section}>
          <Card.Content>
            <Text style={styles.sectionTitle}>🔄 Balance Reconciliation</Text>
            <TextInput label="Opening Balance" value={reconciliationData.openingBalance} onChangeText={v => updateField('openingBalance', v)} keyboardType="decimal-pad" mode="outlined" style={styles.input} left={<TextInput.Icon icon="cash-multiple" />} />
            <TextInput label="Expected Closing Balance" value={reconciliationData.expectedBalance} editable={false} mode="outlined" style={styles.input} left={<TextInput.Icon icon="calculator" />} />
            <TextInput label="Actual Cash Count" value={reconciliationData.actualBalance} onChangeText={v => updateField('actualBalance', v)} keyboardType="decimal-pad" mode="outlined" style={styles.input} left={<TextInput.Icon icon="cash-check" />} />
            {reconciliationData.actualBalance && (
              <Surface style={[styles.discrepancyCard, { backgroundColor: getDiscrepancyColor() + '20' }}}]} elevation={0}>
                <View style={styles.discrepancyContent}>
                  <Text style={[styles.discrepancyTitle, { color: getDiscrepancyColor() }}}}>Discrepancy: ${Math.abs(reconciliationData.discrepancy).toFixed(2)}</Text>
                  <Text style={[styles.discrepancyStatus, { color: getDiscrepancyColor() }}}}>{getDiscrepancyStatus()}</Text>
                  <ProgressBar progress={Math.min(Math.abs(reconciliationData.discrepancy) / 50, 1)} color={getDiscrepancyColor()} style={styles.discrepancyProgress} />
                </View>
              </Surface>
            )}
          </Card.Content>
        </Card>
        <Card style={styles.section}>
          <Card.Content>
            <Text style={styles.sectionTitle}>📋 Recent Transactions</Text>
            <DataTable>
              <DataTable.Header><DataTable.Title>Type</DataTable.Title><DataTable.Title numeric>Amount</DataTable.Title><DataTable.Title numeric>Time</DataTable.Title></DataTable.Header>
              {cashFlowData.slice(0, 5).map((t, i) => {
                const isIncome = t.type === 'income';
                return (
                  <DataTable.Row key={i}>
                    <DataTable.Cell><Chip icon={isIncome ? 'plus' : 'minus'} style={{ backgroundColor: isIncome ? '#4CAF5020' : '#F4433620' }>{t.type}</Chip></DataTable.Cell>
                    <DataTable.Cell numeric><Text style={{ color: isIncome ? '#4CAF50' : '#F44336', fontWeight: 'bold' }>${t.amount.toFixed(2)}</Text></DataTable.Cell>
                    <DataTable.Cell numeric>{new Date(t.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</DataTable.Cell>
                  </DataTable.Row>
                );
             `})}
            </DataTable>
          </Card.Content>
        </Card>
        <View style={styles.bottomSpacer} />
      </ScrollView>
      <Surface style={styles.submitSection} elevation={4}>
        <Button mode="contained" onPress={handleReconciliation} loading={isReconciling} disabled={isReconciling || !reconciliationData.actualBalance} contentStyle={styles.submitButtonContent} icon="check-circle">
          {isReconciling ? 'Reconciling...' : 'Complete Reconciliation'}
        </Button>
      </Surface>
    </KeyboardAvoidingView>
 `);
};

export default CashReconciliationScreen;