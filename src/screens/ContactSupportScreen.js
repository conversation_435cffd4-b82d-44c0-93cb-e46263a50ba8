import React, { useState, useCallback } from 'react';
import { ScrollView, View, StyleSheet, Alert, Linking   } from 'react-native';
import { useNavigation   } from '@react-navigation/native';
import { Text   } from 'react-native-paper';
import { Surface   } from 'react-native-paper';
import { Button   } from 'react-native-paper';
import { TextInput   } from 'react-native-paper';
import { List   } from 'react-native-paper';
import { Chip   } from 'react-native-paper';
import LocalIcon from '../components/LocalIcon';
import { useTheme   } from '../context/ThemeContext';
import CommonHeader from '../components/CommonHeader';
import { SPACING, BORDER_RADIUS   } from '../theme/designTokens';

const ContactSupportScreen = () => {
  const { theme  } = useTheme();
  const navigation = useNavigation();

  const [supportForm, setSupportForm] = useState({
    subject: '',
    message: '',
    priority: 'medium',
    category: 'general'});

  const priorities = [
    { id: 'low', label: 'Low', color: '#4CAF50' },
    { id: 'medium', label: 'Medium', color: '#FF9800' },
    { id: 'high', label: 'High', color: '#F44336' }];
  const categories = [
    { id: 'general', label: 'General', icon: 'help-circle' },
    { id: 'technical', label: 'Technical Issue', icon: 'bug' },
    { id: 'billing', label: 'Billing', icon: 'credit-card' },
    { id: 'feature', label: 'Feature Request', icon: 'lightbulb' }];
  const contactMethods = [
    {
      id: 'email',
      title: 'Email Support',
      subtitle: '<EMAIL>',
      description: 'Get help via email within 24 hours',
      icon: 'email',
      color: '#4285F4',
      availability: '24/7',
      responseTime: '< 24 hours',
      action: () => Linking.openURL('mailto:<EMAIL>?subject=Tailor App Support Request')},
    {
      id: 'phone',
      title: 'Phone Support',
      subtitle: '+1 (800) TAILOR-1',
      description: 'Call us Mon-Fri, 9 AM - 6 PM EST',
      icon: 'phone',
      color: '#34A853',
      availability: 'Mon-Fri 9AM-6PM EST',
      responseTime: 'Immediate',
      action: () => Linking.openURL('tel:+18002253791')},
    {
      id: 'whatsapp',
      title: 'WhatsApp Support',
      subtitle: '+1 (800) BAKERY-2',
      description: 'Message us on WhatsApp for quick help',
      icon: 'whatsapp',
      color: '#25D366',
      availability: '24/7',
      responseTime: '< 1 hour',
      action: () => Linking.openURL('https://wa.me/18002253792?text=Hello, I need help with the Bakery Management App')},
    {
      id: 'chat',
      title: 'Live Chat',
      subtitle: 'Available now',
      description: 'Chat with our support team instantly',
      icon: 'chat',
      color: '#FF6B35',
      availability: 'Mon-Fri 9AM-6PM EST',
      responseTime: '< 5 minutes',
      action: () => Alert.alert('Live Chat', 'Live chat feature coming soon! For immediate help, please use email or phone support.')},
    {
      id: 'community',
      title: 'Community Forum',
      subtitle: 'community.elitetailoring.com',
      description: 'Connect with other tailors and get help',
      icon: 'forum',
      color: '#9C27B0',
      availability: '24/7',
      responseTime: 'Community driven',
      action: () => Linking.openURL('https://community.elitetailoring.com')},
    {
      id: 'video',
      title: 'Video Call Support',
      subtitle: 'Schedule a call',
      description: 'Book a video call for personalized help',
      icon: 'video',
      color: '#FF5722',
      availability: 'By appointment',
      responseTime: 'Scheduled',
      action: () => Alert.alert('Video Support', 'Video call support available for premium users. Contact us to schedule a session.')}];
  const handleSubmitTicket = useCallback(() => {
    if (!supportForm.subject.trim() || !supportForm.message.trim()) {
      Alert.alert('Error', 'Please fill in both subject and message fields.');
      return;
    }

    // Simulate ticket submission
    Alert.alert(
      'Support Ticket Submitted',
      'Your support ticket has been submitted successfully. We\'ll get back to you within 24 hours.',
      [
        {
          text: 'OK',
          onPress: () => {
            setSupportForm({
              subject: '',
              message: '',
              priority: 'medium',
              category: 'general'});
          }
        }
      ]
    );
  }, [supportForm]);

  return (
    <View style={styles.style12}>
      <CommonHeader
        title="Contact Support"
        subtitle="Get help from our support team"
        showSearch={false}
        showBackButton={true}
        onBackPress={() => navigation.goBack()}
      />

      <ScrollView style={styles.style4} showsVerticalScrollIndicator={false}>
        {/* Quick Contact Methods */}
        <Surface style={styles.style9} elevation={1}>
          <Text variant="titleMedium" style={styles.style7}>
            Quick Contact
          </Text>

          {contactMethods.map((method) => (
            <List.Item
              key={method.id}
              title={method.title}
              description={method.description}
              left={props => (
                <View style={styles.style11}>
                  <LocalIcon name={method.icon === 'email' ? 'mail' : method.icon === 'phone' ? 'phone' : method.icon === 'chat' ? 'message-circle' : 'help-circle'} size={24} color={theme.colors.primary} />
                </View>
              )}
              right={props => (
                <View style={styles.style4}>
                  <Text variant="bodySmall" style={styles.style10}>
                    {method.subtitle}
                  </Text>
                </View>
              )}
              onPress={method.action}
              style={styles.style4}
            />
          ))}
        </Surface>

        {/* Support Ticket Form */}
        <Surface style={styles.style9} elevation={1}>
          <Text variant="titleMedium" style={styles.style7}>
            Submit Support Ticket
          </Text>

          {/* Category Selection */}
          <Text variant="bodyMedium" style={styles.style7}>
            Category
          </Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.style4}>
            {categories.map((category) => (
              <Chip
                key={category.id}
                selected={supportForm.category === category.id}
                onPress={() => setSupportForm(prev => ({ ...prev, category: category.id }))}
                icon={category.icon}
                style={styles.style8}
                textStyle={{
                  color: supportForm.category === category.id ? theme.colors.onPrimary : theme.colors.onSurface
                }>
                {category.label}
              </Chip>
            ))}
          </ScrollView>

          {/* Priority Selection */}
          <Text variant="bodyMedium" style={styles.style7}>
            Priority
          </Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.style4}>
            {priorities.map((priority) => (
              <Chip
                key={priority.id}
                selected={supportForm.priority === priority.id}
                onPress={() => setSupportForm(prev => ({ ...prev, priority: priority.id }))}
                style={styles.style6}
                textStyle={{
                  color: supportForm.priority === priority.id ? '#FFFFFF' : theme.colors.onSurface
                }>
                {priority.label}
              </Chip>
            ))}
          </ScrollView>

          {/* Subject Input */}
          <TextInput
            mode="outlined"
            label="Subject *"
            value={supportForm.subject}
            onChangeText={(text) => setSupportForm(prev => ({ ...prev, subject: text }))}
            style={styles.style4}
            placeholder="Brief description of your issue"
          />

          {/* Message Input */}
          <TextInput
            mode="outlined"
            label="Message *"
            value={supportForm.message}
            onChangeText={(text) => setSupportForm(prev => ({ ...prev, message: text }))}
            multiline
            numberOfLines={6}
            style={styles.style4}
            placeholder="Please provide detailed information about your issue..."
          />

          <Button
            mode="contained"
            onPress={handleSubmitTicket}
            style={styles.style4}
            icon="send"
          >
            Submit Ticket
          </Button>
        </Surface>

        {/* Support Hours */}
        <Surface style={styles.style5} elevation={1}>
          <View style={styles.style4}>
            <LocalIcon name="clock" size={32} color={theme.colors.onPrimaryContainer} />
            <View style={styles.style4}>
              <Text variant="titleMedium" style={styles.style3}>
                Support Hours
              </Text>
              <Text variant="bodyMedium" style={styles.style2}>
                Monday - Friday: 9:00 AM - 6:00 PM EST
              </Text>
              <Text variant="bodyMedium" style={styles.style1}>
                Saturday - Sunday: 10:00 AM - 4:00 PM EST
              </Text>
            </View>
          </View>
        </Surface>
      </ScrollView>
    </View>
  );
};






export default ContactSupportScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1},
  content: {
    flex: 1,
    padding: SPACING.lg},
  section: {
    borderRadius: BORDER_RADIUS.xl,
    padding: SPACING.lg,
    marginBottom: SPACING.lg,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)'},
  sectionTitle: {
    fontWeight: '700',
    marginBottom: SPACING.md},
  contactMethod: {
    paddingVertical: SPACING.sm,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)'},
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: BORDER_RADIUS.lg,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.sm},
  contactInfo: {
    alignItems: 'flex-end',
    justifyContent: 'center'},
  fieldLabel: {
    fontWeight: '600',
    marginBottom: SPACING.xs,
    marginTop: SPACING.sm},
  chipsContainer: {
    marginBottom: SPACING.md},
  chip: {
    marginRight: SPACING.sm,
    marginBottom: SPACING.xs},
  input: {
    marginBottom: SPACING.md},
  textArea: {
    marginBottom: SPACING.lg},
  submitButton: {
    marginTop: SPACING.sm},
  supportHours: {
    flexDirection: 'row',
    alignItems: 'center'},
  hoursText: {
    flex: 1,
    marginLeft: SPACING.md},
  style1: {
    marginTop: 4},
  style2: {
    marginTop: 4},
  style3: {
    fontWeight: '700'},
  style4: {
    marginBottom: SPACING.md},
  style5: {
    borderRadius: BORDER_RADIUS.xl,
    padding: SPACING.lg,
    marginBottom: SPACING.lg},
  style6: {
    marginRight: SPACING.sm,
    marginBottom: SPACING.xs},
  style7: {
    fontWeight: '600',
    marginBottom: SPACING.xs,
    marginTop: SPACING.sm},
  style8: {
    marginRight: SPACING.sm,
    marginBottom: SPACING.xs},
  style9: {
    borderRadius: BORDER_RADIUS.xl,
    padding: SPACING.lg,
    marginBottom: SPACING.lg},
  style10: {
    fontWeight: '600'},
  style11: {
    width: 48,
    height: 48,
    borderRadius: BORDER_RADIUS.lg,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.sm},
  style12: {
    flex: 1}});