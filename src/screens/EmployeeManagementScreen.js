/**
 * EmployeeManagementScreen - Admin-only employee management
 */

import React, { useState, useCallback, useMemo } from 'react';
import { FlatList, View, StyleSheet, Alert, RefreshControl } from 'react-native';
import { Text, Button, Surface, IconButton, Menu, Avatar, Chip, FAB } from 'react-native-paper';
import LocalIcon from '../components/LocalIcon';
import { useAuth, ROLES } from '../context/AuthContext';
import { useTheme } from '../context/ThemeContext';
import CommonHeader from '../components/CommonHeader';
import UnifiedFilterChips from '../components/UnifiedFilterChips';
import UnifiedSortMenu from '../components/UnifiedSortMenu';
import UnifiedEmptyState from '../components/UnifiedEmptyState';
import navigationService from '../services/NavigationService';
import { SPACING, BORDER_RADIUS, TYPOGRAPHY } from '../theme/designTokens';

const EmployeeManagementScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { employees, deleteEmployee, isAdmin } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('All');
  const [selectedSort, setSelectedSort] = useState({ key: 'createdAt', direction: 'desc' });
  const [menuVisible, setMenuVisible] = useState({});
  const [refreshing, setRefreshing] = useState(false);

  const filters = ['All', 'Admin', ...Object.values(ROLES).filter(r => r !== ROLES.ADMIN).map(r => r.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())), 'Active', 'Inactive'];
  const sortOptions = [
    { key: 'name', label: 'Name' }, { key: 'email', label: 'Email' },
    { key: 'role', label: 'Role' }, { key: 'createdAt', label: 'Date Added' },
  ];

  React.useEffect(() => {
    if (!isAdmin()) {
      Alert.alert('Access Denied', 'You do not have permission to access this page.');
      navigation.goBack();
    }
  }, [isAdmin, navigation]);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    // In a real app, you would refetch employee data here.
    await new Promise(resolve => setTimeout(resolve, 1000));
    setRefreshing(false);
  }, []);

  const filteredEmployees = useMemo(() => {
    let filtered = employees.filter(employee => {
      const searchLower = searchQuery.toLowerCase();
      const matchesSearch = employee.name.toLowerCase().includes(searchLower) ||
                           employee.email.toLowerCase().includes(searchLower) ||
                           employee.phone.includes(searchQuery);

      if (selectedFilter === 'All') return matchesSearch;
      if (selectedFilter === 'Admin') return matchesSearch && employee.role === ROLES.ADMIN;
      if (selectedFilter === 'Active') return matchesSearch && employee.status === 'active';
      if (selectedFilter === 'Inactive') return matchesSearch && employee.status === 'inactive';
      
      const roleFilter = selectedFilter.replace(' ', '_').toLowerCase();
      return matchesSearch && employee.role === roleFilter;
    });

    return [...filtered].sort((a, b) => {
      let aValue = a[selectedSort.key];
      let bValue = b[selectedSort.key];
      if (selectedSort.key === 'createdAt') {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      }
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }
      if (selectedSort.direction === 'asc') return aValue > bValue ? 1 : -1;
      return aValue < bValue ? 1 : -1;
    });
  }, [employees, searchQuery, selectedFilter, selectedSort]);

  // FIXED: Corrected useCallback syntax
  const handleAddEmployee = useCallback(() => {
    try {
      navigationService.navigate('AddEmployee');
    } catch (error) {
      console.error('Failed to navigate to AddEmployee:', error);
    }
  }, []);

  const handleEditEmployee = useCallback((employee) => {
    try {
      navigationService.navigate('AddEmployee', { employee, isEditing: true });
    } catch (error) {
      console.error('Failed to navigate to edit employee:', error);
    }
  }, []);

  const handleDeleteEmployee = useCallback((employee) => {
    Alert.alert(
      'Delete Employee',
      `Are you sure you want to delete "${employee.name}"? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            const result = await deleteEmployee(employee.id); // FIXED: Removed invalid syntax
            if (result.success) {
              Alert.alert('Success', 'Employee deleted successfully.');
            } else {
              Alert.alert('Error', result.error || 'Failed to delete employee.');
            }
          },
        },
      ]
    );
  }, [deleteEmployee]);

  const toggleMenu = (employeeId) => setMenuVisible(prev => ({ ...prev, [employeeId]: !prev[employeeId]));
  const closeMenu = (employeeId) => setMenuVisible(prev => ({ ...prev, [employeeId]: false }));
  const getEmployeeInitials = (name) => name.split(' ').map(word => word[0]).join('').substring(0, 2).toUpperCase();
  
  const getRoleColor = useCallback((role) => {
    return role === ROLES.ADMIN ? theme.colors.primary : theme.colors.secondary;
  }, [theme.colors.primary, theme.colors.secondary]);
  
  const getStatusColor = useCallback((status) => {
    return status === 'active' ? theme.colors.tertiary : theme.colors.error;
  }, [theme.colors.tertiary, theme.colors.error]);
  
  // FIXED: Stylesheet moved inside component and memoized
  const styles = useMemo(() => StyleSheet.create({
    container: { flex: 1, backgroundColor: theme.colors.background },
    content: { flex: 1, paddingHorizontal: SPACING.sm },
    filtersRow: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', paddingHorizontal: SPACING.md, marginBottom: SPACING.md },
    filtersContainer: { flex: 1, marginRight: SPACING.sm },
    statsRow: { flexDirection: 'row', justifyContent: 'space-around', marginBottom: SPACING.lg, paddingHorizontal: SPACING.md },
    statCard: { flex: 1, padding: SPACING.md, marginHorizontal: SPACING.xs, borderRadius: BORDER_RADIUS.lg, alignItems: 'center', backgroundColor: theme.colors.surface },
    employeeCard: { marginBottom: SPACING.md, borderRadius: BORDER_RADIUS.xl, borderWidth: 1, overflow: 'hidden', backgroundColor: theme.colors.surface, borderColor: theme.colors.outline },
    employeeContent: { padding: SPACING.lg },
    employeeHeader: { flexDirection: 'row', alignItems: 'flex-start' },
    employeeAvatar: { marginRight: SPACING.md },
    employeeInfo: { flex: 1 },
    employeeNameRow: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', marginBottom: 4 },
    employeeName: { ...TYPOGRAPHY.titleMedium, flexShrink: 1 },
    employeeEmail: { ...TYPOGRAPHY.bodySmall, color: theme.colors.onSurfaceVariant },
    employeePhone: { ...TYPOGRAPHY.bodySmall, color: theme.colors.onSurfaceVariant },
    badges: { flexDirection: 'row', gap: SPACING.xs },
    employeeMeta: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', marginTop: SPACING.md },
    actionButtons: { flexDirection: 'row', justifyContent: 'flex-end', paddingTop: SPACING.md, borderTopWidth: 1, borderTopColor: theme.colors.outline, marginTop: SPACING.md },
    actionButton: { minWidth: 80 },
    fab: { position: 'absolute', margin: 16, right: 0, bottom: 0, backgroundColor: theme.colors.primary },
    statNumber: { ...TYPOGRAPHY.headlineSmall, color: theme.colors.primary },
    statLabel: { ...TYPOGRAPHY.bodySmall, color: theme.colors.onSurfaceVariant, marginTop: 4 },
    adminCount: { ...TYPOGRAPHY.headlineSmall, color: theme.colors.secondary },
  }), [theme]);

  const renderEmployeeCard = useCallback(({ item: employee }) => {
    // FIXED: Dynamic styles created here, not in the main stylesheet
    const roleColor = getRoleColor(employee.role);
    const statusColor = getStatusColor(employee.status);
    const avatarStyle = { backgroundColor: roleColor };
    const roleChipStyle = { backgroundColor: roleColor + '20' };
    const statusChipStyle = { backgroundColor: statusColor + '20' };

    return (
      <Surface style={styles.employeeCard} elevation={1}>
        <View style={styles.employeeContent}>
          <View style={styles.employeeHeader}>
            <View style={styles.employeeAvatar}>
              {employee.avatar ? <Avatar.Image size={48} source={{ uri: employee.avatar }} /> : <Avatar.Text size={48} label={getEmployeeInitials(employee.name)} style={avatarStyle} />}
            </View>
            <View style={styles.employeeInfo}>
              <View style={styles.employeeNameRow}>
                <Text style={styles.employeeName} numberOfLines={1}>{employee.name}</Text>
                <Chip icon={employee.role === ROLES.ADMIN ? 'crown' : 'account'} style={roleChipStyle} textStyle={{ color: roleColor, fontSize: 10 }} compact>{employee.role.replace('_', ' ').toUpperCase()}</Chip>
              </View>
              <Text style={styles.employeeEmail}>{employee.email}</Text>
              <Text style={styles.employeePhone}>{employee.phone}</Text>
            </View>
            <Menu visible={menuVisible[employee.id} onDismiss={() => closeMenu(employee.id)} anchor={<IconButton icon="dots-vertical" size={20} onPress={() => toggleMenu(employee.id)} />}>
              <Menu.Item onPress={() => { closeMenu(employee.id); handleEditEmployee(employee); }} title="Edit" leadingIcon="pencil" />
              <Menu.Item onPress={() => { closeMenu(employee.id); handleDeleteEmployee(employee); }} title="Delete" leadingIcon="delete" />
            </Menu>
          </View>
          <View style={styles.employeeMeta}>
            <Chip icon="circle" style={statusChipStyle} textStyle={{ color: statusColor, fontSize: 10 }} compact>{employee.status?.toUpperCase() || 'ACTIVE'}</Chip>
            <Text style={styles.employeePhone}>Added: {new Date(employee.createdAt).toLocaleDateString()}</Text>
          </View>
        </View>
      </Surface>
    );
  }, [theme, menuVisible, handleEditEmployee, handleDeleteEmployee]);

  if (!isAdmin()) return null;

  return (
    <View style={styles.container}>
      <CommonHeader title="Employees" searchPlaceholder="Search employees..." onSearchChange={setSearchQuery} />
      <FlatList
        data={filteredEmployees}
        renderItem={renderEmployeeCard}
        keyExtractor={(item) => item.id.toString()}
        contentContainerStyle={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={handleRefresh} colors={[theme.colors.primary} tintColor={theme.colors.primary} />}
        ListHeaderComponent={<>
          <View style={styles.filtersRow}><View style={styles.filtersContainer}><UnifiedFilterChips filters={filters} selectedFilter={selectedFilter} onFilterChange={setSelectedFilter} /></View><UnifiedSortMenu sortOptions={sortOptions} selectedSort={selectedSort} onSortChange={setSelectedSort} /></View>
          <View style={styles.statsRow}>
            <Surface style={styles.statCard} elevation={1}><Text style={styles.statNumber}>{employees.length}</Text><Text style={styles.statLabel}>Total Staff</Text></Surface>
            <Surface style={styles.statCard} elevation={1}><Text style={styles.adminCount}>{employees.filter(e => e.role === ROLES.ADMIN).length}</Text><Text style={styles.statLabel}>Admins</Text></Surface>
          </View>
        </>}
        ListEmptyComponent={<UnifiedEmptyState type="employees" searchQuery={searchQuery} onActionPress={handleAddEmployee} />}
      />
      <FAB icon="plus" style={styles.fab} onPress={handleAddEmployee} />
    </View>
  );
};

export default EmployeeManagementScreen;