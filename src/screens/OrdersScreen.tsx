/**
 * Modern Orders Screen
 * Optimized, clean implementation with proper architecture
 */

import React, { useState, useCallback, useMemo } from 'react';
import { View,
  StyleSheet,
  FlatList,
  RefreshControl,
  Alert,
  } from 'react-native';
import { FAB   } from 'react-native-paper';
import { Portal   } from 'react-native-paper';
import { Modal   } from 'react-native-paper';
import { useTheme   } from '../context/ThemeContext';
import { useOrders   } from '../hooks/useOrders';
import { Order   } from '../types/order';

// Components
import CommonHeader from '../components/CommonHeader';
import UnifiedEmptyState from '../components/UnifiedEmptyState';
import OrderCard from '../components/orders/OrderCard';
import OrderFilters from '../components/orders/OrderFilters';
import OrderStats from '../components/orders/OrderStats';
import OrderDetailsModal from '../components/orders/OrderDetailsModal';

// Services
import dataReset from '../utils/dataReset';

interface OrdersScreenProps {
  navigation: object;
  route: object;
}

const OrdersScreen: React.FC<OrdersScreenProps> = ({ navigation, route }) => {
  const { theme  } = useTheme();
  const { orders,
    filteredOrders,
    stats,
    loading,
    refreshing,
    loadOrders,
    refreshOrders,
    updateFilters,
    updateSort,
    filters,
    sort,
    overdueOrders,
    dueSoonOrders,
   } = useOrders();

  // Local state
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [showOrderDetails, setShowOrderDetails] = useState(false);

  // Handle search
  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
    updateFilters({ search: query.trim() || undefined });
  }, [updateFilters]);

  // Handle order selection
  const handleOrderPress = useCallback((order: Order) => {
    setSelectedOrder(order);
    setShowOrderDetails(true);
  }, []);

  // Handle order actions
  const handleOrderAction = useCallback(async (action: string, order: Order) => {
    try {
      switch(action) {
        case 'edit':
          navigation.navigate('CreateOrder', { 
            mode: 'edit', 
            orderId: order.id 
          });
          break;
        
        case 'duplicate':
          navigation.navigate('CreateOrder', { 
            mode: 'duplicate', 
            orderId: order.id 
          });
          break;
        
        case 'delete':
          Alert.alert(
            'Archive Order',
            'Are you sure you want to archive this order?',
            [
              { text: 'Cancel', style: 'cancel' },
              {
                text: 'Archive',
                style: 'destructive',
                onPress: async () => {
                  // TODO: Implement archive functionality
                  await refreshOrders();
                },
              },
            ]
          );
          break;
        
        default:

      }
    } catch (error) {
      console.error('Error handling order action:', error);
      Alert.alert('Error', 'Failed to perform action. Please try again.');
    }
  }, [navigation, refreshOrders]);

  // Create new order
  const handleCreateOrder = useCallback(() => {
    navigation.navigate('CreateOrder', { mode: 'create' });
  }, [navigation]);

  // Clean database for fresh start
  const handleCleanDatabase = useCallback(() => {
    Alert.alert(
      'Clean Database',
      'This will remove all orders, customers, measurements, and fabrics. This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clean Database',
          style: 'destructive',
          onPress: async () => {
            try {
              await dataReset.resetAllAppData();
              await refreshOrders();
              Alert.alert('Success', 'Database cleaned successfully. App is now ready for fresh data.');
            } catch (error) {
              console.error('Error cleaning database:', error);
              Alert.alert('Error', 'Failed to clean database. Please try again.');
            }
          },
        },
      ]
    );
  }, [refreshOrders]);

  // Render order item
  const renderOrderItem = useCallback(({ item }: { item: Order }) => (
    <OrderCard
      order={item}
      onPress={() => handleOrderPress(item)}
      onAction={(action) => handleOrderAction(action, item)}
      style={styles.orderCard}
    />
  ), [handleOrderPress, handleOrderAction]);

  // Get item layout for optimization
  const getItemLayout = useCallback((data: unknown, index: number) => ({
    length: 120, // Estimated height of OrderCard
    offset: 120 * index,
    index,
  }), []);

  // Key extractor
  const keyExtractor = useCallback((item: Order) => item.id, []);

  // Memoized empty state
  const emptyState = useMemo(() => {
    if (loading) return null;

    const hasFilters = searchQuery || 
      filters.status?.length || 
      filters.type?.length || 
      filters.priority?.length;

    return (
      <UnifiedEmptyState
        icon="clipboard-text-outline"
        title={hasFilters ? "No orders found" : "No orders yet"}
        subtitle={
          hasFilters 
            ? "Try adjusting your filters or search terms"
            : "Create your first order to get started"
        }
        actionLabel={hasFilters ? "Clear Filters" : "Create Order"}
        onAction={hasFilters ? () => {
          setSearchQuery('');
          updateFilters({});
        } : handleCreateOrder}
      />
    );
  }, [loading, searchQuery, filters, handleCreateOrder, updateFilters]);

  // Memoized header stats
  const headerStats = useMemo(() => (
    <OrderStats
      stats={stats}
      overdueCount={overdueOrders.length}
      dueSoonCount={dueSoonOrders.length}
      style={styles.stats}
    />
  ), [stats, overdueOrders.length, dueSoonOrders.length]);

  return (
    <View style={styles.container}>
      <CommonHeader
        title="Orders"
        showSearch
        searchQuery={searchQuery}
        onSearchChange={handleSearch}
        searchPlaceholder="Search orders..."
        rightActions={[
          {
            icon: 'filter-variant',
            onPress: () => setShowFilters(true),
            badge: Object.keys(filters).length > 0 ? Object.keys(filters).length : undefined,
          },
          {
            icon: 'database-remove',
            onPress: handleCleanDatabase,
            tooltip: 'Clean Database',
          },
        }
      />

      {/* Stats Section */}
      {!loading && orders.length > 0 && headerStats}

      {/* Orders List */}
      <FlatList
        data={filteredOrders}
        renderItem={renderOrderItem}
        keyExtractor={keyExtractor}
        getItemLayout={getItemLayout}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={refreshOrders}
            colors={[theme.colors.primary}
            tintColor={theme.colors.primary}
          />
        }
        ListEmptyComponent={emptyState}
        removeClippedSubviews={true}
        maxToRenderPerBatch={10}
        windowSize={10}
        initialNumToRender={10}
        updateCellsBatchingPeriod={50}
      />

      {/* Floating Action Button */}
      <FAB
        icon="plus"
        style={styles.fab}
        onPress={handleCreateOrder}
        label="New Order"
      />

      {/* Filters Modal */}
      <Portal>
        <Modal
          visible={showFilters}
          onDismiss={() => setShowFilters(false)}
          contentContainerStyle={[
            styles.modal,
            { backgroundColor: theme.colors.surface }
          }>
          <OrderFilters
            filters={filters}
            sort={sort}
            onFiltersChange={updateFilters}
            onSortChange={updateSort}
            onClose={() => setShowFilters(false)}
          />
        </Modal>
      </Portal>

      {/* Order Details Modal */}
      <Portal>
        <OrderDetailsModal
          visible={showOrderDetails}
          order={selectedOrder}
          onDismiss={() => {
            setShowOrderDetails(false);
            setSelectedOrder(null);
          }
          onEdit={(order) => {
            setShowOrderDetails(false);
            navigation.navigate('CreateOrder', { 
              mode: 'edit', 
              orderId: order.id 
            });
          }}
          onRefresh={refreshOrders}
        />
      </Portal>
    </View>
  );
};






export default OrdersScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  stats: {
    marginHorizontal: 16,
    marginBottom: 8,
  },
  listContainer: {
    flexGrow: 1,
    paddingHorizontal: 16,
    paddingBottom: 100, // Space for FAB
  },
  orderCard: {
    marginBottom: 12,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
    elevation: 8,
  },
  modal: {
    margin: 20,
    borderRadius: 12,
    padding: 20,
    maxHeight: '80%',
  },
});