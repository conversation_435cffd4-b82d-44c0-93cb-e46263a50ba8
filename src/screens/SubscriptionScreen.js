/**
 * SubscriptionScreen - Subscription tiers management for tailor management system
 */

import React, { useState, useMemo, useCallback } from 'react';
import { View, ScrollView, StyleSheet, FlatList } from 'react-native';
import { Text } from 'react-native-paper';
import { Surface } from 'react-native-paper';
import { Button } from 'react-native-paper';
import { Chip } from 'react-native-paper';
import { Divider } from 'react-native-paper';
import LocalIcon from '../components/LocalIcon';
import { useAuth } from '../context/AuthContext';
import { useTheme } from '../context/ThemeContext';
import CommonHeader from '../components/CommonHeader';
import StatCardGroup from '../components/StatCardGroup';
import navigationService from '../services/NavigationService';
import { useAlert } from '../services/AlertService';

const SubscriptionScreen = () => {
  const { theme } = useTheme();
  const { user, isAdmin } = useAuth();
  const { showConfirm, showSuccess } = useAlert();
  const [selectedPlan, setSelectedPlan] = useState('growth');

  // Subscription tiers configuration
  const subscriptionTiers = [
    {
      id: 'starter',
      name: 'Starter',
      price: 2500, // BDT per month
      yearlyPrice: 25000, // BDT per year (2 months free)
      description: 'Perfect for small tailor shops',
      color: '#4CAF50',
      icon: 'rocket-launch',
      features: [
        'Order Management',
        'Customer Measurements',
        'Basic Customer Database',
        'Order Status Tracking',
        'Up to 100 orders/month',
        'Email Support',
      ],
      limitations: [
        'No sales tracking',
        'No employee management',
        'No accounting features',
        'No advanced reports',
        'No inventory management',
      ],
      modules: ['orders', 'measurements', 'customers_basic'],
    },
    {
      id: 'growth',
      name: 'Growth',
      price: 4500, // BDT per month
      yearlyPrice: 45000, // BDT per year
      description: 'For growing tailor businesses',
      color: '#2196F3',
      icon: 'trending-up',
      popular: true,
      features: [
        'Everything in Starter',
        'Sales Tracking',
        'Basic Reports',
        'Fabric Inventory',
        'Ready-made Garment Sales',
        'Up to 500 orders/month',
        'Priority Email Support',
        'WhatsApp Integration',
      ],
      limitations: [
        'No employee management',
        'No advanced accounting',
        'No advanced reports',
        'Limited inventory features',
      ],
      modules: ['orders', 'measurements', 'customers', 'sales', 'inventory_basic', 'reports_basic'],
    },
    {
      id: 'pro',
      name: 'Pro',
      price: 7500, // BDT per month
      yearlyPrice: 75000, // BDT per year
      description: 'Complete business management',
      color: '#9C27B0',
      icon: 'crown',
      features: [
        'Everything in Growth',
        'Employee Management',
        'Attendance Tracking',
        'Salary Management',
        'Complete Accounting',
        'Advanced Reports',
        'Complete Inventory Management',
        'Unlimited orders',
        'Phone & WhatsApp Support',
        'Custom Features',
      ],
      limitations: [],
      modules: ['all'],
    },
  ];

  // Current subscription status (mock data)
  const [currentSubscription, setCurrentSubscription] = useState({
    plan: 'growth',
    status: 'active',
    startDate: '2025-01-01',
    endDate: '2025-12-31',
    billingCycle: 'yearly',
    nextBillingDate: '2025-12-31',
    autoRenew: true,
    usage: {
      orders: 45,
      customers: 120,
      employees: 3,
      storage: 2.5, // GB
    },
    limits: {
      orders: 500,
      customers: 1000,
      employees: 10,
      storage: 10, // GB
    },
  });

  const getCurrentPlan = () => {
    return subscriptionTiers.find(tier => tier.id === currentSubscription.plan);
  };

  const handleUpgrade = useCallback((planId) => {
    const planName = subscriptionTiers.find(t => t.id === planId)?.name;
    showConfirm(
      'Upgrade Subscription',
      `Are you sure you want to upgrade to ${planName}?`,
      () => {
        showSuccess('Success', 'Subscription upgraded successfully!');
        setCurrentSubscription(prev => ({ ...prev, plan: planId }));
      }
    );
  }, []);

  const handleDowngrade = useCallback((planId) => {
    const planName = subscriptionTiers.find(t => t.id === planId)?.name;
    showConfirm(
      'Downgrade Subscription',
      `Are you sure you want to downgrade to ${planName}? Some features will be disabled.`,
      () => {
        showSuccess('Success', 'Subscription downgraded successfully!');
        setCurrentSubscription(prev => ({ ...prev, plan: planId }));
      }
    );
  }, []);

  const renderSubscriptionCard = ({ item: tier }) => {
    const isCurrentPlan = tier.id === currentSubscription.plan;
    const currentPlan = getCurrentPlan();
    const isUpgrade = subscriptionTiers.findIndex(t => t.id === tier.id) > subscriptionTiers.findIndex(t => t.id === currentPlan?.id);
    const isDowngrade = subscriptionTiers.findIndex(t => t.id === tier.id) < subscriptionTiers.findIndex(t => t.id === currentPlan?.id);

    return (
      <Surface 
        style={[
          styles.planCard,
          isCurrentPlan && { borderColor: tier.color, borderWidth: 2 }
        ]} 
        elevation={isCurrentPlan ? 3 : 1}
      >
        {tier.popular && (
          <View style={styles.popularBadge}>
            <Text variant="labelSmall" style={styles.popularBadgeText}>
              MOST POPULAR
            </Text>
          </View>
        )}

        <View style={styles.planHeader}>
          <View style={styles.planTitleRow}>
            <LocalIcon name={tier.icon} size={32} color={tier.color} />
            <View style={styles.planTitleText}>
              <Text variant="headlineSmall" style={styles.planTitleStyle}>
                {tier.name}
              </Text>
              <Text variant="bodyMedium" style={styles.planDescription}>
                {tier.description}
              </Text>
            </View>
          </View>

          {isCurrentPlan && (
            <Chip icon="check-circle" style={styles.currentPlanChip}>
              <Text style={styles.currentPlanText}>Current Plan</Text>
            </Chip>
          )}
        </View>

        <View style={styles.planPricing}>
          <Text variant="headlineLarge" style={styles.priceText}>
            ৳{tier.price.toLocaleString()}
          </Text>
          <Text variant="bodyMedium" style={styles.planDescription}>
            /month
          </Text>
        </View>

        <Text variant="bodySmall" style={styles.yearlyPriceText}>
          ৳{tier.yearlyPrice.toLocaleString()}/year (Save ৳{(tier.price * 12 - tier.yearlyPrice).toLocaleString()})
        </Text>

        <View style={styles.planFeatures}>
          <Text variant="titleSmall" style={styles.featuresTitle}>
            Features Included:
          </Text>
          {tier.features.map((feature, index) => (
            <View key={index} style={styles.featureItem}>
              <LocalIcon name="check" size={16} color={tier.color} />
              <Text variant="bodySmall" style={styles.featureText}>
                {feature}
              </Text>
            </View>
          ))}

          {tier.limitations.length > 0 && (
            <>
              <Text variant="titleSmall" style={styles.limitationsTitle}>
                Not Included:
              </Text>
              {tier.limitations.map((limitation, index) => (
                <View key={index} style={styles.featureItem}>
                  <LocalIcon name="close" size={16} color={theme.colors.onSurfaceVariant} />
                  <Text variant="bodySmall" style={styles.limitationText}>
                    {limitation}
                  </Text>
                </View>
              ))}
            </>
          )}
        </View>

        <View style={styles.planActions}>
          {isCurrentPlan ? (
            <Button
              mode="outlined"
              onPress={() => showSuccess('Manage Subscription', 'Subscription management options')}
              style={styles.actionButton}
            >
              Manage Plan
            </Button>
          ) : isUpgrade ? (
            <Button
              mode="contained"
              onPress={() => handleUpgrade(tier.id)}
              style={[styles.actionButton, { backgroundColor: tier.color }]}
            >
              Upgrade to {tier.name}
            </Button>
          ) : isDowngrade ? (
            <Button
              mode="outlined"
              onPress={() => handleDowngrade(tier.id)}
              style={styles.actionButton}
              textColor={theme.colors.onSurfaceVariant}
            >
              Downgrade to {tier.name}
            </Button>
          ) : (
            <Button
              mode="contained"
              onPress={() => handleUpgrade(tier.id)}
              style={[styles.actionButton, { backgroundColor: tier.color }]}
            >
              Select {tier.name}
            </Button>
          )}
        </View>
      </Surface>
    );
  };

  const usagePercentage = (used, limit) => {
    return limit > 0 ? Math.min((used / limit) * 100, 100) : 0;
  };

  return (
    <View style={styles.container}>
      <CommonHeader
        title="Subscription Plans"
        subtitle="Choose the perfect plan for your business"
        showSearch={false}
        onNotificationPress={() => console.log('Notifications pressed')}
        onProfilePress={() => navigationService.navigate('MyProfile')}
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Current Subscription Status */}
        <Surface style={styles.section} elevation={1}>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Current Subscription
          </Text>

          <View style={styles.currentPlanInfo}>
            <View style={styles.currentPlanHeader}>
              <LocalIcon name={getCurrentPlan()?.icon} size={24} color={getCurrentPlan()?.color} />
              <Text variant="titleLarge" style={styles.currentPlanTitle}>
                {getCurrentPlan()?.name} Plan
              </Text>
              <Chip 
                icon="check-circle" 
                style={styles.activeChip}
              >
                <Text style={styles.activeChipText}>Active</Text>
              </Chip>
            </View>

            <Text variant="bodyMedium" style={styles.nextBillingText}>
              Next billing: {new Date(currentSubscription.nextBillingDate).toLocaleDateString()}
            </Text>

            {/* Usage Statistics */}
            <StatCardGroup
              cards={[
                {
                  key: 'orders_usage',
                  title: 'Orders Used',
                  value: `${currentSubscription.usage.orders}/${currentSubscription.limits.orders}`,
                  subtitle: `${usagePercentage(currentSubscription.usage.orders, currentSubscription.limits.orders).toFixed(1)}% used`,
                  icon: 'clipboard-list',
                  iconColor: theme.colors.primary,
                },
                {
                  key: 'customers_usage',
                  title: 'Customers',
                  value: `${currentSubscription.usage.customers}/${currentSubscription.limits.customers}`,
                  subtitle: `${usagePercentage(currentSubscription.usage.customers, currentSubscription.limits.customers).toFixed(1)}% used`,
                  icon: 'account-group',
                  iconColor: theme.colors.secondary,
                },
                {
                  key: 'employees_usage',
                  title: 'Employees',
                  value: `${currentSubscription.usage.employees}/${currentSubscription.limits.employees}`,
                  subtitle: `${usagePercentage(currentSubscription.usage.employees, currentSubscription.limits.employees).toFixed(1)}% used`,
                  icon: 'account-supervisor',
                  iconColor: theme.colors.tertiary,
                },
                {
                  key: 'storage_usage',
                  title: 'Storage',
                  value: `${currentSubscription.usage.storage}/${currentSubscription.limits.storage} GB`,
                  subtitle: `${usagePercentage(currentSubscription.usage.storage, currentSubscription.limits.storage).toFixed(1)}% used`,
                  icon: 'database',
                  iconColor: '#FF9800',
                },
              ]}
              columns={4}
              showTitle={false}
              containerStyle={{ marginTop: 16 }}
            />
          </View>
        </Surface>

        {/* Available Plans */}
        <Surface style={styles.section} elevation={1}>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Available Plans
          </Text>

          <FlatList
            data={subscriptionTiers}
            renderItem={renderSubscriptionCard}
            keyExtractor={(item) => item.id}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
            ItemSeparatorComponent={() => <View style={styles.separator} />}
          />
        </Surface>

        {/* Billing Information */}
        <Surface style={styles.section} elevation={1}>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Billing Information
          </Text>

          <View style={styles.billingInfo}>
            <View style={styles.billingItem}>
              <Text variant="bodyMedium" style={styles.billingLabel}>Billing Cycle</Text>
              <Text variant="bodyMedium" style={styles.billingValue}>
                {currentSubscription.billingCycle === 'yearly' ? 'Yearly' : 'Monthly'}
              </Text>
            </View>
            <Divider />
            <View style={styles.billingItem}>
              <Text variant="bodyMedium" style={styles.billingLabel}>Auto Renewal</Text>
              <Text variant="bodyMedium" style={styles.autoRenewalText}>
                {currentSubscription.autoRenew ? 'Enabled' : 'Disabled'}
              </Text>
            </View>
            <Divider />
            <View style={styles.billingItem}>
              <Text variant="bodyMedium" style={styles.billingLabel}>Subscription Start</Text>
              <Text variant="bodyMedium" style={styles.planDescription}>
                {new Date(currentSubscription.startDate).toLocaleDateString()}
              </Text>
            </View>
          </View>

          <Button
            mode="outlined"
            onPress={() => showSuccess('Billing History', 'View billing history and invoices')}
            style={styles.billingHistoryButton}
            icon="receipt"
          >
            View Billing History
          </Button>
        </Surface>
      </ScrollView>
    </View>
  );
};

export default SubscriptionScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  section: {
    padding: 16,
    marginBottom: 16,
    borderRadius: 12,
    backgroundColor: '#ffffff',
  },
  sectionTitle: {
    marginBottom: 16,
    fontWeight: '600',
    color: '#333333',
  },
  currentPlanInfo: {
    marginTop: 8,
  },
  currentPlanHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  planCard: {
    padding: 20,
    borderRadius: 16,
    position: 'relative',
    backgroundColor: '#ffffff',
  },
  popularBadge: {
    position: 'absolute',
    top: -8,
    left: 20,
    right: 20,
    paddingVertical: 4,
    paddingHorizontal: 12,
    borderRadius: 12,
    alignItems: 'center',
    zIndex: 1,
    backgroundColor: '#2196F3',
  },
  popularBadgeText: {
    color: 'white',
    fontWeight: '600',
  },
  planHeader: {
    marginTop: 8,
    marginBottom: 16,
  },
  planTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  planTitleText: {
    marginLeft: 12,
    flex: 1,
  },
  planTitleStyle: {
    color: '#333333',
    fontWeight: '600',
  },
  planDescription: {
    color: '#666666',
  },
  currentPlanChip: {
    backgroundColor: '#4CAF50',
    marginLeft: 'auto',
  },
  currentPlanText: {
    color: 'white',
    fontWeight: '600',
  },
  planPricing: {
    flexDirection: 'row',
    alignItems: 'baseline',
    justifyContent: 'center',
    marginBottom: 8,
  },
  priceText: {
    color: '#2196F3',
    fontWeight: '700',
  },
  yearlyPriceText: {
    color: '#666666',
    textAlign: 'center',
    marginBottom: 16,
  },
  planFeatures: {
    marginBottom: 20,
  },
  featuresTitle: {
    color: '#333333',
    marginBottom: 8,
    fontWeight: '600',
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  featureText: {
    color: '#333333',
    marginLeft: 8,
  },
  limitationsTitle: {
    color: '#666666',
    marginTop: 12,
    marginBottom: 8,
    fontWeight: '600',
  },
  limitationText: {
    color: '#666666',
    marginLeft: 8,
  },
  planActions: {
    marginTop: 'auto',
  },
  actionButton: {
    width: '100%',
  },
  currentPlanTitle: {
    color: '#333333',
    fontWeight: '600',
    marginLeft: 8,
  },
  activeChip: {
    backgroundColor: '#4CAF50',
    marginLeft: 'auto',
  },
  activeChipText: {
    color: 'white',
    fontWeight: '600',
  },
  nextBillingText: {
    color: '#666666',
    marginVertical: 8,
  },
  separator: {
    height: 16,
  },
  billingInfo: {
    marginTop: 8,
  },
  billingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  billingLabel: {
    color: '#333333',
  },
  billingValue: {
    color: '#2196F3',
    fontWeight: '600',
  },
  autoRenewalText: {
    color: '#4CAF50',
    fontWeight: '600',
  },
  billingHistoryButton: {
    marginTop: 16,
  },
});