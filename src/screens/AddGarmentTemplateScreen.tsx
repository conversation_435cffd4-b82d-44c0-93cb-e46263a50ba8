/**
 * Add/Edit Garment Template Screen
 * Form for creating and editing garment templates with measurements
 */

import React, { useState, useCallback, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Alert   } from 'react-native';
import { Text   } from 'react-native-paper';
import { TextInput   } from 'react-native-paper';
import { Button   } from 'react-native-paper';
import { Surface   } from 'react-native-paper';
import { SegmentedButtons   } from 'react-native-paper';
import { IconButton   } from 'react-native-paper';
import { Divider   } from 'react-native-paper';
import { Switch   } from 'react-native-paper';
import { useSafeAreaInsets   } from 'react-native-safe-area-context';
import LocalIcon from '../components/LocalIcon';
import { useTheme   } from '../context/ThemeContext';
import { useData   } from '../context/DataContext';
import { useAlert   } from '../services/AlertService';
import { Appbar   } from 'react-native-paper';

const AddGarmentTemplateScreen = ({ route, navigation }) => {
  const { theme  } = useTheme();
  const insets = useSafeAreaInsets();
  const { actions  } = useData();
  const alert = useAlert();
  
  const editingTemplate = route?.params?.template;
  const isEditing = !!editingTemplate;

  const [templateData, setTemplateData] = useState({
    name: editingTemplate?.name || '',
    category: editingTemplate?.category || 'Men',
    basePrice: editingTemplate?.basePrice?.toString() || '',
    description: editingTemplate?.description || '',
    measurementFields: editingTemplate?.measurementFields || [],
    isActive: editingTemplate?.isActive ?? true});

  const [errors, setErrors] = useState({});
  const [newFieldData, setNewFieldData] = useState({
    label: '',
    unit: 'inches',
    required: true});

  const categories = ['Men', 'Women', 'Unisex', 'Children'];
  const units = ['inches', 'cm', 'feet'];
  const handleInputChange = useCallback((field, value) => {
    setTemplateData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if(errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  }, [errors]);

  const handleAddMeasurementField = useCallback(() => {
    if (!newFieldData.label.trim()) {
      Alert.alert('Error', 'Please enter a field label');
      return;
    }

    // Check for duplicate field names
    const isDuplicate = templateData.measurementFields.some(
      field => field.label.toLowerCase() === newFieldData.label.toLowerCase().trim()
    );

    if(isDuplicate) {
      Alert.alert('Error', 'A field with this name already exists');
      return;
    }

    const newField = {
      id: Date.now().toString(),
      label: newFieldData.label.trim(),
      unit: newFieldData.unit,
      required: newFieldData.required};

    setTemplateData(prev => ({
      ...prev,
      measurementFields: [...prev.measurementFields, newField]
    }));

    // Reset form
    setNewFieldData({
      label: '',
      unit: 'inches',
      required: true});
  }, [newFieldData, templateData.measurementFields]);

  const handleRemoveMeasurementField = useCallback((fieldId) => {
    setTemplateData(prev => ({
      ...prev,
      measurementFields: prev.measurementFields.filter(field => field.id !== fieldId)
    }));
  }, []);

  const validateForm = useCallback(() => {
    const newErrors = {};

    if (!templateData.name.trim()) {
      newErrors.name = 'Template name is required';
    }

    if (!templateData.basePrice.trim()) {
      newErrors.basePrice = 'Base price is required';
    } else if (isNaN(parseFloat(templateData.basePrice)) || parseFloat(templateData.basePrice) < 0) {
      newErrors.basePrice = 'Please enter a valid price';
    }

    if(templateData.measurementFields.length === 0) {
      newErrors.measurementFields = 'At least one measurement field is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [templateData]);

  const handleSave = useCallback(async () => {
    if (!validateForm()) {
      alert.showError('Validation Error', 'Please fix the errors and try again');
      return;
    }

    try {
      const templateToSave = {
        ...templateData,
        basePrice: parseFloat(templateData.basePrice),
        updatedAt: new Date().toISOString()};

      if(isEditing) {
        const updatedTemplate = {
          ...editingTemplate,
          ...templateToSave};
        await actions.updateGarmentTemplate(updatedTemplate);
        alert.showSuccess('Success', 'Template updated successfully!', [
          { text: 'OK', onPress: () => navigation.goBack() }
        ]);
      } else {
        const newTemplate = {
          id: Date.now().toString(),
          ...templateToSave,
          createdAt: new Date().toISOString()};
        await actions.addGarmentTemplate(newTemplate);
        alert.showSuccess('Success', 'Template created successfully!', [
          { text: 'OK', onPress: () => navigation.goBack() }
        ]);
      }
    } catch (error) {
      console.error('Error saving template:', error);
      alert.showError('Error', 'Failed to save template. Please try again.');
    }
  }, [templateData, validateForm, isEditing, editingTemplate, actions, navigation]);

  return (
    <View style={styles.container}>
      {/* Small Appbar */}
      <Appbar.Header
        style={styles.header}
        elevation={2}>
        <Appbar.BackAction onPress={() => navigation.goBack()} />
        <Appbar.Content
          title={isEditing ? 'Edit Template' : 'Add Template'}
          titleStyle={styles.appbarTitle}
        />
      </Appbar.Header>

      <ScrollView 
        style={styles.content} 
        contentContainerStyle={{ paddingBottom: insets.bottom + 32 }} showsVerticalScrollIndicator={false}>
        {/* Basic Information */}
        <Surface style={styles.section} elevation={1}>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Basic Information
          </Text>

          <TextInput
            label="Template Name *"
            value={templateData.name}
            onChangeText={(text) => handleInputChange('name', text)}
            mode="outlined"
            style={styles.input}
            error={!!errors.name}
            placeholder="e.g., Formal Shirt, Wedding Suit"
          />
          {errors.name && (
            <Text variant="bodySmall" style={styles.errorText}>
              {errors.name}
            </Text>
          )}

          <Text variant="bodyMedium" style={styles.label}>
            Category *
          </Text>
          <SegmentedButtons
            value={templateData.category}
            onValueChange={(value) => handleInputChange('category', value)}
            buttons={categories.map(cat => ({ value: cat, label: cat }))}
            style={styles.input}
          />

          <TextInput
            label="Base Price (৳) *"
            value={templateData.basePrice}
            onChangeText={(text) => handleInputChange('basePrice', text)}
            mode="outlined"
            style={styles.input}
            error={!!errors.basePrice}
            keyboardType="numeric"
            placeholder="0"
          />
          {errors.basePrice && (
            <Text variant="bodySmall" style={styles.errorText}>
              {errors.basePrice}
            </Text>
          )}

          <TextInput
            label="Description"
            value={templateData.description}
            onChangeText={(text) => handleInputChange('description', text)}
            mode="outlined"
            style={styles.input}
            multiline
            numberOfLines={2}
            placeholder="Brief description of the garment"
          />

          <View style={styles.switchRow}>
            <Text variant="bodyMedium" style={styles.sectionTitle}>
              Active Template
            </Text>
            <Switch
              value={templateData.isActive}
              onValueChange={(value) => handleInputChange('isActive', value)}
            />
          </View>
        </Surface>

        {/* Measurement Fields */}
        <Surface style={styles.section} elevation={1}>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Measurement Fields ({templateData.measurementFields.length})
          </Text>
          {errors.measurementFields && (
            <Text variant="bodySmall" style={styles.errorText}>
              {errors.measurementFields}
            </Text>
          )}

          {/* Add New Field */}
          <View style={styles.fieldContainer}>
            <Text variant="bodyMedium" style={styles.sectionTitle}>
              Add Measurement Field
            </Text>
            
            <TextInput
              label="Field Label"
              value={newFieldData.label}
              onChangeText={(text) => setNewFieldData(prev => ({ ...prev, label: text }))}
              mode="outlined"
              style={styles.input}
              placeholder="e.g., Chest, Waist, Length"
            />

            <View style={styles.fieldRow}>
              <View style={styles.fieldColumn}>
                <Text variant="bodySmall" style={styles.label}>
                  Unit
                </Text>
                <SegmentedButtons
                  value={newFieldData.unit}
                  onValueChange={(value) => setNewFieldData(prev => ({ ...prev, unit: value }))}
                  buttons={units.map(unit => ({ value: unit, label: unit }))}
                  style={styles.input}
                />
              </View>

              <View style={styles.fieldColumn}>
                <Text variant="bodySmall" style={styles.label}>
                  Required
                </Text>
                <Switch
                  value={newFieldData.required}
                  onValueChange={(value) => setNewFieldData(prev => ({ ...prev, required: value }))}
                />
              </View>
            </View>

            <Button
              mode="outlined"
              onPress={handleAddMeasurementField}
              style={styles.input}
              icon="plus"
            >
              Add Field
            </Button>
          </View>

          <Divider style={styles.divider} />

          {/* Existing Fields */}
          <View style={styles.fieldContainer}>
            <Text variant="bodyMedium" style={styles.sectionTitle}>
              Current Fields
            </Text>
            
            {templateData.measurementFields.length === 0 ? (
              <Text variant="bodyMedium" style={styles.label}>
                No measurement fields added yet
              </Text>
            ) : (
              templateData.measurementFields.map((field, index) => (
                <View key={field.id} style={styles.fieldItem}>
                  <View style={styles.fieldInfo}>
                    <Text variant="bodyLarge" style={styles.fieldTitle}>
                      {field.label}
                    </Text>
                    <Text variant="bodySmall" style={styles.label}>
                      {field.unit} • {field.required ? 'Required' : 'Optional'}
                    </Text>
                  </View>
                  <IconButton
                    icon="delete"
                    size={20}
                    onPress={() => handleRemoveMeasurementField(field.id)}
                    iconColor={theme.colors.error}
                  />
                </View>
              ))
            )}
          </View>
        </Surface>

        {/* Action Buttons */}
        <View style={styles.buttonContainer}>
          <Button
            mode="outlined"
            onPress={() => navigation.goBack()}
            style={styles.button}>
            Cancel
          </Button>
          <Button
            mode="contained"
            onPress={handleSave}
            style={styles.button}
            icon="check"
          >
            {isEditing ? 'Update Template' : 'Create Template'}
          </Button>
        </View>
      </ScrollView>
    </View>
  );
};






export default AddGarmentTemplateScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1},
  header: {
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)'},
  content: {
    flex: 1,
    padding: 16},
  section: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16},
  sectionTitle: {
    fontWeight: '600',
    marginBottom: 16},
  input: {
    marginBottom: 12},
  label: {
    marginBottom: 8,
    marginTop: 8},
  errorText: {
    color: '#d32f2f',
    marginTop: 4},
  switchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12},
  fieldContainer: {
    marginBottom: 12},
  fieldRow: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 12},
  fieldColumn: {
    flex: 1},
  fieldItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: 8},
  fieldInfo: {
    flex: 1},
  fieldTitle: {
    fontWeight: '600'},
  divider: {
    marginBottom: 12},
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 16},
  button: {
    flex: 1}});