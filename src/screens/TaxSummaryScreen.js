/**
 * TaxSummaryScreen - Dedicated Tax Summary Screen
 * Converted from bottomsheet to full screen for better tax management
 */

import React, { useState, useEffect, useCallback } from 'react';
import { View, ScrollView, StyleSheet, Alert } from 'react-native';
import { Text } from 'react-native-paper';
import { Card } from 'react-native-paper';
import { Button } from 'react-native-paper';
import { Surface } from 'react-native-paper';
import { Divider } from 'react-native-paper';
import { Chip } from 'react-native-paper';
import { SegmentedButtons } from 'react-native-paper';
import LocalIcon from '../components/LocalIcon';
import { useTheme } from '../context/ThemeContext';
import { useData } from '../context/DataContext';
import CommonHeader from '../components/CommonHeader';
import { SPACING, BORDER_RADIUS } from '../theme/designTokens';
import navigationService from '../services/NavigationService';

const TaxSummaryScreen = () => {
  const { theme } = useTheme();
  const { state } = useData();
  const [selectedPeriod, setSelectedPeriod] = useState('month');
  const [taxData, setTaxData] = useState(null);

  // Mock data generator
  const generateTaxData = () => {
    const orders = state.orders || [];
    const totalRevenue = orders.reduce((sum, order) => sum + (order.total || 0), 0);
    
    return {
      period: selectedPeriod,
      summary: {
        totalRevenue: totalRevenue,
        taxableIncome: totalRevenue * 0.85,
        totalTaxDue: totalRevenue * 0.85 * 0.15,
        taxPaid: totalRevenue * 0.85 * 0.12,
        taxPending: totalRevenue * 0.85 * 0.03,
      },
      breakdown: {
        vat: {
          rate: 15,
          amount: totalRevenue * 0.15,
          status: 'due',
        },
        incomeTax: {
          rate: 10,
          amount: totalRevenue * 0.85 * 0.10,
          status: 'paid',
        },
        businessTax: {
          rate: 2.5,
          amount: totalRevenue * 0.025,
          status: 'pending',
        },
      },
      compliance: {
        vatReturn: {
          dueDate: '2024-01-15',
          status: 'pending',
          amount: totalRevenue * 0.15,
        },
        incomeTaxReturn: {
          dueDate: '2024-03-31',
          status: 'filed',
          amount: totalRevenue * 0.85 * 0.10,
        },
      },
      deductions: {
        businessExpenses: totalRevenue * 0.4,
        depreciation: totalRevenue * 0.05,
        other: totalRevenue * 0.02,
        total: totalRevenue * 0.47,
      }
    };
  };

  useEffect(() => {
    setTaxData(generateTaxData());
  }, [selectedPeriod, state.orders]);

  const formatCurrency = (amount) => {
    return `৳${amount.toLocaleString('en-BD', { minimumFractionDigits: 2 })}`;
  };

  const getStatusColor = (status) => {
    const colors = {
      paid: '#4CAF50',
      due: '#FF9800',
      pending: '#F44336',
      filed: '#2196F3',
    };
    return colors[status] || theme.colors.onSurfaceVariant;
  };

  const getStatusIcon = (status) => {
    const icons = {
      paid: 'check-circle',
      due: 'clock',
      pending: 'alert-circle',
      filed: 'file-check',
    };
    return icons[status] || 'information';
  };

  const scheduleTaxReminder = () => {
    Alert.alert('Reminder Set', 'Tax payment reminders have been scheduled.');
  };

  const exportTaxReport = () => {
    Alert.alert('Export', 'Tax report exported successfully!');
  };

  if (!taxData) {
    return (
      <View style={styles.style18}]}>
        <CommonHeader
          title="Tax Summary"
          subtitle="Loading..."
          onNotificationPress={() => console.log('Notifications')}
          onProfilePress={() => navigationService.navigate('MyProfile')}
        />
      </View>
    );
  }

  return (
    <View style={styles.style18}]}>
      <CommonHeader
        title="Tax Summary"
        subtitle="Tax obligations and compliance"
        onNotificationPress={() => console.log('Notifications')}
        onProfilePress={() => navigationService.navigate('MyProfile')}
      />

      {/* Period Selection */}
      <Surface style={styles.style11}]} elevation={1}>
        <SegmentedButtons
          value={selectedPeriod}
          onValueChange={setSelectedPeriod}
          buttons={[
            { value: 'month', label: 'Month' },
            { value: 'quarter', label: 'Quarter' },
            { value: 'year', label: 'Year' },
          ]}
        />
      </Surface>

      <ScrollView style={styles.style1} showsVerticalScrollIndicator={false}>
        {/* Tax Summary */}
        <Card style={styles.style11}]}>
          <Card.Content>
            <View style={styles.style1}>
              <LocalIcon name="calculator" size={24} color={theme.colors.primary} />
              <Text variant="titleMedium" style={styles.style10}}>
                Tax Summary
              </Text>
            </View>
            
            <View style={styles.style1}>
              <Text variant="bodyMedium" style={styles.style12}}>
                Total Revenue
              </Text>
              <Text variant="bodyMedium" style={styles.style17}}>
                {formatCurrency(taxData.summary.totalRevenue)}
              </Text>
            </View>
            
            <View style={styles.style1}>
              <Text variant="bodyMedium" style={styles.style12}}>
                Taxable Income
              </Text>
              <Text variant="bodyMedium" style={styles.style17}}>
                {formatCurrency(taxData.summary.taxableIncome)}
              </Text>
            </View>

            <Divider style={styles.style1} />
            
            <View style={styles.style1}>
              <Text variant="bodyMedium" style={styles.style12}}>
                Total Tax Due
              </Text>
              <Text variant="bodyMedium" style={styles.style16}}>
                {formatCurrency(taxData.summary.totalTaxDue)}
              </Text>
            </View>
            
            <View style={styles.style1}>
              <Text variant="bodyMedium" style={styles.style12}}>
                Tax Paid
              </Text>
              <Text variant="bodyMedium" style={styles.style15}}>
                {formatCurrency(taxData.summary.taxPaid)}
              </Text>
            </View>
            
            <View style={styles.style1}>
              <Text variant="bodyMedium" style={styles.style12}}>
                Tax Pending
              </Text>
              <Text variant="bodyMedium" style={styles.style14}}>
                {formatCurrency(taxData.summary.taxPending)}
              </Text>
            </View>
          </Card.Content>
        </Card>

        {/* Tax Breakdown */}
        <Card style={styles.style11}]}>
          <Card.Content>
            <View style={styles.style1}>
              <LocalIcon name="chart-pie" size={24} color={theme.colors.primary} />
              <Text variant="titleMedium" style={styles.style10}}>
                Tax Breakdown
              </Text>
            </View>

            {Object.entries(taxData.breakdown).map(([taxType, data]) => (
              <View key={taxType} style={styles.style1}>
                <View style={styles.style1}>
                  <Text variant="bodyMedium" style={styles.style9}}>
                    {taxType.replace(/([A-Z])/g, ' $1').trim()}
                  </Text>
                  <Chip 
                    mode="outlined" 
                    textStyle={{ fontSize: 12 }}
                    style={styles.style13}}
                  >
                    {data.rate}%
                  </Chip>
                </View>
                <View style={styles.style1}>
                  <Text variant="bodyMedium" style={styles.style12}}>
                    {formatCurrency(data.amount)}
                  </Text>
                  <View style={styles.style1}>
                    <LocalIcon name={getStatusIcon(data.status)} 
                      size={16} 
                      color={getStatusColor(data.status)} 
                    />
                    <Text variant="bodySmall" style={styles.style8}}>
                      {data.status}
                    </Text>
                  </View>
                </View>
              </View>
            ))}
          </Card.Content>
        </Card>

        {/* Compliance Status */}
        <Card style={styles.style11}]}>
          <Card.Content>
            <View style={styles.style1}>
              <LocalIcon name="shield-check" size={24} color={theme.colors.primary} />
              <Text variant="titleMedium" style={styles.style10}}>
                Compliance Status
              </Text>
            </View>

            {Object.entries(taxData.compliance).map(([returnType, data]) => (
              <View key={returnType} style={styles.style1}>
                <View style={styles.style1}>
                  <Text variant="bodyMedium" style={styles.style9}}>
                    {returnType.replace(/([A-Z])/g, ' $1').trim()}
                  </Text>
                  <View style={styles.style1}>
                    <LocalIcon name={getStatusIcon(data.status)} 
                      size={16} 
                      color={getStatusColor(data.status)} 
                    />
                    <Text variant="bodySmall" style={styles.style8}}>
                      {data.status}
                    </Text>
                  </View>
                </View>
                <View style={styles.style1}>
                  <Text variant="bodySmall" style={styles.style7}}>
                    Due: {data.dueDate}
                  </Text>
                  <Text variant="bodySmall" style={styles.style7}}>
                    Amount: {formatCurrency(data.amount)}
                  </Text>
                </View>
              </View>
            ))}
          </Card.Content>
        </Card>

        {/* Deductions */}
        <Card style={styles.style6}]}>
          <Card.Content>
            <View style={styles.style1}>
              <LocalIcon name="receipt" size={24} color={theme.colors.primary} />
              <Text variant="titleMedium" style={styles.style5}}>
                Tax Deductions
              </Text>
            </View>

            {Object.entries(taxData.deductions).map(([deductionType, amount]) => {
              if (deductionType === 'total') return null;
              return (
                <View key={deductionType} style={styles.style1}>
                  <Text variant="bodyMedium" style={styles.style4}}>
                    {deductionType.replace(/([A-Z])/g, ' $1').trim()}
                  </Text>
                  <Text variant="bodyMedium" style={styles.style3}}>
                    {formatCurrency(amount)}
                  </Text>
                </View>
              );
            })}

            <Divider style={styles.style1} />
            <View style={styles.style1}>
              <Text variant="titleMedium" style={styles.style2}}>
                Total Deductions
              </Text>
              <Text variant="titleMedium" style={styles.style2}}>
                {formatCurrency(taxData.deductions.total)}
              </Text>
            </View>
          </Card.Content>
        </Card>

        {/* Action Buttons */}
        <View style={styles.style1}>
          <Button
            mode="outlined"
            onPress={scheduleTaxReminder}
            icon="bell"
            style={styles.style1}
          >
            Set Reminders
          </Button>
          <Button
            mode="contained"
            onPress={exportTaxReport}
            icon="download"
            style={styles.style1}
          >
            Export Report
          </Button>
        </View>
      </ScrollView>
    </View>
  );
};






export default TaxSummaryScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  periodSelector: {
    margin: SPACING.md,
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
  },
  content: {
    flex: 1,
    padding: SPACING.md,
  },
  section: {
    marginBottom: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  itemRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.xs,
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.sm,
  },
  divider: {
    marginVertical: SPACING.sm,
  },
  taxTypeRow: {
    marginBottom: SPACING.md,
  },
  taxTypeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  taxTypeDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  complianceRow: {
    marginBottom: SPACING.md,
  },
  complianceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  complianceDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: SPACING.lg,
    marginBottom: SPACING.xl,
  },
  actionButton: {
    flex: 1,
    marginHorizontal: SPACING.xs,
    borderRadius: BORDER_RADIUS.md,
  },
  style1: {
  },
  style2: {
    { color: theme.colors.onPrimaryContainer,
    fontWeight: '600',
  },
  style3: {
    { 
                    color: theme.colors.onPrimaryContainer,
    fontWeight: '500',
  },
  style4: {
    { 
                    color: theme.colors.onPrimaryContainer,
    textTransform: 'capitalize',
  },
  style5: {
    { color: theme.colors.onPrimaryContainer,
    marginLeft: 8,
  },
  style6: {
    { backgroundColor: theme.colors.primaryContainer,
  },
  style7: {
    { color: theme.colors.onSurfaceVariant,
  },
  style8: {
    { 
                      color: getStatusColor(data.status),
    marginLeft: 4,
    textTransform: 'capitalize',
  },
  style9: {
    { 
                    color: theme.colors.onSurface,
    textTransform: 'capitalize',
    fontWeight: '500',
  },
  style10: {
    { color: theme.colors.onSurface,
    marginLeft: 8,
  },
  style11: {
    { backgroundColor: theme.colors.surface,
  },
  style12: {
    { color: theme.colors.onSurface,
  },
  style13: {
    { height: 24,
  },
  style14: {
    { color: '#FF9800',
    fontWeight: '500',
  },
  style15: {
    { color: '#4CAF50',
    fontWeight: '500',
  },
  style16: {
    { color: '#F44336',
    fontWeight: '600',
  },
  style17: {
    { color: theme.colors.onSurface,
    fontWeight: '500',
  },
  style18: {
    { backgroundColor: theme.colors.background,
  },
});