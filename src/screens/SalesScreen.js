/**
 * SalesScreen - Simple sales tracking for ready-made garments and fabrics
 */

import React, { useState, useMemo, useCallback } from 'react';
import { View, ScrollView, StyleSheet, Alert, FlatList   } from 'react-native';
import { Text, Surface, Button, TextInput, Card, Chip, FAB, Searchbar, SegmentedButtons   } from 'react-native-paper';
import LocalIcon from '../components/LocalIcon';
import { useData   } from '../context/DataContext';
import { useTheme   } from '../context/ThemeContext';
import CommonHeader from '../components/CommonHeader';
import StatCardGroup from '../components/StatCardGroup';
import navigationService from '../services/NavigationService';
import { TYPOGRAPHY, SPACING, BORDER_RADIUS   } from '../theme/designTokens';

const SalesScreen = () => {
  const { theme  } = useTheme();
  const { state, actions  } = useData();
  const [searchQuery, setSearchQuery] = useState('');
  const [showSalesForm, setShowSalesForm] = useState(false);

  const [saleData, setSaleData] = useState({
    type: 'garment', itemId: '', itemName: '', quantity: 1, unitPrice: 0,
    customerName: '', customerPhone: '', notes: '',
  });

  const salesData = useMemo(() => state.orders.filter(order =>
    order.status === 'Completed' && (order.orderType === 'ready_made' || order.orderType === 'fabric_sale')
  ).sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt)), [state.orders]);

  const salesStats = useMemo(() => {
    const today = new Date().toISOString().split('T')[0];
    const weekStart = new Date();
    weekStart.setDate(weekStart.getDate() - 7);
    const todaysSales = salesData.filter(sale => sale.createdAt.startsWith(today));
    const weekSales = salesData.filter(sale => new Date(sale.createdAt) >= weekStart);
    return { totalSales: salesData.length,
      todaysSales: todaysSales.length,
      weekSales: weekSales.length,
      todaysRevenue: todaysSales.reduce((sum, sale) => sum + sale.total, 0),
      weekRevenue: weekSales.reduce((sum, sale) => sum + sale.total, 0),
      totalRevenue: salesData.reduce((sum, sale) => sum + sale.total, 0),
     };
  }, [salesData]);

  const availableItems = useMemo(() => {
    const items = (saleData.type === 'garment' ? state.products : state.fabrics) || [];
    return items.filter(item => item.stock > 0);
  }, [state.products, state.fabrics, saleData.type]);

  const filteredItems = useMemo(() => {
    if (!searchQuery) return availableItems;
    return availableItems.filter(item => item.name.toLowerCase().includes(searchQuery.toLowerCase()));
  }, [availableItems, searchQuery]);

  // FIXED: Corrected useCallback syntax for all handlers
  const handleInputChange = useCallback((field, value) => {
    setSaleData(prev => ({ ...prev, [field]: value }));
  }, []);

  const handleSelectItem = useCallback((item) => {
    setSaleData(prev => ({
      ...prev,
      itemId: item.id,
      itemName: item.name,
      unitPrice: saleData.type === 'garment' ? item.price : item.pricePerMeter || item.price,
    }));
  }, [saleData.type]);

  const calculateTotal = useCallback(() => saleData.quantity * saleData.unitPrice, [saleData.quantity, saleData.unitPrice]);

  const handleSaveSale = useCallback(async () => {
    if(!saleData.itemId || !saleData.customerName || saleData.quantity <= 0) {
      Alert.alert('Error', 'Please select an item and enter customer name and quantity.');
      return;
    }
    try {
      const total = calculateTotal();
      const saleOrder = {
        id: `sale_${Date.now()} , customerName: saleData.customerName, customer: saleData.customerName, phone: saleData.customerPhone,
        orderType: saleData.type === 'garment' ? 'ready_made' : 'fabric_sale', status: 'Completed',
        // FIXED: Removed extra comma from items array
        items: [{ productId: saleData.itemId, productName: saleData.itemName, quantity: saleData.quantity, price: saleData.unitPrice, total: total }],
        total: total, notes: saleData.notes, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString(),
      };
      await actions.addOrder(saleOrder);

      const itemToUpdate = availableItems.find(i => i.id === saleData.itemId);
      if(itemToUpdate) {
        const updatedStockItem = { ...itemToUpdate, stock: itemToUpdate.stock - saleData.quantity };
        if (saleData.type === 'garment') await actions.updateProduct(updatedStockItem);
        else await actions.updateFabric(updatedStockItem);
      }
      Alert.alert('Success', 'Sale recorded successfully!');
      setShowSalesForm(false);
      setSaleData({ type: 'garment', itemId: '', itemName: '', quantity: 1, unitPrice: 0, customerName: '', customerPhone: '', notes: '' });
    } catch (error) {
      console.error('Error saving sale:', error);
      Alert.alert('Error', 'Failed to record sale.');
    }
  }, [saleData, actions, availableItems, calculateTotal]);

  // FIXED: Stylesheet moved inside component and memoized for performance
  const styles = useMemo(() => StyleSheet.create({
    container: { flex: 1, backgroundColor: theme.colors.background },
    content: { flex: 1, padding: SPACING.lg },
    section: { padding: SPACING.lg, marginBottom: SPACING.lg, borderRadius: BORDER_RADIUS.lg, backgroundColor: theme.colors.surface },
    sectionHeader: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: SPACING.md },
    sectionTitle: { ...TYPOGRAPHY.titleMedium, color: theme.colors.onSurface, marginBottom: SPACING.md },
    searchBar: { marginBottom: SPACING.md },
    itemsList: { maxHeight: 250, gap: 8 },
    itemCard: { backgroundColor: theme.colors.surfaceVariant },
    itemContent: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' },
    itemInfo: { flex: 1 },
    itemNameText: { ...TYPOGRAPHY.bodyLarge, fontWeight: '600' },
    itemStockText: { ...TYPOGRAPHY.bodySmall, color: theme.colors.onSurfaceVariant },
    itemPriceText: { ...TYPOGRAPHY.titleMedium, color: theme.colors.primary },
    input: { marginBottom: SPACING.md },
    quantityRow: { flexDirection: 'row', gap: SPACING.md },
    quantityInput: { flex: 1 },
    priceInput: { flex: 1 },
    totalRow: { alignItems: 'center', paddingVertical: SPACING.lg },
    totalText: { ...TYPOGRAPHY.headlineSmall, color: theme.colors.primary },
    actionButtons: { flexDirection: 'row', gap: SPACING.md, marginTop: SPACING.lg },
    actionButton: { flex: 1 },
    saleCard: { padding: SPACING.md, marginBottom: SPACING.md, borderRadius: BORDER_RADIUS.lg, borderWidth: 1, borderColor: theme.colors.outlineVariant, backgroundColor: theme.colors.surface },
    saleHeader: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: SPACING.sm },
    saleInfo: { flex: 1 },
    saleCustomer: { ...TYPOGRAPHY.titleMedium, fontWeight: '600' },
    saleType: { ...TYPOGRAPHY.bodySmall, color: theme.colors.primary },
    saleDate: { ...TYPOGRAPHY.caption, color: theme.colors.onSurfaceVariant },
    saleAmountContainer: { alignItems: 'flex-end' },
    saleAmountText: { ...TYPOGRAPHY.titleLarge, fontWeight: 'bold', color: theme.colors.primary },
    saleItems: { borderTopWidth: 1, borderTopColor: theme.colors.outlineVariant, paddingTop: SPACING.md, marginTop: SPACING.md },
    saleItem: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingVertical: SPACING.xs },
    saleItemDetails: { ...TYPOGRAPHY.bodySmall, color: theme.colors.onSurfaceVariant },
    emptyState: { alignItems: 'center', paddingVertical: SPACING.xl, opacity: 0.7 },
    emptyStateText: { ...TYPOGRAPHY.titleMedium, color: theme.colors.onSurfaceVariant, marginTop: SPACING.md, textAlign: 'center' },
    fab: { position: 'absolute', margin: 16, right: 0, bottom: 0, backgroundColor: theme.colors.primary },
  }), [theme]);

  const renderSaleCard = ({ item: sale }) => (
    <Surface style={styles.saleCard} elevation={1}>
      <View style={styles.saleHeader}>
        <View style={styles.saleInfo}>
          <Text style={styles.saleCustomer}>{sale.customerName}</Text>
          <Text style={styles.saleType}>{sale.orderType === 'ready_made' ? 'Ready-made Garment' : 'Fabric Sale'}</Text>
          <Text style={styles.saleDate}>{new Date(sale.createdAt).toLocaleString()}</Text>
        </View>
        <View style={styles.saleAmountContainer}><Text style={styles.saleAmountText}>৳{sale.total.toFixed(2)}</Text></View>
      </View>
      <View style={styles.saleItems}>
        {sale.items.map((item, index) => (
          <View key={index} style={styles.saleItem}><Text style={styles.itemNameText}>{item.productName}</Text><Text style={styles.saleItemDetails}>{item.quantity} × ৳{item.price.toFixed(2)}</Text></View>
        ))}
      </View>
    </Surface>
  );

  const SalesForm = () => (
    <View style={styles.container}>
      <CommonHeader title="Record New Sale" showBackButton onBackPress={() => setShowSalesForm(false)} />
      <ScrollView contentContainerStyle={styles.content} showsVerticalScrollIndicator={false}>
        <Surface style={styles.section} elevation={1}>
          <Text style={styles.sectionTitle}>1. Sale Type</Text>
          <SegmentedButtons value={saleData.type} onValueChange={(value) => handleInputChange('type', value)} buttons={[{ value: 'garment', label: 'Garment', icon: 'tshirt-crew' }, { value: 'fabric', label: 'Fabric', icon: 'texture' }} />
        </Surface>
        <Surface style={styles.section} elevation={1}>
          <Text style={styles.sectionTitle}>2. Select Item</Text>
          <Searchbar placeholder={`Search ${saleData.type}s...` value={searchQuery} onChangeText={setSearchQuery} style={styles.searchBar} />
          <ScrollView style={styles.itemsList}>
            {filteredItems.slice(0, 5).map(item => (
              <Card key={item.id} style={[styles.itemCard, saleData.itemId === item.id && {borderColor: theme.colors.primary, borderWidth: 1] onPress={() => handleSelectItem(item)}>
                <Card.Content style={styles.itemContent}><View style={styles.itemInfo}><Text style={styles.itemNameText}>{item.name}</Text><Text style={styles.itemStockText}>Stock: {item.stock}</Text></View><Text style={styles.itemPriceText}>৳{(saleData.type === 'garment' ? item.price : item.pricePerMeter || item.price).toFixed(2)}</Text></Card.Content>
              </Card>
            ))}
          </ScrollView>
        </Surface>
        {saleData.itemId && (
          <Surface style={styles.section} elevation={1}>
            <Text style={styles.sectionTitle}>3. Sale Details</Text>
            <TextInput label="Customer Name *" value={saleData.customerName} onChangeText={text => handleInputChange('customerName', text)} mode="outlined" style={styles.input} left={<TextInput.Icon icon="account" />} />
            <TextInput label="Customer Phone" value={saleData.customerPhone} onChangeText={text => handleInputChange('customerPhone', text)} mode="outlined" style={styles.input} keyboardType="phone-pad" left={<TextInput.Icon icon="phone" />} />
            <View style={styles.quantityRow}><TextInput label="Quantity *" value={String(saleData.quantity)} onChangeText={text => handleInputChange('quantity', parseInt(text) || 1)} mode="outlined" style={styles.quantityInput} keyboardType="numeric" /><TextInput label="Unit Price" value={String(saleData.unitPrice)} onChangeText={text => handleInputChange('unitPrice', parseFloat(text) || 0)} mode="outlined" style={styles.priceInput} keyboardType="numeric" /></View>
            <View style={styles.totalRow}><Text style={styles.totalText}>Total: ৳{calculateTotal().toFixed(2)}</Text></View>
            <TextInput label="Notes" value={saleData.notes} onChangeText={text => handleInputChange('notes', text)} mode="outlined" style={styles.input} multiline numberOfLines={2} />
          </Surface>
        )}
        <View style={styles.actionButtons}><Button mode="outlined" onPress={() => setShowSalesForm(false)} style={styles.actionButton}>Cancel</Button><Button mode="contained" onPress={handleSaveSale} style={styles.actionButton} disabled={!saleData.itemId || !saleData.customerName}>Record Sale</Button></View>
      </ScrollView>
    </View>
  );

  if (showSalesForm) return <SalesForm />;

  return (
    <View style={styles.container}>
      <CommonHeader title="Sales Tracking" subtitle="Ready-made & fabric sales" />
      <ScrollView contentContainerStyle={styles.content} showsVerticalScrollIndicator={false}>
        <StatCardGroup
          cards={[
            { key: 'today', title: "Today's Sales", value: salesStats.todaysSales.toString(), subtitle: `৳${salesStats.todaysRevenue.toFixed(0)} , icon: 'cash-register' },
            { key: 'week', title: 'Week Sales', value: salesStats.weekSales.toString(), subtitle: `৳${salesStats.weekRevenue.toFixed(0)} ,` icon: 'calendar-week' },
            { key: 'total', title: 'Total Sales', value: salesStats.totalSales.toString(), subtitle: `৳${salesStats.totalRevenue.toFixed(0)} ,` icon: 'chart-line' },
          }
        />
        <Surface style={styles.section} elevation={1}>
          <View style={styles.sectionHeader}><Text style={styles.sectionTitle}>Recent Sales</Text></View>
          {salesData.length > 0 ? <FlatList data={salesData.slice(0, 10)} renderItem={renderSaleCard} keyExtractor={(item) => item.id} scrollEnabled={false} /> : <View style={styles.emptyState}><LocalIcon name="point-of-sale" size={48} color={theme.colors.onSurfaceVariant} /><Text style={styles.emptyStateText}>No sales recorded yet.</Text></View>}
        </Surface>
      </ScrollView>
      <FAB icon="plus" style={styles.fab} onPress={() => setShowSalesForm(true)} label="Record Sale" />
    </View>
  );
};

export default SalesScreen;