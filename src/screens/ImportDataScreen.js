import React, { useState, useCallback, useMemo } from 'react';
import { View, StyleSheet, ScrollView, Alert   } from 'react-native';
import { Text, Button, Surface, Card, Divider, ProgressBar   } from 'react-native-paper';
import { useNavigation   } from '@react-navigation/native';
import LocalIcon from '../components/LocalIcon';
import { useTheme   } from '../context/ThemeContext';
import { useData   } from '../context/DataContext';
import { SPACING, BORDER_RADIUS, TYPOGRAPHY   } from '../theme/designTokens';
import CommonHeader from '../components/CommonHeader';

const ImportDataScreen = () => {
  const { theme  } = useTheme();
  const { actions  } = useData();
  const navigation = useNavigation();

  const [importing, setImporting] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [lastImport, setLastImport] = useState(null);
  const [generatingSample, setGeneratingSample] = useState(false);

  const handleImportData = useCallback(async () => {
    Alert.alert(
      'Import Data',
      'File import functionality will be available in a future update. For now, please use the "Generate Sample Data" option for testing.',
      [{ text: 'OK' }]
    );
  }, []);

  const handleExportData = useCallback(async () => {
    try {
      const exportData = actions.exportData();
      Alert.alert(
        'Export Data',
        `Your data has been prepared for export!\n\nProducts: ${exportData.products?.length || 0}\nOrders: ${exportData.orders?.length || 0}\nCustomers: ${exportData.customers?.length || 0}\n\nData has been logged to the console for now.`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Export error:', error);
      Alert.alert('Export Failed', `Failed to export data: ${error.message}`);`
    }
  }, [actions]);

  const handleGenerateSampleData = useCallback(async () => {
    Alert.alert(
      'Generate Sample Data',
      'This will generate sample data and clear any existing data. Are you sure?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Generate',
          style: 'destructive',
          onPress: async () => {
            try {
              setGeneratingSample(true);
              await actions.generateSampleData();
              Alert.alert('Success', 'Sample data generated successfully!', [{ text: 'OK' }]);
            } catch (error) {
              console.error('Sample data generation error:', error);
              Alert.alert('Generation Failed', `Failed to generate sample data: ${error.message}`);`
            } finally {
              setGeneratingSample(false);
            }
          }
        }
      ]
    );
  }, [actions]);

  const importFormats = [
    { title: 'JSON Format', description: 'Standard JSON file from this app', icon: 'code-json', supported: true },
    { title: 'CSV Format', description: 'Comma-separated values (coming soon)', icon: 'file-delimited', supported: false },
    { title: 'Excel Format', description: 'Microsoft Excel files (coming soon)', icon: 'file-excel', supported: false }];
  // FIXED: Stylesheet moved inside component and memoized for performance
  const styles = useMemo(() => StyleSheet.create({
    container: { flex: 1, backgroundColor: theme.colors.background },
    content: { flex: 1, padding: SPACING.lg },
    section: { padding: SPACING.lg, marginBottom: SPACING.lg, borderRadius: BORDER_RADIUS.xl, backgroundColor: theme.colors.surface },
    sectionHeader: { flexDirection: 'row', alignItems: 'center', marginBottom: SPACING.md },
    sectionTitle: { marginLeft: SPACING.md, ...TYPOGRAPHY.titleMedium, color: theme.colors.onSurface },
    description: { marginBottom: SPACING.lg, lineHeight: 20, color: theme.colors.onSurfaceVariant },
    actionButton: { marginTop: SPACING.sm },
    progressContainer: { marginBottom: SPACING.lg },
    progressText: { color: theme.colors.onSurfaceVariant, marginBottom: 8 },
    lastImportCard: { marginTop: SPACING.md, backgroundColor: theme.colors.surfaceVariant },
    formatItem: { flexDirection: 'row', alignItems: 'center', paddingVertical: SPACING.md },
    formatInfo: { flex: 1, marginLeft: SPACING.md },
    formatTitle: { color: theme.colors.onSurface },
    formatDescription: { color: theme.colors.onSurfaceVariant, marginTop: 2 },
    formatDivider: { marginVertical: SPACING.xs },
    warningSection: { flexDirection: 'row', padding: SPACING.lg, marginBottom: SPACING.lg, borderRadius: BORDER_RADIUS.xl, backgroundColor: theme.colors.errorContainer },
    warningContent: { flex: 1, marginLeft: SPACING.md },
    warningTitle: { color: theme.colors.onErrorContainer, ...TYPOGRAPHY.titleSmall },
    warningText: { color: theme.colors.onErrorContainer, marginTop: 4 },
    sampleDataButton: { backgroundColor: theme.colors.tertiary, marginTop: SPACING.sm }}), [theme]);

  return (
    <View style={styles.container}>
      <CommonHeader title="Data Management" subtitle="Import & export your business data" showBackButton={true} onBackPress={() => navigation.goBack()} />
      <ScrollView contentContainerStyle={styles.content} showsVerticalScrollIndicator={false}>
        <Surface style={styles.section} elevation={1}>
          <View style={styles.sectionHeader}><LocalIcon name="import" size={24} color={theme.colors.primary} /><Text style={styles.sectionTitle}>Import Data</Text></View>
          <Text style={styles.description}>Import your business data from a backup file. Currently supports JSON files exported from this app.</Text>
          {importing && (
            <View style={styles.progressContainer}><Text style={styles.progressText}>Importing... {Math.round(importProgress * 100)}%</Text><ProgressBar progress={importProgress} color={theme.colors.primary} /></View>
          )}
          <Button mode="contained" icon="file-import" onPress={handleImportData} loading={importing} disabled={importing} style={styles.actionButton}>{importing ? 'Importing...' : 'Select File to Import'}</Button>
          {lastImport && (
            <Card style={styles.lastImportCard}><Card.Content><Text variant="titleSmall">Last Import</Text><Text variant="bodySmall">{lastImport.filename} • {new Date(lastImport.date).toLocaleDateString()}</Text><Text variant="bodySmall">{lastImport.productsCount} products, {lastImport.ordersCount} orders, {lastImport.customersCount} customers</Text></Card.Content></Card>
          )}
        </Surface>

        <Surface style={styles.section} elevation={1}>
          <View style={styles.sectionHeader}><LocalIcon name="database-plus" size={24} color={theme.colors.tertiary} /><Text style={styles.sectionTitle}>Generate Sample Data</Text></View>
          <Text style={styles.description}>Populate the app with 100 sample products and customers for testing. This will clear any existing data.</Text>
          <Button mode="contained" icon="auto-fix" onPress={handleGenerateSampleData} loading={generatingSample} disabled={generatingSample} style={styles.sampleDataButton}>{generatingSample ? 'Generating...' : 'Generate Sample Data'}</Button>
        </Surface>

        <Surface style={styles.section} elevation={1}>
          <View style={styles.sectionHeader}><LocalIcon name="export" size={24} color={theme.colors.secondary} /><Text style={styles.sectionTitle}>Export Data</Text></View>
          <Text style={styles.description}>Create a JSON backup of all products, orders, customers, and settings. File export will be available in a future update.</Text>
          <Button mode="outlined" icon="file-export" onPress={handleExportData} style={styles.actionButton}>Export Current Data</Button>
        </Surface>

        <Surface style={styles.section} elevation={1}>
          <View style={styles.sectionHeader}><LocalIcon name="file-multiple" size={24} color={theme.colors.onSurface} /><Text style={styles.sectionTitle}>Supported Formats</Text></View>
          {importFormats.map((format, index) => (
            <View key={index}>
              <View style={styles.formatItem}>
                <LocalIcon name={format.icon} size={20} color={format.supported ? theme.colors.onSurface : theme.colors.onSurfaceVariant} />
                <View style={styles.formatInfo}>
                  <Text variant="titleSmall" style={[styles.formatTitle, !format.supported && { color: theme.colors.onSurfaceVariant }>{format.title}</Text>
                  <Text variant="bodySmall" style={styles.formatDescription}>{format.description}</Text>
                </View>
                {format.supported ? <LocalIcon name="check-circle" size={20} color={theme.colors.primary} /> : <LocalIcon name="clock-outline" size={20} color={theme.colors.onSurfaceVariant} />}
              </View>
              {index < importFormats.length - 1 && <Divider style={styles.formatDivider} />}
            </View>
          ))}
        </Surface>

        <Surface style={styles.warningSection} elevation={1}>
          <LocalIcon name="alert-circle" size={24} color={theme.colors.onErrorContainer} />
          <View style={styles.warningContent}>
            <Text style={styles.warningTitle}>Important Notice</Text>
            <Text style={styles.warningText}>Importing data will replace all current data. Always export a backup first if you want to preserve your work.</Text>
          </View>
        </Surface>
      </ScrollView>
    </View>
  );
};

export default ImportDataScreen;