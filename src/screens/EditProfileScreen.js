import React, { useState, useEffect, useCallback } from 'react';
import { ScrollView, View, StyleSheet, Alert   } from 'react-native';
import { useNavigation   } from '@react-navigation/native';
import { Text   } from 'react-native-paper';
import { TextInput   } from 'react-native-paper';
import { Button   } from 'react-native-paper';
import { Surface   } from 'react-native-paper';
import { Card   } from 'react-native-paper';
import LocalIcon from '../components/LocalIcon';
import { useData   } from '../context/DataContext';
import { useTheme   } from '../context/ThemeContext';
import CommonHeader from '../components/CommonHeader';
import ImagePicker from '../components/ImagePicker';

const EditProfileScreen = () => {
  const { theme  } = useTheme();
  const { state, actions  } = useData();
  const navigation = useNavigation();

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    taxRate: '',
    profileImage: null,
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''});

  const [errors, setErrors] = useState({});

  useEffect(() => {
    if(state.settings) {
      setFormData(prev => ({
        ...prev,
        name: state.settings.ownerName || state.settings.name || '',
        email: state.settings.email || '',
        phone: state.settings.phone || '',
        address: state.settings.address || '',
        taxRate: (state.settings.taxRate * 100).toString() || '8',
        profileImage: state.settings.profileImage || null}));
    }
  }, [state.settings]);

  const handleInputChange = useCallback((field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if(errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  }, [errors]);

  const handleImageSelect = useCallback((imageUri) => {
    setFormData(prev => ({
      ...prev,
      profileImage: imageUri
    }));
  }, []);

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    }

    const taxRate = parseFloat(formData.taxRate);
    if (isNaN(taxRate) || taxRate < 0 || taxRate > 50) {
      newErrors.taxRate = 'Tax rate must be between 0% and 50%';
    }

    // Password validation (only if user is trying to change password)
    if(formData.currentPassword || formData.newPassword || formData.confirmPassword) {
      if (!formData.currentPassword.trim()) {
        newErrors.currentPassword = 'Current password is required';
      }
      if (!formData.newPassword.trim()) {
        newErrors.newPassword = 'New password is required';
      } else if(formData.newPassword.length < 6) {
        newErrors.newPassword = 'Password must be at least 6 characters';
      }
      if(formData.newPassword !== formData.confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = useCallback(() => {
    if (!validateForm()) {
      Alert.alert('Validation Error', 'Please fix the errors and try again.');
      return;
    }

    try {
      const updatedProfile = {
        ...formData,
        taxRate: parseFloat(formData.taxRate) / 100 || 0.08};

      actions.updateSettings(updatedProfile);

      Alert.alert('Success', 'Profile updated successfully!', [
        { text: 'OK', onPress: () => navigation.goBack() }
      ]);
    } catch (error) {
      Alert.alert('Error', 'Failed to update profile. Please try again.');
    }
  }, [formData, navigation, actions]);

  return (
    <View style={styles.style5}>
      <CommonHeader
        title="Edit Profile"
        subtitle="Update your business information"
        showSearch={false}
        showBackButton={true}
        onBackPress={() => navigation.goBack()}
      />

      <ScrollView style={styles.style1} showsVerticalScrollIndicator={false}>
        {/* Profile Image */}
        <Card style={styles.style4}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.style3}>
              Profile Image
            </Text>
            <ImagePicker
              currentImage={formData.profileImage}
              onImageSelected={handleImageSelect}
              placeholder="Upload profile image"
            />
          </Card.Content>
        </Card>

        {/* Store Information */}
        <Surface style={styles.style4} elevation={1}>
          <Text variant="titleMedium" style={styles.style3}>
            Store Information
          </Text>

          <TextInput
            label="Store Name *"
            value={formData.storeName}
            onChangeText={(value) => handleInputChange('storeName', value)}
            mode="outlined"
            style={styles.style1}
            error={!!errors.storeName}
            left={<TextInput.Icon icon="store" />}
          />
          {errors.storeName && (
            <Text variant="bodySmall" style={styles.style2}>
              {errors.storeName}
            </Text>
          )}

          <TextInput
            label="Owner Name *"
            value={formData.ownerName}
            onChangeText={(value) => handleInputChange('ownerName', value)}
            mode="outlined"
            style={styles.style1}
            error={!!errors.ownerName}
            left={<TextInput.Icon icon="account" />}
          />
          {errors.ownerName && (
            <Text variant="bodySmall" style={styles.style2}>
              {errors.ownerName}
            </Text>
          )}
        </Surface>

        {/* Contact Information */}
        <Surface style={styles.style4} elevation={1}>
          <Text variant="titleMedium" style={styles.style3}>
            Contact Information
          </Text>

          <TextInput
            label="Email Address *"
            value={formData.email}
            onChangeText={(value) => handleInputChange('email', value)}
            mode="outlined"
            style={styles.style1}
            error={!!errors.email}
            keyboardType="email-address"
            autoCapitalize="none"
            left={<TextInput.Icon icon="email" />}
          />
          {errors.email && (
            <Text variant="bodySmall" style={styles.style2}>
              {errors.email}
            </Text>
          )}

          <TextInput
            label="Phone Number *"
            value={formData.phone}
            onChangeText={(value) => handleInputChange('phone', value)}
            mode="outlined"
            style={styles.style1}
            error={!!errors.phone}
            keyboardType="phone-pad"
            left={<TextInput.Icon icon="phone" />}
          />
          {errors.phone && (
            <Text variant="bodySmall" style={styles.style2}>
              {errors.phone}
            </Text>
          )}

          <TextInput
            label="Business Address"
            value={formData.address}
            onChangeText={(value) => handleInputChange('address', value)}
            mode="outlined"
            style={styles.style1}
            multiline
            numberOfLines={2}
            left={<TextInput.Icon icon="map-marker" />}
          />
        </Surface>

        {/* Password Settings */}
        <Surface style={styles.style4} elevation={1}>
          <Text variant="titleMedium" style={styles.style3}>
            Password Settings
          </Text>

          <TextInput
            label="Current Password"
            value={formData.currentPassword}
            onChangeText={(value) => handleInputChange('currentPassword', value)}
            mode="outlined"
            style={styles.style1}
            error={!!errors.currentPassword}
            secureTextEntry
            left={<TextInput.Icon icon="lock" />}
          />
          {errors.currentPassword && (
            <Text variant="bodySmall" style={styles.style2}>
              {errors.currentPassword}
            </Text>
          )}

          <TextInput
            label="New Password"
            value={formData.newPassword}
            onChangeText={(value) => handleInputChange('newPassword', value)}
            mode="outlined"
            style={styles.style1}
            error={!!errors.newPassword}
            secureTextEntry
            left={<TextInput.Icon icon="lock" />}
          />
          {errors.newPassword && (
            <Text variant="bodySmall" style={styles.style2}>
              {errors.newPassword}
            </Text>
          )}

          <TextInput
            label="Confirm New Password"
            value={formData.confirmPassword}
            onChangeText={(value) => handleInputChange('confirmPassword', value)}
            mode="outlined"
            style={styles.style1}
            error={!!errors.confirmPassword}
            secureTextEntry
            left={<TextInput.Icon icon="lock" />}
          />
          {errors.confirmPassword && (
            <Text variant="bodySmall" style={styles.style2}>
              {errors.confirmPassword}
            </Text>
          )}
        </Surface>

        {/* Action Buttons */}
        <View style={styles.style1}>
          <Button
            mode="outlined"
            onPress={() => navigation.goBack()}
            style={styles.style1}>
            Cancel
          </Button>
          <Button
            mode="contained"
            onPress={handleSave}
            style={styles.style1}
            icon="check"
          >
            Save Changes
          </Button>
        </View>
      </ScrollView>
    </View>
  );
};






export default EditProfileScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1},
  content: {
    flex: 1,
    paddingHorizontal: 16},
  section: {
    marginVertical: 8,
    borderRadius: 12,
    padding: 16},
  card: {
    marginVertical: 8,
    borderRadius: 12},
  sectionTitle: {
    fontWeight: '600',
    marginBottom: 16},
  avatarSection: {
    alignItems: 'center',
    marginBottom: 16},
  avatarContainer: {
    position: 'relative',
    marginBottom: 16},
  cameraButton: {
    position: 'absolute',
    bottom: -5,
    right: -5},
  compactImageContainer: {
    alignItems: 'center',
    width: '100%'},
  input: {
    marginBottom: 8},
  errorText: {
    marginBottom: 8,
    marginLeft: 4},
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
    marginVertical: 24,
    paddingBottom: 32},
  button: {
    flex: 1},
  cancelButton: {
    marginRight: 6},
  saveButton: {
    marginLeft: 6},
  style1: {
    marginBottom: 8},
  style2: {
    marginBottom: 8,
    marginLeft: 4},
  style3: {
    fontWeight: '600',
    marginBottom: 16},
  style4: {
    marginVertical: 8,
    borderRadius: 12,
    padding: 16},
  style5: {
    flex: 1}});