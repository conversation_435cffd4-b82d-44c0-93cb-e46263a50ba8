import React, { useCallback } from 'react';
import { ScrollView, StyleSheet   } from 'react-native';
import { DataImport, DataExport, DataBackup, DataSync   } from '../components/datamanagementscreen';

const DataManagementScreen = () => {
  return (
    <ScrollView style={styles.container}>
      <DataImport />
      <DataExport />
      <DataBackup />
      <DataSync />
    </ScrollView>
  );
};






export default React.memo(DataManagementScreen);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
});