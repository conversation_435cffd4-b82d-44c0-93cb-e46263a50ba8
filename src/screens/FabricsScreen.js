/**
 * FabricsScreen - Manage fabric inventory for tailoring business
 */

import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { View, ScrollView, StyleSheet, Alert, RefreshControl } from 'react-native';
import { Text } from 'react-native-paper';
import { FAB } from 'react-native-paper';
import { Searchbar } from 'react-native-paper';
import { useData } from '../context/DataContext';
import { useTheme } from '../context/ThemeContext';
import { SPACING } from '../theme/designTokens';
import CommonHeader from '../components/CommonHeader';
import UniversalCard from '../components/UniversalCard';
import StatCardGroup from '../components/StatCardGroup';
import UnifiedSortMenu from '../components/UnifiedSortMenu';
import UnifiedEmptyState from '../components/UnifiedEmptyState';
import StandardSortFilter from '../components/StandardSortFilter';
import navigationService from '../services/NavigationService';

const FabricsScreen = () => {
  const { state, actions } = useData();
  const { theme } = useTheme();

  // Use fabrics from state
  const fabrics = state.fabrics || [];

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedType, setSelectedType] = useState('All');
  const [sortBy, setSortBy] = useState('name');
  const [sortOrder, setSortOrder] = useState('asc');
  const [refreshing, setRefreshing] = useState(false);

  // Refresh data
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      // Refresh fabrics data

    } catch (error) {
      console.error('Failed to refresh fabrics:', error);
    } finally {
      setRefreshing(false);
    }
  }, []);

  // Filter and sort options
  const filterOptions = [
    { value: 'All', label: 'All Types' },
    { value: 'Cotton', label: 'Cotton' },
    { value: 'Silk', label: 'Silk' },
    { value: 'Wool', label: 'Wool' },
    { value: 'Linen', label: 'Linen' },
    { value: 'Polyester', label: 'Polyester' },
    { value: 'Blend', label: 'Blend' },
    { value: 'Denim', label: 'Denim' },
    { value: 'Chiffon', label: 'Chiffon' },
    { value: 'Georgette', label: 'Georgette' },
  ];

  const sortOptions = [
    { value: 'name', label: 'Name A-Z', icon: 'sort-alphabetical-ascending' },
    { value: 'name_desc', label: 'Name Z-A', icon: 'sort-alphabetical-descending' },
    { value: 'pricePerMeter', label: 'Price High', icon: 'sort-numeric-descending' },
    { value: 'pricePerMeter_asc', label: 'Price Low', icon: 'sort-numeric-ascending' },
    { value: 'stock', label: 'Stock High', icon: 'sort-numeric-descending' },
    { value: 'stock_asc', label: 'Stock Low', icon: 'sort-numeric-ascending' },
    { value: 'createdAt', label: 'Newest First', icon: 'sort-calendar-descending' },
    { value: 'createdAt_asc', label: 'Oldest First', icon: 'sort-calendar-ascending' },
  ];

  const [currentSort, setCurrentSort] = useState('name');

  // Filter and sort fabrics
  const filteredFabrics = useMemo(() => {
    let filtered = fabrics.filter(fabric => {
      const matchesSearch = fabric.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           fabric.type?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           fabric.color?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           fabric.supplier?.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesType = selectedType === 'All' || fabric.type === selectedType;

      return matchesSearch && matchesType;
    });

    // Sort fabrics
    if (currentSort) {
      filtered.sort((a, b) => {
        const isDesc = !currentSort.includes('_asc');
        const sortKey = currentSort.replace('_asc', '').replace('_desc', '');

        let aValue = a[sortKey];
        let bValue = b[sortKey];

        if (sortKey === 'pricePerMeter' || sortKey === 'stock') {
          aValue = parseFloat(aValue) || 0;
          bValue = parseFloat(bValue) || 0;
        } else if (sortKey === 'createdAt') {
          aValue = new Date(aValue).getTime();
          bValue = new Date(bValue).getTime();
        } else {
          aValue = String(aValue || '').toLowerCase();
          bValue = String(bValue || '').toLowerCase();
        }

        if (isDesc) {
          return aValue < bValue ? 1 : aValue > bValue ? -1 : 0;
        } else {
          return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
        }
      });
    }

    return filtered;
  }, [fabrics, searchQuery, selectedType, currentSort]);

  // Calculate statistics
  const stats = useMemo(() => {
    const totalFabrics = fabrics.length;
    const totalStock = fabrics.reduce((sum, fabric) => sum + (fabric.stock || 0), 0);
    const lowStockItems = fabrics.filter(fabric => (fabric.stock || 0) < 10).length;
    const totalValue = fabrics.reduce((sum, fabric) => sum + ((fabric.pricePerMeter || 0) * (fabric.stock || 0)), 0);

    return {
      totalFabrics,
      totalStock,
      lowStockItems,
      totalValue,
    };
  }, [fabrics]);

  const handleDeleteFabric = useCallback((fabric) => {
    Alert.alert(
      'Delete Fabric',
      `Are you sure you want to delete "${fabric.name}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await actions.deleteFabric(fabric.id), []);

            } catch (error) {
              console.error('Error deleting fabric:', error);
              Alert.alert('Error', 'Failed to delete fabric. Please try again.');
            }
          },
        },
      ]
    );
  };

  const handleEditFabric = useCallback((fabric) => {
    navigationService.navigate('AddFabric', {
      fabric,
      isEditing: true
    }), []);
  };

  const handleAddFabric = useCallback(() => {
    navigationService.navigate('AddFabric'), []);
  };

  return (
    <View style={styles.container}>
      <CommonHeader
        title="Fabrics"
        subtitle={`${filteredFabrics.length} items`}
        showSearch={false}
        onNotificationPress={() => console.log('Notifications pressed')}
        onProfilePress={() => navigationService.navigate('MyProfile')}
      />

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary}
            tintColor={theme.colors.primary}
          />
        }
      >
        {/* Search */}
        <View style={styles.searchSection}>
          <Searchbar
            placeholder="Search fabrics..."
            onChangeText={setSearchQuery}
            value={searchQuery}
            style={styles.searchbar}
            iconColor={theme.colors.onSurfaceVariant}
            placeholderTextColor={theme.colors.onSurfaceVariant}
          />
        </View>

        {/* Standard Sort & Filter */}
        <StandardSortFilter
          filters={filterOptions}
          selectedFilter={selectedType}
          onFilterChange={setSelectedType}
          sortOptions={sortOptions}
          selectedSort={currentSort}
          onSortChange={setCurrentSort}
          showSortButton={true}
          containerStyle={{ marginBottom: 16 }}
        />

        {/* Stats Row */}
        <StatCardGroup
          cards={[
            {
              key: 'fabrics',
              title: 'Total Fabrics',
              value: stats.totalFabrics.toString(),
              icon: 'texture',
              iconColor: theme.colors.primary,
              elevation: 1,
            },
            {
              key: 'stock',
              title: 'Total Stock (m)',
              value: stats.totalStock.toFixed(1),
              icon: 'package-variant',
              iconColor: theme.colors.secondary,
              elevation: 1,
            },
            {
              key: 'low-stock',
              title: 'Low Stock',
              value: stats.lowStockItems.toString(),
              icon: 'alert-circle-outline',
              iconColor: stats.lowStockItems > 0 ? theme.colors.error : theme.colors.tertiary,
              elevation: 1,
            },
            {
              key: 'value',
              title: 'Total Value',
              value: `৳${stats.totalValue.toFixed(0)}`,
              icon: 'currency-bdt',
              iconColor: theme.colors.primary,
              elevation: 1,
            },
          }
          columns={2}
          showTitle={false}
          containerStyle={{ marginBottom: 16 }}
        />

        {/* Fabrics List */}
        {filteredFabrics.length === 0 ? (
          <View style={styles.emptyState}>
            <Text variant="headlineSmall" style={styles.emptyTitle}>
              {searchQuery || selectedType !== 'All' ? 'No fabrics found' : 'No fabrics yet'}
            </Text>
            <Text variant="bodyMedium" style={styles.emptySubtitle}>
              {searchQuery || selectedType !== 'All'
                ? 'Try adjusting your search or filters'
                : 'Add your first fabric to get started'
              }
            </Text>
          </View>
        ) : (
          <View style={styles.fabricsList}>
            {filteredFabrics.map((fabric) => (
              <UniversalCard
                key={fabric.id}
                type="fabric"
                title={fabric.name}
                subtitle={`${fabric.type} • ${fabric.color}`}
                description={fabric.description}
                price={fabric.pricePerMeter}
                priceLabel="per meter"
                stock={fabric.stock}
                stockLabel="meters"
                image={fabric.image}
                onPress={() => handleEditFabric(fabric)}
                onEdit={() => handleEditFabric(fabric)}
                onDelete={() => handleDeleteFabric(fabric)}
                style={styles.cardStyle}
                additionalInfo={[
                  { label: 'Supplier', value: fabric.supplier },
                  { label: 'Width', value: fabric.width ? `${fabric.width} inches` : null },
                  { label: 'GSM', value: fabric.gsm },
                ].filter(info => info.value)}
              />
            ))}
          </View>
        )}
      </ScrollView>

      {/* Add Fabric FAB */}
      <FAB
        icon="plus"
        style={styles.fab}
        onPress={handleAddFabric}
        label="Add Fabric"
      />
    </View>
  );
};






export default FabricsScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  content: {
    flex: 1,
    padding: SPACING.md,
  },
  searchSection: {
    marginBottom: SPACING.lg,
  },
  searchbar: {
    marginBottom: SPACING.md,
    elevation: 2,
    backgroundColor: theme.colors.surface,
  },
  typeScroll: {
    marginBottom: SPACING.md,
  },
  typeContainer: {
    paddingHorizontal: SPACING.xs,
  },
  typeChip: {
    marginRight: SPACING.sm,
  },
  typeChipText: {
    fontSize: 12,
  },
  fabricsList: {
    paddingBottom: 100, // Space for FAB
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: SPACING.xxxl,
  },
  emptyTitle: {
    fontWeight: '600',
    marginBottom: SPACING.sm,
    textAlign: 'center',
    color: theme.colors.onSurface,
  },
  emptySubtitle: {
    textAlign: 'center',
    paddingHorizontal: SPACING.xl,
    color: theme.colors.onSurfaceVariant,
  },
  fab: {
    position: 'absolute',
    margin: SPACING.lg,
    right: 0,
    bottom: 0,
    backgroundColor: theme.colors.primary,
  },
  cardStyle: {
    marginBottom: 12,
  },
});