import React, { useCallback } from 'react';
import { ScrollView, StyleSheet   } from 'react-native';
import { SalesChart, RevenueChart, OrdersChart, ReportsFilters, ReportsHeader   } from '../components/reportsscreen';

const ReportsScreen = () => {
  return (
    <ScrollView style={styles.container}>
      <SalesChart />
      <RevenueChart />
      <OrdersChart />
      <ReportsFilters />
      <ReportsHeader />
    </ScrollView>
  );
};






export default React.memo(ReportsScreen);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
});