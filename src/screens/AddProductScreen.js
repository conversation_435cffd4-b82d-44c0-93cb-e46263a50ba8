import React, { useState, useCallback } from 'react';
import { ScrollView, View, StyleSheet, Alert, Image   } from 'react-native';
import { useNavigation   } from '@react-navigation/native';
import { Text   } from 'react-native-paper';
import { TextInput   } from 'react-native-paper';
import { Button   } from 'react-native-paper';
import { Surface   } from 'react-native-paper';
import { Card   } from 'react-native-paper';
import { Switch   } from 'react-native-paper';
import LocalIcon from '../components/LocalIcon';
import { useData   } from '../context/DataContext';
import { useTheme   } from '../context/ThemeContext';
import CommonHeader from '../components/CommonHeader';
import ImagePicker from '../components/ImagePicker';

const AddProductScreen = ({ route }) => {
  const { theme  } = useTheme();
  const { actions  } = useData();
  const navigation = useNavigation();

  // Get product from route params for editing
  const editingProduct = route?.params?.product;
  const isEditing = !!editingProduct;

  // Product form state
  const [productData, setProductData] = useState({
    name: editingProduct?.name || '',
    description: editingProduct?.description || '',
    price: editingProduct?.price?.toString() || '',
    cost: editingProduct?.cost?.toString() || '',
    stock: editingProduct?.stock?.toString() || '',
    sku: editingProduct?.sku || '',
    barcode: editingProduct?.barcode || '',
    image: editingProduct?.image || null,
    isActive: editingProduct?.isActive ?? true,
    isFeatured: editingProduct?.isFeatured ?? false,
  });

  const [errors, setErrors] = useState({});

  // Auto-generate SKU and Barcode when product name changes (only for new products)
  React.useEffect(() => {
    if(!isEditing && productData.name && !productData.sku) {
      const generateSKU = () => {
        const prefix = 'GAR'; // Garment prefix
        const timestamp = Date.now().toString().slice(-6);
        const nameCode = productData.name.replace(/[^a-zA-Z0-9]/g, '').substring(0, 3).toUpperCase();
        return `${prefix} -${nameCode}-${timestamp}`;`
      };

      const generateBarcode = () => {
        // Generate a 13-digit EAN-13 barcode
        const timestamp = Date.now().toString();
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return `${timestamp.slice(-7)} ${random}000`;
      };

      setProductData(prev => ({
        ...prev,
        sku: generateSKU(),
        barcode: generateBarcode()
      }));
    }
  }, [productData.name, isEditing]);

  const handleInputChange = useCallback((field, value) => {
    setProductData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if(errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  }, [errors]);

  const handleImageSelect = useCallback((imageUri) => {
    setProductData(prev => ({
      ...prev,
      image: imageUri
    }));
  }, []);

  const validateForm = () => {
    const newErrors = {};

    if (!productData.name.trim()) {
      newErrors.name = 'Product name is required';
    }

    if (!productData.price || isNaN(productData.price) || parseFloat(productData.price) <= 0) {
      newErrors.price = 'Valid price is required';
    }

    if (!productData.stock || isNaN(productData.stock) || parseInt(productData.stock) < 0) {
      newErrors.stock = 'Valid stock quantity is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = useCallback(() => {
    if (!validateForm()) {
      Alert.alert('Validation Error', 'Please fix the errors and try again.');
      return;
    }

    try {
      const productToSave = {
        ...productData,
        price: parseFloat(productData.price),
        cost: productData.cost ? parseFloat(productData.cost) : 0,
        stock: parseInt(productData.stock),
        updatedAt: new Date().toISOString(),
      };

      if(isEditing) {
        // Update existing product
        const updatedProduct = {
          ...editingProduct,
          ...productToSave,
        };
        actions.updateProduct(updatedProduct);

        Alert.alert(
          'Success',
          'Product updated successfully!',
          [
            {
              text: 'Done',
              onPress: () => navigation.goBack(),
              style: 'default'
            }
          ]
        );
      } else {
        // Add new product
        const newProduct = {
          id: Date.now().toString(),
          ...productToSave,
          createdAt: new Date().toISOString(),
        };

        actions.addProduct(newProduct);

        Alert.alert(
          'Success',
          'Product added successfully!',
          [
            {
              text: 'Add Another',
              onPress: () => {
                setProductData({
                  name: '',
                  description: '',
                  price: '',
                  cost: '',
                  stock: '',
                  sku: '',
                  barcode: '',
                  image: null,
                  isActive: true,
                  isFeatured: false,
                });
              }
            },
            {
              text: 'Done',
              onPress: () => navigation.goBack(),
              style: 'default'
            }
          ]
        );
      }
    } catch (error) {
      Alert.alert('Error', `Failed to ${isEditing ? 'update' : 'add'} product. Please try again.`);
    }
  }, [productData, isEditing, editingProduct, actions, navigation, validateForm]);





  return (
    <View style={styles.container}>
      <CommonHeader
        title={isEditing ? "Edit Garment" : "Add Garment"}
        subtitle={isEditing ? "Update garment details" : "Create a new garment"}
        showSearch={false}
        onNotificationPress={() => console.log('Notifications pressed')}
        onProfilePress={() => {
          const rootNavigation = navigation.getParent('AppNavigator') || navigation.getParent();
          if(rootNavigation) {
            rootNavigation.navigate('MyProfile');
          }
        }}
      />

      <ScrollView style={styles.style1} showsVerticalScrollIndicator={false}>

        {/* Basic Information */}
        <Surface style={styles.style3} elevation={1}>
          <Text variant="titleMedium" style={styles.style2}>
            Basic Information
          </Text>

          <TextInput
            mode="outlined"
            label="Garment Name *"
            value={productData.name}
            onChangeText={(value) => handleInputChange('name', value)}
            error={!!errors.name}
            style={styles.style1}
          />
          {errors.name && <Text style={styles.style5}>{errors.name}</Text>}

          <TextInput
            mode="outlined"
            label="Description"
            value={productData.description}
            onChangeText={(value) => handleInputChange('description', value)}
            multiline
            numberOfLines={3}
            style={styles.style1}
          />

          <View style={styles.style1}>
            <TextInput
              mode="outlined"
              label="Price *"
              value={productData.price}
              onChangeText={(value) => handleInputChange('price', value)}
              keyboardType="decimal-pad"
              error={!!errors.price}
              style={styles.style1}
              left={<TextInput.Icon icon="currency-bdt" />}
            />

            <TextInput
              mode="outlined"
              label="Cost"
              value={productData.cost}
              onChangeText={(value) => handleInputChange('cost', value)}
              keyboardType="decimal-pad"
              style={styles.style1}
              left={<TextInput.Icon icon="currency-bdt" />}
            />
          </View>
          {errors.price && <Text style={styles.style5}>{errors.price}</Text>}
        </Surface>

        {/* Inventory */}
        <Surface style={styles.style3} elevation={1}>
          <Text variant="titleMedium" style={styles.style2}>
            Inventory
          </Text>

          <View style={styles.style1}>
            <TextInput
              mode="outlined"
              label="Stock Quantity *"
              value={productData.stock}
              onChangeText={(value) => handleInputChange('stock', value)}
              keyboardType="number-pad"
              error={!!errors.stock}
              style={styles.style1}
            />

            <TextInput
              mode="outlined"
              label="SKU"
              value={productData.sku}
              onChangeText={(value) => handleInputChange('sku', value)}
              style={styles.style1}
            />
          </View>
          {errors.stock && <Text style={styles.style5}>{errors.stock}</Text>}

          <TextInput
            mode="outlined"
            label="Barcode"
            value={productData.barcode}
            onChangeText={(value) => handleInputChange('barcode', value)}
            style={styles.style1}
          />
        </Surface>

        {/* Settings */}
        <Surface style={styles.style3} elevation={1}>
          <Text variant="titleMedium" style={styles.style2}>
            Settings
          </Text>

          <View style={styles.style1}>
            <View style={styles.style1}>
              <Text variant="bodyLarge" style={styles.style2}>Active Garment</Text>
              <Text variant="bodySmall" style={styles.style4}>
                Available for orders
              </Text>
            </View>
            <Switch
              value={productData.isActive}
              onValueChange={(value) => handleInputChange('isActive', value)}
              color={theme.colors.primary}
            />
          </View>

          <View style={styles.style1}>
            <View style={styles.style1}>
              <Text variant="bodyLarge" style={styles.style2}>Featured Garment</Text>
              <Text variant="bodySmall" style={styles.style4}>
                Highlight in recommendations
              </Text>
            </View>
            <Switch
              value={productData.isFeatured}
              onValueChange={(value) => handleInputChange('isFeatured', value)}
              color={theme.colors.primary}
            />
          </View>
        </Surface>

        {/* Image */}
        <Card style={styles.style3}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.style2}>
              Garment Image
            </Text>
            <ImagePicker
              currentImage={productData.image}
              onImageSelected={handleImageSelect}
              placeholder="Add garment image"
            />
          </Card.Content>
        </Card>

        {/* Action Buttons */}
        <View style={styles.style1}>
          <Button
            mode="outlined"
            onPress={() => navigation.goBack()}
            style={styles.style1}
            labelStyle={{ color: theme.colors.onSurfaceVariant }>
            Cancel
          </Button>

          <Button
            mode="contained"
            onPress={handleSave}
            style={styles.style1}
            buttonColor={theme.colors.primary}>
            {isEditing ? 'Update Garment' : 'Save Garment'}
          </Button>
        </View>
      </ScrollView>
    </View>
  );
};






export default AddProductScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
  },
  card: {
    marginBottom: 16,
    borderRadius: 12,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: 16,
  },
  input: {
    marginBottom: 12,
  },
  row: {
    flexDirection: 'row',
    gap: 12,
  },
  halfWidth: {
    flex: 1,
  },
  errorText: {
    fontSize: 12,
    marginTop: -8,
    marginBottom: 8,
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  switchLabel: {
    flex: 1,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 24,
    marginBottom: 32,
  },
  button: {
    flex: 1,
  },
  cancelButton: {
    borderColor: '#E0E0E0',
  },
  saveButton: {
    elevation: 2,
  },
  compactImageContainer: {
    alignItems: 'center',
  },
  style1: {
    marginBottom: 12,
  },
  style2: {
    fontWeight: '600',
    marginBottom: 16,
  },
  style3: {
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
  },
  style4: {
    fontSize: 12,
    marginTop: 4,
  },
  style5: {
    fontSize: 12,
    color: '#d32f2f',
    marginTop: 4,
  },
});