/**
 * Universal Search Screen
 * Comprehensive search functionality with real-time results, filters, and history
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { View,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Keyboard,
  RefreshControl
  } from 'react-native';
import { Text   } from 'react-native-paper';
import { Searchbar   } from 'react-native-paper';
import { Card   } from 'react-native-paper';
import { Chip   } from 'react-native-paper';
import { Button   } from 'react-native-paper';
import { IconButton   } from 'react-native-paper';
import { ActivityIndicator   } from 'react-native-paper';
import { useSafeAreaInsets   } from 'react-native-safe-area-context';
import LocalIcon from '../components/LocalIcon';
import { useData   } from '../context/DataContext';
import { useTheme   } from '../context/ThemeContext';
import searchService from '../services/SearchService';
import lazyLoadService, { useLazyData } from '../services/LazyLoadService';
import navigationService from '../services/NavigationService';

const UniversalSearchScreen = ({ navigation, route }) => {
  const { theme  } = useTheme() || {};
  const insets = useSafeAreaInsets();
  const { state  } = useData();

  // Fallback theme if useTheme returns undefined
  const safeTheme = theme || {
    colors: {
      background: '#ffffff',
      surface: '#f5f5f5',
      primary: '#6200ee',
      onSurface: '#000000',
      onSurfaceVariant: '#666666',
      surfaceVariant: '#e0e0e0',
      onPrimary: '#ffffff'
    }
  };
  
  // Search state
  const [searchQuery, setSearchQuery] = useState(route?.params?.initialQuery || '');
  const [searchResults, setSearchResults] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState(['all']);
  const [showHistory, setShowHistory] = useState(true);
  const [searchHistory, setSearchHistory] = useState([]);
  const [suggestions, setSuggestions] = useState([]);
  
  // Refs
  const searchbarRef = useRef(null);
  const searchTimeoutRef = useRef(null);

  // Available search filters
  const searchFilters = [
    { key: 'all', label: 'All', icon: 'search' },
    { key: 'customers', label: 'Customers', icon: 'users' },
    { key: 'orders', label: 'Orders', icon: 'file-text' },
    { key: 'products', label: 'Products', icon: 'package' },
    { key: 'garments', label: 'Garments', icon: 'tshirt-crew' },
    { key: 'fabrics', label: 'Fabrics', icon: 'layers' }
  ];
  // Initialize search service with data
  useEffect(() => {
    initializeSearchService();
    loadSearchHistory();
  }, [state]);

  // Initialize search service with current data
  const initializeSearchService = useCallback(() => {
    // Update search providers with current data
    searchService.updateSearchProvider('customers', async () => state.customers || []);
    searchService.updateSearchProvider('orders', async () => state.orders || []);
    searchService.updateSearchProvider('products', async () => state.products || []);
    searchService.updateSearchProvider('garments', async () => state.garments || []);
    searchService.updateSearchProvider('fabrics', async () => state.fabrics || []);
  }, [state]);

  // Load search history
  const loadSearchHistory = async () => {
    await searchService.initializeHistory();
    const history = searchService.getSearchHistory();
    setSearchHistory(history);
  };

  // Handle search query change
  const handleSearchChange = useCallback((query) => {
    setSearchQuery(query);
    
    // Clear previous timeout
    if(searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    if (query.trim().length === 0) {
      setSearchResults([]);
      setShowHistory(true);
      setSuggestions([]);
      return;
    }

    if (query.trim().length < 2) {
      setShowHistory(false);
      return;
    }

    // Debounced search
    searchTimeoutRef.current = setTimeout(async () => {
      await performSearch(query);
    }, 300);

    // Get suggestions immediately
    getSuggestions(query);
  }, [selectedFilters]);

  // Perform search
  const performSearch = async (query) => {
    if (!query.trim()) return;

    setIsSearching(true);
    setShowHistory(false);

    try {
      const searchTypes = selectedFilters.includes('all') 
        ? ['customers', 'orders', 'products', 'garments', 'fabrics']
        : selectedFilters;

      const results = await searchService.search(query, {
        types: searchTypes,
        limit: 100
      });

      setSearchResults(results);
    } catch (error) {
      console.error('Search failed:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  // Get search suggestions
  const getSuggestions = async (query) => {
    try {
      const suggestions = await searchService.getSuggestions(query, 5);
      setSuggestions(suggestions);
    } catch (error) {
      console.error('Failed to get suggestions:', error);
    }
  };

  // Handle filter selection
  const handleFilterSelect = useCallback((filterKey) => {
    let newFilters;
    
    if(filterKey === 'all') {
      newFilters = ['all'];
    } else {
      newFilters = selectedFilters.filter(f => f !== 'all');
      
      if (newFilters.includes(filterKey)) {
        newFilters = newFilters.filter(f => f !== filterKey);
      } else {
        newFilters.push(filterKey);
      }
      
      if(newFilters.length === 0) {
        newFilters = ['all'];
      }
    }
    
    setSelectedFilters(newFilters);
    
    // Re-search with new filters
    if (searchQuery.trim()) {
      performSearch(searchQuery);
    }
  }, [selectedFilters, searchQuery]);

  // Handle result selection with proper navigation
  const handleResultSelect = useCallback((result) => {
    Keyboard.dismiss();

    // Add to search history
    searchService.addToHistory(searchQuery);

    switch(result.type) {
      case 'customers':
        // Navigate to customer profile/details
        if(result.id) {
          navigationService.navigate('CustomerDetails', { customerId: result.id, customer: result });
        } else {
          // Fallback to customers list
          navigationService.navigate('Customers');
        }
        break;
      case 'orders':
        // Navigate to specific order details
        if(result.id) {
          navigationService.navigate('OrderDetails', { orderId: result.id, order: result });
        } else {
          // Fallback to orders list
          navigationService.navigate('Orders');
        }
        break;
      case 'products':
        // Navigate to product details or products list
        if(result.id) {
          navigationService.navigate('ProductDetails', { productId: result.id, product: result });
        } else {
          navigationService.navigate('Products');
        }
        break;
      case 'garments':
        // Navigate to garment templates
        navigationService.navigate('GarmentTemplates');
        break;
      case 'fabrics':
        // Navigate to fabrics screen
        navigationService.navigate('Fabrics');
        break;
      case 'history':
        setSearchQuery(result.query);
        performSearch(result.query);
        break;
      case 'suggestion':
        setSearchQuery(result.query);
        performSearch(result.query);
        break;
    }
  }, [searchQuery]);

  // Clear search
  const clearSearch = () => {
    setSearchQuery('');
    setSearchResults([]);
    setShowHistory(true);
    setSuggestions([]);
    searchbarRef.current?.focus();
  };

  // Clear search history
  const clearHistory = () => {
    searchService.clearHistory();
    setSearchHistory([]);
  };

  // Render search result item
  const renderSearchResult = ({ item }) => (
    <TouchableOpacity
      onPress={() => handleResultSelect(item)}
      style={styles.style2}>
      <Card style={styles.style5}>
        <Card.Content style={styles.style2}>
          <View style={styles.style2}>
            <View style={styles.style2}>
              <LocalIcon
                name={getResultIcon(item.type)}
                size={20}
                color={safeTheme.colors.primary}
              />
            </View>
            <View style={styles.style2}>
              <Text variant="titleMedium" style={styles.style2}>
                {getResultTitle(item)}
              </Text>
              <Text variant="bodySmall" style={styles.style1}>
                {getResultSubtitle(item)}
              </Text>
            </View>
          </View>
        </Card.Content>
      </Card>
    </TouchableOpacity>
  );

  // Get result icon based on type
  const getResultIcon = (type) => {
    const iconMap = {
      customers: 'account-group',
      orders: 'clipboard-list',
      products: 'package-variant',
      garments: 'tshirt-crew',
      fabrics: 'texture',
      history: 'clock-outline',
      suggestion: 'magnify'
    };
    return iconMap[type] || 'magnify';
  };

  // Get result title
  const getResultTitle = (item) => {
    if(item.type === 'history' || item.type === 'suggestion') {
      return item.query;
    }
    return item.name || item.orderNumber || item.title || 'Unknown';
  };

  // Get result subtitle
  const getResultSubtitle = (item) => {
    switch(item.type) {
      case 'customers':
        return item.email || item.phone || 'Customer';
      case 'orders':
        return `${item.status || 'Order'} - ${item.customerName || 'Unknown Customer'}`;
      case 'products':
        return item.category || item.description || 'Product';
      case 'garments':
        return item.category || 'Garment Template';
      case 'fabrics':
        return `${item.type || 'Fabric'} - ${item.color || 'Unknown Color'}`;
      case 'history':
        return 'Recent search';
      case 'suggestion':
        return 'Suggested search';
      default:
        return item.type || 'Unknown';
    }
  };

  // Create styles inside component to access theme and insets
  const styles = StyleSheet.create({
    container: {
      flex: 1},
    header: {
      paddingBottom: 8,
      elevation: 2,
      shadowOffset: { width: 0, height: 2 }},
    searchContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 8,
      paddingVertical: 8},
    backButton: {
      margin: 0},
    searchbar: {
      flex: 1,
      marginLeft: 8,
      elevation: 0},
    searchInput: {
      fontSize: 16},
    filtersContainer: {
      paddingHorizontal: 16,
      paddingBottom: 8},
    filterChip: {
      marginRight: 8},
    content: {
      flex: 1},
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 32},
    loadingText: {
      marginTop: 16},
    resultsList: {
      padding: 16},
    resultItem: {
      marginBottom: 8},
    resultCard: {
      elevation: 1},
    resultContent: {
      paddingVertical: 12},
    resultHeader: {
      flexDirection: 'row',
      alignItems: 'center'},
    resultIconContainer: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: 'rgba(0,0,0,0.05)',
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 12},
    resultInfo: {
      flex: 1},
    resultTitle: {
      fontWeight: '600'},
    resultSubtitle: {
      marginTop: 2},
    typeChip: {
      height: 24},
    typeChipText: {
      fontSize: 12},
    scoreContainer: {
      marginTop: 8,
      alignItems: 'flex-end'},
    scoreText: {
      fontSize: 12},
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 32},
    emptyTitle: {
      marginTop: 16,
      fontWeight: '600'},
    emptySubtitle: {
      marginTop: 8,
      textAlign: 'center'},
    historyContainer: {
      padding: 16},
    historyHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 12},
    historyTitle: {
      fontWeight: '600'},
    suggestionsContainer: {
      padding: 16,
      paddingTop: 0},
    suggestionsTitle: {
      marginBottom: 8,
      fontWeight: '600'},
    style1: {
      color: safeTheme.colors.onSurfaceVariant},
    style2: {
      flex: 1},
    style3: {
      color: safeTheme.colors.onSurface},
    style4: {
      backgroundColor: safeTheme.colors.surfaceVariant},
    style5: {
      backgroundColor: safeTheme.colors.surface},
    style6: {
      backgroundColor: safeTheme.colors.background,
      paddingTop: insets.top}});

  return (
    <View style={styles.style6}>
      {/* Header */}
      <View style={styles.style5}>
        <View style={styles.style2}>
          <IconButton
            icon={() => <LocalIcon name="arrow-left" size={24} color={safeTheme.colors.onSurface} />}
            onPress={() => navigation.goBack()}
            style={styles.style2}
          />
          <Searchbar
            ref={searchbarRef}
            placeholder="Search customers, orders, products..."
            value={searchQuery}
            onChangeText={handleSearchChange}
            style={styles.style4}
            inputStyle={styles.searchInput}
            autoFocus
            right={() => searchQuery ? (
              <IconButton
                icon={() => <LocalIcon name="x" size={20} color={safeTheme.colors.onSurfaceVariant} />}
                onPress={clearSearch}
                size={20}
              />
            ) : null}
          />
        </View>

        {/* Search Filters */}
        <FlatList
          data={searchFilters}
          horizontal
          showsHorizontalScrollIndicator={false}
          keyExtractor={(item) => item.key}
          contentContainerStyle={styles.filtersContainer}
          renderItem={({ item }) => (
            <Chip
              mode={selectedFilters.includes(item.key) ? 'flat' : 'outlined'}
              selected={selectedFilters.includes(item.key)}
              onPress={() => handleFilterSelect(item.key)}
              style={styles.style2}
              icon={() => (
                <LocalIcon
                  name={item.icon}
                  size={16}
                  color={selectedFilters.includes(item.key) ? safeTheme.colors.onPrimary : safeTheme.colors.primary}
                />
              )}
            >
              {item.label}
            </Chip>
          )}
        />
      </View>

      {/* Content */}
      <View style={styles.style2}>
        {isSearching && (
          <View style={styles.style2}>
            <ActivityIndicator size="large" color={safeTheme.colors.primary} />
            <Text variant="bodyMedium" style={styles.style3}>
              Searching...
            </Text>
          </View>
        )}

        {!isSearching && searchResults.length > 0 && (
          <FlatList
            data={searchResults}
            keyExtractor={(item, index) => `${item.type} _${index}`}`
            renderItem={renderSearchResult}
            contentContainerStyle={styles.resultsList}
            showsVerticalScrollIndicator={false}
          />
        )}

        {!isSearching && searchQuery.trim() && searchResults.length === 0 && (
          <View style={styles.style2}>
            <LocalIcon name="search" size={64} color={safeTheme.colors.onSurfaceVariant} />
            <Text variant="titleMedium" style={styles.style3}>
              No results found
            </Text>
            <Text variant="bodyMedium" style={styles.style1}>
              Try adjusting your search terms or filters
            </Text>
          </View>
        )}

        {showHistory && searchHistory.length > 0 && (
          <View style={styles.style2}>
            <View style={styles.style2}>
              <Text variant="titleMedium" style={styles.style3}>
                Recent Searches
              </Text>
              <Button
                mode="text"
                onPress={clearHistory}
                compact
              >
                Clear
              </Button>
            </View>
            <FlatList
              data={searchHistory.slice(0, 10)}
              keyExtractor={(item, index) => `history_${index} 
              renderItem={renderSearchResult}
              showsVerticalScrollIndicator={false}
            />
          </View>
       `)}

        {suggestions.length > 0 && searchQuery.trim() && (
          <View style={styles.style2}>
            <Text variant="titleSmall" style={styles.style1}>
              Suggestions
            </Text>
            <FlatList
              data={suggestions}
              keyExtractor={(item, index) => `suggestion_${index}`
              renderItem={renderSearchResult}
              showsVerticalScrollIndicator={false}
            />
          </View>
        )}
      </View>
    </View>
  );
};

export default UniversalSearchScreen;