/**
 * AddFabricScreen - Add/Edit fabric inventory for tailoring business
 */

import React, { useState, useEffect, useCallback } from 'react';
import { View, ScrollView, StyleSheet, Alert   } from 'react-native';
import { Text   } from 'react-native-paper';
import { TextInput   } from 'react-native-paper';
import { Button   } from 'react-native-paper';
import { Card   } from 'react-native-paper';
import { useData   } from '../context/DataContext';
import { useTheme   } from '../context/ThemeContext';
import { SPACING, BORDER_RADIUS   } from '../theme/designTokens';
import CommonHeader from '../components/CommonHeader';
import ImagePicker from '../components/ImagePicker';
import navigationService from '../services/NavigationService';

const AddFabricScreen = ({ route }) => {
  const { theme  } = useTheme();
  const { state, actions  } = useData();
  const { fabric, isEditing  } = route?.params || {};

  const [fabricData, setFabricData] = useState({
    id: fabric?.id || Date.now().toString(),
    name: fabric?.name || '',
    color: fabric?.color || '',
    pricePerMeter: fabric?.pricePerMeter || '',
    stock: fabric?.stock || '',
    width: fabric?.width || '',
    gsm: fabric?.gsm || '',
    supplier: fabric?.supplier || '',
    description: fabric?.description || '',
    image: fabric?.image || null,
    createdAt: fabric?.createdAt || new Date().toISOString(),
    updatedAt: new Date().toISOString()});

  const [errors, setErrors] = useState({});

  const handleInputChange = useCallback((field, value) => {
    setFabricData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if(errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  }, [errors]);

  const handleImageSelect = useCallback((imageUri) => {
    setFabricData(prev => ({
      ...prev,
      image: imageUri
    }));
  }, []);

  const validateForm = () => {
    const newErrors = {};

    if (!fabricData.name.trim()) {
      newErrors.name = 'Fabric name is required';
    }

    if (!fabricData.color.trim()) {
      newErrors.color = 'Color is required';
    }

    if (!fabricData.pricePerMeter || isNaN(fabricData.pricePerMeter) || parseFloat(fabricData.pricePerMeter) <= 0) {
      newErrors.pricePerMeter = 'Valid price per meter is required';
    }

    if (!fabricData.stock || isNaN(fabricData.stock) || parseFloat(fabricData.stock) < 0) {
      newErrors.stock = 'Valid stock quantity is required';
    }

    if (fabricData.width && (isNaN(fabricData.width) || parseFloat(fabricData.width) <= 0)) {
      newErrors.width = 'Width must be a valid number';
    }

    if (fabricData.gsm && (isNaN(fabricData.gsm) || parseFloat(fabricData.gsm) <= 0)) {
      newErrors.gsm = 'GSM must be a valid number';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = useCallback(async () => {
    if (!validateForm()) {
      return;
    }

    try {
      const fabricToSave = {
        ...fabricData,
        pricePerMeter: parseFloat(fabricData.pricePerMeter),
        stock: parseFloat(fabricData.stock),
        width: fabricData.width ? parseFloat(fabricData.width) : null,
        gsm: fabricData.gsm ? parseFloat(fabricData.gsm) : null};

      if(isEditing) {
        await actions.updateFabric(fabricToSave);
        Alert.alert('Success', 'Fabric updated successfully');
      } else {
        await actions.addFabric(fabricToSave);
        Alert.alert('Success', 'Fabric added successfully');
      }
      navigationService.goBack();
    } catch (error) {
      console.error('Error saving fabric:', error);
      Alert.alert('Error', 'Failed to save fabric');
    }
  }, [actions, navigationService, fabricData, validateForm]);

  return (
    <View style={styles.style5}>
      <CommonHeader
        title={isEditing ? 'Edit Fabric' : 'Add Fabric'}
        subtitle="Fabric inventory management"
        showSearch={false}
        onNotificationPress={() => console.log('Notifications pressed')}
        onProfilePress={() => navigationService.goBack()}
      />

      <ScrollView style={styles.style1} showsVerticalScrollIndicator={false}>
        {/* Basic Information */}
        <Card style={styles.style3}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.style2}>
              Basic Information
            </Text>

            <TextInput
              label="Fabric Name *"
              value={fabricData.name}
              onChangeText={(value) => handleInputChange('name', value)}
              style={styles.style1}
              mode="outlined"
              error={!!errors.name}
              placeholder="e.g., Premium Cotton Fabric"
            />
            {errors.name && (
              <Text style={styles.style4}>
                {errors.name}
              </Text>
            )}

            <TextInput
              label="Color *"
              value={fabricData.color}
              onChangeText={(value) => handleInputChange('color', value)}
              style={styles.style1}
              mode="outlined"
              error={!!errors.color}
              placeholder="e.g., Navy Blue, Red, White"
            />
            {errors.color && (
              <Text style={styles.style4}>
                {errors.color}
              </Text>
            )}

            <TextInput
              label="Supplier"
              value={fabricData.supplier}
              onChangeText={(value) => handleInputChange('supplier', value)}
              style={styles.style1}
              mode="outlined"
              placeholder="Supplier name"
            />

            <TextInput
              label="Description"
              value={fabricData.description}
              onChangeText={(value) => handleInputChange('description', value)}
              multiline
              numberOfLines={3}
              style={styles.style1}
              mode="outlined"
              placeholder="Fabric description, care instructions, etc."
            />
          </Card.Content>
        </Card>

        {/* Pricing & Stock */}
        <Card style={styles.style3}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.style2}>
              Pricing & Stock
            </Text>

            <TextInput
              label="Price per Meter *"
              value={fabricData.pricePerMeter}
              onChangeText={(value) => handleInputChange('pricePerMeter', value)}
              keyboardType="numeric"
              style={styles.style1}
              mode="outlined"
              error={!!errors.pricePerMeter}
              left={<TextInput.Affix text="৳" />}
            />
            {errors.pricePerMeter && (
              <Text style={styles.style4}>
                {errors.pricePerMeter}
              </Text>
            )}

            <TextInput
              label="Stock Quantity *"
              value={fabricData.stock}
              onChangeText={(value) => handleInputChange('stock', value)}
              keyboardType="numeric"
              style={styles.style1}
              mode="outlined"
              error={!!errors.stock}
              right={<TextInput.Affix text="meters" />}
            />
            {errors.stock && (
              <Text style={styles.style4}>
                {errors.stock}
              </Text>
            )}
          </Card.Content>
        </Card>

        {/* Technical Specifications */}
        <Card style={styles.style3}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.style2}>
              Technical Specifications
            </Text>

            <TextInput
              label="Width"
              value={fabricData.width}
              onChangeText={(value) => handleInputChange('width', value)}
              keyboardType="numeric"
              style={styles.style1}
              mode="outlined"
              error={!!errors.width}
              right={<TextInput.Affix text="inches" />}
              placeholder="Fabric width"
            />
            {errors.width && (
              <Text style={styles.style4}>
                {errors.width}
              </Text>
            )}

            <TextInput
              label="GSM (Grams per Square Meter)"
              value={fabricData.gsm}
              onChangeText={(value) => handleInputChange('gsm', value)}
              keyboardType="numeric"
              style={styles.style1}
              mode="outlined"
              error={!!errors.gsm}
              placeholder="Fabric weight"
            />
            {errors.gsm && (
              <Text style={styles.style4}>
                {errors.gsm}
              </Text>
            )}
          </Card.Content>
        </Card>

        {/* Image */}
        <Card style={styles.style3}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.style2}>
              Fabric Image
            </Text>
            <ImagePicker
              currentImage={fabricData.image}
              onImageSelected={handleImageSelect}
              placeholder="Add fabric image"
            />
          </Card.Content>
        </Card>

        {/* Action Buttons */}
        <View style={styles.style1}>
          <Button
            mode="outlined"
            onPress={() => navigationService.goBack()}
            style={styles.style1}>
            Cancel
          </Button>
          <Button
            mode="contained"
            onPress={handleSave}
            style={styles.style1}>
            {isEditing ? 'Update' : 'Save'} Fabric
          </Button>
        </View>
      </ScrollView>
    </View>
  );
};






export default AddFabricScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1},
  content: {
    flex: 1,
    paddingHorizontal: SPACING.lg},
  card: {
    marginBottom: SPACING.lg,
    borderRadius: BORDER_RADIUS.xl},
  sectionTitle: {
    marginBottom: SPACING.md,
    fontWeight: '600'},
  input: {
    marginBottom: SPACING.md},
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.sm,
    marginBottom: SPACING.sm},
  chip: {
    marginRight: SPACING.xs,
    marginBottom: SPACING.xs},
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: SPACING.lg,
    marginBottom: SPACING.xl},
  button: {
    flex: 1,
    marginHorizontal: SPACING.xs},
  cancelButton: {
    marginRight: SPACING.sm},
  saveButton: {
    marginLeft: SPACING.sm},
  errorText: {
    fontSize: 12,
    marginTop: SPACING.xs},
  style1: {
    marginBottom: SPACING.md},
  style2: {
    marginBottom: SPACING.md,
    fontWeight: '600'},
  style3: {
    marginBottom: SPACING.lg,
    borderRadius: BORDER_RADIUS.xl},
  style4: {
    fontSize: 12,
    marginTop: SPACING.xs},
  style5: {
    flex: 1}});