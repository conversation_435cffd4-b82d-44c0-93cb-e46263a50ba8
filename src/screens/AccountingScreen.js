/**
 * AccountingScreen - Comprehensive accounting and financial management
 */

import React, { useState, useMemo, useCallback } from 'react';
import { View, ScrollView, StyleSheet, Alert, FlatList } from 'react-native';
import { Text, Surface, Button, FAB, SegmentedButtons, IconButton, Menu } from 'react-native-paper';
import LocalIcon from '../components/LocalIcon';
import { useData } from '../context/DataContext';
import { useTheme } from '../context/ThemeContext';
import CommonHeader from '../components/CommonHeader';
import StatCardGroup from '../components/StatCardGroup';
import navigationService from '../services/NavigationService';
import { TYPOGRAPHY, SPACING, BORDER_RADIUS } from '../theme/designTokens';

const AccountingScreen = () => {
  const { theme } = useTheme();
  const { state } = useData();
  const [selectedPeriod, setSelectedPeriod] = useState('month');
  const [selectedView, setSelectedView] = useState('overview');
  const [menuVisible, setMenuVisible] = useState({});

  // Mock data for demonstration
  const [accountingData, setAccountingData] = useState({
    income: [
      { id: 'inc_1', type: 'tailoring_order', description: 'Order #001 - Wedding Suit', amount: 15000, date: '2025-07-12T10:00:00.000Z', category: 'Tailoring Services', customerName: 'Ahmed Hassan' },
      { id: 'inc_2', type: 'fabric_sale', description: 'Silk Fabric Sale - 5 meters', amount: 3250, date: '2025-07-11T14:30:00.000Z', category: 'Fabric Sales', customerName: 'Fatima Khan' },
    ],
    expenses: [
      { id: 'exp_1', type: 'staff_salary', description: 'Monthly Salary - Ahmed', amount: 25000, date: '2025-07-01T18:00:00.000Z', category: 'Staff Salaries', employeeName: 'Ahmed Hassan' },
      { id: 'exp_2', type: 'rent', description: 'Shop Rent - July 2025', amount: 15000, date: '2025-07-05T12:00:00.000Z', category: 'Rent & Utilities' },
      { id: 'exp_3', type: 'material_purchase', description: 'Cotton Fabric Purchase', amount: 6000, date: '2025-07-08T11:00:00.000Z', category: 'Material Purchase', supplierName: 'Dhaka Textiles' },
    ],
  });

  const financialMetrics = useMemo(() => {
    const totalIncome = accountingData.income.reduce((sum, item) => sum + item.amount, 0);
    const totalExpenses = accountingData.expenses.reduce((sum, item) => sum + item.amount, 0);
    const netProfit = totalIncome - totalExpenses;
    const profitMargin = totalIncome > 0 ? (netProfit / totalIncome) * 100 : 0;
    const incomeByCategory = accountingData.income.reduce((acc, item) => { acc[item.category] = (acc[item.category] || 0) + item.amount; return acc; }, {});
    const expensesByCategory = accountingData.expenses.reduce((acc, item) => { acc[item.category] = (acc[item.category] || 0) + item.amount; return acc; }, {});
    return { totalIncome, totalExpenses, netProfit, profitMargin, incomeByCategory, expensesByCategory };
  }, [accountingData]);

  // FIXED: Corrected useCallback syntax for all handlers
  const handleAddIncome = useCallback(() => {
    navigationService.navigate('AddIncomeExpense', { type: 'income' });
  }, []);

  const handleAddExpense = useCallback(() => {
    navigationService.navigate('AddIncomeExpense', { type: 'expense' });
  }, []);

  const generateProfitLossReport = useCallback(() => Alert.alert('Success', 'Profit & Loss report generated!'), []);
  const generateIncomeStatement = useCallback(() => Alert.alert('Success', 'Income statement generated!'), []);
  const generateExpenseReport = useCallback(() => Alert.alert('Success', 'Expense report generated!'), []);

  const handleGenerateReport = useCallback(() => {
    Alert.alert('Generate Report', 'Select report type:', [
      { text: 'Cancel', style: 'cancel' },
      { text: 'Profit & Loss', onPress: generateProfitLossReport },
      { text: 'Income Statement', onPress: generateIncomeStatement },
      { text: 'Expense Report', onPress: generateExpenseReport },
    ]);
  }, [generateProfitLossReport, generateIncomeStatement, generateExpenseReport]);

  const getTransactionIcon = (type) => ({
    tailoring_order: 'needle', fabric_sale: 'texture', ready_made_sale: 'tshirt-crew',
    staff_salary: 'account-cash', rent: 'home', material_purchase: 'package-variant',
    electricity: 'lightning-bolt',
  }[type] || 'cash');

  const toggleMenu = (itemId) => setMenuVisible(prev => ({ ...prev, [itemId]: !prev[itemId]));
  const closeMenu = (itemId) => setMenuVisible(prev => ({ ...prev, [itemId]: false }));
  
  // FIXED: Stylesheet moved inside component and memoized
  const styles = useMemo(() => StyleSheet.create({
    container: { flex: 1, backgroundColor: theme.colors.background },
    content: { flex: 1, padding: SPACING.lg },
    section: { padding: SPACING.lg, marginBottom: SPACING.lg, borderRadius: BORDER_RADIUS.lg, backgroundColor: theme.colors.surface },
    sectionTitle: { ...TYPOGRAPHY.titleMedium, color: theme.colors.onSurface, marginBottom: SPACING.md },
    transactionCard: { padding: SPACING.md, marginBottom: SPACING.md, borderRadius: BORDER_RADIUS.lg, borderWidth: 1, backgroundColor: theme.colors.surface, borderColor: theme.colors.outlineVariant },
    transactionHeader: { flexDirection: 'row', alignItems: 'center' },
    transactionInfo: { flex: 1, flexDirection: 'row', alignItems: 'center' },
    transactionIcon: { marginRight: SPACING.md, width: 40, height: 40, borderRadius: 20, justifyContent: 'center', alignItems: 'center' },
    transactionDetails: { flex: 1 },
    transactionDesc: { ...TYPOGRAPHY.bodyLarge, fontWeight: '600', color: theme.colors.onSurface },
    transactionSub: { ...TYPOGRAPHY.bodySmall, color: theme.colors.onSurfaceVariant },
    transactionContext: { ...TYPOGRAPHY.bodySmall, color: theme.colors.primary, fontStyle: 'italic' },
    transactionAmountContainer: { alignItems: 'flex-end', flexDirection: 'row' },
    transactionAmountText: { ...TYPOGRAPHY.titleMedium, fontWeight: 'bold' },
    breakdownItem: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingVertical: SPACING.sm, borderBottomWidth: 1, borderBottomColor: theme.colors.outlineVariant },
    breakdownCategory: { ...TYPOGRAPHY.bodyMedium, color: theme.colors.onSurface },
    breakdownAmount: { ...TYPOGRAPHY.bodyMedium, fontWeight: '600' },
    quickActions: { flexDirection: 'row', gap: SPACING.md },
    quickActionButton: { flex: 1 },
    emptyState: { alignItems: 'center', paddingVertical: SPACING.xl },
    fab: { position: 'absolute', margin: 16, right: 0, bottom: 0, backgroundColor: theme.colors.primary },
    headerRow: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: SPACING.sm },
  }), [theme]);

  const renderTransaction = ({ item, isIncome }) => {
    const amountColor = isIncome ? '#4CAF50' : '#F44336';
    const iconBgColor = amountColor + '20'; // Add 20% opacity
    
    return (
      <Surface style={styles.transactionCard} elevation={1}>
        <View style={styles.transactionHeader}>
          <View style={styles.transactionInfo}>
            <View style={[styles.transactionIcon, { backgroundColor: iconBgColor }}>
              <LocalIcon name={getTransactionIcon(item.type)} size={20} color={amountColor} />
            </View>
            <View style={styles.transactionDetails}>
              <Text style={styles.transactionDesc} numberOfLines={1}>{item.description}</Text>
              <Text style={styles.transactionSub}>{item.category} • {new Date(item.date).toLocaleDateString()}</Text>
              {(item.customerName || item.employeeName || item.supplierName) && <Text style={styles.transactionContext}>{item.customerName || item.employeeName || item.supplierName}</Text>}
            </View>
          </View>
          <View style={styles.transactionAmountContainer}>
            <Text style={[styles.transactionAmountText, { color: amountColor }}>{isIncome ? '+' : '-'}৳{item.amount.toLocaleString()}</Text>
            <Menu visible={menuVisible[item.id] || false} onDismiss={() => closeMenu(item.id)} anchor={<IconButton icon="dots-vertical" size={20} onPress={() => toggleMenu(item.id)} />}>
              <Menu.Item onPress={() => { closeMenu(item.id); navigationService.navigate('AddIncomeExpense', { type: isIncome ? 'income' : 'expense', transaction: item, isEditing: true }); }} title="Edit" leadingIcon="pencil" />
              <Menu.Item onPress={() => { closeMenu(item.id); Alert.alert('Delete Transaction', 'Are you sure?', [{ text: 'Cancel' }, { text: 'Delete', style: 'destructive' }]); }} title="Delete" leadingIcon="delete" />
            </Menu>
          </View>
        </View>
      </Surface>
    );
  };

  const renderBreakdown = (title, data, color) => (
    <Surface style={styles.section} elevation={1}>
      <Text style={styles.sectionTitle}>{title}</Text>
      {Object.entries(data).map(([category, amount]) => (
        <View key={category} style={styles.breakdownItem}><Text style={styles.breakdownCategory}>{category}</Text><Text style={[styles.breakdownAmount, { color }}>৳{amount.toLocaleString()}</Text></View>
      ))}
    </Surface>
  );

  return (
    <View style={styles.container}>
      <CommonHeader title="Accounting" subtitle="Financial management & reports" showBackButton={true} />
      <ScrollView contentContainerStyle={styles.content} showsVerticalScrollIndicator={false}>
        <StatCardGroup
          cards={[
            { key: 'income', title: 'Total Income', value: `৳${financialMetrics.totalIncome.toLocaleString()}`, icon: 'trending-up', iconColor: '#4CAF50' },
            { key: 'expenses', title: 'Total Expenses', value: `৳${financialMetrics.totalExpenses.toLocaleString()}`, icon: 'trending-down', iconColor: '#F44336' },
            { key: 'profit', title: 'Net Profit', value: `৳${financialMetrics.netProfit.toLocaleString()}`, icon: 'chart-line', iconColor: financialMetrics.netProfit >= 0 ? theme.colors.primary : '#F44336' },
          }
        />
        <Surface style={styles.section} elevation={1}>
          <Text style={styles.sectionTitle}>Period & View</Text>
          <SegmentedButtons value={selectedPeriod} onValueChange={setSelectedPeriod} buttons={[{ value: 'week', label: 'Week' }, { value: 'month', label: 'Month' }, { value: 'year', label: 'Year' }} style={{ marginBottom: SPACING.md }} />
          <SegmentedButtons value={selectedView} onValueChange={setSelectedView} buttons={[{ value: 'overview', label: 'Overview' }, { value: 'income', label: 'Income' }, { value: 'expenses', label: 'Expenses' }} />
        </Surface>

        {selectedView === 'overview' && (<>{renderBreakdown('Income Breakdown', financialMetrics.incomeByCategory, '#4CAF50')}{renderBreakdown('Expense Breakdown', financialMetrics.expensesByCategory, '#F44336')}</>)}
        {selectedView === 'income' && (
          <Surface style={styles.section} elevation={1}>
            <View style={styles.headerRow}><Text style={styles.sectionTitle}>Income Transactions</Text><Button mode="outlined" onPress={handleAddIncome} icon="plus" compact>Add</Button></View>
            <FlatList data={accountingData.income} renderItem={({ item }) => renderTransaction({ item, isIncome: true })} keyExtractor={(item) => item.id} scrollEnabled={false} ListEmptyComponent={<View style={styles.emptyState}><Text>No income recorded for this period.</Text></View>} />
          </Surface>
        )}
        {selectedView === 'expenses' && (
          <Surface style={styles.section} elevation={1}>
            <View style={styles.headerRow}><Text style={styles.sectionTitle}>Expense Transactions</Text><Button mode="outlined" onPress={handleAddExpense} icon="plus" compact>Add</Button></View>
            <FlatList data={accountingData.expenses} renderItem={({ item }) => renderTransaction({ item, isIncome: false })} keyExtractor={(item) => item.id} scrollEnabled={false} ListEmptyComponent={<View style={styles.emptyState}><Text>No expenses recorded for this period.</Text></View>} />
          </Surface>
        )}

        <Surface style={styles.section} elevation={1}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActions}><Button mode="contained" onPress={handleGenerateReport} icon="file-chart" style={styles.quickActionButton}>Generate Report</Button><Button mode="outlined" onPress={() => navigationService.navigate('TaxCalculator')} icon="calculator" style={styles.quickActionButton}>Tax Calculator</Button></View>
        </Surface>
      </ScrollView>
      <FAB icon="plus" style={styles.fab} onPress={() => Alert.alert('Add Transaction', 'Select transaction type:', [{ text: 'Cancel' }, { text: 'Add Income', onPress: handleAddIncome }, { text: 'Add Expense', onPress: handleAddExpense }])} label="Add" />
    </View>
  );
};

export default AccountingScreen;