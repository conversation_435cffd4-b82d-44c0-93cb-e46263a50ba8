import React, { useState, useEffect, useCallback } from 'react';
import { ScrollView, View, StyleSheet, Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Text } from 'react-native-paper';
import { Button } from 'react-native-paper';
import { Surface } from 'react-native-paper';
import { Switch } from 'react-native-paper';
import { TextInput } from 'react-native-paper';
import { Card } from 'react-native-paper';
// Removed malformed import statement
import LocalIcon from '../components/LocalIcon';
import { useData } from '../context/DataContext';
import { useTheme } from '../context/ThemeContext';
import CommonHeader from '../components/CommonHeader';

const PaymentMethodsScreen = () => {
  const { theme } = useTheme();
  const { state, actions } = useData();
  const navigation = useNavigation();

  const [methods, setMethods] = useState({
    cash: { enabled: true, processingFee: 0 },
    card: { enabled: true, processingFee: 2.9 },
    digitalWallet: { enabled: false, processingFee: 2.5 },
    bankTransfer: { enabled: false, processingFee: 1.0 },
    giftCard: { enabled: true, processingFee: 0 },
  });

  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(true);

  const paymentOptions = [
    {
      key: 'cash',
      label: 'Cash',
      icon: 'cash',
      description: 'Physical cash payments',
    },
    {
      key: 'card',
      label: 'Credit/Debit Card',
      icon: 'credit-card',
      description: 'Visa, Mastercard, American Express',
    },
    {
      key: 'digitalWallet',
      label: 'Digital Wallet',
      icon: 'wallet',
      description: 'Apple Pay, Google Pay, Samsung Pay',
    },
    {
      key: 'bankTransfer',
      label: 'Bank Transfer',
      icon: 'bank-transfer',
      description: 'Direct bank transfers',
    },
    {
      key: 'giftCard',
      label: 'Gift Cards',
      icon: 'gift',
      description: 'Store gift cards and vouchers',
    },
  ];

  useEffect(() => {
    // Set loading to false after a short delay to ensure component is ready
    const timer = setTimeout(() => {
      if (state.settings?.paymentMethods) {
        // Merge saved settings with default structure to ensure all keys exist
        const defaultMethods = {
          cash: { enabled: true, processingFee: 0 },
          card: { enabled: true, processingFee: 2.9 },
          digitalWallet: { enabled: false, processingFee: 2.5 },
          bankTransfer: { enabled: false, processingFee: 1.0 },
          giftCard: { enabled: true, processingFee: 0 },
        };

        const mergedMethods = { ...defaultMethods };
        Object.keys(state.settings.paymentMethods || {}).forEach(key => {
          if (mergedMethods[key] && state.settings.paymentMethods[key]) {
            mergedMethods[key] = {
              ...mergedMethods[key],
              ...state.settings.paymentMethods[key]
            };
          }
        });

        setMethods(mergedMethods);
      }
      setErrors({});
      setIsLoading(false);
    }, 100);

    return () => clearTimeout(timer);
  }, [state.settings?.paymentMethods]);

  const toggleMethod = (key) => {
    setMethods(prev => ({
      ...prev,
      [key]: {
        enabled: true,
        processingFee: 0,
        ...prev[key],
        enabled: !(prev[key]?.enabled || false)
      }
    }));
  };

  const updateProcessingFee = (key, fee) => {
    const numericFee = parseFloat(fee) || 0;
    if (numericFee < 0 || numericFee > 10) {
      setErrors(prev => ({ ...prev, [key]: 'Fee must be between 0% and 10%' }));
      return;
    }
    
    setErrors(prev => ({ ...prev, [key]: null }));
    setMethods(prev => ({
      ...prev,
      [key]: {
        enabled: true,
        processingFee: 0,
        ...prev[key],
        processingFee: numericFee
      }
    }));
  };

  const handleSave = useCallback(() => {
    const enabledMethods = Object.keys(methods).filter(key => methods[key]?.enabled);
    
    if (enabledMethods.length === 0) {
      Alert.alert('Error', 'Please enable at least one payment method.');
      return;
    }

    actions.updateSettings({ paymentMethods: methods });
    Alert.alert('Success', 'Payment methods updated successfully!', [
      { text: 'OK', onPress: () => navigation.goBack() }
    ]);
  }, [methods, actions, navigation]);

  const enabledMethods = Object.keys(methods).filter(key => methods[key]?.enabled);

  const PaymentMethodCard = ({ option }) => (
    <Surface style={styles.methodCard} elevation={1}>
      <View style={styles.methodHeader}>
        <View style={styles.methodInfo}>
          <View style={styles.iconContainer}>
            <LocalIcon name={option.icon === 'credit-card' ? 'credit-card' : option.icon === 'cash' ? 'dollar-sign' : 'credit-card'} size={24} color={theme.colors.primary} />
          </View>
          <View style={styles.methodText}>
            <Text variant="titleMedium" style={styles.onSurfaceText}>
              {option.label}
            </Text>
            <Text variant="bodySmall" style={styles.surfaceVariantText}>
              {option.description}
            </Text>
          </View>
        </View>
        <Switch
          value={methods[option.key]?.enabled || false}
          onValueChange={() => toggleMethod(option.key)}
          color={theme.colors.primary}
        />
      </View>

      {methods[option.key]?.enabled && (
        <View style={styles.feeSection}>
          <TextInput
            label="Processing Fee (%)"
            value={(methods[option.key]?.processingFee || 0).toString()}
            onChangeText={(value) => updateProcessingFee(option.key, value)}
            mode="outlined"
            keyboardType="numeric"
            style={styles.feeInput}
            error={!!errors[option.key]}
            right={<TextInput.Affix text="%" />}
          />
          {errors[option.key] && (
            <Text variant="bodySmall" style={styles.errorText}>
              {errors[option.key]}
            </Text>
          )}
        </View>
      )}
    </Surface>
  );

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <CommonHeader
          title="Payment Methods"
          subtitle="Configure accepted payment options"
          showSearch={false}
          showBackButton={true}
          onBackPress={() => navigation.goBack()}
        />
        <View style={styles.loadingContainer}>
          <Text variant="bodyMedium" style={styles.loadingText}>
            Loading payment methods...
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <CommonHeader
        title="Payment Methods"
        subtitle="Configure accepted payment options"
        showSearch={false}
        showBackButton={true}
        onBackPress={() => navigation.goBack()}
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Summary Card */}
        <Surface style={styles.summaryCard} elevation={1}>
          <View style={styles.flexRow}>
            <LocalIcon name="info" size={20} color={theme.colors.onPrimaryContainer} />
            <View style={styles.summaryText}>
              <Text variant="titleMedium" style={styles.primaryTitle}>
                {enabledMethods.length} payment method{enabledMethods.length !== 1 ? 's' : ''} enabled
              </Text>
              {enabledMethods.length > 0 && (
                <Text variant="bodySmall" style={styles.primaryText}>
                  {paymentOptions
                    .filter(option => methods[option.key]?.enabled)
                    .map(option => option.label)
                    .join(', ')}
                </Text>
              )}
            </View>
          </View>
        </Surface>

        {/* Payment Methods */}
        {paymentOptions.map((option) => (
          <PaymentMethodCard key={option.key} option={option} />
        ))}

        {/* Info Card */}
        <Surface style={styles.infoCard} elevation={1}>
          <View style={styles.infoHeader}>
            <LocalIcon name="zap" size={20} color={theme.colors.primary} />
            <Text variant="titleMedium" style={styles.titleText}>
              Processing Fees
            </Text>
          </View>
          <Text variant="bodySmall" style={styles.infoText}>
            Processing fees are automatically calculated and added to transactions. 
            These fees help cover payment processing costs and can be adjusted based on your payment processor's rates.
          </Text>
        </Surface>

        {/* Action Buttons */}
        <View style={styles.buttonContainer}>
          <Button
            mode="outlined"
            onPress={() => navigation.goBack()}
            style={styles.button}
          >
            Cancel
          </Button>
          <Button
            mode="contained"
            onPress={handleSave}
            style={styles.button}
            icon="check"
          >
            Save Changes
          </Button>
        </View>
      </ScrollView>
    </View>
  );
};






export default PaymentMethodsScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  summaryCard: {
    marginVertical: 16,
    borderRadius: 12,
    padding: 16,
    backgroundColor: '#E3F2FD',
  },
  summaryContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  summaryText: {
    flex: 1,
    marginLeft: 12,
  },
  methodCard: {
    marginBottom: 12,
    borderRadius: 12,
    padding: 16,
  },
  methodHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  methodInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  methodText: {
    flex: 1,
  },
  feeSection: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  feeInput: {
    marginBottom: 8,
  },
  errorText: {
    marginLeft: 4,
    color: '#D32F2F',
  },
  infoCard: {
    marginVertical: 16,
    borderRadius: 12,
    padding: 16,
  },
  infoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
    marginVertical: 24,
    paddingBottom: 32,
  },
  button: {
    flex: 1,
  },
  cancelButton: {
    marginRight: 6,
  },
  saveButton: {
    marginLeft: 6,
  },
  flexRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoText: {
    color: '#666',
    marginTop: 8,
  },
  titleText: {
    marginLeft: 8,
    color: '#333',
  },
  surfaceVariant: {
    backgroundColor: '#F5F5F5',
  },
  primaryText: {
    color: '#1976D2',
    marginTop: 4,
  },
  primaryTitle: {
    color: '#1976D2',
  },
  primaryContainer: {
    backgroundColor: '#E3F2FD',
  },
  backgroundContainer: {
    backgroundColor: '#FFFFFF',
  },
  onSurfaceText: {
    color: '#333',
  },
  surfaceVariantText: {
    color: '#666',
  },
  surfaceContainer: {
    backgroundColor: '#FFFFFF',
  }
});