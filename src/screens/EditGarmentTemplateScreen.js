/**
 * EditGarmentTemplateScreen - Edit garment template with price and category
 */

import React, { useState, useEffect, useCallback } from 'react';
import { View, ScrollView, StyleSheet, Alert, TouchableOpacity   } from 'react-native';
import { Text   } from 'react-native-paper';
import { TextInput   } from 'react-native-paper';
import { Button   } from 'react-native-paper';
import { Surface   } from 'react-native-paper';
import { IconButton   } from 'react-native-paper';
import { Chip   } from 'react-native-paper';
import { useTheme   } from '../context/ThemeContext';
import { useData   } from '../context/DataContext';
import CommonHeader from '../components/CommonHeader';
import { SPACING, BORDER_RADIUS   } from '../theme/designTokens';
import navigationService from '../services/NavigationService';
import LocalIcon from '../components/LocalIcon';

const EditGarmentTemplateScreen = ({ route }) => {
  const { theme  } = useTheme();
  const { state, actions  } = useData();
  const { template, isEditing  } = route?.params || {};

  const [templateData, setTemplateData] = useState({
    id: template?.id || Date.now().toString(),
    name: template?.name || '',
    category: template?.category || 'Men',
    price: template?.price?.toString() || '',
    fields: template?.fields || [],
  });

  const [errors, setErrors] = useState({});
  const [newFieldName, setNewFieldName] = useState('');

  const categories = ['Men', 'Women', 'Unisex', 'Children'];

  const commonMeasurementFields = [
    'chest', 'waist', 'shoulder', 'armLength', 'hip', 'thigh', 'inseam', 'outseam',
    'bust', 'neckSize', 'wrist', 'bicep', 'forearm', 'backWidth', 'frontWidth',
    'shirtLength', 'jacketLength', 'blazerLength', 'coatLength', 'kurtaLength',
    'sherwaniLength', 'dressLength', 'blouseLength', 'skirtLength', 'pantLength',
    'pajamaLength', 'uniformLength', 'tshirtLength', 'kurtiLength',
    'choliLength', 'petticoatLength', 'rise'
  ];

  const handleInputChange = useCallback((field, value) => {
    setTemplateData(prev => ({
      ...prev,
      [field]: value
    }));

    if(errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  }, [errors]);

  const handleCategorySelect = useCallback((category) => {
    setTemplateData(prev => ({
      ...prev,
      category: category
    }));
  }, []);

  const handleAddField = useCallback(() => {
    if (!newFieldName.trim()) return;

    const fieldName = newFieldName.trim().toLowerCase().replace(/\s+/g, '');
    if (templateData.fields.includes(fieldName)) {
      Alert.alert('Error', 'This measurement field already exists');
      return;
    }

    setTemplateData(prev => ({
      ...prev,
      fields: [...prev.fields, fieldName]));
    setNewFieldName('');
  }, [newFieldName, templateData.fields]);

  const handleRemoveField = useCallback((fieldToRemove) => {
    setTemplateData(prev => ({
      ...prev,
      fields: prev.fields.filter(field => field !== fieldToRemove)
    }));
  }, []);

  const handleAddCommonField = useCallback((field) => {
    if (templateData.fields.includes(field)) {
      Alert.alert('Error', 'This measurement field already exists');
      return;
    }

    setTemplateData(prev => ({
      ...prev,
      fields: [...prev.fields, field]));
  }, [templateData.fields]);

  const validateForm = () => {
    const newErrors = {};

    if (!templateData.name.trim()) {
      newErrors.name = 'Template name is required';
    }

    if (!templateData.price.trim()) {
      newErrors.price = 'Price is required';
    } else if (isNaN(parseFloat(templateData.price))) {
      newErrors.price = 'Price must be a valid number';
    }

    if(templateData.fields.length === 0) {
      newErrors.fields = 'At least one measurement field is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = useCallback(async () => {
    if (!validateForm()) {
      return;
    }

    const finalTemplateData = {
      ...templateData,
      price: parseFloat(templateData.price),
      updatedAt: new Date().toISOString(),
      createdAt: templateData.createdAt || new Date().toISOString(),
    };

    try {
      if(isEditing) {
        await actions.updateGarmentTemplate(finalTemplateData);
        Alert.alert('Success', 'Template updated successfully');
      } else {
        await actions.addGarmentTemplate(finalTemplateData);
        Alert.alert('Success', 'Template created successfully');
      }

      navigationService.goBack();
    } catch (error) {
      console.error('Error saving template:', error);
      Alert.alert('Error', 'Failed to save template');
    }
  }, [templateData, validateForm, isEditing, actions, navigationService]);

  const formatFieldName = (field) => {
    return field.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
  };

  return (
    <View style={styles.container}>
      <CommonHeader
        title={isEditing ? 'Edit Template' : 'Create Template'}
        subtitle="Garment measurement template"
        showSearch={false}
        onNotificationPress={() => console.log('Notifications pressed')}
        onProfilePress={() => navigationService.goBack()}
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Basic Information */}
        <Surface style={styles.section} elevation={1}>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Basic Information
          </Text>

          <TextInput
            mode="outlined"
            label="Template Name *"
            value={templateData.name}
            onChangeText={(value) => handleInputChange('name', value)}
            error={!!errors.name}
            style={styles.input}
            placeholder="e.g., Men's Formal Shirt"
          />
          {errors.name && <Text style={styles.errorText}>{errors.name}</Text>}

          <TextInput
            mode="outlined"
            label="Price (৳) *"
            value={templateData.price}
            onChangeText={(value) => handleInputChange('price', value)}
            keyboardType="numeric"
            error={!!errors.price}
            style={styles.input}
            placeholder="0"
          />
          {errors.price && <Text style={styles.errorText}>{errors.price}</Text>}
        </Surface>

        {/* Category Selection */}
        <Surface style={styles.section} elevation={1}>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Category
          </Text>

          <View style={styles.categoryChipsContainer}>
            {categories.map((category) => (
              <TouchableOpacity
                key={category}
                style={[styles.categoryChip, {
                  backgroundColor: templateData.category === category
                    ? theme.colors.primary
                    : theme.colors.surfaceVariant,
                  borderColor: templateData.category === category
                    ? theme.colors.primary
                    : theme.colors.outline,
                }]}}
                onPress={() => handleCategorySelect(category)}>
                <Text
                  variant="bodyMedium"
                  style={{
                    color: templateData.category === category
                      ? theme.colors.onPrimary
                      : theme.colors.onSurfaceVariant,
                    fontWeight: templateData.category === category ? '600' : '400',
                  }>
                  {category}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </Surface>

        {/* Measurement Fields */}
        <Surface style={styles.section} elevation={1}>
          <View style={styles.sectionHeader}>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Measurement Fields
            </Text>
          </View>

          {/* Add Custom Field */}
          <View style={styles.addFieldContainer}>
            <TextInput
              mode="outlined"
              label="Add measurement field"
              value={newFieldName}
              onChangeText={setNewFieldName}
              style={styles.input}
              placeholder="e.g., sleeve length"
            />
            <IconButton
              icon="plus"
              mode="contained"
              onPress={handleAddField}
              style={styles.addFieldButton}
            />
          </View>

          {/* Current Fields */}
          {templateData.fields.length > 0 && (
            <View style={styles.fieldsContainer}>
              <Text variant="bodyMedium" style={styles.sectionTitle}>
                Current Fields:
              </Text>
              <View style={styles.fieldsGrid}>
                {templateData.fields.map((field, index) => (
                  <View key={index} style={styles.fieldChip}>
                    <Text variant="bodySmall" style={styles.fieldText}>
                      {formatFieldName(field)}
                    </Text>
                    <TouchableOpacity onPress={() => handleRemoveField(field)}>
                      <LocalIcon name="close" size={16} color={theme.colors.error} />
                    </TouchableOpacity>
                  </View>
                ))}
              </View>
            </View>
          )}

          {errors.fields && <Text style={styles.errorText}>{errors.fields}</Text>}

          {/* Common Fields */}
          <View style={styles.commonFieldsContainer}>
            <Text variant="bodyMedium" style={styles.sectionTitle}>
              Quick Add Common Fields:
            </Text>
            <View style={styles.commonFieldsGrid}>
              {commonMeasurementFields.map((field) => (
                <TouchableOpacity
                  key={field}
                  style={[styles.commonFieldChip, {
                    backgroundColor: templateData.fields.includes(field)
                      ? theme.colors.primaryContainer
                      : theme.colors.surfaceVariant,
                    borderColor: templateData.fields.includes(field)
                      ? theme.colors.primary
                      : theme.colors.outline,
                  }]}}
                  onPress={() => handleAddCommonField(field)}
                  disabled={templateData.fields.includes(field)}>
                  <Text
                    variant="bodySmall"
                    style={{
                      color: templateData.fields.includes(field)
                        ? theme.colors.primary
                        : theme.colors.onSurfaceVariant,
                      fontWeight: templateData.fields.includes(field) ? '600' : '400',
                    }>
                    {formatFieldName(field)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </Surface>

        {/* Action Buttons */}
        <View style={styles.buttonContainer}>
          <Button
            mode="outlined"
            onPress={() => navigationService.goBack()}
            style={[styles.button, styles.cancelButton}>
            Cancel
          </Button>
          <Button
            mode="contained"
            onPress={handleSave}
            style={[styles.button, styles.saveButton}>
            {isEditing ? 'Update' : 'Create'} Template
          </Button>
        </View>
      </ScrollView>
    </View>
  );
};






export default EditGarmentTemplateScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: SPACING.lg,
  },
  section: {
    marginBottom: SPACING.lg,
    borderRadius: BORDER_RADIUS.xl,
    padding: SPACING.lg,
  },
  sectionTitle: {
    marginBottom: SPACING.md,
    fontWeight: '600',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  input: {
    marginBottom: SPACING.sm,
  },
  errorText: {
    fontSize: 12,
    marginTop: -SPACING.xs,
    marginBottom: SPACING.sm,
  },
  categoryChipsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.xs,
  },
  categoryChip: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.md,
    borderWidth: 1,
  },
  addFieldContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.sm,
    marginBottom: SPACING.md,
  },
  addFieldButton: {
    margin: 0,
  },
  fieldsContainer: {
    marginBottom: SPACING.md,
  },
  fieldsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.xs,
  },
  fieldChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.sm,
    gap: SPACING.xs,
  },
  commonFieldsContainer: {
    marginTop: SPACING.md,
  },
  commonFieldsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.xs,
  },
  commonFieldChip: {
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.sm,
    borderWidth: 1,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: SPACING.lg,
    marginBottom: SPACING.xl,
  },
  button: {
    flex: 1,
    marginHorizontal: SPACING.xs,
  },
  cancelButton: {
    marginRight: SPACING.sm,
  },
  saveButton: {
    marginLeft: SPACING.sm,
  },
  fieldText: {
     flex: 1,
   },
   commonFieldText: {
     fontWeight: '400',
   },
   categoryChipText: {
     fontSize: 14,
   },
});