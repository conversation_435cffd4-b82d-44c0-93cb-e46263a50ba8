/**
 * PaymentsScreen - Comprehensive payment management for tailor shop
 */

import React, { useState, useMemo, useCallback } from 'react';
import { View, ScrollView, StyleSheet, Alert, FlatList   } from 'react-native';
import { Text   } from 'react-native-paper';
import { Surface   } from 'react-native-paper';
import { Button   } from 'react-native-paper';
import { Chip   } from 'react-native-paper';
import { Searchbar   } from 'react-native-paper';
import { FAB   } from 'react-native-paper';
import { Divider   } from 'react-native-paper';
import LocalIcon from '../components/LocalIcon';
import { useData   } from '../context/DataContext';
import { useTheme   } from '../context/ThemeContext';
import CommonHeader from '../components/CommonHeader';
import StatCardGroup from '../components/StatCardGroup';
import navigationService from '../services/NavigationService';

const PaymentsScreen = () => {
  const { theme  } = useTheme();
  const { state, actions  } = useData();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [selectedSort, setSelectedSort] = useState('date');

  // Payment filters
  const paymentFilters = [
    { key: 'all', label: 'All', icon: 'format-list-bulleted' },
    { key: 'unpaid', label: 'Unpaid', icon: 'clock-outline' },
    { key: 'partial', label: 'Partial', icon: 'clock-alert-outline' },
    { key: 'paid', label: 'Paid', icon: 'check-circle' },
    { key: 'overdue', label: 'Overdue', icon: 'alert-circle' },
  ];

  // Sort options
  const sortOptions = [
    { key: 'date', label: 'Date' },
    { key: 'amount', label: 'Amount' },
    { key: 'customer', label: 'Customer' },
    { key: 'status', label: 'Status' },
  ];

  // Get orders with payment information
  const ordersWithPayments = useMemo(() => {
    return state.orders.map(order => ({
      ...order,
      paymentStatus: order.paymentStatus || 'unpaid',
      paidAmount: order.paidAmount || 0,
      balanceAmount: order.total - (order.paidAmount || 0),
      dueDate: order.dueDate || order.date,
      isOverdue: order.dueDate && new Date(order.dueDate) < new Date() && order.paymentStatus !== 'paid',
    }));
  }, [state.orders]);

  // Filter and sort orders
  const filteredOrders = useMemo(() => {
    let filtered = ordersWithPayments;

    // Apply search filter
    if(searchQuery) {
      filtered = filtered.filter(order =>
        order.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        order.id.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply status filter
    if(selectedFilter !== 'all') {
      if(selectedFilter === 'overdue') {
        filtered = filtered.filter(order => order.isOverdue);
      } else {
        filtered = filtered.filter(order => order.paymentStatus === selectedFilter);
      }
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch(selectedSort) {
        case 'amount':
          return b.total - a.total;
        case 'customer':
          return a.customerName.localeCompare(b.customerName);
        case 'status':
          return a.paymentStatus.localeCompare(b.paymentStatus);
        case 'date':
        default:
          return new Date(b.date) - new Date(a.date);
      }
    });

    return filtered;
  }, [ordersWithPayments, searchQuery, selectedFilter, selectedSort]);

  // Calculate payment statistics
  const paymentStats = useMemo(() => {
    const totalOrders = ordersWithPayments.length;
    const unpaidOrders = ordersWithPayments.filter(o => o.paymentStatus === 'unpaid').length;
    const partialOrders = ordersWithPayments.filter(o => o.paymentStatus === 'partial').length;
    const paidOrders = ordersWithPayments.filter(o => o.paymentStatus === 'paid').length;
    const overdueOrders = ordersWithPayments.filter(o => o.isOverdue).length;

    const totalRevenue = ordersWithPayments.reduce((sum, order) => sum + order.total, 0);
    const totalPaid = ordersWithPayments.reduce((sum, order) => sum + (order.paidAmount || 0), 0);
    const totalPending = totalRevenue - totalPaid;

    return { totalOrders,
      unpaidOrders,
      partialOrders,
      paidOrders,
      overdueOrders,
      totalRevenue,
      totalPaid,
      totalPending,
     };
  }, [ordersWithPayments]);

  const handleMarkPaid = useCallback(async (order) => {
    try {
      const updatedOrder = {
        ...order,
        paymentStatus: 'paid',
        paidAmount: order.total,
        balanceAmount: 0,
        paidAt: new Date().toISOString(),
      };

      await actions.updateOrder(updatedOrder);
      Alert.alert('Success', 'Payment marked as paid successfully!');
    } catch (error) {
      Alert.alert('Error', 'Failed to update payment status');
    }
  }, [actions]);

  const handlePartialPayment = useCallback((order) => {
    Alert.prompt(
      'Partial Payment',
      `Enter amount received (Balance: ৳${order.balanceAmount.toFixed(2)}) ,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Save',
          onPress: async (amount) => {
            const paidAmount = parseFloat(amount);
            if (isNaN(paidAmount) || paidAmount <= 0) {
              Alert.alert('Error', 'Please enter a valid amount');
              return;
            }

            if(paidAmount > order.balanceAmount) {
              Alert.alert('Error', 'Amount cannot exceed balance');
              return;
            }

            try {
              const newPaidAmount = (order.paidAmount || 0) + paidAmount;
              const newBalanceAmount = order.total - newPaidAmount;
              const newStatus = newBalanceAmount <= 0 ? 'paid' : 'partial';

              const updatedOrder = {
                ...order,
                paymentStatus: newStatus,
                paidAmount: newPaidAmount,
                balanceAmount: newBalanceAmount,
                updatedAt: new Date().toISOString(),
              };

              await actions.updateOrder(updatedOrder);
              Alert.alert('Success', 'Partial payment recorded successfully!');
            } catch (error) {
              Alert.alert('Error', 'Failed to record payment');
            }
          },
        },
      ],
      'plain-text',
      '',
      'numeric'
    );
  }, [actions]);

  const handleGenerateInvoice = useCallback(async (order) => {
    try {
      // Import the PDF generator
      const { PDFInvoiceGenerator  } = await import('../utils/pdfInvoiceGenerator');

      Alert.alert(
        'Generate Invoice',
        'Choose how to generate the invoice',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Generate & Share',
            onPress: async () => {
              try {
                const result = await PDFInvoiceGenerator.generatePDF(order);
                if(result.success) {
                  Alert.alert('Success', 'Invoice generated successfully!');
                }
              } catch (error) {
                Alert.alert('Error', 'Failed to generate invoice: ' + error.message);
              }
            }
          },
          {
            text: 'Print',
            onPress: async () => {
              try {
                await PDFInvoiceGenerator.printInvoice(order);
              } catch (error) {
                Alert.alert('Error', 'Failed to print invoice: ' + error.message);
              }
            }
          },
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to load invoice generator');
    }
  }, []);

  const getPaymentStatusColor = (status, isOverdue) => {
    if (isOverdue) return theme.colors.error;
    switch(status) {
      case 'paid': return '#4CAF50';
      case 'partial': return '#FF9800';
      case 'unpaid': return '#F44336';
      default: return theme.colors.onSurfaceVariant;
    }
  };

  const getPaymentStatusIcon = (status, isOverdue) => {
    if (isOverdue) return 'alert-circle';
    switch(status) {
      case 'paid': return 'check-circle';
      case 'partial': return 'clock-alert-outline';
      case 'unpaid': return 'clock-outline';
      default: return 'help-circle';
    }
  };

  const renderPaymentCard = useCallback(({ item: order }) => (
    <Surface style={styles.paymentCard} elevation={1}>
      <View style={styles.cardHeader}>
        <View style={styles.orderInfo}>
          <Text variant="titleMedium" style={styles.orderTitle}>
            Order #{order.id}
          </Text>
          <Text variant="bodyMedium" style={styles.customerName}>
            {order.customerName}
          </Text>
          <Text variant="bodySmall" style={styles.dateText}>
            {new Date(order.date).toLocaleDateString()}
          </Text>
        </View>

        <View style={styles.statusContainer}>
          <Chip
            icon={getPaymentStatusIcon(order.paymentStatus, order.isOverdue)}
            style={styles.statusChip}
            textStyle={{ color: getPaymentStatusColor(order.paymentStatus, order.isOverdue) }>
            {order.isOverdue ? 'Overdue' : order.paymentStatus.charAt(0).toUpperCase() + order.paymentStatus.slice(1)}
          </Chip>
        </View>
      </View>

      <Divider style={styles.divider} />

      <View style={styles.amountSection}>
        <View style={styles.amountRow}>
          <Text variant="bodyMedium" style={styles.amountLabel}>Total Amount:</Text>
          <Text variant="titleMedium" style={styles.amountValue}>
            ৳{order.total.toFixed(2)}
          </Text>
        </View>

        {order.paymentStatus !== 'unpaid' && (
          <View style={styles.amountRow}>
            <Text variant="bodyMedium" style={styles.amountLabel}>Paid Amount:</Text>
            <Text variant="bodyMedium" style={styles.paidAmount}>
              ৳{(order.paidAmount || 0).toFixed(2)}
            </Text>
          </View>
        )}

        {order.balanceAmount > 0 && (
          <View style={styles.amountRow}>
            <Text variant="bodyMedium" style={styles.amountLabel}>Balance:</Text>
            <Text variant="bodyMedium" style={styles.balanceAmount}>
              ৳{order.balanceAmount.toFixed(2)}
            </Text>
          </View>
        )}
      </View>

      <View style={styles.actionButtons}>
        {order.paymentStatus !== 'paid' && (
          <>
            <Button
              mode="outlined"
              onPress={() => handlePartialPayment(order)}
              style={styles.actionButton}
              compact
            >
              Partial
            </Button>
            <Button
              mode="contained"
              onPress={() => handleMarkPaid(order)}
              style={styles.actionButton}
              compact
            >
              Mark Paid
            </Button>
          </>
        )}
        <Button
          mode="outlined"
          onPress={() => handleGenerateInvoice(order)}
          icon="file-pdf-box"
          style={styles.actionButton}
          compact
        >
          Invoice
        </Button>
      </View>
    </Surface>
  ), [theme, handleMarkPaid, handlePartialPayment, handleGenerateInvoice]);

  return (
    <View style={styles.container}>
      <CommonHeader
        title="Payments"
        subtitle="Manage payments and invoices"
        showSearch={false}
        onNotificationPress={() => console.log('Notifications pressed')}
        onProfilePress={() => navigationService.navigate('MyProfile')}
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Payment Statistics */}
        <StatCardGroup
          cards={[
            {
              key: 'total_revenue',
              title: 'Total Revenue',
              value: `৳${paymentStats.totalRevenue.toFixed(0)}`,`
              icon: 'cash-multiple',
              iconColor: theme.colors.primary,
            },
            {
              key: 'total_paid',
              title: 'Paid',
              value: `৳${paymentStats.totalPaid.toFixed(0)}`,
              icon: 'check-circle',
              iconColor: '#4CAF50',
            },
            {
              key: 'total_pending',
              title: 'Pending',
              value: `৳${paymentStats.totalPending.toFixed(0)}`,`
              icon: 'clock-outline',
              iconColor: '#FF9800',
            },
          }
          columns={3}
          showTitle={false}
          containerStyle={{ marginBottom: 16 }
        />

        {/* Search and Filters */}
        <Surface style={styles.filtersSection} elevation={1}>
          <Searchbar
            placeholder="Search by customer or order ID..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            style={styles.searchBar}
          />

          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filtersRow}>
            {paymentFilters.map((filter) => (
              <Chip
                key={filter.key}
                selected={selectedFilter === filter.key}
                onPress={() => setSelectedFilter(filter.key)}
                icon={filter.icon}
                style={styles.filterChip}>
                {filter.label}
              </Chip>
            ))}
          </ScrollView>
        </Surface>

        {/* Payment List */}
        <View style={styles.listContainer}>
          <View style={styles.listHeader}>
            <Text variant="titleMedium" style={styles.listTitle}>
              Payment Records ({filteredOrders.length})
            </Text>
            <Button
              mode="outlined"
              onPress={() => {
                // Show sort options
                Alert.alert(
                  'Sort By',
                  'Choose sorting option',
                  sortOptions.map(option => ({
                    text: option.label,
                    onPress: () => setSelectedSort(option.key),
                  }))
                );
              }}
              icon="sort"
              compact
            >
              Sort
            </Button>
          </View>

          {filteredOrders.length > 0 ? (
            <FlatList
              data={filteredOrders}
              renderItem={renderPaymentCard}
              keyExtractor={(item) => item.id}
              showsVerticalScrollIndicator={false}
              scrollEnabled={false}
            />
          ) : (
            <Surface style={styles.emptyState} elevation={1}>
              <LocalIcon name="credit-card" size={48} color={theme.colors.onSurfaceVariant} />
              <Text variant="titleMedium" style={styles.emptyTitle}>
                No payment records found
              </Text>
              <Text variant="bodyMedium" style={styles.emptySubtitle}>
                {searchQuery ? 'Try adjusting your search or filters' : 'Payment records will appear here when orders are created'}
              </Text>
            </Surface>
          )}
        </View>
      </ScrollView>

      {/* Quick Actions FAB */}
      <FAB
        icon="plus"
        style={styles.fab}
        onPress={() => navigationService.navigate('CreateOrder')}
        label="New Order"
      />
    </View>
  );
};






export default PaymentsScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  filtersSection: {
    padding: 16,
    marginBottom: 16,
    borderRadius: 12,
    backgroundColor: theme.colors.surface,
  },
  searchBar: {
    marginBottom: 12,
  },
  filtersRow: {
    flexDirection: 'row',
  },
  filterChip: {
    marginRight: 8,
  },
  listContainer: {
    marginBottom: 80,
  },
  listHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  listTitle: {
    color: theme.colors.onSurface,
  },
  paymentCard: {
    padding: 16,
    marginBottom: 12,
    borderRadius: 12,
    backgroundColor: theme.colors.surface,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  orderInfo: {
    flex: 1,
  },
  orderTitle: {
    fontWeight: '600',
    color: theme.colors.onSurface,
  },
  customerName: {
    color: theme.colors.primary,
  },
  dateText: {
    color: theme.colors.onSurfaceVariant,
  },
  statusContainer: {
    alignItems: 'flex-end',
  },
  statusChip: {
    marginBottom: 4,
  },
  divider: {
    marginVertical: 12,
  },
  amountSection: {
    marginVertical: 8,
  },
  amountRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  amountLabel: {
    color: theme.colors.onSurfaceVariant,
  },
  amountValue: {
    fontWeight: '600',
    color: theme.colors.onSurface,
  },
  paidAmount: {
    color: '#4CAF50',
  },
  balanceAmount: {
    color: theme.colors.error,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 12,
  },
  actionButton: {
    flex: 1,
  },
  emptyState: {
    padding: 32,
    alignItems: 'center',
    borderRadius: 12,
    backgroundColor: theme.colors.surface,
  },
  emptyTitle: {
    color: theme.colors.onSurfaceVariant,
    marginTop: 16,
  },
  emptySubtitle: {
    color: theme.colors.onSurfaceVariant,
    textAlign: 'center',
    marginTop: 8,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
    backgroundColor: theme.colors.primary,
  },
});