/**
 * LoginScreen - Authentication screen with email/phone login
 */

import React, { useState, useCallback } from 'react';
import { View, StyleSheet, Alert, KeyboardAvoidingView, ScrollView   } from 'react-native';
import { Text   } from 'react-native-paper';
import { TextInput   } from 'react-native-paper';
import { Button   } from 'react-native-paper';
import { Card   } from 'react-native-paper';
import { Surface   } from 'react-native-paper';
import { SegmentedButtons   } from 'react-native-paper';
import { Divider   } from 'react-native-paper';
// Removed malformed import statement
import LocalIcon from '../components/LocalIcon';
import { useAuth   } from '../context/AuthContext';
import { useTheme   } from '../context/ThemeContext';
import { SPACING, BORDER_RADIUS   } from '../theme/designTokens';

const LoginScreen = ({ navigation }) => {
  const { theme  } = useTheme();
  const { login  } = useAuth();
  const [loginType, setLoginType] = useState('email');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const handleLogin = useCallback(async () => {
    if (loginType === 'email' && !email.trim()) {
      Alert.alert('Error', 'Please enter your email address');
      return;
    }

    if (loginType === 'phone' && !phone.trim()) {
      Alert.alert('Error', 'Please enter your phone number');
      return;
    }

    if (!password.trim()) {
      Alert.alert('Error', 'Please enter your password');
      return;
    }

    setIsLoading(true);

    try {
      const credentials = {
        email: loginType === 'email' ? email.trim() : '',
        phone: loginType === 'phone' ? phone.trim() : '',
        password: password.trim(),
      };

      const result = await login(credentials);

      if(result.success) {
        // Navigation will be handled by App.js based on auth state

      } else {
        Alert.alert('Login Failed', result.error || 'Invalid credentials');
      }
    } catch (error) {
      console.error('Login error:', error);
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }, [loginType, email, phone, password, login]);

  const handleDemoLogin = useCallback(async (role) => {
    setIsLoading(true);

    try {
      const credentials = {
        email: '<EMAIL>',
        phone: '+1234567890',
        password: 'admin123',
      };

      const result = await login(credentials);

      if(result.success) {

      } else {
        Alert.alert('Demo Login Failed', result.error || 'Demo login failed');
      }
    } catch (error) {
      console.error('Demo login error:', error);
      Alert.alert('Error', 'Demo login failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }, [login]);

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior="padding"
    >
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        {/* Header */}
        <View style={styles.header}>
          <Surface style={styles.logoContainer} elevation={2}>
            <LocalIcon name="needle" size={48} color={theme.colors.primary} />
          </Surface>
          <Text variant="headlineMedium" style={styles.title}>
            Elite Tailor Management
          </Text>
          <Text variant="bodyLarge" style={styles.subtitle}>
            Sign in to your account
          </Text>
        </View>

        {/* Login Form */}
        <Card style={styles.card} mode="elevated">
          <Card.Content style={styles.cardContent}>
            {/* Login Type Selector */}
            <SegmentedButtons
              value={loginType}
              onValueChange={setLoginType}
              buttons={[
                {
                  value: 'email',
                  label: 'Email',
                  icon: 'email',
                },
                {
                  value: 'phone',
                  label: 'Phone',
                  icon: 'phone',
                },
              }
              style={styles.segmentedButtons}
            />

            {/* Email/Phone Input */}
            {loginType === 'email' ? (
              <TextInput
                label="Email Address"
                value={email}
                onChangeText={setEmail}
                mode="outlined"
                keyboardType="email-address"
                autoCapitalize="none"
                autoComplete="email"
                left={<TextInput.Icon icon="email" />}
                style={styles.input}
                disabled={isLoading}
              />
            ) : (
              <TextInput
                label="Phone Number"
                value={phone}
                onChangeText={setPhone}
                mode="outlined"
                keyboardType="phone-pad"
                autoComplete="tel"
                left={<TextInput.Icon icon="phone" />}
                style={styles.input}
                disabled={isLoading}
                placeholder="+1234567890"
              />
            )}

            {/* Password Input */}
            <TextInput
              label="Password"
              value={password}
              onChangeText={setPassword}
              mode="outlined"
              secureTextEntry={!showPassword}
              autoComplete="password"
              left={<TextInput.Icon icon="lock" />}
              right={
                <TextInput.Icon
                  icon={showPassword ? 'eye-off' : 'eye'}
                  onPress={() => setShowPassword(!showPassword)}
                />
              }
              style={styles.input}
              disabled={isLoading}
            />

            {/* Login Button */}
            <Button
              mode="contained"
              onPress={handleLogin}
              loading={isLoading}
              disabled={isLoading}
              style={styles.loginButton}
              contentStyle={styles.loginButtonContent}>
              Sign In
            </Button>

            <Divider style={styles.divider} />

            {/* Demo Login Section */}
            <View style={styles.demoSection}>
              <Text variant="bodyMedium" style={styles.demoTitle}>
                Demo Access
              </Text>
              <Text variant="bodySmall" style={styles.demoSubtitle}>
                Try the app with demo credentials
              </Text>

              <Button
                mode="outlined"
                onPress={() => handleDemoLogin('admin')}
                loading={isLoading}
                disabled={isLoading}
                style={styles.demoButton}
                icon="account-supervisor"
              >
                Admin Demo
              </Button>
            </View>
          </Card.Content>
        </Card>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: SPACING.lg,
    justifyContent: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: SPACING.xl,
  },
  logoContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  title: {
    textAlign: 'center',
    marginBottom: SPACING.sm,
    fontWeight: 'bold',
  },
  subtitle: {
    textAlign: 'center',
    opacity: 0.7,
  },
  card: {
    borderRadius: BORDER_RADIUS.lg,
  },
  cardContent: {
    padding: SPACING.lg,
  },
  segmentedButtons: {
    marginBottom: SPACING.lg,
  },
  input: {
    marginBottom: SPACING.md,
  },
  loginButton: {
    marginTop: SPACING.md,
    marginBottom: SPACING.lg,
  },
  loginButtonContent: {
    paddingVertical: SPACING.sm,
  },
  divider: {
    marginVertical: SPACING.lg,
  },
  demoSection: {
    alignItems: 'center',
  },
  demoTitle: {
    fontWeight: 'bold',
    marginBottom: SPACING.xs,
  },
  demoSubtitle: {
    opacity: 0.7,
    marginBottom: SPACING.md,
    textAlign: 'center',
  },
  demoButton: {
    marginTop: SPACING.sm,
  },
});

export default LoginScreen;