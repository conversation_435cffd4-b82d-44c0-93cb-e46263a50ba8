/**
 * AddMeasurementScreen - Add/Edit customer measurements for tailoring
 */

import React, { useState, useEffect, useCallback } from 'react';
import { View, ScrollView, StyleSheet, Alert, TouchableOpacity   } from 'react-native';
import { Text   } from 'react-native-paper';
import { TextInput   } from 'react-native-paper';
import { Button   } from 'react-native-paper';
import { Card   } from 'react-native-paper';
import { IconButton   } from 'react-native-paper';
import { useData   } from '../context/DataContext';
import { useTheme   } from '../context/ThemeContext';
import { SPACING, BORDER_RADIUS   } from '../theme/designTokens';
import navigationService from '../services/NavigationService';

import LocalIcon, { NavigationLocalIcon, ActionLocalIcon, BusinessLocalIcon, SettingsLocalIcon, StatusLocalIcon } from '../components/LocalIcon';

const AddMeasurementScreen = ({ route, navigation }) => {
  const { theme  } = useTheme();
  const { actions, customers, state  } = useData();
  const { measurement, isEditing, customer  } = route?.params || {};

  const [measurementData, setMeasurementData] = useState({
    id: measurement?.id || Date.now().toString(),
    customerId: measurement?.customerId || customer?.id || '',
    garmentType: measurement?.garmentType || 'Shirt',
    measurements: measurement?.measurements || {},
    notes: measurement?.notes || '',
    createdAt: measurement?.createdAt || new Date().toISOString(),
    updatedAt: new Date().toISOString()});

  const [errors, setErrors] = useState({});
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [showCustomerPicker, setShowCustomerPicker] = useState(false);
  const [garmentTemplateBottomSheetVisible, setGarmentTemplateBottomSheetVisible] = useState(false);
  const [templateSearchQuery, setTemplateSearchQuery] = useState('');
  const [selectedTemplateCategory, setSelectedTemplateCategory] = useState('All');

  // Get garment templates from settings or use defaults
  const garmentTemplates = state.settings?.garmentTemplates || {
    Men: {
      Shirt: ['chest', 'waist', 'shoulder', 'armLength', 'shirtLength'],
      Pant: ['waist', 'hip', 'thigh', 'inseam', 'outseam', 'rise'],
      Suit: ['chest', 'waist', 'shoulder', 'armLength', 'jacketLength', 'pantWaist', 'pantLength'],
      Blazer: ['chest', 'waist', 'shoulder', 'armLength', 'blazerLength'],
      Kurta: ['chest', 'waist', 'shoulder', 'armLength', 'kurtaLength'],
      Sherwani: ['chest', 'waist', 'shoulder', 'armLength', 'sherwaniLength', 'neckSize']},
    Women: {
      Dress: ['bust', 'waist', 'hip', 'shoulder', 'armLength', 'dressLength'],
      Blouse: ['bust', 'waist', 'shoulder', 'armLength', 'blouseLength'],
      Skirt: ['waist', 'hip', 'skirtLength'],
      Saree: ['bust', 'waist', 'hip', 'blouseLength', 'petticoatLength'],
      Lehenga: ['bust', 'waist', 'hip', 'choliLength', 'skirtLength'],
      Kurti: ['bust', 'waist', 'shoulder', 'armLength', 'kurtiLength']},
    Unisex: {
      'T-Shirt': ['chest', 'waist', 'shoulder', 'armLength', 'tshirtLength'],
      Jacket: ['chest', 'waist', 'shoulder', 'armLength', 'jacketLength'],
      Coat: ['chest', 'waist', 'shoulder', 'armLength', 'coatLength'],
      Pajama: ['waist', 'hip', 'thigh', 'pajamaLength']},
    Children: {
      'Kids Shirt': ['chest', 'waist', 'shoulder', 'armLength', 'shirtLength'],
      'Kids Pant': ['waist', 'hip', 'thigh', 'inseam', 'pantLength'],
      'Kids Dress': ['chest', 'waist', 'shoulder', 'armLength', 'dressLength'],
      'School Uniform': ['chest', 'waist', 'shoulder', 'armLength', 'uniformLength']}
  };

  // GARMENT TEMPLATE FUNCTIONS
  const getAllTemplates = () => {
    const allTemplates = [];
    Object.keys(garmentTemplates).forEach(category => {
      Object.keys(garmentTemplates[category]).forEach(garmentType => {
        allTemplates.push({
          id: `${category}-${garmentType}`,
          name: garmentType,
          category: category,
          fields: garmentTemplates[category][garmentType],
          price: 0 // No pricing needed for measurements
        });
      });
    });
    return allTemplates;
  };

  const filteredTemplates = getAllTemplates().filter(template => {
    const matchesCategory = selectedTemplateCategory === 'All' || template.category === selectedTemplateCategory;
    const matchesSearch = template.name.toLowerCase().includes(templateSearchQuery.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  // Convert template arrays to measurement field objects
  const measurementFields = {};
  Object.entries(garmentTemplates).forEach(([category, categoryTemplates]) => {
    Object.entries(categoryTemplates).forEach(([garmentType, fields]) => {
      measurementFields[garmentType] = {};
      fields.forEach(field => {
        // Convert camelCase to Title Case
        const label = field.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
        measurementFields[garmentType][field] = label;
      });
    });
  });

  useEffect(() => {
    if(measurement?.customerId && customers) {
      const foundCustomer = customers.find(c => c.id === measurement.customerId);
      setSelectedCustomer(foundCustomer);
    } else if(customer) {
      setSelectedCustomer(customer);
    }
  }, [measurement, customer, customers]);

  const handleInputChange = useCallback((field, value) => {
    setMeasurementData(prev => ({
      ...prev,
      [field]: value
    }));

    if(errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  }, [errors]);

  const handleMeasurementChange = useCallback((field, value) => {
    setMeasurementData(prev => ({
      ...prev,
      measurements: {
        ...prev.measurements,
        [field]: value
      }
    }));
  }, []);

  const handleCustomerSelect = useCallback((customer) => {
    setSelectedCustomer(customer);
    setMeasurementData(prev => ({
      ...prev,
      customerId: customer.id
    }));
  }, []);

  const handleTemplateSelect = useCallback((template) => {
    setMeasurementData(prev => ({
      ...prev,
      garmentType: template.name,
      measurements: {} // Reset measurements when changing garment type
    }));
    setGarmentTemplateBottomSheetVisible(false);
  }, []);

  const validateForm = () => {
    const newErrors = {};

    if(!measurementData.customerId) {
      newErrors.customerId = 'Please select a customer';
    }

    if(!measurementData.garmentType) {
      newErrors.garmentType = 'Please select a garment type';
    }

    const currentFields = measurementFields[measurementData.garmentType] || {};
    const hasAnyMeasurement = Object.keys(currentFields).some(
      field => measurementData.measurements[field]
    );

    if(!hasAnyMeasurement) {
      newErrors.measurements = 'Please enter at least one measurement';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = useCallback(async () => {
    if (!validateForm()) {
      return;
    }

    try {
      const measurementToSave = {
        id: measurementData.id,
        customerId: measurementData.customerId,
        garmentType: measurementData.garmentType,
        measurements: measurementData.measurements,
        notes: measurementData.notes,
        takenBy: 'Current User', // You can get this from auth context
        measurementDate: new Date().toISOString().split('T')[0],
        isActive: true
      };

      await actions.addMeasurement(measurementToSave);
      Alert.alert('Success', 'Measurement added successfully');

      // Call callback if provided (for CreateOrder integration)
      if(route?.params?.onMeasurementSaved) {
        route.params.onMeasurementSaved(measurementData.measurements);
      }

      navigation.goBack();
    } catch (error) {
      console.error('Error saving measurement:', error);
      Alert.alert('Error', 'Failed to save measurement');
    }
  }, [measurementData, actions, navigation, route]);

  const currentFields = measurementFields[measurementData.garmentType] || {};
  const unit = 'cm'; // Default to centimeters

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>

        {/* Customer Selection */}
        <Card style={styles.card}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Customer
            </Text>
            <TouchableOpacity onPress={() => setShowCustomerPicker(true)}>
              <TextInput
                mode="outlined"
                label="Select Customer *"
                value={selectedCustomer?.name || ''}
                error={!!errors.customerId}
                style={styles.input}
                editable={false}
                right={<TextInput.Icon icon="chevron-down" />}
              />
            </TouchableOpacity>
            {errors.customerId && (
              <Text style={styles.errorText}>
                {errors.customerId}
              </Text>
            )}
          </Card.Content>
        </Card>

        {/* Garment Type Selection */}
        <Card style={styles.card}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Garment Type
            </Text>
            <TouchableOpacity onPress={() => setGarmentTemplateBottomSheetVisible(true)}>
              <TextInput
                mode="outlined"
                label="Select Garment Type *"
                value={measurementData.garmentType}
                error={!!errors.garmentType}
                style={styles.input}
                editable={false}
                right={<TextInput.Icon icon="chevron-down" />}
              />
            </TouchableOpacity>
            {errors.garmentType && (
              <Text style={styles.errorText}>
                {errors.garmentType}
              </Text>
            )}
          </Card.Content>
        </Card>

        {/* Measurements */}
        <Card style={styles.card}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Measurements ({unit})
            </Text>
            {Object.entries(currentFields || {}).map(([field, label]) => (
              <TextInput
                key={field}
                label={label}
                value={measurementData.measurements[field] || ''}
                onChangeText={(value) => handleMeasurementChange(field, value)}
                keyboardType="numeric"
                style={styles.input}
                mode="outlined"
                right={<TextInput.Affix text={unit} />}
              />
            ))}
            {errors.measurements && (
              <Text style={styles.errorText}>
                {errors.measurements}
              </Text>
            )}
          </Card.Content>
        </Card>

        {/* Notes */}
        <Card style={styles.card}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Notes
            </Text>
            <TextInput
              label="Additional notes"
              value={measurementData.notes}
              onChangeText={(value) => handleInputChange('notes', value)}
              multiline
              numberOfLines={3}
              style={styles.input}
              mode="outlined"
              placeholder="Any special instructions or notes..."
            />
          </Card.Content>
        </Card>

        {/* Action Buttons */}
        <View style={styles.buttonContainer}>
          <Button
            mode="outlined"
            onPress={() => navigationService.goBack()}
            style={styles.button}>
            Cancel
          </Button>
          <Button
            mode="contained"
            onPress={handleSave}
            style={styles.button}>
            {isEditing ? 'Update' : 'Save'} Measurement
          </Button>
        </View>
      </ScrollView>

      {/* Garment Template Selection Bottom Sheet */}
      {garmentTemplateBottomSheetVisible && (
        <View style={styles.bottomSheetOverlay}>
          <TouchableOpacity
            style={styles.bottomSheetBackdrop}
            onPress={() => setGarmentTemplateBottomSheetVisible(false)}
          />
          <View style={styles.bottomSheetContainer}>
            <View style={styles.bottomSheetHeader}>
              <Text variant="titleLarge" style={styles.sectionTitle}>
                Select Garment Type
              </Text>
              <IconButton
                icon="close"
                size={24}
                onPress={() => setGarmentTemplateBottomSheetVisible(false)}
              />
            </View>

            {/* Category Filter Chips */}
            <View style={styles.categoryChipsContainer}>
              {['All', 'Men', 'Women', 'Unisex', 'Children'].map((category) => (
                <TouchableOpacity
                  key={category}
                  style={[
                    styles.categoryChip,
                    {
                      backgroundColor: selectedTemplateCategory === category
                        ? theme.colors.primary
                        : theme.colors.surfaceVariant,
                      borderColor: selectedTemplateCategory === category
                        ? theme.colors.primary
                        : theme.colors.outline}
                  }
                  onPress={() => setSelectedTemplateCategory(category)}>
                  <Text
                    variant="bodyMedium"
                    style={{
                      color: selectedTemplateCategory === category
                        ? theme.colors.onPrimary
                        : theme.colors.onSurfaceVariant,
                      fontWeight: selectedTemplateCategory === category ? '600' : '400'}>
                    {category}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            <TextInput
              label="Search templates..."
              value={templateSearchQuery}
              onChangeText={setTemplateSearchQuery}
              mode="outlined"
              style={styles.searchInput}
              left={<TextInput.Icon icon="magnify" />}
            />

            <ScrollView style={styles.templateList} showsVerticalScrollIndicator={false}>
              {(filteredTemplates || []).map((template) => (
                <TouchableOpacity
                  key={template.id}
                  style={[
                    styles.templateItem,
                    {
                      borderBottomColor: theme.colors.outline,
                      backgroundColor: measurementData.garmentType === template.name
                        ? theme.colors.primaryContainer
                        : 'transparent'}
                  }
                  onPress={() => handleTemplateSelect(template)}>
                  <View style={styles.templateItemWithIcon}>
                    <View style={[
                      styles.templateIcon,
                      { backgroundColor: theme.colors.surfaceVariant }
                    }>
                      <BusinessLocalIcon
                        name={(() => {
                          const iconMap = {
                            'Shirt': 'tshirt-crew',
                            'Pant': 'human-handsdown',
                            'Suit': 'tie',
                            'Blazer': 'coat-rack',
                            'Kurta': 'tshirt-crew-outline',
                            'Sherwani': 'tie',
                            'Dress': 'tshirt-v-outline',
                            'Blouse': 'tshirt-v',
                            'Skirt': 'tshirt-v-outline',
                            'Saree': 'tshirt-v-outline',
                            'Lehenga': 'tshirt-v-outline',
                            'Kurti': 'tshirt-v-outline',
                            'T-Shirt': 'tshirt-crew',
                            'Jacket': 'coat-rack',
                            'Coat': 'coat-rack',
                            'Pajama': 'human-handsdown',
                            'Kids Shirt': 'baby',
                            'Kids Pant': 'baby',
                            'Kids Dress': 'baby',
                            'School Uniform': 'school'
                          };
                          return iconMap[template.name] || 'hanger';
                        })()
                        }
                        size={24}
                        color={theme.colors.primary}
                      />
                    </View>
                    <View style={styles.templateInfo}>
                      <Text variant="titleMedium" style={styles.templateTitle}>
                        {template.name}
                      </Text>
                      <Text variant="bodySmall" style={styles.templateSubtitle}>
                        {template.category} • {template.fields.length} measurements
                      </Text>
                    </View>
                    {measurementData.garmentType === template.name && (
                      <ActionLocalIcon name="check-circle" size={20} color={theme.colors.primary} />
                    )}
                  </View>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      )}

      {/* Customer Picker Modal */}
      {showCustomerPicker && (
        <View style={styles.bottomSheetOverlay}>
          <TouchableOpacity
            style={styles.bottomSheetBackdrop}
            onPress={() => setShowCustomerPicker(false)}
          />
          <View style={styles.bottomSheetContainer}>
            <View style={styles.bottomSheetHeader}>
              <Text variant="titleLarge" style={styles.sectionTitle}>
                Select Customer
              </Text>
              <IconButton
                icon="close"
                size={24}
                onPress={() => setShowCustomerPicker(false)}
              />
            </View>
            <ScrollView style={styles.templateList}>
              {(customers || []).length > 0 ? (
                (customers || []).map((customer) => (
                  <TouchableOpacity
                    key={customer.id}
                    style={[
                      styles.templateItem,
                      { borderBottomColor: theme.colors.outline }
                    }
                    onPress={() => {
                      handleCustomerSelect(customer);
                      setShowCustomerPicker(false);
                    }>
                    <View style={styles.templateItemWithIcon}>
                      <View style={[
                        styles.templateIcon,
                        { backgroundColor: theme.colors.primaryContainer }
                      }>
                        <BusinessLocalIcon
                          name="account"
                          size={24}
                          color={theme.colors.primary}
                        />
                      </View>
                      <View style={styles.templateInfo}>
                        <Text variant="titleMedium" style={styles.templateTitle}>
                          {customer.name}
                        </Text>
                        <Text variant="bodySmall" style={styles.templateSubtitle}>
                          {customer.phone} • {customer.totalOrders || 0} orders
                        </Text>
                      </View>
                      {selectedCustomer?.id === customer.id && (
                        <ActionLocalIcon name="check-circle" size={20} color={theme.colors.primary} />
                      )}
                    </View>
                  </TouchableOpacity>
                ))
              ) : (
                <View style={styles.emptyState}>
                  <Text variant="bodyMedium" style={styles.emptyText}>
                    No customers found. Add a customer first to create measurements.
                  </Text>
                </View>
              )}
            </ScrollView>
          </View>
        </View>
      )}
    </View>
  );
};






export default AddMeasurementScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5'},
  content: {
    flex: 1,
    paddingHorizontal: SPACING.lg,
    paddingTop: SPACING.xl},
  card: {
    marginBottom: SPACING.lg,
    borderRadius: BORDER_RADIUS.xl,
    backgroundColor: '#ffffff'},
  sectionTitle: {
    marginBottom: SPACING.md,
    fontWeight: '600',
    color: '#1a1a1a'},
  input: {
    marginBottom: SPACING.md},
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.sm,
    marginBottom: SPACING.sm},
  chip: {
    marginRight: SPACING.xs,
    marginBottom: SPACING.xs},
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: SPACING.lg,
    marginBottom: SPACING.xl,
    gap: SPACING.md},
  button: {
    flex: 1},
  cancelButton: {
    marginRight: SPACING.sm},
  saveButton: {
    marginLeft: SPACING.sm},
  errorText: {
    fontSize: 12,
    marginTop: SPACING.xs,
    color: '#d32f2f'},
  bottomSheetOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000},
  bottomSheetBackdrop: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)'},
  bottomSheetContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '70%',
    backgroundColor: '#ffffff',
    borderTopLeftRadius: BORDER_RADIUS.xl,
    borderTopRightRadius: BORDER_RADIUS.xl,
    padding: SPACING.lg},
  bottomSheetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.md},
  searchInput: {
    marginBottom: SPACING.md},
  templateList: {
    flex: 1},
  templateItem: {
    paddingVertical: SPACING.md,
    borderBottomWidth: 1},
  templateItemWithIcon: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.sm},
  templateIcon: {
    width: 48,
    height: 48,
    borderRadius: BORDER_RADIUS.md,
    alignItems: 'center',
    justifyContent: 'center'},
  templateInfo: {
    flex: 1,
    gap: SPACING.xs},
  templateTitle: {
    color: '#1a1a1a',
    fontWeight: '600'},
  templateSubtitle: {
    color: '#666666'},
  categoryChipsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.xs,
    marginBottom: SPACING.md},
  categoryChip: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.md,
    borderWidth: 1},
  emptyState: {
    padding: 20,
    alignItems: 'center'},
  emptyText: {
    color: '#666666',
    textAlign: 'center'}});