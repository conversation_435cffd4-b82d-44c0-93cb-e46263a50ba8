/**
 * AddEmployeeScreen - Add/Edit employee form (Admin only)
 */

import React, { useState, useEffect, useCallback } from 'react';
import { View, StyleSheet, Alert, ScrollView, KeyboardAvoidingView   } from 'react-native';
import { Text   } from 'react-native-paper';
import { TextInput   } from 'react-native-paper';
import { Button   } from 'react-native-paper';
import { Card   } from 'react-native-paper';
import { SegmentedButtons   } from 'react-native-paper';
import { Switch   } from 'react-native-paper';
import { Divider   } from 'react-native-paper';
import LocalIcon from '../components/LocalIcon';
import { useAuth, ROLES   } from '../context/AuthContext';
import { useTheme   } from '../context/ThemeContext';
import CommonHeader from '../components/CommonHeader';
import ImagePicker from '../components/ImagePicker';
import { SPACING, BORDER_RADIUS   } from '../theme/designTokens';

const AddEmployeeScreen = ({ navigation, route }) => {
  const { theme  } = useTheme();
  const { addEmployee, updateEmployee, isAdmin  } = useAuth();
  const { employee  } = route.params || {};
  const isEditing = !!employee;

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    password: '',
    role: ROLES.USER,
    status: 'active',
    avatar: null});
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  // Redirect if not admin
  useEffect(() => {
    if (!isAdmin()) {
      Alert.alert('Access Denied', 'You do not have permission to access this page.');
      navigation.goBack();
      return;
    }

    // Pre-fill form if editing
    if(isEditing && employee) {
      setFormData({
        name: employee.name || '',
        email: employee.email || '',
        phone: employee.phone || '',
        password: '', // Don't pre-fill password for security
        role: employee.role || ROLES.USER,
        status: employee.status || 'active',
        avatar: employee.avatar || null});
    }
  }, [isAdmin, navigation, isEditing, employee]);

  const handleInputChange = useCallback((field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value}));
  }, []);

  const validateForm = () => {
    if (!formData.name.trim()) {
      Alert.alert('Validation Error', 'Please enter employee name');
      return false;
    }

    if (!formData.email.trim()) {
      Alert.alert('Validation Error', 'Please enter email address');
      return false;
    }

    if (!formData.phone.trim()) {
      Alert.alert('Validation Error', 'Please enter phone number');
      return false;
    }

    if (!isEditing && !formData.password.trim()) {
      Alert.alert('Validation Error', 'Please enter password');
      return false;
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      Alert.alert('Validation Error', 'Please enter a valid email address');
      return false;
    }

    // Basic phone validation
    const phoneRegex = /^\+?[\d\s\-\(\)]+$/;
    if (!phoneRegex.test(formData.phone)) {
      Alert.alert('Validation Error', 'Please enter a valid phone number');
      return false;
    }

    if(!isEditing && formData.password.length < 6) {
      Alert.alert('Validation Error', 'Password must be at least 6 characters long');
      return false;
    }

    return true;
  };

  const handleSave = useCallback(async () => {
    if (!validateForm()) return;

    setIsLoading(true);

    try {
      const employeeData = {
        ...formData,
        name: formData.name.trim(),
        email: formData.email.trim().toLowerCase(),
        phone: formData.phone.trim()};

      // Don't include password if editing and it's empty
      if (isEditing && !formData.password.trim()) {
        delete employeeData.password;
      }

      let result;
      if(isEditing) {
        result = await updateEmployee(employee.id, employeeData);
      } else {
        result = await addEmployee(employeeData);
      }

      if(result.success) {
        Alert.alert(
          'Success',
          `Employee ${isEditing ? 'updated' : 'added'} successfully!`,
          [
            {
              text: 'OK',
              onPress: () => navigation.goBack()]
        );
      } else {
        Alert.alert('Error', result.error || `Failed to ${isEditing ? 'update' : 'add'} employee`);
      }
    } catch (error) {
      console.error('Save employee error:', error);
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const handleImageSelected = useCallback((imageUri) => {
    handleInputChange('avatar', imageUri);
  }, []);

  if (!isAdmin()) {
    return null; // Don't render anything if not admin
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior="padding"
    >
      <CommonHeader
        title={isEditing ? 'Edit Employee' : 'Add Employee'}
        showSearch={false}
        showBack={true}
        onBackPress={() => navigation.goBack()}
      />

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        <Card style={styles.formCard} mode="elevated">
          <Card.Content style={styles.cardContent}>
            {/* Avatar Section */}
            <View style={styles.avatarSection}>
              <Text variant="titleMedium" style={styles.sectionTitle}>
                Profile Photo
              </Text>
              <ImagePicker
                onImageSelected={handleImageSelected}
                currentImage={formData.avatar}
                style={styles.imagePicker}
              />
            </View>

            <Divider style={styles.divider} />

            {/* Basic Information */}
            <View style={styles.section}>
              <Text variant="titleMedium" style={styles.sectionTitle}>
                Basic Information
              </Text>

              <TextInput
                label="Full Name *"
                value={formData.name}
                onChangeText={(value) => handleInputChange('name', value)}
                mode="outlined"
                left={<TextInput.Icon icon="account" />}
                style={styles.input}
                disabled={isLoading}
              />

              <TextInput
                label="Email Address *"
                value={formData.email}
                onChangeText={(value) => handleInputChange('email', value)}
                mode="outlined"
                keyboardType="email-address"
                autoCapitalize="none"
                left={<TextInput.Icon icon="email" />}
                style={styles.input}
                disabled={isLoading}
              />

              <TextInput
                label="Phone Number *"
                value={formData.phone}
                onChangeText={(value) => handleInputChange('phone', value)}
                mode="outlined"
                keyboardType="phone-pad"
                left={<TextInput.Icon icon="phone" />}
                style={styles.input}
                disabled={isLoading}
                placeholder="+**********"
              />
            </View>

            <Divider style={styles.divider} />

            {/* Security */}
            <View style={styles.section}>
              <Text variant="titleMedium" style={styles.sectionTitle}>
                Security
              </Text>

              <TextInput
                label={isEditing ? "New Password (leave empty to keep current)" : "Password *"}
                value={formData.password}
                onChangeText={(value) => handleInputChange('password', value)}
                mode="outlined"
                secureTextEntry={!showPassword}
                left={<TextInput.Icon icon="lock" />}
                right={
                  <TextInput.Icon
                    icon={showPassword ? 'eye-off' : 'eye'}
                    onPress={() => setShowPassword(!showPassword)}
                  />
                }
                style={styles.input}
                disabled={isLoading}
              />
            </View>

            <Divider style={styles.divider} />

            {/* Role & Status */}
            <View style={styles.section}>
              <Text variant="titleMedium" style={styles.sectionTitle}>
                Role & Status
              </Text>

              <Text variant="bodyMedium" style={styles.fieldLabel}>
                Role
              </Text>
              <SegmentedButtons
                value={formData.role}
                onValueChange={(value) => handleInputChange('role', value)}
                buttons={[
                  {
                    value: ROLES.USER,
                    label: 'User',
                    icon: 'account'},
                  {
                    value: ROLES.ADMIN,
                    label: 'Admin',
                    icon: 'crown'}}
                style={styles.segmentedButtons}
                disabled={isLoading}
              />

              <View style={styles.switchRow}>
                <View style={styles.switchLabel}>
                  <Text variant="bodyMedium" style={styles.sectionTitle}>
                    Active Status
                  </Text>
                  <Text variant="bodySmall" style={styles.fieldLabel}>
                    Employee can login and access the system
                  </Text>
                </View>
                <Switch
                  value={formData.status === 'active'}
                  onValueChange={(value) => handleInputChange('status', value ? 'active' : 'inactive')}
                  disabled={isLoading}
                />
              </View>
            </View>
          </Card.Content>
        </Card>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <Button
            mode="outlined"
            onPress={() => navigation.goBack()}
            style={styles.button}
            disabled={isLoading}>
            Cancel
          </Button>
          <Button
            mode="contained"
            onPress={handleSave}
            loading={isLoading}
            disabled={isLoading}
            style={styles.button}>
            {isEditing ? 'Update Employee' : 'Add Employee'}
          </Button>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default AddEmployeeScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background},
  content: {
    flex: 1,
    padding: SPACING.lg},
  formCard: {
    borderRadius: BORDER_RADIUS.xl,
    marginBottom: SPACING.lg,
    backgroundColor: theme.colors.surface},
  cardContent: {
    padding: SPACING.xl},
  avatarSection: {
    alignItems: 'center',
    marginBottom: SPACING.lg},
  imagePicker: {
    marginTop: SPACING.md},
  section: {
    marginBottom: SPACING.lg},
  sectionTitle: {
    fontWeight: '600',
    marginBottom: SPACING.md,
    color: theme.colors.onSurface},
  input: {
    marginBottom: SPACING.md},
  fieldLabel: {
    marginBottom: SPACING.sm,
    fontWeight: '500',
    color: theme.colors.onSurfaceVariant},
  segmentedButtons: {
    marginBottom: SPACING.lg},
  switchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: SPACING.sm},
  switchLabel: {
    flex: 1,
    marginRight: SPACING.md},
  divider: {
    marginVertical: SPACING.lg},
  actionButtons: {
    flexDirection: 'row',
    gap: SPACING.md,
    marginBottom: SPACING.xl},
  button: {
    flex: 1,
    borderRadius: BORDER_RADIUS.lg},
  cancelButton: {
    // Additional styles for cancel button if needed
  },
  saveButton: {
    // Additional styles for save button if needed
  }});