import React, { useState, useCallback, useEffect, useMemo } from 'react';
// FIXED: Removed invalid semicolon from import
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Text, TextInput, Button, Card, Chip, IconButton, Surface, SegmentedButtons } from 'react-native-paper';
// FIXED: Corrected useTheme import to use the custom context
import { useTheme } from '../context/ThemeContext'; 
import { useNavigation } from '@react-navigation/native';
import { useFinancial } from '../context/FinancialContext';
import DateTimePicker from '@react-native-community/datetimepicker';

// Constants (assuming these are defined elsewhere, included here for context)
const SPACING = { xs: 4, sm: 8, md: 16, lg: 24, xl: 32 };
const BORDER_RADIUS = { sm: 4, md: 8, lg: 12, xl: 16 };

const EXPENSE_CATEGORIES = [
  { label: 'Materials', value: 'materials', icon: 'package-variant' },
  { label: 'Equipment', value: 'equipment', icon: 'tools' },
  { label: 'Utilities', value: 'utilities', icon: 'lightning-bolt' },
  { label: 'Rent', value: 'rent', icon: 'home' },
  { label: 'Marketing', value: 'marketing', icon: 'bullhorn' },
  { label: 'Transport', value: 'transportation', icon: 'car' },
  { label: 'Other', value: 'other', icon: 'dots-horizontal' },
];

const PAYMENT_METHODS = [
  { label: 'Cash', value: 'cash', icon: 'cash' },
  { label: 'Card', value: 'card', icon: 'credit-card' },
  { label: 'Transfer', value: 'transfer', icon: 'bank' },
];

const ExpenseFormScreen = () => {
  const { theme } = useTheme();
  const navigation = useNavigation();
  const { addExpense } = useFinancial();

  const [formData, setFormData] = useState({
    amount: '', description: '', category: 'materials', paymentMethod: 'cash',
    date: new Date(), vendor: '', notes: '', receiptNumber: '', isRecurring: false,
  });

  const [showDatePicker, setShowDatePicker] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState({});

  const validateForm = useCallback(() => {
    const newErrors = {};
    if (!formData.amount || parseFloat(formData.amount) <= 0) newErrors.amount = 'Please enter a valid amount';
    if (!formData.description.trim()) newErrors.description = 'Please enter a description';
    if (!formData.vendor.trim()) newErrors.vendor = 'Please enter vendor name';
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  const handleSubmit = useCallback(async () => {
    if (!validateForm()) return;
    setIsSubmitting(true);
    try {
      const expenseData = {
        ...formData,
        amount: parseFloat(formData.amount),
        id: `exp_${Date.now()}`,
        createdAt: new Date().toISOString(),
      };
      await addExpense(expenseData);
      Alert.alert('Success', 'Expense added successfully!', [
        { text: 'Add Another', onPress: () => {
            setFormData({ amount: '', description: '', category: 'materials', paymentMethod: 'cash', date: new Date(), vendor: '', notes: '', receiptNumber: '', isRecurring: false });
            setErrors({});
        }},
        { text: 'Done', onPress: () => navigation.goBack() },
      ]);
    } catch (error) {
      Alert.alert('Error', 'Failed to add expense. Please try again.');
      console.error('Error adding expense:', error);
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, validateForm, addExpense, navigation]);

  const handleDateChange = useCallback((event, selectedDate) => {
    setShowDatePicker(false);
    if (selectedDate) updateField('date', selectedDate);
  }, []);

  const updateField = useCallback((field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) setErrors(prev => ({ ...prev, [field]: null }));
  }, [errors]);

  // FIXED: Styles moved inside component and memoized
  const styles = useMemo(() => StyleSheet.create({
    container: { flex: 1, backgroundColor: theme.colors.background },
    header: { paddingTop: 0, backgroundColor: theme.colors.surface },
    headerContent: { flexDirection: 'row', alignItems: 'center', paddingHorizontal: SPACING.sm, paddingVertical: SPACING.sm, borderBottomWidth: 1, borderBottomColor: theme.colors.outlineVariant },
    headerTitle: { flex: 1, textAlign: 'center', fontWeight: 'bold', fontSize: 20, color: theme.colors.onSurface },
    headerSpacer: { width: 48 },
    content: { flex: 1, padding: SPACING.md },
    section: { marginBottom: SPACING.lg, borderRadius: BORDER_RADIUS.lg, backgroundColor: theme.colors.surface, padding: SPACING.lg },
    sectionTitle: { fontWeight: '600', marginBottom: SPACING.md, color: theme.colors.onSurface, fontSize: 18 },
    input: { marginBottom: SPACING.md },
    errorText: { fontSize: 12, color: theme.colors.error, marginTop: -SPACING.sm, marginBottom: SPACING.sm, marginLeft: SPACING.sm },
    categoryGrid: { flexDirection: 'row', flexWrap: 'wrap', gap: SPACING.sm },
    categoryChip: { marginBottom: SPACING.xs },
    segmentedButtons: { marginBottom: SPACING.md },
    dateButton: { marginBottom: SPACING.sm, justifyContent: 'flex-start' },
    submitSection: { padding: SPACING.lg, borderTopLeftRadius: BORDER_RADIUS.xl, borderTopRightRadius: BORDER_RADIUS.xl, borderTopWidth: 1, borderTopColor: theme.colors.outlineVariant, backgroundColor: theme.colors.surface },
    submitButton: { borderRadius: BORDER_RADIUS.md },
    submitButtonContent: { paddingVertical: SPACING.sm },
    bottomSpacer: { height: SPACING.xl },
  }), [theme]);

  return (
    <KeyboardAvoidingView style={styles.container} behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
      <Surface style={styles.header} elevation={2}>
        <View style={styles.headerContent}>
          <IconButton icon="arrow-left" size={24} onPress={() => navigation.goBack()} />
          <Text style={styles.headerTitle}>Add Expense</Text>
          <View style={styles.headerSpacer} />
        </View>
      </Surface>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false} keyboardShouldPersistTaps="handled">
        <Card style={styles.section}>
          <Card.Content>
            <Text style={styles.sectionTitle}>💰 Amount & Basic Info</Text>
            <TextInput label="Amount *" value={formData.amount} onChangeText={v => updateField('amount', v)} keyboardType="decimal-pad" mode="outlined" style={styles.input} error={!!errors.amount} left={<TextInput.Icon icon="currency-bdt" />} />
            {errors.amount && <Text style={styles.errorText}>{errors.amount}</Text>}
            <TextInput label="Description *" value={formData.description} onChangeText={v => updateField('description', v)} mode="outlined" style={styles.input} error={!!errors.description} />
            {errors.description && <Text style={styles.errorText}>{errors.description}</Text>}
            <TextInput label="Vendor/Supplier *" value={formData.vendor} onChangeText={v => updateField('vendor', v)} mode="outlined" style={styles.input} error={!!errors.vendor} left={<TextInput.Icon icon="store" />} />
            {errors.vendor && <Text style={styles.errorText}>{errors.vendor}</Text>}
          </Card.Content>
        </Card>
        <Card style={styles.section}>
          <Card.Content>
            <Text style={styles.sectionTitle}>📂 Category</Text>
            <View style={styles.categoryGrid}>
              {EXPENSE_CATEGORIES.map(cat => <Chip key={cat.value} mode={formData.category === cat.value ? 'flat' : 'outlined'} selected={formData.category === cat.value} onPress={() => updateField('category', cat.value)} style={styles.categoryChip} icon={cat.icon}>{cat.label}</Chip>)}
            </View>
          </Card.Content>
        </Card>
        <Card style={styles.section}>
          <Card.Content>
            <Text style={styles.sectionTitle}>💳 Payment & Date</Text>
            <SegmentedButtons value={formData.paymentMethod} onValueChange={v => updateField('paymentMethod', v)} buttons={PAYMENT_METHODS} style={styles.segmentedButtons} />
            <Button mode="outlined" onPress={() => setShowDatePicker(true)} style={styles.dateButton} icon="calendar" contentStyle={{justifyContent: 'flex-start'}>{`Date: ${formData.date.toLocaleDateString()}`}</Button>
            {showDatePicker && <DateTimePicker value={formData.date} mode="date" display="default" onChange={handleDateChange} />}
          </Card.Content>
        </Card>
        <Card style={styles.section}>
          <Card.Content>
            <Text style={styles.sectionTitle}>📝 Additional Details</Text>
            <TextInput label="Receipt Number" value={formData.receiptNumber} onChangeText={v => updateField('receiptNumber', v)} mode="outlined" style={styles.input} left={<TextInput.Icon icon="receipt" />} />
            <TextInput label="Notes" value={formData.notes} onChangeText={v => updateField('notes', v)} mode="outlined" style={styles.input} multiline numberOfLines={3} placeholder="Additional notes..." />
          </Card.Content>
        </Card>
        <View style={styles.bottomSpacer} />
      </ScrollView>
      <Surface style={styles.submitSection} elevation={4}>
        <Button mode="contained" onPress={handleSubmit} loading={isSubmitting} disabled={isSubmitting} style={styles.submitButton} contentStyle={styles.submitButtonContent} icon="plus">
          {isSubmitting ? 'Adding Expense...' : 'Add Expense'}
        </Button>
      </Surface>
    </KeyboardAvoidingView>
  );
};

export default ExpenseFormScreen;