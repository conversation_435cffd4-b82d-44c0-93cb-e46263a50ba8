/**
 * BusinessHoursScreen - Manage shop business hours
 * Part of the Elite Tailoring Management System
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import {
  Text,
  Card,
  Button,
  Switch,
  Surface,
  Chip,
  IconButton,
  Portal,
  Modal,
  TextInput,
} from 'react-native-paper';
import { useTheme } from '../context/ThemeContext';
import { useData } from '../context/DataContext';
import CommonHeader from '../components/CommonHeader';
import { SPACING, BORDER_RADIUS } from '../theme/designTokens';
import LocalIcon from '../components/LocalIcon';

const BusinessHoursScreen = () => {
  const { theme } = useTheme();
  const { state, actions } = useData();

  const [businessHours, setBusinessHours] = useState({
    monday: { open: '09:00', close: '18:00', isOpen: true },
    tuesday: { open: '09:00', close: '18:00', isOpen: true },
    wednesday: { open: '09:00', close: '18:00', isOpen: true },
    thursday: { open: '09:00', close: '18:00', isOpen: true },
    friday: { open: '09:00', close: '18:00', isOpen: true },
    saturday: { open: '10:00', close: '16:00', isOpen: true },
    sunday: { open: '10:00', close: '16:00', isOpen: false },
  });

  const [editingDay, setEditingDay] = useState(null);
  const [tempTime, setTempTime] = useState({ open: '', close: '' });

  useEffect(() => {
    if (state.settings?.businessHours) {
      setBusinessHours(state.settings.businessHours);
    }
  }, [state.settings]);

  // FIXED: Corrected useCallback syntax
  const handleToggleDay = useCallback((day) => {
    const updated = {
      ...businessHours,
      [day]: {
        ...businessHours[day],
        isOpen: !businessHours[day].isOpen,
      },
    };
    setBusinessHours(updated);
    saveBusinessHours(updated);
  }, [businessHours]);

  // FIXED: Corrected useCallback syntax
  const handleEditTime = useCallback((day) => {
    setEditingDay(day);
    setTempTime({
      open: businessHours[day].open,
      close: businessHours[day].close,
    });
  }, [businessHours]);

  // FIXED: Corrected useCallback syntax
  const handleSaveTime = useCallback(() => {
    if (!tempTime.open || !tempTime.close) {
      Alert.alert('Error', 'Please enter both opening and closing times');
      return;
    }

    const updated = {
      ...businessHours,
      [editingDay]: {
        ...businessHours[editingDay],
        open: tempTime.open,
        close: tempTime.close,
      },
    };
    setBusinessHours(updated);
    saveBusinessHours(updated);
    setEditingDay(null);
  }, [businessHours, editingDay, tempTime]);

  const saveBusinessHours = async (hours) => {
    try {
      await actions.updateSettings({ businessHours: hours });
    } catch (error) {
      console.error('Failed to save business hours:', error);
      Alert.alert('Error', 'Failed to save business hours');
    }
  };

  const getDayName = (day) => {
    return day.charAt(0).toUpperCase() + day.slice(1);
  };

  // FIXED: Stylesheet moved inside the component and wrapped in useMemo
  const styles = useMemo(() => StyleSheet.create({
    container: { flex: 1, backgroundColor: theme.colors.background },
    content: { paddingHorizontal: SPACING.lg, paddingTop: SPACING.lg },
    quickActions: { padding: SPACING.lg, marginBottom: SPACING.lg, borderRadius: BORDER_RADIUS.lg, backgroundColor: theme.colors.surface },
    sectionTitle: { fontWeight: '600', marginBottom: SPACING.md },
    actionButtons: { flexDirection: 'row', gap: SPACING.md },
    actionButton: { flex: 1 },
    daysContainer: { gap: SPACING.md, paddingBottom: SPACING.xl },
    dayCard: { borderRadius: BORDER_RADIUS.lg, marginBottom: SPACING.md, backgroundColor: theme.colors.surface },
    dayHeader: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: SPACING.sm },
    dayInfo: { flexDirection: 'row', alignItems: 'center', gap: SPACING.sm },
    todayChip: { height: 24, marginLeft: SPACING.sm, borderColor: theme.colors.primary },
    timeContainer: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginTop: SPACING.sm },
    timeDisplay: { flexDirection: 'row', alignItems: 'center', gap: SPACING.sm },
    closedContainer: { flexDirection: 'row', alignItems: 'center', gap: SPACING.sm, marginTop: SPACING.sm },
    modal: { margin: SPACING.xl, padding: SPACING.xl, borderRadius: BORDER_RADIUS.lg },
    modalTitle: { fontWeight: '600', marginBottom: SPACING.lg, textAlign: 'center' },
    timeInputs: { gap: SPACING.md, marginBottom: SPACING.lg },
    timeInput: { flex: 1 },
    modalActions: { flexDirection: 'row', gap: SPACING.md, marginTop: SPACING.lg },
    modalButton: { flex: 1 },
    dayNameText: { fontWeight: '600', color: theme.colors.onSurface },
    timeText: { marginLeft: 8, color: theme.colors.onSurface },
    closedText: { marginLeft: 8, color: theme.colors.error },
  }), [theme]);

  const renderDayCard = (day) => {
    const dayData = businessHours[day];
    const todayIndex = new Date().getDay(); // Sunday = 0, Monday = 1, ...
    const dayIndex = Object.keys(businessHours).indexOf(day) + 1; // Our index: Monday = 1, ... Sunday = 7
    const isToday = todayIndex === (dayIndex % 7);

    return (
      <Card key={day} style={styles.dayCard}>
        <Card.Content>
          <View style={styles.dayHeader}>
            <View style={styles.dayInfo}>
              <Text variant="titleMedium" style={styles.dayNameText}>
                {getDayName(day)}
              </Text>
              {isToday && (
                <Chip
                  mode="outlined"
                  compact
                  style={styles.todayChip}
                  textStyle={{ color: theme.colors.primary, fontSize: 10 }}
                >
                  Today
                </Chip>
              )}
            </View>
            <Switch
              value={dayData.isOpen}
              onValueChange={() => handleToggleDay(day)}
            />
          </View>

          {dayData.isOpen ? (
            <View style={styles.timeContainer}>
              <View style={styles.timeDisplay}>
                <LocalIcon name="clock-outline" size={16} color={theme.colors.onSurfaceVariant} />
                <Text variant="bodyMedium" style={styles.timeText}>
                  {dayData.open} - {dayData.close}
                </Text>
              </View>
              <IconButton
                icon="pencil"
                size={20}
                iconColor={theme.colors.primary}
                onPress={() => handleEditTime(day)}
              />
            </View>
          ) : (
            <View style={styles.closedContainer}>
              <LocalIcon name="close-circle-outline" size={16} color={theme.colors.error} />
              <Text variant="bodyMedium" style={styles.closedText}>
                Closed
              </Text>
            </View>
          )}
        </Card.Content>
      </Card>
    );
  };

  return (
    <View style={styles.container}>
      <CommonHeader
        title="Business Hours"
        subtitle="Manage your shop opening hours"
        showSearch={false}
        showBackButton={true}
      />

      <ScrollView contentContainerStyle={styles.content} showsVerticalScrollIndicator={false}>
        <Surface style={styles.quickActions} elevation={1}>
          <Text variant="titleSmall" style={styles.sectionTitle}>
            Quick Actions
          </Text>
          <View style={styles.actionButtons}>
            <Button
              mode="outlined"
              onPress={() => {
                const updated = Object.keys(businessHours).reduce((acc, day) => {
                  acc[day] = { ...businessHours[day], isOpen: true };
                  return acc;
                }, {});
                setBusinessHours(updated);
                saveBusinessHours(updated);
              }}
              style={styles.actionButton}
            >
              Open All Days
            </Button>
            <Button
              mode="outlined"
              onPress={() => {
                const updated = Object.keys(businessHours).reduce((acc, day) => {
                  acc[day] = { ...businessHours[day], isOpen: day !== 'sunday' };
                  return acc;
                }, {});
                setBusinessHours(updated);
                saveBusinessHours(updated);
              }}
              style={styles.actionButton}
            >
              Weekdays Only
            </Button>
          </View>
        </Surface>

        <View style={styles.daysContainer}>
          {Object.keys(businessHours).map(renderDayCard)}
        </View>
      </ScrollView>

      <Portal>
        <Modal
          visible={editingDay !== null}
          onDismiss={() => setEditingDay(null)}
          contentContainerStyle={[styles.modal, { backgroundColor: theme.colors.surface }]}
        >
          <Text variant="titleLarge" style={styles.modalTitle}>
            Edit {editingDay ? getDayName(editingDay) : ''} Hours
          </Text>

          <View style={styles.timeInputs}>
            <TextInput
              label="Opening Time"
              value={tempTime.open}
              onChangeText={(text) => setTempTime({ ...tempTime, open: text })}
              placeholder="09:00"
              mode="outlined"
              style={styles.timeInput}
            />
            <TextInput
              label="Closing Time"
              value={tempTime.close}
              onChangeText={(text) => setTempTime({ ...tempTime, close: text })}
              placeholder="18:00"
              mode="outlined"
              style={styles.timeInput}
            />
          </View>

          <View style={styles.modalActions}>
            <Button
              mode="outlined"
              onPress={() => setEditingDay(null)}
              style={styles.modalButton}
            >
              Cancel
            </Button>
            <Button
              mode="contained"
              onPress={handleSaveTime}
              style={styles.modalButton}
            >
              Save
            </Button>
          </View>
        </Modal>
      </Portal>
    </View>
  );
};

export default BusinessHoursScreen;