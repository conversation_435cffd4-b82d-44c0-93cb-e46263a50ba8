/**
 * NotificationSettingsScreen - Manage notification preferences
 * Part of the Elite Tailoring Management System
 */

import React, { useState, useEffect, useCallback } from 'react';
import { View, StyleSheet, ScrollView, Alert   } from 'react-native';
import { Text   } from 'react-native-paper';
import { Switch   } from 'react-native-paper';
import { Surface   } from 'react-native-paper';
import { Button   } from 'react-native-paper';
import { List   } from 'react-native-paper';
import { useTheme   } from '../context/ThemeContext';
import { useData   } from '../context/DataContext';
import CommonHeader from '../components/CommonHeader';
import { SPACING, BORDER_RADIUS   } from '../theme/designTokens';
import LocalIcon from '../components/LocalIcon';

const NotificationSettingsScreen = () => {
  const { theme  } = useTheme();
  const { state, actions  } = useData();

  const [notifications, setNotifications] = useState({
    // Order notifications
    newOrders: true,
    orderUpdates: true,
    orderReminders: true,
    orderDeadlines: true,

    // Customer notifications
    newCustomers: false,
    customerMessages: true,
    appointmentReminders: true,

    // Business notifications
    lowStock: true,
    dailyReports: false,
    weeklyReports: true,
    monthlyReports: true,

    // System notifications
    systemUpdates: true,
    securityAlerts: true,
    backupReminders: false,

    // Marketing notifications
    promotions: false,
    tips: true,
    newsletters: false,
  });

  const [soundEnabled, setSoundEnabled] = useState(true);
  const [vibrationEnabled, setVibrationEnabled] = useState(true);
  const [quietHours, setQuietHours] = useState({
    enabled: false,
    start: '22:00',
    end: '08:00',
  });

  useEffect(() => {
    // Load notification settings
    if(state.settings?.notifications) {
      setNotifications(state.settings.notifications);
    }
    if(state.settings?.soundEnabled !== undefined) {
      setSoundEnabled(state.settings.soundEnabled);
    }
    if(state.settings?.vibrationEnabled !== undefined) {
      setVibrationEnabled(state.settings.vibrationEnabled);
    }
    if(state.settings?.quietHours) {
      setQuietHours(state.settings.quietHours);
    }
  }, [state.settings]);

  const handleNotificationToggle = useCallback((key) => {
    const updated = {
      ...notifications,
      [key]: !notifications[key],
    };
    setNotifications(updated);
    saveSettings({ notifications: updated });
  }, [notifications]);

  const handleSoundToggle = useCallback(() => {
    const newValue = !soundEnabled;
    setSoundEnabled(newValue);
    saveSettings({ soundEnabled: newValue });
  }, [soundEnabled]);

  const handleVibrationToggle = useCallback(() => {
    const newValue = !vibrationEnabled;
    setVibrationEnabled(newValue);
    saveSettings({ vibrationEnabled: newValue });
  }, [vibrationEnabled]);

  const handleQuietHoursToggle = useCallback(() => {
    const updated = {
      ...quietHours,
      enabled: !quietHours.enabled,
    };
    setQuietHours(updated);
    saveSettings({ quietHours: updated });
  }, [quietHours]);

  const saveSettings = async (settings) => {
    try {
      await actions.updateSettings(settings);
    } catch (error) {
      console.error('Failed to save notification settings:', error);
      Alert.alert('Error', 'Failed to save settings');
    }
  };

  const renderNotificationItem = (key, title, description, icon) => (
    <List.Item
      key={key}
      title={title}
      description={description}
      left={(props) => (
        <View style={styles.iconContainer}>
          <LocalIcon name={icon} size={24} color={theme.colors.primary} />
        </View>
      )}
      right={() => (
        <Switch
          value={notifications[key]
          onValueChange={() => handleNotificationToggle(key)}
          thumbColor={notifications[key] ? theme.colors.primary : theme.colors.outline}
          trackColor={{
            false: theme.colors.surfaceVariant,
            true: theme.colors.primaryContainer,
          }
        />
      )}
      style={styles.listItem}
    />
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}}>
      <CommonHeader
        title="Notification Settings"
        subtitle="Manage your notification preferences"
        showSearch={false}
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* General Settings */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]}]} elevation={1}>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            General Settings
          </Text>

          <List.Item
            title="Sound"
            description="Play sound for notifications"
            left={(props) => (
              <View style={styles.iconContainer}>
                <LocalIcon name="bell" size={24} color={theme.colors.primary} />
              </View>
            )}
            right={() => (
              <Switch
                value={soundEnabled}
                onValueChange={handleSoundToggle}
                thumbColor={soundEnabled ? theme.colors.primary : theme.colors.outline}
                trackColor={{
                  false: theme.colors.surfaceVariant,
                  true: theme.colors.primaryContainer,
                }
              />
            )}
            style={styles.listItem}
          />

          <List.Item
            title="Vibration"
            description="Vibrate for notifications"
            left={(props) => (
              <View style={styles.iconContainer}>
                <LocalIcon name="zap" size={24} color={theme.colors.primary} />
              </View>
            )}
            right={() => (
              <Switch
                value={vibrationEnabled}
                onValueChange={handleVibrationToggle}
                thumbColor={vibrationEnabled ? theme.colors.primary : theme.colors.outline}
                trackColor={{
                  false: theme.colors.surfaceVariant,
                  true: theme.colors.primaryContainer,
                }
              />
            )}
            style={styles.listItem}
          />

          <List.Item
            title="Quiet Hours"
            description={quietHours.enabled ? `${quietHours.start}`}  - ${quietHours.end}` : 'Disabled'}
            left={(props) => (
              <View style={styles.iconContainer}>
                <LocalIcon name="clock" size={24} color={theme.colors.primary} />
              </View>
            )}
            right={() => (
              <Switch
                value={quietHours.enabled}
                onValueChange={handleQuietHoursToggle}
                thumbColor={quietHours.enabled ? theme.colors.primary : theme.colors.outline}
                trackColor={{
                  false: theme.colors.surfaceVariant,
                  true: theme.colors.primaryContainer,
                }
              />
            )}
            style={styles.listItem}
          />
        </Surface>

        {/* Order Notifications */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]}]} elevation={1}>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Order Notifications
          </Text>

          {renderNotificationItem('newOrders', 'New Orders', 'When new orders are received', 'shopping-bag')}
          {renderNotificationItem('orderUpdates', 'Order Updates', 'When order status changes', 'refresh')}
          {renderNotificationItem('orderReminders', 'Order Reminders', 'Reminders for pending orders', 'bell')}
          {renderNotificationItem('orderDeadlines', 'Order Deadlines', 'When orders are due soon', 'clock')}
        </Surface>

        {/* Customer Notifications */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]}]} elevation={1}>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Customer Notifications
          </Text>

          {renderNotificationItem('newCustomers', 'New Customers', 'When new customers register', 'UserPlus')}
          {renderNotificationItem('customerMessages', 'Customer Messages', 'When customers send messages', 'email')}
          {renderNotificationItem('appointmentReminders', 'Appointment Reminders', 'Upcoming appointments', 'calendar')}
        </Surface>

        {/* Business Notifications */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]}]} elevation={1}>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Business Notifications
          </Text>

          {renderNotificationItem('lowStock', 'Low Stock Alerts', 'When inventory is running low', 'package')}
          {renderNotificationItem('dailyReports', 'Daily Reports', 'Daily business summary', 'ChartBar')}
          {renderNotificationItem('weeklyReports', 'Weekly Reports', 'Weekly business summary', 'ChartBar')}
          {renderNotificationItem('monthlyReports', 'Monthly Reports', 'Monthly business summary', 'ChartBar')}
        </Surface>

        {/* System Notifications */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]}]} elevation={1}>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            System Notifications
          </Text>

          {renderNotificationItem('systemUpdates', 'System Updates', 'App updates and maintenance', 'refresh')}
          {renderNotificationItem('securityAlerts', 'Security Alerts', 'Security-related notifications', 'shield')}
          {renderNotificationItem('backupReminders', 'Backup Reminders', 'Data backup reminders', 'database')}
        </Surface>

        {/* SMS & WhatsApp Settings */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]}]} elevation={1}>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            SMS & WhatsApp Notifications
          </Text>

          <List.Item
            title="SMS Notifications"
            description="Send SMS notifications to customers"
            left={(props) => (
              <View style={styles.iconContainer}>
                <LocalIcon name="text" size={24} color={theme.colors.primary} />
              </View>
            )}
            right={() => (
              <Switch
                value={notifications.smsEnabled || false}
                onValueChange={() => handleNotificationToggle('smsEnabled')}
                thumbColor={notifications.smsEnabled ? theme.colors.primary : theme.colors.outline}
                trackColor={{
                  false: theme.colors.surfaceVariant,
                  true: theme.colors.primaryContainer,
                }
              />
            )}
            style={styles.listItem}
          />

          <List.Item
            title="WhatsApp Notifications"
            description="Send WhatsApp messages to customers"
            left={(props) => (
              <View style={styles.iconContainer}>
                <LocalIcon name="phone" size={24} color={theme.colors.primary} />
              </View>
            )}
            right={() => (
              <Switch
                value={notifications.whatsappEnabled || false}
                onValueChange={() => handleNotificationToggle('whatsappEnabled')}
                thumbColor={notifications.whatsappEnabled ? theme.colors.primary : theme.colors.outline}
                trackColor={{
                  false: theme.colors.surfaceVariant,
                  true: theme.colors.primaryContainer,
                }
              />
            )}
            style={styles.listItem}
          />
        </Surface>

        {/* Marketing Notifications */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]}]} elevation={1}>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Marketing & Tips
          </Text>

          {renderNotificationItem('promotions', 'Promotions', 'Special offers and discounts', 'dollar-sign')}
          {renderNotificationItem('tips', 'Business Tips', 'Tips to grow your business', 'zap')}
          {renderNotificationItem('newsletters', 'Newsletters', 'Monthly newsletters', 'email')}
        </Surface>

        {/* Quick Actions */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]}]} elevation={1}>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Quick Actions
          </Text>

          <View style={styles.actionButtons}>
            <Button
              mode="outlined"
              onPress={() => {
                const allEnabled = Object.keys(notifications).reduce((acc, key) => {
                  acc[key] = true;
                  return acc;
                }, {});
                setNotifications(allEnabled);
                saveSettings({ notifications: allEnabled });
              }}
              style={styles.actionButton}>
              Enable All
            </Button>
            <Button
              mode="outlined"
              onPress={() => {
                const allDisabled = Object.keys(notifications).reduce((acc, key) => {
                  acc[key] = false;
                  return acc;
                }, {});
                setNotifications(allDisabled);
                saveSettings({ notifications: allDisabled });
              }}
              style={styles.actionButton}>
              Disable All
            </Button>
          </View>
        </Surface>
      </ScrollView>
    </View>
  );
};

export default NotificationSettingsScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: SPACING.lg,
  },
  section: {
    marginBottom: SPACING.lg,
    borderRadius: BORDER_RADIUS.lg,
    paddingVertical: SPACING.md,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: SPACING.sm,
    paddingHorizontal: SPACING.lg,
  },
  listItem: {
    paddingHorizontal: SPACING.lg,
  },
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    width: 40,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: SPACING.md,
    paddingHorizontal: SPACING.lg,
    paddingTop: SPACING.sm,
  },
  actionButton: {
    flex: 1,
  },
});