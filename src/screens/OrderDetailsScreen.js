import React, { useState, useEffect, useCallback } from 'react';
// FIXED: Removed extra semicolon from import
import {
  View,
  ScrollView,
  StyleSheet,
  Alert,
  Linking,
} from 'react-native';
import { Text, Button, Surface, Chip, Divider } from 'react-native-paper';
import { useTheme } from '../context/ThemeContext';
import { useData } from '../context/DataContext';
import CommonHeader from '../components/CommonHeader';
import LoadingSpinner from '../components/LoadingSpinner';
import InvoiceActions from '../components/InvoiceActions';
import { SPACING, BORDER_RADIUS } from '../theme/designTokens';

const OrderDetailsScreen = ({ navigation, route }) => {
  const { theme } = useTheme();
  const { updateOrderStatus, deleteOrder } = useData();
  const [loading, setLoading] = useState(false);
  const [order, setOrder] = useState(route?.params?.order);

  useEffect(() => {
    if (route?.params?.order) {
      setOrder(route.params.order);
    }
  }, [route?.params?.order]);

  const getStatusColor = (status) => {
    switch (status) {
      case 'Completed': return '#10b981';
      case 'In Progress': return '#3b82f6';
      case 'Ready': return '#8b5cf6';
      case 'Pending': return '#f59e0b';
      case 'Delivered': return '#059669';
      default: return '#6b7280';
    }
  };

  // FIXED: Corrected useCallback syntax and added dependencies
  const handleStatusUpdate = useCallback(async (newStatus) => {
    try {
      setLoading(true);
      await updateOrderStatus(order.id, newStatus);
      setOrder(prev => ({ ...prev, status: newStatus }));
      Alert.alert('Success', `Order status updated to ${newStatus}`);
    } catch (error) {
      console.error('Failed to update order status:', error);
      Alert.alert('Error', 'Failed to update order status');
    } finally {
      setLoading(false);
    }
  }, [order?.id, updateOrderStatus]);

  // FIXED: Corrected useCallback syntax and added dependencies
  const handleEdit = useCallback(() => {
    navigation.navigate('CreateOrder', { order });
  }, [navigation, order]);

  // FIXED: Corrected useCallback syntax and added dependencies
  const handleDelete = useCallback(() => {
    Alert.alert(
      'Delete Order',
      'Are you sure you want to delete this order? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              setLoading(true);
              await deleteOrder(order.id);
              Alert.alert('Success', 'Order deleted successfully', [
                { text: 'OK', onPress: () => navigation.goBack() }
              ]);
            } catch (error) {
              console.error('Failed to delete order:', error);
              Alert.alert('Error', 'Failed to delete order');
            } finally {
              setLoading(false);
            }
          }
        }
      ]
    );
  }, [deleteOrder, order?.id, navigation]);

  // FIXED: Corrected useCallback syntax and added dependencies
  const handleCallCustomer = useCallback(() => {
    if (order.customer_phone) {
      Linking.openURL(`tel:${order.customer_phone}`);
    }
  }, [order?.customer_phone]);

  // FIXED: Corrected useCallback syntax and added dependencies
  const handleViewCustomer = useCallback(() => {
    const customer = {
      id: order.customer_id,
      name: order.customer_name,
      phone: order.customer_phone,
      email: order.customer_email,
    };
    navigation.navigate('CustomerDetails', { customer });
  }, [navigation, order]);

  const formatCurrency = (amount) => `৳${amount?.toFixed(2) || '0.00'}`;
  
  const formatDate = (dateString) => {
    if (!dateString) return 'Not set';
    return new Date(dateString).toLocaleDateString('en-GB', { year: 'numeric', month: 'short', day: 'numeric' });
  };
  
  const calculateBalance = () => (order.amount || 0) - (order.advance_paid || 0);

  const orderStatuses = ['Pending', 'In Progress', 'Ready', 'Completed', 'Delivered'];

  if (loading) return <LoadingSpinner message="Updating order..." />;

  if (!order) {
    return (
      <View style={styles.container}>
        <CommonHeader title="Order Details" showBack />
        <View style={styles.errorContainer}><Text style={styles.errorText}>Order not found.</Text></View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <CommonHeader
        title={`Order #${order.id}`}
        showBackButton={true}
        onBackPress={() => navigation.goBack()}
        rightActions={[
          { icon: 'pencil', onPress: handleEdit, tooltip: 'Edit Order' },
          { icon: 'delete', onPress: handleDelete, tooltip: 'Delete Order' },
        }
      />

      <ScrollView contentContainerStyle={styles.scrollView} showsVerticalScrollIndicator={false}>
        <Surface style={styles.section} elevation={1}>
          <View style={styles.statusHeader}>
            <Text style={styles.sectionTitle}>Order Status</Text>
            <Chip style={{ backgroundColor: getStatusColor(order.status) + '20' }} textStyle={{ color: getStatusColor(order.status), fontWeight: '600' }}>
              {order.status}
            </Chip>
          </View>
          <Text style={styles.sectionSubtitle}>Update Status</Text>
          <View style={styles.statusChips}>
            {orderStatuses.map((status) => (
              <Chip key={status} mode={order.status === status ? 'flat' : 'outlined'} selected={order.status === status} onPress={() => handleStatusUpdate(status)} style={styles.statusOption} disabled={loading}>
                {status}
              </Chip>
            ))}
          </View>
        </Surface>

        <Surface style={styles.section} elevation={1}>
          <Text style={styles.sectionTitle}>Customer Information</Text>
          <View style={styles.customerInfo}>
            <View style={styles.customerDetails}>
              <Text style={styles.customerName}>{order.customer_name}</Text>
              {order.customer_phone && <Text style={styles.customerContact}>📞 {order.customer_phone}</Text>}
              {order.customer_email && <Text style={styles.customerContact}>✉️ {order.customer_email}</Text>}
            </View>
            <View style={styles.customerActions}>
              {order.customer_phone && <Button mode="outlined" onPress={handleCallCustomer} icon="phone" compact>Call</Button>}
              <Button mode="outlined" onPress={handleViewCustomer} icon="account" compact>View</Button>
            </View>
          </View>
        </Surface>

        <Surface style={styles.section} elevation={1}>
          <Text style={styles.sectionTitle}>Order Details</Text>
          <View style={styles.orderDetails}>
            <View style={styles.detailRow}><Text style={styles.detailLabel}>Garment Type:</Text><Text style={styles.detailValue}>{order.garment_type}</Text></View>
            <View style={styles.detailRow}><Text style={styles.detailLabel}>Quantity:</Text><Text style={styles.detailValue}>{order.quantity}</Text></View>
            <View style={styles.detailRow}><Text style={styles.detailLabel}>Due Date:</Text><Text style={styles.detailValue}>{formatDate(order.due_date)}</Text></View>
            <View style={styles.detailRow}><Text style={styles.detailLabel}>Created:</Text><Text style={styles.detailValue}>{formatDate(order.created_at)}</Text></View>
          </View>
        </Surface>

        <Surface style={styles.section} elevation={1}>
          <Text style={styles.sectionTitle}>Payment Information</Text>
          <View style={styles.paymentDetails}>
            <View style={styles.paymentRow}><Text style={styles.paymentLabel}>Total Amount:</Text><Text style={styles.paymentValue}>{formatCurrency(order.amount)}</Text></View>
            <View style={styles.paymentRow}><Text style={styles.paymentLabel}>Advance Paid:</Text><Text style={[styles.paymentValue, { color: theme.colors.primary }}>{formatCurrency(order.advance_paid)}</Text></View>
            <Divider style={styles.divider} />
            <View style={styles.paymentRow}>
              <Text style={[styles.paymentLabel, { fontWeight: '600' }}>Balance Due:</Text>
              <Text style={[styles.paymentValue, { color: calculateBalance() > 0 ? theme.colors.error : theme.colors.primary, fontWeight: '600' }}>
                {formatCurrency(calculateBalance())}
              </Text>
            </View>
          </View>
        </Surface>

        {order.notes && (
          <Surface style={styles.section} elevation={1}>
            <Text style={styles.sectionTitle}>Notes</Text>
            <Text style={styles.notesText}>{order.notes}</Text>
          </Surface>
        )}

        <Surface style={styles.section} elevation={1}>
          <Text style={styles.sectionTitle}>Invoice & Documents</Text>
          <InvoiceActions
            order={order}
            customer={{ id: order.customer_id, name: order.customer_name, phone: order.customer_phone, email: order.customer_email }}
          />
        </Surface>
      </ScrollView>
    </View>
  );
};

export default OrderDetailsScreen;

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#F5F5F5' },
  scrollView: { flex: 1, paddingTop: SPACING.sm },
  section: { marginHorizontal: SPACING.md, marginBottom: SPACING.md, padding: SPACING.lg, borderRadius: BORDER_RADIUS.lg, backgroundColor: '#FFFFFF' },
  sectionTitle: { fontSize: 18, fontWeight: '600', marginBottom: SPACING.md },
  sectionSubtitle: { fontSize: 14, marginBottom: SPACING.sm, color: '#666' },
  statusHeader: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: SPACING.md },
  statusChip: { borderRadius: BORDER_RADIUS.full },
  statusChips: { flexDirection: 'row', flexWrap: 'wrap', gap: SPACING.sm },
  statusOption: { marginRight: SPACING.xs, marginBottom: SPACING.xs },
  customerInfo: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' },
  customerDetails: { flex: 1 },
  customerName: { fontSize: 18, fontWeight: '600', marginBottom: SPACING.xs },
  customerContact: { fontSize: 14, marginBottom: SPACING.xs, color: '#444' },
  customerActions: { flexDirection: 'row', gap: SPACING.sm },
  orderDetails: { gap: SPACING.md },
  detailRow: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' },
  detailLabel: { fontSize: 14, color: '#666' },
  detailValue: { fontSize: 14, fontWeight: '500' },
  paymentDetails: { gap: SPACING.md },
  paymentRow: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' },
  paymentLabel: { fontSize: 16 },
  paymentValue: { fontSize: 16, fontWeight: '500' },
  divider: { marginVertical: SPACING.sm },
  notesText: { fontSize: 14, lineHeight: 20 },
  errorContainer: { flex: 1, justifyContent: 'center', alignItems: 'center' },
  errorText: { fontSize: 16, textAlign: 'center' },
});