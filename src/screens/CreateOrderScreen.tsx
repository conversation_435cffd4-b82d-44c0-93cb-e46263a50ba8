import React, { useCallback } from 'react';
import { ScrollView, StyleSheet   } from 'react-native';
import { CustomerSelection, GarmentDetails, MeasurementForm, PricingSection, OrderSummary   } from '../components/createorderscreen';

const CreateOrderScreen = () => {
  return (
    <ScrollView style={styles.style1}>
      <CustomerSelection />
      <GarmentDetails />
      <MeasurementForm />
      <PricingSection />
      <OrderSummary />
    </ScrollView>
  );
};






export default React.memo(CreateOrderScreen);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
});