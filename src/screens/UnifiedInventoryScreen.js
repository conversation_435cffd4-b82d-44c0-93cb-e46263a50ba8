import React, { useCallback } from 'react';
import { ScrollView, StyleSheet   } from 'react-native';
import { InventoryFilters, InventoryList, InventoryStats, AddInventoryModal   } from '../components/unifiedinventoryscreen';

const UnifiedInventoryScreen = () => {
  return (
    <ScrollView style={styles.style1}>
      <InventoryFilters />
      <InventoryList />
      <InventoryStats />
      <AddInventoryModal />
    </ScrollView>
  );
};






export default React.memo(UnifiedInventoryScreen);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
});