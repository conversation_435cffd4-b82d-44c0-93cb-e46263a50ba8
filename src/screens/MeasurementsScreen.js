/**
 * MeasurementsScreen - Manage customer measurements for tailoring
 */

import React, { useState, useMemo, useCallback } from 'react';
import { View, ScrollView, StyleSheet, Alert   } from 'react-native';
import { Text   } from 'react-native-paper';
import { FAB   } from 'react-native-paper';
import { Searchbar   } from 'react-native-paper';
import { Card   } from 'react-native-paper';
import { Button   } from 'react-native-paper';
import { Chip   } from 'react-native-paper';
import { useData   } from '../context/DataContext';
import { useTheme   } from '../context/ThemeContext';
import { SPACING   } from '../theme/designTokens';
import CommonHeader from '../components/CommonHeader';
import StatCardGroup from '../components/StatCardGroup';
import UniversalCard from '../components/UniversalCard';
import navigationService from '../services/NavigationService';

const MeasurementsScreen = () => {
  const { state, actions  } = useData();
  const { theme  } = useTheme();

  const measurements = state.measurements || [];
  const customers = state.customers || [];

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedType, setSelectedType] = useState('All');

  // Measurement types
  const measurementTypes = ['All', 'Shirt', 'Pant', 'Suit', 'Kurta', 'Sherwani'];

  // Filter measurements
  const filteredMeasurements = useMemo(() => {
    return measurements.filter(measurement => {
      const customer = customers.find(c => c.id === measurement.customerId);
      const customerName = customer ? customer.name : 'Unknown Customer';

      const matchesSearch = customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           measurement.garmentType?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           measurement.notes?.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesType = selectedType === 'All' || measurement.garmentType === selectedType;

      return matchesSearch && matchesType;
    });
  }, [measurements, customers, searchQuery, selectedType]);

  // Calculate statistics
  const stats = useMemo(() => {
    const totalMeasurements = measurements.length;
    const uniqueCustomers = new Set(measurements.map(m => m.customerId)).size;
    const recentMeasurements = measurements.filter(m => {
      const measurementDate = new Date(m.createdAt || m.date);
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);
      return measurementDate >= weekAgo;
    }).length;

    return { totalMeasurements,
      uniqueCustomers,
      recentMeasurements,
     };
  }, [measurements]);

  const handleDeleteMeasurement = useCallback((measurement) => {
    Alert.alert(
      'Delete Measurement',
      'Are you sure you want to delete this measurement?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await actions.deleteMeasurement(measurement.id);
            } catch (error) {
              console.error('Error deleting measurement:', error);
              Alert.alert('Error', 'Failed to delete measurement');
            }
          },
        },
      ]
    );
  }, [actions]);

  const handleEditMeasurement = useCallback((measurement) => {
    navigationService.navigate('AddMeasurement', {
      measurement,
      isEditing: true
    });
  }, []);

  const handleAddMeasurement = useCallback(() => {
    navigationService.navigate('AddMeasurement');
  }, []);

  const getCustomerName = (customerId) => {
    const customer = customers.find(c => c.id === customerId);
    return customer ? customer.name : 'Unknown Customer';
  };

  const formatMeasurements = (measurementData) => {
    if (!measurementData) return 'No measurements';

    const key = Object.keys(measurementData)[0];
    const value = measurementData[key];
    const unit = state.settings.measurementUnit || 'inches';

    if(typeof value === 'object') {
      const firstKey = Object.keys(value)[0];
      return `${firstKey}`} : ${value[firstKey] ${unit} ;`
    }

    return `${key}`} : ${value} ${unit} ;`
  };

  return (
    <View style={styles.container}>
      <CommonHeader
        title="Measurements"
        subtitle={`${filteredMeasurements.length}`}  records`}
        showSearch={false}
        onNotificationPress={() => console.log('Notifications pressed')}
        onProfilePress={() => navigationService.navigate('MyProfile')}
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Search and Filters */}
        <View style={styles.searchSection}>
          <Searchbar
            placeholder="Search measurements..."
            onChangeText={setSearchQuery}
            value={searchQuery}
            style={styles.searchbar}
            iconColor={theme.colors.onSurfaceVariant}
            placeholderTextColor={theme.colors.onSurfaceVariant}
          />

          {/* Type Filter */}
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.typeScroll}
            contentContainerStyle={styles.typeContainer}>
            {measurementTypes.map((type) => (
              <Chip
                key={type}
                selected={selectedType === type}
                onPress={() => setSelectedType(type)}
                style={styles.typeChip}
                textStyle={[
                  styles.typeChipText,
                  selectedType === type && { color: theme.colors.onPrimary }
                }>
                {type}
              </Chip>
            ))}
          </ScrollView>
        </View>

        {/* Stats Row */}
        <StatCardGroup
          cards={[
            {
              key: 'total',
              title: 'Total Measurements',
              value: stats.totalMeasurements.toString(),
              icon: 'ruler',
              iconColor: theme.colors.primary,
              elevation: 1,
            },
            {
              key: 'customers',
              title: 'Customers',
              value: stats.uniqueCustomers.toString(),
              icon: 'account-group',
              iconColor: theme.colors.secondary,
              elevation: 1,
            },
            {
              key: 'recent',
              title: 'This Week',
              value: stats.recentMeasurements.toString(),
              icon: 'calendar-week',
              iconColor: theme.colors.tertiary,
              elevation: 1,
            },
          }
          columns={3}
          showTitle={false}
          containerStyle={{ marginBottom: 16 }
        />

        {/* Measurements List */}
        {filteredMeasurements.length === 0 ? (
          <View style={styles.emptyState}>
            <Text variant="headlineSmall" style={styles.emptyTitle}>
              {searchQuery || selectedType !== 'All' ? 'No measurements found' : 'No measurements yet'}
            </Text>
            <Text variant="bodyMedium" style={styles.emptySubtitle}>
              {searchQuery || selectedType !== 'All'
                ? 'Try adjusting your search or filters'
                : 'Add your first customer measurement to get started'
              }
            </Text>
            {!searchQuery && selectedType === 'All' && (
              <Button
                mode="contained"
                onPress={handleAddMeasurement}
                style={styles.emptyButton}
                icon="plus"
              >
                Add Measurement
              </Button>
            )}
          </View>
        ) : (
          <View style={styles.measurementsList}>
            {filteredMeasurements.map((measurement) => (
              <Card key={measurement.id} style={styles.measurementCard}>
                <Card.Content>
                  <View style={styles.measurementHeader}>
                    <View style={styles.measurementInfo}>
                      <Text variant="titleMedium" style={styles.customerName}>
                        {getCustomerName(measurement.customerId)}
                      </Text>
                      <Text variant="bodyMedium" style={styles.garmentType}>
                        {measurement.garmentType}
                      </Text>
                    </View>
                    <Text variant="bodySmall" style={styles.measurementDate}>
                      {new Date(measurement.createdAt || measurement.date).toLocaleDateString()}
                    </Text>
                  </View>

                  <Text variant="bodySmall" style={styles.measurementDetails}>
                    {formatMeasurements(measurement.measurements)}
                  </Text>

                  {measurement.notes && (
                    <Text variant="bodySmall" style={styles.measurementNotes}>
                      Notes: {measurement.notes}
                    </Text>
                  )}
                </Card.Content>

                <Card.Actions>
                  <Button onPress={() => handleEditMeasurement(measurement)}>
                    Edit
                  </Button>
                  <Button
                    onPress={() => handleDeleteMeasurement(measurement)}
                    textColor={theme.colors.error}>
                    Delete
                  </Button>
                </Card.Actions>
              </Card>
            ))}
          </View>
        )}
      </ScrollView>

      {/* Add Measurement FAB */}
      <FAB
        icon="plus"
        style={styles.fab}
        onPress={handleAddMeasurement}
        label="Add Measurement"
      />
    </View>
  );
};






export default MeasurementsScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: SPACING.md,
  },
  searchSection: {
    marginBottom: SPACING.lg,
  },
  searchbar: {
    marginBottom: SPACING.md,
    elevation: 2,
  },
  typeScroll: {
    marginBottom: SPACING.md,
  },
  typeContainer: {
    paddingHorizontal: SPACING.xs,
  },
  typeChip: {
    marginRight: SPACING.sm,
  },
  typeChipText: {
    fontSize: 12,
  },
  measurementsList: {
    paddingBottom: 100, // Space for FAB
  },
  measurementCard: {
    marginBottom: SPACING.md,
    elevation: 2,
  },
  measurementHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: SPACING.sm,
  },
  measurementInfo: {
    flex: 1,
  },
  customerName: {
    fontWeight: '600',
    marginBottom: 2,
  },
  garmentType: {
    fontWeight: '500',
  },
  measurementDate: {
    fontSize: 12,
  },
  measurementDetails: {
    marginBottom: SPACING.xs,
  },
  measurementNotes: {
    fontStyle: 'italic',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: SPACING.xxxl,
  },
  emptyTitle: {
    fontWeight: '600',
    marginBottom: SPACING.sm,
    textAlign: 'center',
  },
  emptySubtitle: {
    textAlign: 'center',
    paddingHorizontal: SPACING.xl,
    marginBottom: SPACING.lg,
  },
  emptyButton: {
    marginTop: SPACING.md,
  },
  fab: {
    position: 'absolute',
    margin: SPACING.lg,
    right: 0,
    bottom: 0,
  },

});