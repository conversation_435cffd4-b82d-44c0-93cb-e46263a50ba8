import React from 'react';
import { <PERSON>, ScrollView, StyleSheet   } from 'react-native';
// FIXED: Reordered components for a more logical screen layout
import { ReportsHeader, ReportsFilters, SalesChart, RevenueChart, OrdersChart   } from '../components/reportsscreen';

const ReportsScreen = () => {
  return (
    // FIXED: Used the 'container' style instead of an empty one
    <View style={styles.container}>
      {/* The header is now at the top, as expected */}
      <ReportsHeader />
      
      <ScrollView>
        {/* Filters are below the header */}
        <ReportsFilters />
        
        {/* Charts are the main content */}
        <SalesChart />
        <RevenueChart />
        <OrdersChart />
      </ScrollView>
    </View>
  );
};

export default React.memo(ReportsScreen);

// FIXED: Corrected syntax errors in the stylesheet
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
});