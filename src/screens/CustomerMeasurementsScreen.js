/**
 * CustomerMeasurementsScreen - Tab-based measurement history
 */

import React, { useState, useEffect, useCallback } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Text, Surface, Card, Chip, Button, IconButton } from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTheme } from '../context/ThemeContext';
import { useData } from '../context/DataContext';
import CommonHeader from '../components/CommonHeader';
import { SPACING, BORDER_RADIUS } from '../theme/designTokens';
import navigationService from '../services/NavigationService';

import LocalIcon, { NavigationLocalIcon, ActionLocalIcon, BusinessLocalIcon, SettingsLocalIcon, StatusLocalIcon } from '../components/LocalIcon';

const CustomerMeasurementsScreen = ({ route }) => {
  const { theme } = useTheme();
  const { state } = useData();
  const insets = useSafeAreaInsets();
  const { customerId, customer } = route?.params || {};

  const [selectedTab, setSelectedTab] = useState('all');
  const [measurements, setMeasurements] = useState([]);

  // Get customer measurements
  useEffect(() => {
    const customerMeasurements = state.measurements.filter(m => m.customerId === customerId);
    setMeasurements(customerMeasurements);
  }, [state.measurements, customerId]);

  // Get unique garment types for tabs
  const garmentTypes = [...new Set(measurements.map(m => m.garmentType))];
  
  // Create tab buttons
  const tabButtons = [
    { value: 'all', label: 'All' },
    ...garmentTypes.map(type => ({ value: type, label: type }))
  ];

  // Filter measurements based on selected tab
  const filteredMeasurements = selectedTab === 'all' 
    ? measurements 
    : measurements.filter(m => m.garmentType === selectedTab);

  const handleEditMeasurement = useCallback((measurement) => {
    navigationService.navigate('AddMeasurement', {
      measurement: measurement,
      isEditing: true,
      customer: customer
    });
  }, []);

  const handleAddMeasurement = useCallback(() => {
    navigationService.navigate('AddMeasurement', {
      customer: customer
    });
  }, []);

  const renderMeasurementCard = (measurement) => {
    const measurementDate = new Date(measurement.createdAt || measurement.measurementDate);
    
    return (
      <Card 
        key={measurement.id} 
        style={styles.style5}
        elevation={1}>
        <Card.Content>
          <View style={styles.style4}>
            <View style={styles.style4}>
              <Text style={[styles.style7, styles.titleMedium}>
                {measurement.garmentType}
              </Text>
              <Text style={[styles.style10, styles.bodySmall}>
                {measurementDate.toLocaleDateString()} • {measurementDate.toLocaleTimeString()}
              </Text>
              {measurement.notes && (
                <Text style={[styles.style9, styles.bodySmall}>
                  {measurement.notes}
                </Text>
              )}
            </View>
            <IconButton
              icon="pencil"
              size={20}
              iconColor={theme.colors.primary}
              onPress={() => handleEditMeasurement(measurement)}
            />
          </View>

          {/* Measurement Details */}
          <View style={styles.style4}>
            {Object.entries(measurement.measurements || {}).map(([key, value]) => (
              <View key={key} style={styles.style4}>
                <Text style={[styles.style8, styles.bodyMedium}>
                  {key.replace(/([A-Z])/g, ' $1').trim()}:
                </Text>
                <Text style={[styles.style7, styles.bodyMedium}>
                  {value}"
                </Text>
              </View>
            ))}
          </View>
        </Card.Content>
      </Card>
    );
  };

  return (
    <View style={styles.style6}>
      <CommonHeader
        title="Measurements"
        subtitle={customer?.name || 'Customer Measurements'}
        showSearch={false}
        onNotificationPress={() => console.log('Notifications pressed')}
        onProfilePress={() => navigationService.goBack()}
      />

      <View style={styles.style4}>
        {/* Tab Navigation */}
        {tabButtons.length > 1 && (
          <Surface style={styles.style5}>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <View style={styles.style4}>
                {tabButtons.map((tab) => (
                  <Chip
                    key={tab.value}
                    mode={selectedTab === tab.value ? 'filled' : 'outlined'}
                    onPress={() => setSelectedTab(tab.value)}
                    style={styles.style4}>
                    {tab.label}
                  </Chip>
                ))}
              </View>
            </ScrollView>
          </Surface>
        )}

        {/* Measurements List */}
        <ScrollView 
          style={styles.style4}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: insets.bottom + 100 }}
        >
          {filteredMeasurements.length > 0 ? (
            <View style={styles.style4}>
              {filteredMeasurements
                .sort((a, b) => new Date(b.createdAt || b.measurementDate) - new Date(a.createdAt || a.measurementDate))
                .map(renderMeasurementCard)}
            </View>
          ) : (
            <View style={styles.style4}>
              <LocalIcon name="ruler" size={64} color={theme.colors.onSurfaceVariant} />
              <Text style={[styles.style3, styles.titleMedium}>
                No measurements found
              </Text>
              <Text style={[styles.style2, styles.bodyMedium}>
                {selectedTab === 'all' 
                  ? 'No measurements recorded for this customer yet.'
                  : `No ${selectedTab} measurements recorded yet.`
                }
              </Text>
              <Button
                mode="contained"
                onPress={handleAddMeasurement}
                style={styles.style1}
                icon="plus"
              >
                Add First Measurement
              </Button>
            </View>
          )}
        </ScrollView>


      </View>
    </View>
  );
};






export default CustomerMeasurementsScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: SPACING.md,
  },
  tabContainer: {
    marginBottom: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    paddingVertical: SPACING.sm,
  },
  tabButtons: {
    flexDirection: 'row',
    paddingHorizontal: SPACING.md,
    gap: SPACING.sm,
  },
  tabChip: {
    marginRight: SPACING.xs,
  },
  measurementsList: {
    flex: 1,
  },
  measurementsContainer: {
    gap: SPACING.md,
  },
  measurementCard: {
    borderRadius: BORDER_RADIUS.md,
    marginBottom: SPACING.sm,
  },
  measurementHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: SPACING.md,
  },
  measurementInfo: {
    flex: 1,
  },
  measurementDetails: {
    gap: SPACING.xs,
  },
  measurementRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 2,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: SPACING.xl * 2,
  },
  style1: {
    marginTop: 16,
  },
  style2: {
    marginTop: 8,
    textAlign: 'center',
  },
  style3: {
    marginTop: 16,
  },
  style4: {
    flex: 1,
  },
  style5: {
    marginBottom: 16,
  },
  style6: {
    flex: 1,
  },
  style7: {
    fontWeight: '600',
  },
  style8: {
    textTransform: 'capitalize',
  },
  style9: {
    marginTop: 4,
  },
  style10: {
    marginTop: 4,
  },
  titleMedium: {
    fontSize: 16,
    fontWeight: '500',
    lineHeight: 24,
  },
  bodyMedium: {
    fontSize: 14,
    fontWeight: '400',
    lineHeight: 20,
  },
  bodySmall: {
    fontSize: 12,
    fontWeight: '400',
    lineHeight: 16,
  },
});