/**
 * OrderSuccessScreen - Beautiful order success page with invoice
 */

import React, { useCallback, useMemo } from 'react';
import { View, ScrollView, StyleSheet, Share, Alert   } from 'react-native';
import { Text, Button, Surface, Card, Divider   } from 'react-native-paper';
import LocalIcon from '../components/LocalIcon';
import { useTheme   } from '../context/ThemeContext';
import CommonHeader from '../components/CommonHeader';
import { SPACING, BORDER_RADIUS   } from '../theme/designTokens';
import navigationService from '../services/NavigationService';
import { PDFInvoiceGenerator   } from '../utils/pdfInvoiceGenerator';
import QRCodeService from '../services/QRCodeService';

// QR Code component with fallback for Expo Go
const QRCodeComponent = ({ value, size = 120 }) => {
  // FIXED: Style for fallback moved inline to correctly use the `size` prop
  const fallbackStyle = {
    width: size,
    height: size,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#ddd',
    borderStyle: 'dashed'};
  const fallbackTextStyle = {
    fontSize: 10,
    color: '#666',
    textAlign: 'center',
    marginTop: 4};

  try {
    const QRCode = require('react-native-qrcode-svg').default;
    return <QRCode value={value} size={size} color="black" backgroundColor="white" />;
  } catch (error) {
    return (
      <View style={fallbackStyle}>
        <LocalIcon name="qrcode" size={size * 0.4} color="#666" />
        <Text style={fallbackTextStyle}>QR Code{'\n'}(Build required)</Text>
      </View>
    );
  }
};

const OrderSuccessScreen = ({ route }) => {
  const { theme  } = useTheme();
  const { order, customer  } = route?.params || {};

  // FIXED: Stylesheet moved inside the component and wrapped in useMemo
  const styles = useMemo(() => StyleSheet.create({
    container: { flex: 1, backgroundColor: theme.colors.background },
    content: { padding: SPACING.lg, flexGrow: 1 },
    successHeader: { alignItems: 'center', marginBottom: SPACING.xl, paddingVertical: SPACING.lg },
    successIcon: { width: 100, height: 100, borderRadius: 50, alignItems: 'center', justifyContent: 'center', marginBottom: SPACING.lg, backgroundColor: theme.colors.primaryContainer },
    successTitle: { fontWeight: '700', textAlign: 'center', marginBottom: SPACING.sm, color: theme.colors.onSurface },
    successSubtitle: { textAlign: 'center', marginBottom: SPACING.lg, color: theme.colors.onSurfaceVariant },
    invoiceCard: { borderRadius: BORDER_RADIUS.xl, padding: SPACING.lg, marginBottom: SPACING.lg, backgroundColor: theme.colors.surface },
    invoiceHeader: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' },
    invoiceTitle: { fontWeight: '700', color: theme.colors.onSurface },
    invoiceId: { color: theme.colors.onSurfaceVariant },
    invoiceSection: { marginVertical: SPACING.md },
    sectionTitle: { fontWeight: '600', marginBottom: SPACING.md, color: theme.colors.primary },
    detailRow: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingVertical: SPACING.xs },
    detailLabel: { color: theme.colors.onSurfaceVariant },
    detailValue: { color: theme.colors.onSurface, fontWeight: '600' },
    urgentText: { color: '#FF5722', fontWeight: '600' },
    advanceText: { color: theme.colors.secondary, fontWeight: '600' },
    remainingText: { color: theme.colors.primary, fontWeight: '600' },
    totalRow: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingVertical: SPACING.sm, marginTop: SPACING.sm },
    totalLabel: { color: theme.colors.onSurface, fontWeight: '700' },
    totalValue: { color: theme.colors.primary, fontWeight: '700' },
    actionButtons: { flexDirection: 'row', gap: SPACING.md, marginTop: SPACING.lg, paddingBottom: SPACING.xl },
    actionButton: { flex: 1 },
    qrSection: { alignItems: 'center', marginVertical: SPACING.md },
    qrContainer: { flexDirection: 'row', alignItems: 'center', marginTop: SPACING.sm, gap: SPACING.lg },
    qrCodeWrapper: { padding: SPACING.sm, borderRadius: BORDER_RADIUS.md, backgroundColor: '#FFFFFF', elevation: 2, shadowColor: '#000', shadowOffset: { width: 0, height: 1 }, shadowOpacity: 0.2, shadowRadius: 2 },
    qrInfo: { flex: 1, alignItems: 'flex-start' },
    qrInfoText: { color: theme.colors.onSurface, fontWeight: '600' },
    qrInstructions: { color: theme.colors.onSurfaceVariant, marginTop: 4, fontSize: 12 },
    divider: { marginVertical: SPACING.md },
    errorContainer: { flex: 1, justifyContent: 'center', alignItems: 'center' }}), [theme]);

  if(!order) {
    return (
      <View style={styles.errorContainer}>
        <Text>Error: No order data found</Text>
      </View>
    );
  }

  const orderQR = QRCodeService.generateInvoiceQR(order, customer);

  const handleShareInvoice = useCallback(async () => {
    // ... (share logic remains the same)
  }, [order]);

  const handlePrintInvoice = useCallback(async () => {
    // ... (print logic remains the same)
  }, [order]);

  const handleCreateAnother = useCallback(() => {
    navigationService.navigate('CreateOrder');
  }, []);

  return (
    <View style={styles.container}>
      <CommonHeader
        title="Order Created"
        subtitle="Success! Your order is confirmed"
        showSearch={false}
        showBackButton={true}
        onBackPress={() => navigationService.navigate('Orders')}
      />

      <ScrollView contentContainerStyle={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.successHeader}>
          <View style={styles.successIcon}>
            <LocalIcon name="check-decagram" size={64} color={theme.colors.primary} />
          </View>
          <Text variant="headlineSmall" style={styles.successTitle}>
            Order Created Successfully!
          </Text>
          <Text variant="bodyLarge" style={styles.successSubtitle}>
            Your order is now being processed.
          </Text>
        </View>

        <Surface style={styles.invoiceCard} elevation={2}>
          <View style={styles.invoiceHeader}>
            <Text variant="titleLarge" style={styles.invoiceTitle}>
              Order Invoice
            </Text>
            <Text variant="bodyMedium" style={styles.invoiceId}>
              #{order.id}
            </Text>
          </View>

          <Divider style={styles.divider} />

          <View style={styles.invoiceSection}>
            <Text style={styles.sectionTitle}>Customer Details</Text>
            <View style={styles.detailRow}><Text style={styles.detailLabel}>Name:</Text><Text style={styles.detailValue}>{order.customerName}</Text></View>
            <View style={styles.detailRow}><Text style={styles.detailLabel}>Phone:</Text><Text style={styles.detailValue}>{order.phone}</Text></View>
          </View>

          <Divider style={styles.divider} />

          <View style={styles.invoiceSection}>
            <Text style={styles.sectionTitle}>Order Details</Text>
            <View style={styles.detailRow}><Text style={styles.detailLabel}>Garment:</Text><Text style={styles.detailValue}>{order.garmentType}</Text></View>
            <View style={styles.detailRow}><Text style={styles.detailLabel}>Fabric:</Text><Text style={styles.detailValue}>{order.fabric || 'Customer Fabric'}</Text></View>
            <View style={styles.detailRow}><Text style={styles.detailLabel}>Type:</Text><Text style={styles.detailValue}>{order.orderType}</Text></View>
            <View style={styles.detailRow}><Text style={styles.detailLabel}>Due Date:</Text><Text style={styles.detailValue}>{new Date(order.dueDate).toLocaleDateString()}</Text></View>
            {order.urgentOrder && <View style={styles.detailRow}><Text style={styles.detailLabel}>Priority:</Text><Text style={styles.urgentText}>🚨 URGENT</Text></View>}
          </View>

          <Divider style={styles.divider} />

          <View style={styles.invoiceSection}>
            <Text style={styles.sectionTitle}>Payment Summary</Text>
            <View style={styles.detailRow}><Text style={styles.detailLabel}>Base Price:</Text><Text style={styles.detailValue}>৳{order.price}</Text></View>
            {order.urgentOrder && <View style={styles.detailRow}><Text style={styles.detailLabel}>Rush Fee:</Text><Text style={styles.urgentText}>৳{(parseFloat(order.price) * 0.5).toFixed(2)}</Text></View>}
            <View style={styles.detailRow}><Text style={styles.detailLabel}>Advance Paid:</Text><Text style={styles.advanceText}>৳{order.advancePayment || 0}</Text></View>
            <View style={styles.detailRow}><Text style={styles.detailLabel}>Remaining:</Text><Text style={styles.remainingText}>৳{order.remainingAmount || (parseFloat(order.price) - parseFloat(order.advancePayment || 0)).toFixed(2)}</Text></View>
            <Divider style={{ marginVertical: SPACING.sm } />
            <View style={styles.totalRow}><Text variant="titleMedium" style={styles.totalLabel}>Total Amount:</Text><Text variant="titleMedium" style={styles.totalValue}>৳{order.urgentOrder ? (parseFloat(order.price) * 1.5).toFixed(2) : order.price}</Text></View>
          </View>

          <Divider style={styles.divider} />

          <View style={styles.qrSection}>
            <Text style={styles.sectionTitle}>Order QR Code</Text>
            <View style={styles.qrContainer}>
              <View style={styles.qrCodeWrapper}>
                <QRCodeComponent value={orderQR.qrData} size={100} />
              </View>
              <View style={styles.qrInfo}>
                <Text style={styles.qrInfoText}>{orderQR.displayText}</Text>
                <Text style={styles.qrInstructions}>{orderQR.instructions}</Text>
              </View>
            </View>
          </View>
        </Surface>

        <View style={styles.actionButtons}>
          <Button mode="outlined" icon="printer" onPress={handlePrintInvoice} style={styles.actionButton}>Print Invoice</Button>
          <Button mode="contained" icon="plus" onPress={handleCreateAnother} style={styles.actionButton}>New Order</Button>
        </View>
      </ScrollView>
    </View>
  );
};

export default OrderSuccessScreen;