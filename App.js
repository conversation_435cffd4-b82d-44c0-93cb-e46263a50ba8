import React, { useState, useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import { NavigationContainer } from '@react-navigation/native';
import { PaperProvider } from 'react-native-paper';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Linking } from 'react-native';

// Enable screens for react-navigation
import { enableScreens } from 'react-native-screens';
enableScreens();

// Import navigation and contexts
import AppNavigator from './src/navigation/AppNavigator';
import { DataProvider } from './src/context/DataContext';
import { ThemeProvider, useTheme } from './src/context/ThemeContext';
import { FinancialProvider } from './src/context/FinancialContext';
import { AuthProvider } from './src/context/AuthContext';
import { SubscriptionProvider } from './src/context/SubscriptionContext';
import { AlertProvider } from './src/services/AlertService';
import { NavigationProvider } from './src/context/NavigationContext';

import ErrorBoundary from './src/components/ErrorBoundary';
import { navigationRef } from './src/services/NavigationService';
import { LinkingConfiguration, registerDeepLinkHandlers } from './src/config/deepLinking';
import Toaster from './src/components/Toaster';
import SplashScreen from './src/components/SplashScreen';

// Main App Component that uses theme
const AppContent = () => {
  const { theme, isDarkMode } = useTheme();

  return (
    <PaperProvider theme={theme}>
      <AlertProvider>
        <NavigationProvider>
          <NavigationContainer 
            ref={navigationRef}
            linking={LinkingConfiguration}
            onReady={() => {
              console.log('Navigation container ready');
              registerDeepLinkHandlers();
            }}
            onStateChange={(state) => {
              console.log('Navigation state changed:', state);
            }}
          >
            <AppNavigator />
            <Toaster />
            <StatusBar style={isDarkMode ? "light" : "dark"} />
          </NavigationContainer>
        </NavigationProvider>
      </AlertProvider>
    </PaperProvider>
  );
};

// Simple Test App Component (with auth for debugging)
const SimpleTestApp = () => {
  const [showSplash, setShowSplash] = useState(true);

  const handleSplashFinish = () => {
    setShowSplash(false);
  };

  if (showSplash) {
    return (
      <SafeAreaProvider>
        <ThemeProvider>
          <SplashScreen onFinish={handleSplashFinish} />
        </ThemeProvider>
      </SafeAreaProvider>
    );
  }

  return (
    <SafeAreaProvider>
      <ThemeProvider>
        <AuthProvider>
          <SubscriptionProvider>
            <DataProvider>
              <FinancialProvider>
                <AppContent />
              </FinancialProvider>
            </DataProvider>
          </SubscriptionProvider>
        </AuthProvider>
      </ThemeProvider>
    </SafeAreaProvider>
  );
};

// Root App Component with providers and error boundary
export default function App() {
  const handleAppError = () => {
    console.log('App error handled');
  };

  const handleAppReload = () => {
    console.log('App reload requested');
  };

  // For debugging: Use simple test app to bypass auth
  const USE_SIMPLE_TEST = true;

  if (USE_SIMPLE_TEST) {
    return (
      <ErrorBoundary
        onRetry={handleAppError}
        onReload={handleAppReload}
      >
        <SimpleTestApp />
      </ErrorBoundary>
    );
  }

  return (
    <ErrorBoundary
      onRetry={handleAppError}
      onReload={handleAppReload}
    >
      <SafeAreaProvider>
        <ThemeProvider>
          <AuthProvider>
            <SubscriptionProvider>
              <DataProvider>
                <FinancialProvider>
                  <AppContent />
                </FinancialProvider>
              </DataProvider>
            </SubscriptionProvider>
          </AuthProvider>
        </ThemeProvider>
      </SafeAreaProvider>
    </ErrorBoundary>
  );
}



