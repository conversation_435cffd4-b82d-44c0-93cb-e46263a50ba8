{"expo": {"name": "Tailora", "slug": "tailora", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "iconUrl": "./assets/icon.png", "userInterfaceStyle": "automatic", "scheme": "tailora", "privacy": "public", "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#1a237e", "hideExpoLoadingScreen": true}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.tailora.app", "infoPlist": {"NSCameraUsageDescription": "This app uses the camera to take photos of garments and fabrics.", "NSPhotoLibraryUsageDescription": "This app needs access to your photo library to select garment and fabric images.", "NSMicrophoneUsageDescription": "This app does not use the microphone.", "CFBundleURLTypes": [{"CFBundleURLName": "tailora", "CFBundleURLSchemes": ["tailora"]}]}}, "android": {"icon": "./assets/icon.png", "adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.tailora.app", "permissions": ["CAMERA", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE", "READ_MEDIA_IMAGES"], "intentFilters": [{"action": "VIEW", "data": [{"scheme": "tailora"}], "category": ["BROWSABLE", "DEFAULT"]}]}, "web": {"favicon": "./assets/favicon.png", "bundler": "metro", "build": {"babel": {"include": ["@expo/metro-runtime"]}}, "staticWebDir": "public", "crossOriginIsolated": false}, "owner": "samsull", "plugins": ["expo-font", ["expo-sqlite", {"enableFTS": true, "useSQLCipher": false}]]}}