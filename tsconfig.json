{"compilerOptions": {"strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "jsx": "react-native", "lib": ["dom", "esnext"], "moduleResolution": "bundler", "noEmit": true, "skipLibCheck": true, "resolveJsonModule": true, "allowJs": true, "types": ["jest", "node"]}, "extends": "expo/tsconfig.base", "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx"], "exclude": ["node_modules", "android/build", "ios/build", "dist", "build"]}