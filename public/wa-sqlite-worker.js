/**
 * Simple Web Worker for SQLite simulation
 * This provides a basic in-memory storage for web platform
 */

// Simple in-memory database simulation
let tables = new Map();
let autoIncrement = new Map();

self.onmessage = async function(e) {
  const { type, payload, id } = e.data;
  
  try {
    switch (type) {
      case 'init':
        // Initialize in-memory storage
        tables.clear();
        autoIncrement.clear();
        self.postMessage({ type: 'init', success: true, id });
        break;
        
      case 'exec':
        const result = await execSQL(payload.sql, payload.params);
        self.postMessage({ type: 'exec', result, id });
        break;
        
      case 'close':
        // Clear in-memory storage
        tables.clear();
        autoIncrement.clear();
        self.postMessage({ type: 'close', success: true, id });
        break;
        
      default:
        throw new Error(`Unknown message type: ${type}`);
    }
  } catch (error) {
    self.postMessage({ 
      type: 'error', 
      error: error.message, 
      stack: error.stack,
      id 
    });
  }
};

function execSQL(sql, params = []) {
  const sqlUpper = sql.trim().toUpperCase();
  
  // Handle CREATE TABLE
  if (sqlUpper.startsWith('CREATE TABLE')) {
    const match = sql.match(/CREATE TABLE (?:IF NOT EXISTS )?([\w_]+)/i);
    if (match) {
      const tableName = match[1];
      if (!tables.has(tableName)) {
        tables.set(tableName, []);
        autoIncrement.set(tableName, 1);
      }
    }
    return { rows: [], rowsAffected: 0, insertId: 0 };
  }
  
  // Handle CREATE INDEX
  if (sqlUpper.startsWith('CREATE INDEX') || sqlUpper.startsWith('CREATE UNIQUE INDEX')) {
    return { rows: [], rowsAffected: 0, insertId: 0 };
  }
  
  // Handle INSERT
  if (sqlUpper.startsWith('INSERT INTO')) {
    const match = sql.match(/INSERT INTO ([\w_]+)/i);
    if (match) {
      const tableName = match[1];
      if (!tables.has(tableName)) {
        tables.set(tableName, []);
        autoIncrement.set(tableName, 1);
      }
      
      const table = tables.get(tableName);
      const id = autoIncrement.get(tableName);
      
      // Create a simple row object
      const row = { id };
      if (params && params.length > 0) {
        params.forEach((param, index) => {
          row[`col_${index}`] = param;
        });
      }
      
      table.push(row);
      autoIncrement.set(tableName, id + 1);
      
      return { rows: [], rowsAffected: 1, insertId: id };
    }
  }
  
  // Handle SELECT
  if (sqlUpper.startsWith('SELECT')) {
    const match = sql.match(/FROM ([\w_]+)/i);
    if (match) {
      const tableName = match[1];
      const table = tables.get(tableName) || [];
      return { rows: table, rowsAffected: 0, insertId: 0 };
    }
    return { rows: [], rowsAffected: 0, insertId: 0 };
  }
  
  // Handle UPDATE
  if (sqlUpper.startsWith('UPDATE')) {
    const match = sql.match(/UPDATE ([\w_]+)/i);
    if (match) {
      const tableName = match[1];
      const table = tables.get(tableName) || [];
      return { rows: [], rowsAffected: table.length, insertId: 0 };
    }
  }
  
  // Handle DELETE
  if (sqlUpper.startsWith('DELETE FROM')) {
    const match = sql.match(/DELETE FROM ([\w_]+)/i);
    if (match) {
      const tableName = match[1];
      const table = tables.get(tableName) || [];
      const count = table.length;
      tables.set(tableName, []);
      return { rows: [], rowsAffected: count, insertId: 0 };
    }
  }
  
  // Handle PRAGMA and other statements
  if (sqlUpper.startsWith('PRAGMA') || sqlUpper.startsWith('DROP')) {
    return { rows: [], rowsAffected: 0, insertId: 0 };
  }
  
  // Default response
  return { rows: [], rowsAffected: 0, insertId: 0 };
}

// Handle worker errors
self.onerror = function(error) {
  console.error('Worker error:', error);
  self.postMessage({ 
    type: 'error', 
    error: error.message || 'Unknown worker error'
  });
};

// Handle unhandled promise rejections
self.onunhandledrejection = function(event) {
  console.error('Unhandled promise rejection in worker:', event.reason);
  self.postMessage({ 
    type: 'error', 
    error: event.reason?.message || 'Unhandled promise rejection'
  });
};