#!/usr/bin/env node

/**
 * System-wide Icon Migration Script for Tailora App
 * Automatically replaces all FeatherIcon and MaterialCommunityIcons with Phosphor Icons
 */

const fs = require('fs');
const path = require('path');

class SystemWideIconMigrator {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
    this.srcPath = path.join(this.projectRoot, 'src');
    this.results = {
      filesProcessed: 0,
      iconsReplaced: 0,
      errors: [],
      summary: []
    };
  }

  // Get all JS/TS files in src directory
  getAllJSFiles(dir) {
    const files = [];
    const items = fs.readdirSync(dir);

    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory()) {
        files.push(...this.getAllJSFiles(fullPath));
      } else if (/\.(js|jsx|ts|tsx)$/.test(item)) {
        files.push(fullPath);
      }
    }

    return files;
  }

  // Icon replacement patterns
  getReplacementPatterns() {
    return [
      // FeatherIcon replacements
      {
        pattern: /import\s+FeatherIcon\s+from\s+['"][^'"]*FeatherIcon['"];?/g,
        replacement: "import FeatherIcon from '../components/FeatherIcon';\nimport UnifiedIcon, { NavigationUnifiedIcon, ActionUnifiedIcon, BusinessUnifiedIcon, SettingsUnifiedIcon, StatusUnifiedIcon } from '../components/UnifiedIcon';"
      },
      
      // MaterialCommunityIcons import replacements
      {
        pattern: /import\s+Icon\s+from\s+['"]react-native-vector-icons\/MaterialCommunityIcons['"];?/g,
        replacement: "import Icon from 'react-native-vector-icons/MaterialCommunityIcons';\nimport UnifiedIcon, { BusinessUnifiedIcon, ActionUnifiedIcon } from '../components/UnifiedIcon';"
      },
      
      // FeatherIcon component replacements
      {
        pattern: /<FeatherIcon\s+name=["']([^"']+)["']\s+size=\{?(\d+)\}?\s+color=\{([^}]+)\}\s*\/>/g,
        replacement: '<NavigationUnifiedIcon name="$1" size={$2} color={$3} />'
      },
      
      // MaterialCommunityIcons component replacements
      {
        pattern: /<Icon\s+name=["']([^"']+)["']\s+size=\{?(\d+)\}?\s+color=\{([^}]+)\}\s*\/>/g,
        replacement: '<BusinessUnifiedIcon name="$1" size={$2} color={$3} />'
      },
      
      // Button icon prop replacements
      {
        pattern: /icon=["']([^"']+)["']/g,
        replacement: 'icon={() => <ActionUnifiedIcon name="$1" size="medium" colorContext="primary" />}'
      },
      
      // @expo/vector-icons replacements
      {
        pattern: /import\s*\{\s*([^}]*)\s*\}\s*from\s*['"]@expo\/vector-icons['"];?/g,
        replacement: "import { $1 } from '@expo/vector-icons';\nimport UnifiedIcon from '../components/UnifiedIcon';"
      }
    ];
  }

  // Process a single file
  processFile(filePath) {
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let modified = false;
      let replacements = 0;

      const patterns = this.getReplacementPatterns();
      
      for (const { pattern, replacement } of patterns) {
        const matches = content.match(pattern);
        if (matches) {
          content = content.replace(pattern, replacement);
          modified = true;
          replacements += matches.length;
        }
      }

      // Additional specific replacements for common patterns
      const specificReplacements = [
        // Navigation icons
        { from: 'FeatherIcon name="home"', to: 'NavigationUnifiedIcon name="home"' },
        { from: 'FeatherIcon name="search"', to: 'NavigationUnifiedIcon name="search"' },
        { from: 'FeatherIcon name="bell"', to: 'NavigationUnifiedIcon name="bell"' },
        { from: 'FeatherIcon name="user"', to: 'NavigationUnifiedIcon name="user"' },
        { from: 'FeatherIcon name="settings"', to: 'NavigationUnifiedIcon name="settings"' },
        
        // Action icons
        { from: 'FeatherIcon name="plus"', to: 'ActionUnifiedIcon name="plus"' },
        { from: 'FeatherIcon name="edit"', to: 'ActionUnifiedIcon name="edit"' },
        { from: 'FeatherIcon name="trash"', to: 'ActionUnifiedIcon name="trash"' },
        { from: 'FeatherIcon name="save"', to: 'ActionUnifiedIcon name="save"' },
        { from: 'FeatherIcon name="download"', to: 'ActionUnifiedIcon name="download"' },
        
        // Business icons
        { from: 'Icon name="account"', to: 'BusinessUnifiedIcon name="account"' },
        { from: 'Icon name="hanger"', to: 'BusinessUnifiedIcon name="hanger"' },
        { from: 'Icon name="calendar"', to: 'BusinessUnifiedIcon name="calendar"' },
        
        // Settings icons
        { from: 'FeatherIcon name="shield"', to: 'SettingsUnifiedIcon name="shield"' },
        { from: 'FeatherIcon name="help-circle"', to: 'SettingsUnifiedIcon name="help-circle"' },
        { from: 'FeatherIcon name="info"', to: 'SettingsUnifiedIcon name="info"' },
      ];

      for (const { from, to } of specificReplacements) {
        if (content.includes(from)) {
          content = content.replace(new RegExp(from, 'g'), to);
          modified = true;
          replacements++;
        }
      }

      if (modified) {
        fs.writeFileSync(filePath, content, 'utf8');
        this.results.iconsReplaced += replacements;
        
        const relativePath = path.relative(this.projectRoot, filePath);
        this.results.summary.push({
          file: relativePath,
          replacements
        });
      }

      this.results.filesProcessed++;
      
    } catch (error) {
      this.results.errors.push({
        file: path.relative(this.projectRoot, filePath),
        error: error.message
      });
    }
  }

  // Main migration function
  async migrate() {
    console.log('🚀 Starting system-wide icon migration...\n');
    
    const files = this.getAllJSFiles(this.srcPath);
    console.log(`📁 Found ${files.length} files to process\n`);

    for (const file of files) {
      this.processFile(file);
    }

    this.generateReport();
  }

  // Generate migration report
  generateReport() {
    console.log('\n🎉 Migration Complete!\n');
    console.log('📊 Migration Summary:');
    console.log(`   Files Processed: ${this.results.filesProcessed}`);
    console.log(`   Icons Replaced: ${this.results.iconsReplaced}`);
    console.log(`   Errors: ${this.results.errors.length}\n`);

    if (this.results.summary.length > 0) {
      console.log('📝 Files Modified:');
      this.results.summary.forEach(({ file, replacements }) => {
        console.log(`   ✅ ${file} (${replacements} replacements)`);
      });
      console.log('');
    }

    if (this.results.errors.length > 0) {
      console.log('❌ Errors:');
      this.results.errors.forEach(({ file, error }) => {
        console.log(`   ❌ ${file}: ${error}`);
      });
      console.log('');
    }

    console.log('🎯 Next Steps:');
    console.log('   1. Test the app to ensure all icons are working');
    console.log('   2. Check for any remaining manual replacements needed');
    console.log('   3. Update imports if necessary');
    console.log('   4. Run the app and verify icon rendering\n');

    // Save detailed report
    const reportPath = path.join(this.projectRoot, 'migration-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(this.results, null, 2));
    console.log(`📄 Detailed report saved to: ${reportPath}\n`);
  }
}

// Run migration if called directly
if (require.main === module) {
  const migrator = new SystemWideIconMigrator();
  migrator.migrate().catch(console.error);
}

module.exports = SystemWideIconMigrator;
