#!/bin/bash

# Image Optimization Script for Tailora React Native App
# This script optimizes images for better performance

echo "🖼️ Starting image optimization..."

# Check if ImageMagick is installed
if ! command -v convert &> /dev/null; then
    echo "❌ ImageMagick not found. Please install it first:"
    echo "   macOS: brew install imagemagick"
    echo "   Ubuntu: sudo apt-get install imagemagick"
    exit 1
fi

# Create backup directory
BACKUP_DIR="./assets/images/backup"
mkdir -p "$BACKUP_DIR"

# Function to optimize PNG images
optimize_png() {
    local file="$1"
    local backup="$BACKUP_DIR/$(basename "$file")"
    
    echo "📸 Optimizing PNG: $(basename "$file")"
    
    # Create backup
    cp "$file" "$backup"
    
    # Optimize PNG
    convert "$file" -strip -interlace Plane -quality 85 "$file"
}

# Function to optimize JPEG images
optimize_jpeg() {
    local file="$1"
    local backup="$BACKUP_DIR/$(basename "$file")"
    
    echo "📸 Optimizing JPEG: $(basename "$file")"
    
    # Create backup
    cp "$file" "$backup"
    
    # Optimize JPEG
    convert "$file" -strip -interlace Plane -quality 80 "$file"
}

# Function to convert to WebP
convert_to_webp() {
    local file="$1"
    local webp_file="${file%.*}.webp"
    
    echo "🔄 Converting to WebP: $(basename "$file")"
    
    # Convert to WebP
    convert "$file" -quality 80 "$webp_file"
    
    echo "✅ Created: $(basename "$webp_file")"
}

# Find and optimize images
find ./assets -name "*.png" -type f | while read file; do
    optimize_png "$file"
done

find ./assets -name "*.jpg" -o -name "*.jpeg" -type f | while read file; do
    optimize_jpeg "$file"
done

# Convert large images to WebP
echo "
🔄 Converting large images to WebP..."
find ./assets -name "*.png" -o -name "*.jpg" -o -name "*.jpeg" -type f | while read file; do
    # Check file size (greater than 100KB)
    if [ $(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null) -gt 102400 ]; then
        convert_to_webp "$file"
    fi
done

echo "
✨ Image optimization completed!"
echo "📁 Backups saved in: $BACKUP_DIR"
echo "
💡 Next steps:"
echo "   1. Test your app to ensure images display correctly"
echo "   2. Update image references to use WebP where appropriate"
echo "   3. Remove backup files once you're satisfied with results"
