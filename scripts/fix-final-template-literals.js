#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Function to recursively find all JS/JSX/TS/TSX files
function findFiles(dir, extensions = ['.js', '.jsx', '.ts', '.tsx']) {
  let results = [];
  const list = fs.readdirSync(dir);
  
  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat && stat.isDirectory()) {
      if (!['node_modules', 'build', 'dist', '.git', 'android', 'ios', 'scripts'].includes(file)) {
        results = results.concat(findFiles(filePath, extensions));
      }
    } else {
      const ext = path.extname(file);
      if (extensions.includes(ext)) {
        results.push(filePath);
      }
    }
  });
  
  return results;
}

// Final comprehensive fix for template literals and JSX
function fixFinalTemplateAndJSX(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Fix the most common template literal patterns
    const templateFixes = [
      // Fix template literals with extra brackets/characters
      { pattern: /`([^`]*\$\{[^}]+\}[^`]*)`\}\s*([^`]*)`/g, replacement: '`$1 $2`' },
      { pattern: /`([^`]*\$\{[^}]+\}[^`]*)`\}\s*([^`\s]+)/g, replacement: '`$1 $2`' },
      { pattern: /`([^`]*\$\{[^}]+\}[^`]*) ,/g, replacement: '`$1`,' },
      { pattern: /`([^`]*\$\{[^}]+\}[^`]*) ;/g, replacement: '`$1`;' },
      { pattern: /`([^`]*\$\{[^}]+\}[^`]*) \)/g, replacement: '`$1`)' },
      { pattern: /`([^`]*\$\{[^}]+\}[^`]*) \]/g, replacement: '`$1`]' },
      { pattern: /`([^`]*\$\{[^}]+\}[^`]*) \}/g, replacement: '`$1`}' },
      
      // Fix specific template literal endings
      { pattern: /\$\{([^}]+)\} ,`/g, replacement: '${$1}`,' },
      { pattern: /\$\{([^}]+)\} ;`/g, replacement: '${$1}`;' },
      { pattern: /\$\{([^}]+)\} \)`/g, replacement: '${$1}`)' },
      { pattern: /\$\{([^}]+)\} \]`/g, replacement: '${$1}`]' },
      { pattern: /\$\{([^}]+)\} \}`/g, replacement: '${$1}`}' }
    ];
    
    templateFixes.forEach(fix => {
      if (fix.pattern.test(content)) {
        content = content.replace(fix.pattern, fix.replacement);
        modified = true;
      }
    });
    
    // Fix JSX style prop bracket issues
    const jsxFixes = [
      // Fix JSX style arrays with missing closing brackets
      { pattern: /style=\{\[([^}]+), \{ ([^}]+) \}\>/g, replacement: 'style={[$1, { $2 }]}>' },
      { pattern: /style=\{\[([^}]+), \{ ([^}]+) \}\]/g, replacement: 'style={[$1, { $2 }]}' },
      { pattern: /style=\{\[([^}]+), \{ ([^}]+) \}\}\]/g, replacement: 'style={[$1, { $2 }]}' },
      
      // Fix JSX props with extra brackets
      { pattern: /(\w+)=\{([^}]+)\}\}/g, replacement: '$1={$2}' },
      { pattern: /(\w+)=\{([^}]+)\}\]/g, replacement: '$1={$2}' },
      { pattern: /(\w+)=\{([^}]+)\}>/g, replacement: '$1={$2}>' },
      
      // Fix JSX source props
      { pattern: /source=\{\{ uri: ([^}]+) \} style=/g, replacement: 'source={{ uri: $1 }} style=' },
      { pattern: /source=\{\{ uri: ([^}]+) \}/g, replacement: 'source={{ uri: $1 }}' }
    ];
    
    jsxFixes.forEach(fix => {
      if (fix.pattern.test(content)) {
        content = content.replace(fix.pattern, fix.replacement);
        modified = true;
      }
    });
    
    // Fix object literal issues
    const objectFixes = [
      // Fix return statements with extra spaces
      { pattern: /return \{ ([^}]+)   \}/g, replacement: 'return { $1 }' },
      { pattern: /return \{ ([^}]+), \}/g, replacement: 'return { $1 }' },
      
      // Fix object property syntax
      { pattern: /\{ ([^:]+): ([^,}]+), \}/g, replacement: '{ $1: $2 }' },
      { pattern: /\{ ([^:]+): ([^,}]+)   \}/g, replacement: '{ $1: $2 }' }
    ];
    
    objectFixes.forEach(fix => {
      if (fix.pattern.test(content)) {
        content = content.replace(fix.pattern, fix.replacement);
        modified = true;
      }
    });
    
    // Fix console and logger statements
    const logFixes = [
      { pattern: /Logger\.error\(`([^`]*): `, ([^)]+)\);/g, replacement: "Logger.error(`$1:`, $2);" },
      { pattern: /Logger\.info\(`([^`]*) \)\;/g, replacement: "Logger.info(`$1`);" },
      { pattern: /console\.error\('([^']*): ', ([^)]+)\);/g, replacement: "console.error('$1:', $2);" },
      { pattern: /console\.log\('([^']*): ', ([^)]+)\);/g, replacement: "console.log('$1:', $2);" }
    ];
    
    logFixes.forEach(fix => {
      if (fix.pattern.test(content)) {
        content = content.replace(fix.pattern, fix.replacement);
        modified = true;
      }
    });
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Main function
function main() {
  const srcDir = path.join(process.cwd(), 'src');
  
  if (!fs.existsSync(srcDir)) {
    console.error('src directory not found');
    process.exit(1);
  }
  
  console.log('Finding all files for final template literal and JSX fixes...');
  const files = findFiles(srcDir);
  
  console.log(`Found ${files.length} files to check`);
  
  let fixedCount = 0;
  files.forEach(file => {
    if (fixFinalTemplateAndJSX(file)) {
      fixedCount++;
    }
  });
  
  console.log(`\nFixed final template literal and JSX issues in ${fixedCount} files`);
  
  if (fixedCount > 0) {
    console.log('\nRunning type check to verify final improvements...');
    const { execSync } = require('child_process');
    try {
      execSync('npm run type-check 2>&1 | head -30', { stdio: 'inherit' });
    } catch (error) {
      console.log('Final type check completed.');
    }
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixFinalTemplateAndJSX, findFiles };
