#!/usr/bin/env node

/**
 * Extract Used Icons Script
 * Scans the codebase for all icon usage and creates local SVG files
 * This allows us to remove heavy icon libraries and use only what we need
 */

const fs = require('fs');
const path = require('path');
const https = require('https');

class IconExtractor {
  constructor() {
    this.projectRoot = process.cwd();
    this.iconsDir = path.join(this.projectRoot, 'src', 'assets', 'icons');
    this.usedIcons = new Set();
    this.iconSources = {
      phosphor: 'https://raw.githubusercontent.com/phosphor-icons/core/main/assets',
      feather: 'https://raw.githubusercontent.com/feathericons/feather/master/icons'
    };
  }

  // Scan codebase for icon usage
  scanForIcons() {
    console.log('🔍 Scanning codebase for icon usage...');
    
    const sourceDir = path.join(this.projectRoot, 'src');
    const files = this.getAllFiles(sourceDir, ['.js', '.jsx', '.ts', '.tsx']);
    
    for (const file of files) {
      this.scanFile(file);
    }
    
    console.log(`📊 Found ${this.usedIcons.size} unique icons`);
    return Array.from(this.usedIcons);
  }

  // Get all files recursively
  getAllFiles(dir, extensions) {
    const files = [];
    
    const scan = (currentDir) => {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          scan(fullPath);
        } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
          files.push(fullPath);
        }
      }
    };
    
    scan(dir);
    return files;
  }

  // Scan individual file for icon usage
  scanFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const relativePath = path.relative(this.projectRoot, filePath);
      
      // Icon usage patterns
      const patterns = [
        // Direct icon names in props
        /name=["']([A-Z][a-zA-Z]+)["']/g,
        /icon=["']([A-Z][a-zA-Z]+)["']/g,
        
        // Feather icon names
        /name=["']([a-z-]+)["']/g,
        /icon=["']([a-z-]+)["']/g,
        
        // Icon mappings in constants
        /['"]([A-Z][a-zA-Z]+)['"]:/g,
        /:\s*['"]([A-Z][a-zA-Z]+)['"],?/g,
      ];
      
      for (const pattern of patterns) {
        let match;
        while ((match = pattern.exec(content)) !== null) {
          const iconName = match[1];
          if (iconName && iconName.length > 1) {
            this.usedIcons.add(iconName);
          }
        }
      }
      
    } catch (error) {
      console.warn(`⚠️  Could not scan ${filePath}:`, error.message);
    }
  }

  // Create icons directory
  createIconsDirectory() {
    if (!fs.existsSync(this.iconsDir)) {
      fs.mkdirSync(this.iconsDir, { recursive: true });
      console.log(`📁 Created icons directory: ${this.iconsDir}`);
    }
  }

  // Download SVG from URL
  async downloadSVG(url) {
    return new Promise((resolve, reject) => {
      https.get(url, (response) => {
        if (response.statusCode !== 200) {
          reject(new Error(`HTTP ${response.statusCode}`));
          return;
        }
        
        let data = '';
        response.on('data', chunk => data += chunk);
        response.on('end', () => resolve(data));
      }).on('error', reject);
    });
  }

  // Extract Phosphor icons
  async extractPhosphorIcons(iconNames) {
    console.log('📦 Extracting Phosphor icons...');
    
    const phosphorIcons = iconNames.filter(name => 
      /^[A-Z][a-zA-Z]*$/.test(name) && name.length > 1
    );
    
    for (const iconName of phosphorIcons) {
      try {
        const url = `${this.iconSources.phosphor}/regular/${iconName.toLowerCase()}.svg`;
        const svg = await this.downloadSVG(url);
        
        const filePath = path.join(this.iconsDir, `${iconName}.svg`);
        fs.writeFileSync(filePath, svg);
        console.log(`✅ Downloaded: ${iconName}.svg`);
        
        // Small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (error) {
        console.warn(`⚠️  Could not download ${iconName}:`, error.message);
      }
    }
  }

  // Extract Feather icons
  async extractFeatherIcons(iconNames) {
    console.log('🪶 Extracting Feather icons...');
    
    const featherIcons = iconNames.filter(name => 
      /^[a-z-]+$/.test(name) && name.includes('-') || ['home', 'user', 'bell', 'info', 'x'].includes(name)
    );
    
    for (const iconName of featherIcons) {
      try {
        const url = `${this.iconSources.feather}/${iconName}.svg`;
        const svg = await this.downloadSVG(url);
        
        const filePath = path.join(this.iconsDir, `${iconName}.svg`);
        fs.writeFileSync(filePath, svg);
        console.log(`✅ Downloaded: ${iconName}.svg`);
        
        // Small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (error) {
        console.warn(`⚠️  Could not download ${iconName}:`, error.message);
      }
    }
  }

  // Generate icon component
  generateIconComponent() {
    console.log('🔧 Generating LocalIcon component...');
    
    const svgFiles = fs.readdirSync(this.iconsDir).filter(file => file.endsWith('.svg'));
    
    const iconImports = svgFiles.map(file => {
      const iconName = path.basename(file, '.svg');
      const componentName = iconName.charAt(0).toUpperCase() + iconName.slice(1).replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());
      return `import ${componentName}Svg from '../assets/icons/${file}';`;
    }).join('\n');
    
    const iconMap = svgFiles.map(file => {
      const iconName = path.basename(file, '.svg');
      const componentName = iconName.charAt(0).toUpperCase() + iconName.slice(1).replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());
      return `  '${iconName}': ${componentName}Svg,`;
    }).join('\n');
    
    const componentCode = `/**
 * LocalIcon - Ultra-lightweight local SVG icon component
 * Uses only the icons we actually need, dramatically reducing bundle size
 * Generated automatically from extracted SVGs
 */

import React from 'react';
import { SvgXml } from 'react-native-svg';
import { useTheme } from '../context/ThemeContext';

${iconImports}

// Icon map - only includes icons we actually use
const ICON_MAP = {
${iconMap}
};

// Icon sizes
const ICON_SIZES = {
  small: 16,
  medium: 20,
  large: 24,
  extraLarge: 28,
  huge: 32,
  jumbo: 48,
};

const LocalIcon = ({
  name,
  size = 'large',
  color,
  style,
  ...props
}) => {
  const { theme } = useTheme();
  
  // Determine icon size
  const iconSize = typeof size === 'number' ? size : ICON_SIZES[size] || ICON_SIZES.large;
  
  // Determine icon color
  const iconColor = color || theme.colors.onSurface;
  
  // Get SVG content
  const svgContent = ICON_MAP[name];
  
  if (!svgContent) {
    console.warn(\`LocalIcon: Icon "\${name}" not found\`);
    return null;
  }
  
  // Modify SVG to use our color
  const colorizedSvg = svgContent.replace(/fill="[^"]*"/g, \`fill="\${iconColor}"\`)
                                .replace(/stroke="[^"]*"/g, \`stroke="\${iconColor}"\`);
  
  return (
    <SvgXml
      xml={colorizedSvg}
      width={iconSize}
      height={iconSize}
      style={style}
      {...props}
    />
  );
};

export default LocalIcon;
`;

    const componentPath = path.join(this.projectRoot, 'src', 'components', 'LocalIcon.js');
    fs.writeFileSync(componentPath, componentCode);
    console.log(`✅ Generated LocalIcon component: ${componentPath}`);
  }

  // Main extraction process
  async extract() {
    console.log('🚀 Starting icon extraction process...');
    
    // Scan for used icons
    const usedIcons = this.scanForIcons();
    
    // Create icons directory
    this.createIconsDirectory();
    
    // Extract icons
    await this.extractPhosphorIcons(usedIcons);
    await this.extractFeatherIcons(usedIcons);
    
    // Generate component
    this.generateIconComponent();
    
    console.log('🎉 Icon extraction completed!');
    console.log(`📊 Total icons extracted: ${fs.readdirSync(this.iconsDir).filter(f => f.endsWith('.svg')).length}`);
    console.log('💡 Next steps:');
    console.log('   1. Install react-native-svg if not already installed');
    console.log('   2. Replace icon library imports with LocalIcon');
    console.log('   3. Remove heavy icon libraries from package.json');
  }
}

// Run the extraction
if (require.main === module) {
  const extractor = new IconExtractor();
  extractor.extract().catch(console.error);
}

module.exports = IconExtractor;
