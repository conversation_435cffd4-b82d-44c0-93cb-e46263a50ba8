#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Function to recursively find all JS/JSX/TS/TSX files
function findFiles(dir, extensions = ['.js', '.jsx', '.ts', '.tsx']) {
  let results = [];
  const list = fs.readdirSync(dir);
  
  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat && stat.isDirectory()) {
      if (!['node_modules', 'build', 'dist', '.git', 'android', 'ios'].includes(file)) {
        results = results.concat(findFiles(filePath, extensions));
      }
    } else {
      const ext = path.extname(file);
      if (extensions.includes(ext)) {
        results.push(filePath);
      }
    }
  });
  
  return results;
}

// Function to fix all remaining comprehensive errors
function fixFinalComprehensive(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Fix template literal syntax errors - most critical
    // Pattern: `${variable}`text` -> `${variable} text`
    const templateLiteralRegex = /`([^`]*\$\{[^}]+\}[^`]*)`([^`]+)`/g;
    if (templateLiteralRegex.test(content)) {
      content = content.replace(templateLiteralRegex, '`$1 $2`');
      modified = true;
    }
    
    // Fix malformed template literals with extra backticks
    // Pattern: `${var}`text -> `${var} text`
    const malformedTemplateRegex = /`([^`]*\$\{[^}]+\})`([^`\s]+)/g;
    if (malformedTemplateRegex.test(content)) {
      content = content.replace(malformedTemplateRegex, '`$1 $2`');
      modified = true;
    }
    
    // Fix JSX closing bracket errors
    // Pattern: style={styles.something}} -> style={styles.something}
    const jsxClosingRegex = /(\w+)=\{([^}]+)\}\}/g;
    if (jsxClosingRegex.test(content)) {
      content = content.replace(jsxClosingRegex, '$1={$2}');
      modified = true;
    }
    
    // Fix JSX style array bracket errors
    // Pattern: style={styles.something}]} -> style={styles.something}
    const jsxStyleArrayRegex = /style=\{([^}]+)\}\]/g;
    if (jsxStyleArrayRegex.test(content)) {
      content = content.replace(jsxStyleArrayRegex, 'style={$1}');
      modified = true;
    }
    
    // Fix JSX prop bracket errors
    // Pattern: prop={value}> -> prop={value}>
    const jsxPropRegex = /(\w+)=\{([^}]+)\}\s*>/g;
    if (jsxPropRegex.test(content)) {
      content = content.replace(jsxPropRegex, '$1={$2}>');
      modified = true;
    }
    
    // Fix StyleSheet object syntax errors
    // Pattern: { property: value, -> property: value,
    const styleSheetRegex = /(\w+):\s*\{\s*\{\s*([^}]+)\s*,/g;
    if (styleSheetRegex.test(content)) {
      content = content.replace(styleSheetRegex, '$1: {\n    $2,');
      modified = true;
    }
    
    // Fix array closing bracket errors
    // Pattern: array: [item1, item2}; -> array: [item1, item2];
    const arrayClosingRegex = /(\w+:\s*\[[^\]]*)\};/g;
    if (arrayClosingRegex.test(content)) {
      content = content.replace(arrayClosingRegex, '$1];');
      modified = true;
    }
    
    // Fix object property syntax in StyleSheet
    // Pattern: property: { -> property: {
    const objectPropertyRegex = /(\s+)(\w+):\s*\{\s*\{/g;
    if (objectPropertyRegex.test(content)) {
      content = content.replace(objectPropertyRegex, '$1$2: {');
      modified = true;
    }
    
    // Fix function parameter syntax
    // Pattern: function(param) { -> function(param) {
    const functionParamRegex = /(\w+)\s*\(\s*([^)]*)\s*\)\s*\{/g;
    if (functionParamRegex.test(content)) {
      content = content.replace(functionParamRegex, '$1($2) {');
      modified = true;
    }
    
    // Fix async function syntax
    // Pattern: async function(param) { -> async function(param) {
    const asyncFunctionRegex = /async\s+(\w+)\s*\(\s*([^)]*)\s*\)\s*\{/g;
    if (asyncFunctionRegex.test(content)) {
      content = content.replace(asyncFunctionRegex, 'async $1($2) {');
      modified = true;
    }
    
    // Fix try-catch syntax
    // Pattern: } catch(error) { -> } catch (error) {
    const tryCatchRegex = /\}\s*catch\s*\(\s*(\w+)\s*\)\s*\{/g;
    if (tryCatchRegex.test(content)) {
      content = content.replace(tryCatchRegex, '} catch ($1) {');
      modified = true;
    }
    
    // Fix object destructuring syntax
    // Pattern: const { prop } = obj; -> const { prop } = obj;
    const destructuringRegex = /const\s*\{\s*([^}]+)\s*\}\s*=\s*([^;]+);/g;
    if (destructuringRegex.test(content)) {
      content = content.replace(destructuringRegex, 'const { $1 } = $2;');
      modified = true;
    }
    
    // Fix import statement syntax
    // Pattern: import { item } from 'module'; -> import { item } from 'module';
    const importRegex = /import\s*\{\s*([^}]+)\s*\}\s*from\s*['"]([^'"]+)['"];/g;
    if (importRegex.test(content)) {
      content = content.replace(importRegex, "import { $1 } from '$2';");
      modified = true;
    }
    
    // Fix export statement syntax
    // Pattern: export { item }; -> export { item };
    const exportRegex = /export\s*\{\s*([^}]+)\s*\};/g;
    if (exportRegex.test(content)) {
      content = content.replace(exportRegex, 'export { $1 };');
      modified = true;
    }
    
    // Fix return statement syntax
    // Pattern: return { prop: value }; -> return { prop: value };
    const returnRegex = /return\s*\{\s*([^}]+)\s*\};/g;
    if (returnRegex.test(content)) {
      content = content.replace(returnRegex, 'return { $1 };');
      modified = true;
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`Fixed comprehensive errors in: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Main function
function main() {
  const srcDir = path.join(process.cwd(), 'src');
  
  if (!fs.existsSync(srcDir)) {
    console.error('src directory not found');
    process.exit(1);
  }
  
  console.log('Finding files for final comprehensive fix...');
  const files = findFiles(srcDir);
  
  console.log(`Found ${files.length} files to check`);
  
  let fixedCount = 0;
  files.forEach(file => {
    if (fixFinalComprehensive(file)) {
      fixedCount++;
    }
  });
  
  console.log(`\nFixed comprehensive errors in ${fixedCount} files`);
  
  if (fixedCount > 0) {
    console.log('\nRunning final comprehensive type check...');
    const { execSync } = require('child_process');
    try {
      execSync('npm run type-check 2>&1 | head -30', { stdio: 'inherit' });
    } catch (error) {
      console.log('Final comprehensive check completed.');
    }
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixFinalComprehensive, findFiles };
