#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Critical files with highest error counts
const CRITICAL_FILES = [
  'src/components/content/ProductDetailsContent.js',
  'src/components/content/AnalyticsContent.js', 
  'src/services/PrintService.js',
  'src/services/MigrationService.js',
  'src/navigation/AppNavigator.tsx',
  'src/components/charts/AdvancedTailoringCharts.js',
  'src/components/charts/NativeCharts.js',
  'src/services/storageService.js',
  'src/services/ValidationService.js',
  'src/utils/pdfInvoiceGenerator.js',
  'src/utils/deepLinkUtils.ts',
  'src/utils/FormValidation.js',
  'src/utils/numberUtils.js',
  'src/utils/validation.js',
  'src/utils/common/formattingUtils.js'
];

// Comprehensive fix patterns for critical syntax errors
function fixCriticalSyntaxErrors(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 1. Fix JSX style prop extra brackets (most common issue)
    const jsxStyleFixes = [
      // Fix triple closing brackets in JSX props
      { pattern: /style=\{([^}]+)\}\}\}>/g, replacement: 'style={$1}}>' },
      { pattern: /style=\{([^}]+)\}\}\}/g, replacement: 'style={$1}}' },
      { pattern: /\{ color: ([^}]+) \}\}\}>/g, replacement: '{ color: $1 }}>' },
      { pattern: /\{ color: ([^}]+) \}\}\}/g, replacement: '{ color: $1 }}' },
      
      // Fix JSX props with extra closing brackets
      { pattern: /(\w+)=\{([^}]+)\}\}>/g, replacement: '$1={$2}>' },
      { pattern: /(\w+)=\{([^}]+)\}\}\]/g, replacement: '$1={$2}]' },
      { pattern: /(\w+)=\{([^}]+)\}\} /g, replacement: '$1={$2} ' },
      
      // Fix source props specifically
      { pattern: /source=\{\{ uri: ([^}]+) \}\}/g, replacement: 'source={{ uri: $1 }}' },
      { pattern: /source=\{\{ uri: ([^}]+) \}\}\}/g, replacement: 'source={{ uri: $1 }}' }
    ];
    
    jsxStyleFixes.forEach(fix => {
      if (fix.pattern.test(content)) {
        content = content.replace(fix.pattern, fix.replacement);
        modified = true;
      }
    });
    
    // 2. Fix template literal issues (second most common)
    const templateLiteralFixes = [
      // Fix unterminated template literals with extra backticks
      { pattern: /`([^`]*\$\{[^}]+\}[^`]*)`;\s*`/g, replacement: '`$1`;' },
      { pattern: /`([^`]*\$\{[^}]+\}[^`]*)`\s*`/g, replacement: '`$1`' },
      { pattern: /Logger\.debug\(`([^`]*): \$\{([^}]+)\}\`\);\s*`/g, replacement: 'Logger.debug(`$1: ${$2}`);' },
      { pattern: /Logger\.error\(`([^`]*): \$\{([^}]+)\}\`, ([^)]+)\);\s*`/g, replacement: 'Logger.error(`$1: ${$2}`, $3);' },
      { pattern: /Logger\.info\(`([^`]*): \$\{([^}]+)\}\`\);\s*`/g, replacement: 'Logger.info(`$1: ${$2}`);' },
      
      // Fix template literals with malformed endings
      { pattern: /return `([^`]*\$\{[^}]+\}[^`]*)`;\s*`/g, replacement: 'return `$1`;' },
      { pattern: /`([^`]*\$\{[^}]+\}[^`]*) %`;\s*`/g, replacement: '`$1 %`;' },
      { pattern: /`([^`]*\$\{[^}]+\}[^`]*)`\s*;/g, replacement: '`$1`;' }
    ];
    
    templateLiteralFixes.forEach(fix => {
      if (fix.pattern.test(content)) {
        content = content.replace(fix.pattern, fix.replacement);
        modified = true;
      }
    });
    
    // 3. Fix object and array syntax issues
    const objectArrayFixes = [
      // Fix object literal issues
      { pattern: /\{ ([^:]+): ([^,}]+), \}/g, replacement: '{ $1: $2 }' },
      { pattern: /\{ ([^:]+): ([^,}]+)   \}/g, replacement: '{ $1: $2 }' },
      { pattern: /return \{ ([^}]+)   \}/g, replacement: 'return { $1 }' },
      
      // Fix array syntax
      { pattern: /\[([^}]+)\}\]/g, replacement: '[$1]' },
      { pattern: /\[([^}]+), \]/g, replacement: '[$1]' }
    ];
    
    objectArrayFixes.forEach(fix => {
      if (fix.pattern.test(content)) {
        content = content.replace(fix.pattern, fix.replacement);
        modified = true;
      }
    });
    
    // 4. Fix method parameter and function syntax
    const functionFixes = [
      // Fix method parameters
      { pattern: /\(([^)]+), \)/g, replacement: '($1)' },
      { pattern: /\(\s*,\s*([^)]+)\)/g, replacement: '($1)' },
      
      // Fix arrow function syntax
      { pattern: /=> \(\s*\)/g, replacement: '=> ()' },
      { pattern: /=> \{([^}]+)   \}/g, replacement: '=> { $1 }' }
    ];
    
    functionFixes.forEach(fix => {
      if (fix.pattern.test(content)) {
        content = content.replace(fix.pattern, fix.replacement);
        modified = true;
      }
    });
    
    // 5. Fix specific patterns found in error analysis
    const specificFixes = [
      // Fix HTML-like syntax in JS files
      { pattern: /<\/style>/g, replacement: '' },
      { pattern: /<\/head>/g, replacement: '' },
      { pattern: /<\/html>/g, replacement: '' },
      { pattern: /<br>/g, replacement: '' },
      
      // Fix export/import issues
      { pattern: /export const ([A-Z_]+) = \{/g, replacement: 'export const $1 = {' },
      { pattern: /\]\s*;\s*$/gm, replacement: '];' }
    ];
    
    specificFixes.forEach(fix => {
      if (fix.pattern.test(content)) {
        content = content.replace(fix.pattern, fix.replacement);
        modified = true;
      }
    });
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Main function
function main() {
  console.log('Starting critical file syntax fixes...');
  
  let fixedCount = 0;
  let totalFiles = 0;
  
  CRITICAL_FILES.forEach(file => {
    const filePath = path.join(process.cwd(), file);
    
    if (fs.existsSync(filePath)) {
      totalFiles++;
      console.log(`Processing: ${file}`);
      
      if (fixCriticalSyntaxErrors(filePath)) {
        fixedCount++;
        console.log(`  ✓ Fixed syntax errors in ${file}`);
      } else {
        console.log(`  - No changes needed in ${file}`);
      }
    } else {
      console.log(`  ⚠ File not found: ${file}`);
    }
  });
  
  console.log(`\nCompleted: Fixed ${fixedCount} out of ${totalFiles} critical files`);
  
  if (fixedCount > 0) {
    console.log('\nRunning type check to verify improvements...');
    const { execSync } = require('child_process');
    try {
      execSync('npm run type-check 2>&1 | head -20', { stdio: 'inherit' });
    } catch (error) {
      console.log('Type check completed - errors may remain but should be reduced.');
    }
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixCriticalSyntaxErrors, CRITICAL_FILES };
