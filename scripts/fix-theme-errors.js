#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Function to recursively find all JS/JSX/TS/TSX files
function findFiles(dir, extensions = ['.js', '.jsx', '.ts', '.tsx']) {
  let results = [];
  const list = fs.readdirSync(dir);
  
  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat && stat.isDirectory()) {
      // Skip node_modules and other build directories
      if (!['node_modules', 'build', 'dist', '.git', 'android', 'ios'].includes(file)) {
        results = results.concat(findFiles(filePath, extensions));
      }
    } else {
      const ext = path.extname(file);
      if (extensions.includes(ext)) {
        results.push(filePath);
      }
    }
  });
  
  return results;
}

// Function to fix theme-related style array errors
function fixThemeErrors(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Fix missing closing brackets in style arrays with theme colors
    // Pattern: { color: theme.colors.something }> -> { color: theme.colors.something }>
    const themeStyleRegex = /(\{\s*[^}]*theme\.colors\.[^}]*\s*\})\s*>/g;
    if (themeStyleRegex.test(content)) {
      content = content.replace(themeStyleRegex, '$1}>');
      modified = true;
    }
    
    // Fix missing closing brackets in style arrays
    // Pattern: [styles.something, { ... }> -> [styles.something, { ... }>
    const styleArrayRegex = /(\[styles\.[^,]+,\s*\{[^}]*\})\s*>/g;
    if (styleArrayRegex.test(content)) {
      content = content.replace(styleArrayRegex, '$1}>');
      modified = true;
    }
    
    // Fix destructuring assignment errors
    // Pattern: = [] = -> = [] } =
    const destructuringRegex = /=\s*\[\]\s*=/g;
    if (destructuringRegex.test(content)) {
      content = content.replace(destructuringRegex, '= [] } =');
      modified = true;
    }
    
    // Fix array destructuring with missing closing bracket
    // Pattern: datasets = [] = data -> datasets = [] } = data
    const arrayDestructuringRegex = /(\w+\s*=\s*\[\])\s*=\s*(\w+)/g;
    if (arrayDestructuringRegex.test(content)) {
      content = content.replace(arrayDestructuringRegex, '$1 } = $2');
      modified = true;
    }
    
    // Fix object destructuring with missing closing bracket
    // Pattern: data: values = [] = data -> data: values = [] } = data
    const objectDestructuringRegex = /(\w+:\s*\w+\s*=\s*\[\])\s*=\s*(\w+)/g;
    if (objectDestructuringRegex.test(content)) {
      content = content.replace(objectDestructuringRegex, '$1 } = $2');
      modified = true;
    }
    
    // Fix array closing bracket errors
    // Pattern: [85000, 65000, 35000, 25000, 15000}} -> [85000, 65000, 35000, 25000, 15000]]
    const arrayClosingRegex = /(\[[^\]]*\d+)\}\}/g;
    if (arrayClosingRegex.test(content)) {
      content = content.replace(arrayClosingRegex, '$1]]');
      modified = true;
    }
    
    // Fix object closing bracket errors in arrays
    // Pattern: data: [85000, 65000, 35000, 25000, 15000}} -> data: [85000, 65000, 35000, 25000, 15000]]
    const objectArrayClosingRegex = /(\w+:\s*\[[^\]]*\d+)\}\}/g;
    if (objectArrayClosingRegex.test(content)) {
      content = content.replace(objectArrayClosingRegex, '$1]]');
      modified = true;
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`Fixed theme errors in: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Main function
function main() {
  const srcDir = path.join(process.cwd(), 'src');
  
  if (!fs.existsSync(srcDir)) {
    console.error('src directory not found');
    process.exit(1);
  }
  
  console.log('Finding files to fix theme errors...');
  const files = findFiles(srcDir);
  
  console.log(`Found ${files.length} files to check`);
  
  let fixedCount = 0;
  files.forEach(file => {
    if (fixThemeErrors(file)) {
      fixedCount++;
    }
  });
  
  console.log(`\nFixed theme errors in ${fixedCount} files`);
  
  if (fixedCount > 0) {
    console.log('\nRunning type check to verify fixes...');
    const { execSync } = require('child_process');
    try {
      execSync('npm run type-check', { stdio: 'inherit' });
      console.log('Type check passed!');
    } catch (error) {
      console.log('Some errors remain, but theme errors have been fixed.');
    }
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixThemeErrors, findFiles };
