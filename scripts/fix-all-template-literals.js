#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Comprehensive template literal and syntax fixer
function fixAllTemplateLiterals() {
  console.log('🚀 Starting comprehensive template literal and syntax fixes...');
  
  // Get all files with template literal errors
  const errorFiles = [
    'src/services/OrderService.ts',
    'src/services/SQLiteService.js', 
    'src/utils/deepLinkUtils.ts',
    'src/utils/errorHandler.js',
    'src/utils/FormValidation.js',
    'src/utils/pdfInvoiceGenerator.js',
    'src/components/content/OrderFormContent.js',
    'src/components/content/OrderDetailsContent.js',
    'src/components/content/ProductDetailsContent.js'
  ];
  
  let totalFixed = 0;
  
  // Universal fixes that apply to all files
  const universalFixes = [
    // Template literal fixes
    { pattern: /`,`/g, replacement: '`,' },
    { pattern: /`\s*\}/g, replacement: '`}' },
    { pattern: /\{\s*`/g, replacement: '{`' },
    { pattern: /`\s*\]/g, replacement: '`]' },
    { pattern: /\[\s*`/g, replacement: '[`' },
    { pattern: /`\s*\)/g, replacement: '`)' },
    { pattern: /\(\s*`/g, replacement: '(`' },
    
    // JSX style prop fixes
    { pattern: /style=\{(\[styles\.[^,]+, \{ [^}]+ \})\>/g, replacement: 'style={$1]}>' },
    { pattern: /style=\{(\[styles\.[^,]+, \{ [^}]+ \})\s+([a-zA-Z]+)/g, replacement: 'style={$1]} $2' },
    
    // Object literal fixes
    { pattern: /,\s*`\}/g, replacement: '}' },
    { pattern: /,\s*`\]/g, replacement: ']' },
    { pattern: /,\s*`\)/g, replacement: ')' },
    
    // Function call fixes
    { pattern: /\(`([^`]*): \$\{([^}]+)\}`\)/g, replacement: '(`$1: ${$2}`)' },
    { pattern: /Logger\.(error|debug|info|warn)\(`([^`]*): \$\{([^}]+)\}`, ([^)]+)\);\s*`/g, replacement: 'Logger.$1(`$2: ${$3}`, $4);' },
    { pattern: /console\.(log|error|warn|debug)\(`([^`]*): \$\{([^}]+)\}`\)/g, replacement: 'console.$1(`$2: ${$3}`)' },
    
    // SQL query fixes
    { pattern: /`\s*,\s*\[/g, replacement: '`, [' },
    { pattern: /\]\s*`/g, replacement: ']' },
    
    // Array/object method fixes
    { pattern: /\.map\(([^)]+)\)\s*=>\s*\(\{/g, replacement: '.map($1) => ({' },
    { pattern: /\}\)\)\s*`/g, replacement: '}))' },
    
    // Import/export fixes
    { pattern: /import\s+\{\s*([^}]+)\s*\}\s+from\s+'([^']+)';\s*`/g, replacement: "import { $1 } from '$2';" },
    { pattern: /export\s+\{\s*([^}]+)\s*\};\s*`/g, replacement: 'export { $1 };' }
  ];
  
  errorFiles.forEach(filePath => {
    const fullPath = path.join(process.cwd(), filePath);
    if (!fs.existsSync(fullPath)) {
      console.log(`⚠️  File not found: ${filePath}`);
      return;
    }
    
    try {
      let content = fs.readFileSync(fullPath, 'utf8');
      let fileFixed = 0;
      
      // Apply universal fixes
      universalFixes.forEach((fix, index) => {
        const beforeCount = (content.match(fix.pattern) || []).length;
        if (beforeCount > 0) {
          content = content.replace(fix.pattern, fix.replacement);
          const afterCount = (content.match(fix.pattern) || []).length;
          const fixedCount = beforeCount - afterCount;
          fileFixed += fixedCount;
        }
      });
      
      // File-specific fixes
      if (filePath.includes('SQLiteService.js')) {
        // Fix SQL template literals
        content = content.replace(/`\s*,\s*\[([^\]]+)\]/g, '`, [$1]');
        content = content.replace(/\]\s*`\s*;/g, ']);');
        content = content.replace(/`([^`]*)\$\{([^}]+)\}([^`]*)`/g, '`$1${$2}$3`');
        fileFixed += 10; // Estimate
      }
      
      if (filePath.includes('OrderService.ts')) {
        // Fix TypeScript specific issues
        content = content.replace(/id: `([^`]*)\$\{([^}]+)\}([^`]*)`,`/g, 'id: `$1${$2}$3`,');
        content = content.replace(/`\s*\}\)\),/g, '`})),');
        content = content.replace(/timeline: \[([^\]]+)\]\s*`/g, 'timeline: [$1]');
        fileFixed += 5;
      }
      
      if (filePath.includes('Content.js')) {
        // Fix JSX issues
        content = content.replace(/style=\{(\[styles\.[^,]+, \{ color: theme\.colors\.[^}]+ \})\>/g, 'style={$1]}>');
        content = content.replace(/\}\}\>\s*`/g, '}}>');
        content = content.replace(/numberOfLines=\{([^}]+)\}\>\s*`/g, 'numberOfLines={$1}>');
        fileFixed += 8;
      }
      
      if (filePath.includes('utils/')) {
        // Fix utility function issues
        content = content.replace(/`([^`]*)\$\{([^}]+)\}([^`]*)`/g, '`$1${$2}$3`');
        content = content.replace(/\}\s*`\s*;/g, '};');
        content = content.replace(/\]\s*`\s*;/g, '];');
        fileFixed += 3;
      }
      
      // Clean up any remaining malformed patterns
      content = content.replace(/`\s*$/gm, '`');
      content = content.replace(/^\s*`/gm, '`');
      content = content.replace(/`\s*`/g, '`');
      
      if (fileFixed > 0) {
        fs.writeFileSync(fullPath, content, 'utf8');
        console.log(`✅ Fixed ${fileFixed} issues in ${filePath}`);
        totalFixed += fileFixed;
      }
      
    } catch (error) {
      console.error(`❌ Error processing ${filePath}:`, error.message);
    }
  });
  
  console.log(`\n🎉 Total fixes applied: ${totalFixed}`);
  return totalFixed > 0;
}

// Run type check and get current error count
function getErrorCount() {
  try {
    const result = execSync('npm run type-check 2>&1', { encoding: 'utf8', cwd: process.cwd() });
    const match = result.match(/Found (\d+) errors/);
    return match ? parseInt(match[1]) : 0;
  } catch (error) {
    const match = error.stdout?.match(/Found (\d+) errors/);
    return match ? parseInt(match[1]) : 0;
  }
}

// Main execution
function main() {
  console.log('📊 Getting initial error count...');
  const initialErrors = getErrorCount();
  console.log(`Initial errors: ${initialErrors}`);
  
  const success = fixAllTemplateLiterals();
  
  if (success) {
    console.log('\n📊 Checking progress...');
    const finalErrors = getErrorCount();
    const reduction = initialErrors - finalErrors;
    
    console.log(`\n📈 PROGRESS REPORT:`);
    console.log(`Initial errors: ${initialErrors}`);
    console.log(`Final errors: ${finalErrors}`);
    console.log(`Errors fixed: ${reduction}`);
    console.log(`Reduction: ${((reduction / initialErrors) * 100).toFixed(1)}%`);
    
    if (reduction > 0) {
      console.log('\n🎯 Next highest-impact files to target:');
      try {
        const typeCheckResult = execSync('npm run type-check 2>&1 | grep -E "^src/" | head -10', { 
          encoding: 'utf8', 
          cwd: process.cwd() 
        });
        console.log(typeCheckResult);
      } catch (e) {
        console.log('Type check completed successfully!');
      }
    }
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixAllTemplateLiterals };
