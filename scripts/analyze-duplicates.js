/**
 * Comprehensive Duplicate Analysis Tool
 * Finds all duplicate pages, components, functionality, and requests
 */

const fs = require('fs');
const path = require('path');

class DuplicateAnalyzer {
  constructor() {
    this.projectRoot = process.cwd();
    this.srcPath = path.join(this.projectRoot, 'src');
    this.duplicates = {
      screens: [],
      components: [],
      functions: [],
      imports: [],
      requests: [],
      templates: []
    };
  }

  getAllJSFiles(dir) {
    const files = [];
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        files.push(...this.getAllJSFiles(fullPath));
      } else if (item.match(/\.(js|jsx|ts|tsx)$/)) {
        files.push(fullPath);
      }
    }
    
    return files;
  }

  async analyzeDuplicateScreens() {
    console.log('🔍 Analyzing duplicate screens...');
    
    const screenFiles = this.getAllJSFiles(path.join(this.srcPath, 'screens'));
    const screenMap = new Map();
    
    for (const file of screenFiles) {
      const content = fs.readFileSync(file, 'utf8');
      const fileName = path.basename(file, path.extname(file));
      
      // Check for similar screen names
      const similarScreens = screenFiles.filter(f => {
        const otherName = path.basename(f, path.extname(f));
        return otherName !== fileName && 
               (otherName.includes(fileName.replace('Screen', '')) || 
                fileName.includes(otherName.replace('Screen', '')));
      });
      
      if (similarScreens.length > 0) {
        this.duplicates.screens.push({
          primary: fileName,
          file: path.relative(this.projectRoot, file),
          duplicates: similarScreens.map(f => ({
            name: path.basename(f, path.extname(f)),
            file: path.relative(this.projectRoot, f)
          }))
        });
      }
    }
    
    console.log(`Found ${this.duplicates.screens.length} potential duplicate screens`);
  }

  async analyzeDuplicateComponents() {
    console.log('🔍 Analyzing duplicate components...');
    
    const componentFiles = this.getAllJSFiles(path.join(this.srcPath, 'components'));
    const componentMap = new Map();
    
    for (const file of componentFiles) {
      const content = fs.readFileSync(file, 'utf8');
      const fileName = path.basename(file, path.extname(file));
      
      // Look for similar component functionality
      const functionality = this.extractComponentFunctionality(content);
      
      if (componentMap.has(functionality.type)) {
        componentMap.get(functionality.type).push({
          name: fileName,
          file: path.relative(this.projectRoot, file),
          features: functionality.features
        });
      } else {
        componentMap.set(functionality.type, [{
          name: fileName,
          file: path.relative(this.projectRoot, file),
          features: functionality.features
        }]);
      }
    }
    
    // Find duplicates
    for (const [type, components] of componentMap) {
      if (components.length > 1) {
        this.duplicates.components.push({
          type,
          components
        });
      }
    }
    
    console.log(`Found ${this.duplicates.components.length} potential duplicate component types`);
  }

  extractComponentFunctionality(content) {
    const features = [];
    
    // Check for common patterns
    if (content.includes('QR') || content.includes('Scanner')) {
      features.push('QR_SCANNING');
    }
    if (content.includes('Profile') || content.includes('User')) {
      features.push('PROFILE_MANAGEMENT');
    }
    if (content.includes('Template') || content.includes('Garment')) {
      features.push('TEMPLATE_MANAGEMENT');
    }
    if (content.includes('Modal') || content.includes('BottomSheet')) {
      features.push('MODAL_DISPLAY');
    }
    if (content.includes('Picker') || content.includes('Image')) {
      features.push('IMAGE_HANDLING');
    }
    
    // Determine primary type
    let type = 'GENERAL';
    if (features.includes('QR_SCANNING')) type = 'QR_SCANNER';
    else if (features.includes('PROFILE_MANAGEMENT')) type = 'PROFILE';
    else if (features.includes('TEMPLATE_MANAGEMENT')) type = 'TEMPLATE';
    else if (features.includes('MODAL_DISPLAY')) type = 'MODAL';
    else if (features.includes('IMAGE_HANDLING')) type = 'IMAGE';
    
    return { type, features };
  }

  async analyzeDuplicateFunctions() {
    console.log('🔍 Analyzing duplicate functions...');
    
    const files = this.getAllJSFiles(this.srcPath);
    const functionMap = new Map();
    
    for (const file of files) {
      const content = fs.readFileSync(file, 'utf8');
      
      // Extract function definitions
      const functions = content.match(/(?:const|function)\s+(\w+)\s*[=\(]/g) || [];
      
      for (const func of functions) {
        const funcName = func.match(/(?:const|function)\s+(\w+)/)?.[1];
        if (funcName) {
          if (functionMap.has(funcName)) {
            functionMap.get(funcName).push(path.relative(this.projectRoot, file));
          } else {
            functionMap.set(funcName, [path.relative(this.projectRoot, file)]);
          }
        }
      }
    }
    
    // Find duplicates
    for (const [funcName, files] of functionMap) {
      if (files.length > 1) {
        this.duplicates.functions.push({
          name: funcName,
          files
        });
      }
    }
    
    console.log(`Found ${this.duplicates.functions.length} duplicate function names`);
  }

  async analyzeDuplicateRequests() {
    console.log('🔍 Analyzing duplicate API requests...');
    
    const files = this.getAllJSFiles(this.srcPath);
    const requestMap = new Map();
    
    for (const file of files) {
      const content = fs.readFileSync(file, 'utf8');
      
      // Look for API calls and requests
      const requests = content.match(/(?:fetch|axios|api)\s*\([^)]+\)/g) || [];
      const endpoints = content.match(/['"`][^'"`]*\/api\/[^'"`]*['"`]/g) || [];
      
      [...requests, ...endpoints].forEach(request => {
        if (requestMap.has(request)) {
          requestMap.get(request).push(path.relative(this.projectRoot, file));
        } else {
          requestMap.set(request, [path.relative(this.projectRoot, file)]);
        }
      });
    }
    
    // Find duplicates
    for (const [request, files] of requestMap) {
      if (files.length > 1) {
        this.duplicates.requests.push({
          request,
          files
        });
      }
    }
    
    console.log(`Found ${this.duplicates.requests.length} duplicate requests`);
  }

  async generateReport() {
    console.log('\n📊 DUPLICATE ANALYSIS REPORT\n');
    
    // Duplicate Screens
    if (this.duplicates.screens.length > 0) {
      console.log('🔴 DUPLICATE SCREENS:');
      this.duplicates.screens.forEach(screen => {
        console.log(`  • ${screen.primary} (${screen.file})`);
        screen.duplicates.forEach(dup => {
          console.log(`    ↳ ${dup.name} (${dup.file})`);
        });
      });
      console.log('');
    }
    
    // Duplicate Components
    if (this.duplicates.components.length > 0) {
      console.log('🔴 DUPLICATE COMPONENTS:');
      this.duplicates.components.forEach(comp => {
        console.log(`  • ${comp.type}:`);
        comp.components.forEach(c => {
          console.log(`    ↳ ${c.name} (${c.file}) - Features: ${c.features.join(', ')}`);
        });
      });
      console.log('');
    }
    
    // Top Duplicate Functions
    if (this.duplicates.functions.length > 0) {
      console.log('🔴 TOP DUPLICATE FUNCTIONS:');
      this.duplicates.functions
        .sort((a, b) => b.files.length - a.files.length)
        .slice(0, 10)
        .forEach(func => {
          console.log(`  • ${func.name} (${func.files.length} files)`);
          func.files.forEach(file => {
            console.log(`    ↳ ${file}`);
          });
        });
      console.log('');
    }
    
    // Summary
    console.log('📈 SUMMARY:');
    console.log(`  • ${this.duplicates.screens.length} duplicate screen groups`);
    console.log(`  • ${this.duplicates.components.length} duplicate component types`);
    console.log(`  • ${this.duplicates.functions.length} duplicate function names`);
    console.log(`  • ${this.duplicates.requests.length} duplicate requests`);
    
    return this.duplicates;
  }

  async run() {
    console.log('🚀 Starting comprehensive duplicate analysis...\n');
    
    await this.analyzeDuplicateScreens();
    await this.analyzeDuplicateComponents();
    await this.analyzeDuplicateFunctions();
    await this.analyzeDuplicateRequests();
    
    return await this.generateReport();
  }
}

// Run analysis
if (require.main === module) {
  const analyzer = new DuplicateAnalyzer();
  analyzer.run().catch(console.error);
}

module.exports = DuplicateAnalyzer;
