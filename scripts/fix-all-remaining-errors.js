#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Function to recursively find all JS/JSX/TS/TSX files
function findFiles(dir, extensions = ['.js', '.jsx', '.ts', '.tsx']) {
  let results = [];
  const list = fs.readdirSync(dir);
  
  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat && stat.isDirectory()) {
      if (!['node_modules', 'build', 'dist', '.git', 'android', 'ios'].includes(file)) {
        results = results.concat(findFiles(filePath, extensions));
      }
    } else {
      const ext = path.extname(file);
      if (extensions.includes(ext)) {
        results.push(filePath);
      }
    }
  });
  
  return results;
}

// Function to fix all remaining errors
function fixAllRemainingErrors(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Fix missing closing brackets in style arrays with theme colors - most comprehensive pattern
    // Pattern: style={[styles.something, { color: theme.colors.something > -> style={[styles.something, { color: theme.colors.something }]}>
    const themeStyleRegex = /style=\{(\[styles\.[^,]+,\s*\{[^}]*theme\.colors\.[^}]*\})\s*>/g;
    if (themeStyleRegex.test(content)) {
      content = content.replace(themeStyleRegex, 'style={$1]}>');
      modified = true;
    }
    
    // Fix missing closing brackets in all style arrays
    // Pattern: style={[styles.something, { property: value } -> style={[styles.something, { property: value }]}
    const allStyleRegex = /style=\{(\[styles\.[^,]+,\s*\{[^}]*\})\s*(?!])/g;
    if (allStyleRegex.test(content)) {
      content = content.replace(allStyleRegex, 'style={$1]}');
      modified = true;
    }
    
    // Fix missing closing brackets in Surface style arrays
    // Pattern: style={[styles.something, { backgroundColor: theme.colors.something }} elevation -> style={[styles.something, { backgroundColor: theme.colors.something }]} elevation
    const surfaceStyleRegex = /style=\{(\[styles\.[^,]+,\s*\{[^}]*backgroundColor:[^}]*\})\s*\}\s+elevation/g;
    if (surfaceStyleRegex.test(content)) {
      content = content.replace(surfaceStyleRegex, 'style={$1]} elevation');
      modified = true;
    }
    
    // Fix missing closing brackets in Text style arrays
    // Pattern: style={[styles.something, { color: theme.colors.something > -> style={[styles.something, { color: theme.colors.something }]}>
    const textStyleRegex = /style=\{(\[styles\.[^,]+,\s*\{[^}]*color:[^}]*\})\s*>/g;
    if (textStyleRegex.test(content)) {
      content = content.replace(textStyleRegex, 'style={$1]}>');
      modified = true;
    }
    
    // Fix missing closing brackets in numberOfLines props
    // Pattern: }} numberOfLines -> }]} numberOfLines
    const numberOfLinesRegex = /\}\}\s+numberOfLines/g;
    if (numberOfLinesRegex.test(content)) {
      content = content.replace(numberOfLinesRegex, '}]} numberOfLines');
      modified = true;
    }
    
    // Fix missing closing brackets in elevation props
    // Pattern: }} elevation -> }]} elevation
    const elevationRegex = /\}\}\s+elevation/g;
    if (elevationRegex.test(content)) {
      content = content.replace(elevationRegex, '}]} elevation');
      modified = true;
    }
    
    // Fix missing closing brackets in showsVerticalScrollIndicator props
    // Pattern: }} showsVerticalScrollIndicator -> }]} showsVerticalScrollIndicator
    const scrollIndicatorRegex = /\}\}\s+showsVerticalScrollIndicator/g;
    if (scrollIndicatorRegex.test(content)) {
      content = content.replace(scrollIndicatorRegex, '}]} showsVerticalScrollIndicator');
      modified = true;
    }
    
    // Fix malformed JSX props with }}>
    // Pattern: prop={value}}> -> prop={value}>
    const malformedPropRegex = /(\w+)=\{([^}]+)\}\}>/g;
    if (malformedPropRegex.test(content)) {
      content = content.replace(malformedPropRegex, '$1={$2}>');
      modified = true;
    }
    
    // Fix template literal syntax errors
    // Pattern: `${variable -> `${variable}`
    const templateLiteralRegex = /`\$\{([^}]+)(?!`)/g;
    if (templateLiteralRegex.test(content)) {
      content = content.replace(templateLiteralRegex, '`${$1}`');
      modified = true;
    }
    
    // Fix array destructuring errors
    // Pattern: = [] = -> = []
    const destructuringRegex = /=\s*\[\]\s*=/g;
    if (destructuringRegex.test(content)) {
      content = content.replace(destructuringRegex, '= []');
      modified = true;
    }
    
    // Fix object property array errors
    // Pattern: property: [...array, item} -> property: [...array, item]
    const arrayPropertyRegex = /(\w+:\s*\[[^\]]*\w+)\}/g;
    if (arrayPropertyRegex.test(content)) {
      content = content.replace(arrayPropertyRegex, '$1]');
      modified = true;
    }
    
    // Fix indexFields array closing brackets
    // Pattern: indexFields: ['name', 'email'} -> indexFields: ['name', 'email']
    const indexFieldsRegex = /(indexFields:\s*\[[^\]]*'[^']*')\}/g;
    if (indexFieldsRegex.test(content)) {
      content = content.replace(indexFieldsRegex, '$1]');
      modified = true;
    }
    
    // Fix switch case syntax
    // Pattern: case 'value': -> case 'value':
    const switchCaseRegex = /case\s+'([^']+)':\s*$/gm;
    if (switchCaseRegex.test(content)) {
      content = content.replace(switchCaseRegex, "case '$1':");
      modified = true;
    }
    
    // Fix async method declarations
    // Pattern: async methodName(params) { -> async methodName(params) {
    const asyncMethodRegex = /(\s+)async\s+(\w+)\s*\(([^)]*)\)\s*\{/g;
    if (asyncMethodRegex.test(content)) {
      content = content.replace(asyncMethodRegex, '$1async $2($3) {');
      modified = true;
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`Fixed remaining errors in: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Main function
function main() {
  const srcDir = path.join(process.cwd(), 'src');
  
  if (!fs.existsSync(srcDir)) {
    console.error('src directory not found');
    process.exit(1);
  }
  
  console.log('Finding files to fix all remaining errors...');
  const files = findFiles(srcDir);
  
  console.log(`Found ${files.length} files to check`);
  
  let fixedCount = 0;
  files.forEach(file => {
    if (fixAllRemainingErrors(file)) {
      fixedCount++;
    }
  });
  
  console.log(`\nFixed remaining errors in ${fixedCount} files`);
  
  if (fixedCount > 0) {
    console.log('\nRunning type check to verify fixes...');
    const { execSync } = require('child_process');
    try {
      execSync('npm run type-check 2>&1 | head -20', { stdio: 'inherit' });
    } catch (error) {
      console.log('Continuing with fixes...');
    }
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixAllRemainingErrors, findFiles };
