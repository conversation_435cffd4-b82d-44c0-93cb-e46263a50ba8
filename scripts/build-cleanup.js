#!/usr/bin/env node

/**
 * Build-Time Database Cleanup Script
 * Cleans all databases and caches before APK build
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class BuildCleanup {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
    this.androidPath = path.join(this.projectRoot, 'android');
  }

  async cleanup() {
    console.log('🧹 Starting build-time cleanup...\n');
    
    try {
      await this.clearDatabases();
      await this.clearCaches();
      await this.clearBuildArtifacts();
      await this.createCleanMarker();
      
      console.log('✅ Build cleanup completed successfully!\n');
      return true;
    } catch (error) {
      console.error('❌ Build cleanup failed:', error.message);
      return false;
    }
  }

  async clearDatabases() {
    console.log('🗄️ Clearing databases...');
    
    try {
      // Create database reset marker for the app to detect
      const resetMarker = {
        resetTimestamp: new Date().toISOString(),
        resetBy: 'build-cleanup.js',
        buildCleanup: true,
        clearSampleData: true,
        resetMigration: true
      };
      
      const markerPath = path.join(this.projectRoot, 'database-reset-marker.json');
      fs.writeFileSync(markerPath, JSON.stringify(resetMarker, null, 2));
      
      console.log('✅ Database reset marker created');
    } catch (error) {
      console.warn('⚠️  Could not create database reset marker:', error.message);
    }
  }

  async clearCaches() {
    console.log('🧽 Clearing caches...');
    
    try {
      // Clear Metro cache
      const metroCacheDir = path.join(this.projectRoot, 'node_modules', '.cache');
      if (fs.existsSync(metroCacheDir)) {
        execSync(`rm -rf "${metroCacheDir}"`, { stdio: 'inherit' });
        console.log('  ✅ Metro cache cleared');
      }

      // Clear Expo cache
      try {
        execSync('npx expo r -c', { 
          cwd: this.projectRoot, 
          stdio: 'pipe',
          timeout: 10000 
        });
        console.log('  ✅ Expo cache cleared');
      } catch (error) {
        console.log('  ⚠️  Expo cache clear skipped (not critical)');
      }

      // Clear React Native cache
      const rnCacheDir = path.join(require('os').tmpdir(), 'react-native-*');
      try {
        execSync(`rm -rf ${rnCacheDir}`, { stdio: 'pipe' });
        console.log('  ✅ React Native cache cleared');
      } catch (error) {
        console.log('  ⚠️  React Native cache clear skipped');
      }

    } catch (error) {
      console.warn('⚠️  Some caches could not be cleared:', error.message);
    }
  }

  async clearBuildArtifacts() {
    console.log('🏗️ Clearing build artifacts...');
    
    try {
      // Clear Android build artifacts
      const androidBuildDir = path.join(this.androidPath, 'app', 'build');
      if (fs.existsSync(androidBuildDir)) {
        execSync(`rm -rf "${androidBuildDir}"`, { stdio: 'inherit' });
        console.log('  ✅ Android build artifacts cleared');
      }

      // Clear Gradle cache
      const gradleCacheDir = path.join(this.androidPath, '.gradle');
      if (fs.existsSync(gradleCacheDir)) {
        execSync(`rm -rf "${gradleCacheDir}"`, { stdio: 'inherit' });
        console.log('  ✅ Gradle cache cleared');
      }

      // Clear .expo directory
      const expoDir = path.join(this.projectRoot, '.expo');
      if (fs.existsSync(expoDir)) {
        execSync(`rm -rf "${expoDir}"`, { stdio: 'inherit' });
        console.log('  ✅ .expo directory cleared');
      }

    } catch (error) {
      console.warn('⚠️  Some build artifacts could not be cleared:', error.message);
    }
  }

  async createCleanMarker() {
    console.log('📝 Creating clean build marker...');
    
    try {
      const marker = {
        cleanBuildTimestamp: new Date().toISOString(),
        version: require('../package.json').version,
        buildType: 'clean-release'
      };
      
      const markerPath = path.join(this.projectRoot, 'clean-build-marker.json');
      fs.writeFileSync(markerPath, JSON.stringify(marker, null, 2));
      
      console.log('✅ Clean build marker created');
    } catch (error) {
      console.warn('⚠️  Could not create clean build marker:', error.message);
    }
  }
}

// Run cleanup if called directly
if (require.main === module) {
  const cleanup = new BuildCleanup();
  cleanup.cleanup().then(success => {
    process.exit(success ? 0 : 1);
  });
}

module.exports = BuildCleanup;
