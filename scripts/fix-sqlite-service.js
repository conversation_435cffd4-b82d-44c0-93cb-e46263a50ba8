#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

function fixSQLiteService() {
  const filePath = path.join(process.cwd(), 'src/services/SQLiteService.js');
  
  if (!fs.existsSync(filePath)) {
    console.error('SQLiteService.js not found');
    return;
  }
  
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  
  // Fix method parameter syntax - replace $3 with proper parameters
  const methodFixes = [
    { pattern: /async migrateIfNeeded\(\$3\)/g, replacement: 'async migrateIfNeeded()' },
    { pattern: /async ensureConnection\(\$3\)/g, replacement: 'async ensureConnection()' },
    { pattern: /async createTables\(\$3\)/g, replacement: 'async createTables()' },
    { pattern: /async createIndexes\(\$3\)/g, replacement: 'async createIndexes()' },
    { pattern: /async getProducts\(\$3\)/g, replacement: 'async getProducts(filters = {})' },
    { pattern: /async getProductById\(\$3\)/g, replacement: 'async getProductById(id)' },
    { pattern: /async saveProduct\(\$3\)/g, replacement: 'async saveProduct(product)' },
    { pattern: /async deleteProduct\(\$3\)/g, replacement: 'async deleteProduct(id)' },
    { pattern: /async getOrders\(\$3\)/g, replacement: 'async getOrders(filters = {})' },
    { pattern: /async getOrderById\(\$3\)/g, replacement: 'async getOrderById(id)' },
    { pattern: /async saveOrder\(\$3\)/g, replacement: 'async saveOrder(order)' },
    { pattern: /async deleteOrder\(\$3\)/g, replacement: 'async deleteOrder(id)' },
    { pattern: /async deleteCustomer\(\$3\)/g, replacement: 'async deleteCustomer(id)' },
    { pattern: /async migrateFromAsyncStorage\(\$3\)/g, replacement: 'async migrateFromAsyncStorage(data)' },
    { pattern: /async saveCustomer\(\$3\)/g, replacement: 'async saveCustomer(customer)' },
    { pattern: /async getCustomers\(\$3\)/g, replacement: 'async getCustomers()' },
    { pattern: /async getMeasurements\(\$3\)/g, replacement: 'async getMeasurements()' },
    { pattern: /async getMeasurementsByCustomer\(\$3\)/g, replacement: 'async getMeasurementsByCustomer(customerId)' },
    { pattern: /async saveMeasurement\(\$3\)/g, replacement: 'async saveMeasurement(measurement)' },
    { pattern: /async getMeasurementById\(\$3\)/g, replacement: 'async getMeasurementById(id)' },
    { pattern: /async deleteMeasurement\(\$3\)/g, replacement: 'async deleteMeasurement(id)' },
    { pattern: /async getFabrics\(\$3\)/g, replacement: 'async getFabrics()' },
    { pattern: /async saveFabric\(\$3\)/g, replacement: 'async saveFabric(fabric)' },
    { pattern: /async getFabricById\(\$3\)/g, replacement: 'async getFabricById(id)' },
    { pattern: /async updateFabricStock\(\$3\)/g, replacement: 'async updateFabricStock(id, change)' },
    { pattern: /async deleteFabric\(\$3\)/g, replacement: 'async deleteFabric(id)' },
    { pattern: /async clearAllData\(\$3\)/g, replacement: 'async clearAllData()' },
    { pattern: /async resetDatabase\(\$3\)/g, replacement: 'async resetDatabase()' },
    { pattern: /async getGarmentTemplates\(\$3\)/g, replacement: 'async getGarmentTemplates()' },
    { pattern: /async saveGarmentTemplate\(\$3\)/g, replacement: 'async saveGarmentTemplate(template)' },
    { pattern: /async deleteGarmentTemplate\(\$3\)/g, replacement: 'async deleteGarmentTemplate(id)' }
  ];
  
  methodFixes.forEach(fix => {
    if (fix.pattern.test(content)) {
      content = content.replace(fix.pattern, fix.replacement);
      modified = true;
    }
  });
  
  // Fix template literal issues
  const templateLiteralFixes = [
    { pattern: /`MEAS-\$\{Date\.now\(\)\} ,/g, replacement: '`MEAS-${Date.now()}`' },
    { pattern: /`FAB-\$\{Date\.now\(\)\} ,/g, replacement: '`FAB-${Date.now()}`' },
    { pattern: /`DROP TABLE IF EXISTS \$\{table\} \)\;`/g, replacement: '`DROP TABLE IF EXISTS ${table}`' },
    { pattern: /return \{ \.\.\.safeCustomer, id: customer\.id, updatedAt: now   \}/g, replacement: 'return { ...safeCustomer, id: customer.id, updatedAt: now }' },
    { pattern: /return \{ \.\.\.safeCustomer, id: result\.lastInsertRowId, createdAt: now, updatedAt: now   \}/g, replacement: 'return { ...safeCustomer, id: result.lastInsertRowId, createdAt: now, updatedAt: now }' },
    { pattern: /return \{ \.\.\.safeMeasurement, updatedAt: now   \}/g, replacement: 'return { ...safeMeasurement, updatedAt: now }' },
    { pattern: /return \{ \.\.\.safeMeasurement, createdAt: now, updatedAt: now   \}/g, replacement: 'return { ...safeMeasurement, createdAt: now, updatedAt: now }' },
    { pattern: /return \{ \.\.\.safeFabric, updatedAt: now   \}/g, replacement: 'return { ...safeFabric, updatedAt: now }' },
    { pattern: /return \{ \.\.\.safeFabric, createdAt: now, updatedAt: now   \}/g, replacement: 'return { ...safeFabric, createdAt: now, updatedAt: now }' },
    { pattern: /return \{ \.\.\.product, id: result\.lastInsertRowId, createdAt: now, updatedAt: now   \}/g, replacement: 'return { ...product, id: result.lastInsertRowId, createdAt: now, updatedAt: now }' },
    { pattern: /return \{ \.\.\.safeOrder, updatedAt: now   \}/g, replacement: 'return { ...safeOrder, updatedAt: now }' }
  ];
  
  templateLiteralFixes.forEach(fix => {
    if (fix.pattern.test(content)) {
      content = content.replace(fix.pattern, fix.replacement);
      modified = true;
    }
  });
  
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log('Fixed SQLiteService.js method parameters and template literals');
    return true;
  }
  
  return false;
}

// Main function
function main() {
  console.log('Fixing SQLiteService.js...');
  
  if (fixSQLiteService()) {
    console.log('SQLiteService.js fixed successfully');
  } else {
    console.log('No changes needed for SQLiteService.js');
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixSQLiteService };
