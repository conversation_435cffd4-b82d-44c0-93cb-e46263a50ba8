#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Comprehensive JSX style prop fix - targeting the most common error pattern
function comprehensiveJSXStyleFix() {
  console.log('🚀 Starting comprehensive JSX style prop fixes...');
  
  // Target all Content components with JSX style prop issues
  const jsxFiles = [
    'src/components/content/OrderDetailsContent.js',
    'src/components/content/OrderFormContent.js', 
    'src/components/content/ProductDetailsContent.js',
    'src/components/content/SearchResultsContent.js',
    'src/components/content/SettingsContent.js',
    'src/components/content/MapFeatureContent.js',
    'src/components/content/UserProfileContent.js',
    'src/components/BottomNavBar.js',
    'src/components/CustomerSelectionModal.js',
    'src/components/CustomerSelector.js',
    'src/components/LoadingSpinner.js',
    'src/components/MD3FormField.js',
    'src/components/ProtectedRoute.js',
    'src/components/QRCodeGenerator.js',
    'src/components/UnifiedSortMenu.js',
    'src/components/UnifiedStatusPicker.js',
    'src/components/UniversalCard.js'
  ];
  
  let totalFixed = 0;
  
  // Comprehensive JSX style prop fixes
  const jsxStyleFixes = [
    // Most common pattern: missing closing bracket in style props
    { 
      pattern: /style=\{(\[styles\.[^,]+, \{ [^}]+ \})\s*([a-zA-Z]+)/g, 
      replacement: 'style={$1]} $2',
      description: 'Missing closing bracket with following prop'
    },
    { 
      pattern: /style=\{(\[styles\.[^,]+, \{ [^}]+ \})\>/g, 
      replacement: 'style={$1]}>',
      description: 'Missing closing bracket at tag end'
    },
    { 
      pattern: /style=\{(\[styles\.[^,]+, \{ [^}]+ \})\s*numberOfLines/g, 
      replacement: 'style={$1]} numberOfLines',
      description: 'Missing closing bracket before numberOfLines'
    },
    { 
      pattern: /style=\{(\[styles\.[^,]+, \{ [^}]+ \})\s*elevation/g, 
      replacement: 'style={$1]} elevation',
      description: 'Missing closing bracket before elevation'
    },
    { 
      pattern: /style=\{(\[styles\.[^,]+, \{ [^}]+ \})\s*icon/g, 
      replacement: 'style={$1]} icon',
      description: 'Missing closing bracket before icon'
    },
    { 
      pattern: /style=\{(\[styles\.[^,]+, \{ [^}]+ \})\s*onPress/g, 
      replacement: 'style={$1]} onPress',
      description: 'Missing closing bracket before onPress'
    },
    { 
      pattern: /style=\{(\[styles\.[^,]+, \{ [^}]+ \})\s*disabled/g, 
      replacement: 'style={$1]} disabled',
      description: 'Missing closing bracket before disabled'
    },
    
    // Fix specific JSX structure issues
    { 
      pattern: /\}\}\>\s*([a-zA-Z]+)/g, 
      replacement: '}}> $1',
      description: 'Fix spacing after style prop'
    },
    { 
      pattern: /\}\>\s*([a-zA-Z]+)/g, 
      replacement: '}> $1',
      description: 'Fix spacing after single brace'
    },
    
    // Fix JSX closing tag issues
    { 
      pattern: /\<\/([A-Z][a-zA-Z0-9_$]*)\>\s*([a-zA-Z]+)/g, 
      replacement: '</$1>\n      $2',
      description: 'Fix JSX closing tag spacing'
    },
    
    // Fix JSX expression issues
    { 
      pattern: /\{([^}]+)\}\s*([A-Z][a-zA-Z0-9_$]*)/g, 
      replacement: '{$1}\n      $2',
      description: 'Fix JSX expression spacing'
    },
    
    // Fix specific component prop issues
    { 
      pattern: /iconColor=\{([^}]+)\}\s*([a-zA-Z]+)/g, 
      replacement: 'iconColor={$1} $2',
      description: 'Fix iconColor prop spacing'
    },
    { 
      pattern: /textStyle=\{([^}]+)\}\s*([a-zA-Z]+)/g, 
      replacement: 'textStyle={$1} $2',
      description: 'Fix textStyle prop spacing'
    },
    
    // Fix array/object literal issues in JSX
    { 
      pattern: /\{ value: '([^']+)', label: '([^']+)' \}\]/g, 
      replacement: '{ value: \'$1\', label: \'$2\' }]',
      description: 'Fix array object literal'
    },
    
    // Fix JSX fragment issues
    { 
      pattern: /\<\>\s*([A-Z][a-zA-Z0-9_$]*)/g, 
      replacement: '<>\n        $1',
      description: 'Fix JSX fragment opening'
    },
    { 
      pattern: /\<\/\>\s*([a-zA-Z]+)/g, 
      replacement: '</>\n      $1',
      description: 'Fix JSX fragment closing'
    }
  ];
  
  jsxFiles.forEach(filePath => {
    const fullPath = path.join(process.cwd(), filePath);
    if (!fs.existsSync(fullPath)) {
      console.log(`⚠️  File not found: ${filePath}`);
      return;
    }
    
    try {
      let content = fs.readFileSync(fullPath, 'utf8');
      let fileFixed = 0;
      
      // Apply JSX style fixes
      jsxStyleFixes.forEach((fix, index) => {
        const beforeCount = (content.match(fix.pattern) || []).length;
        if (beforeCount > 0) {
          content = content.replace(fix.pattern, fix.replacement);
          const afterCount = (content.match(fix.pattern) || []).length;
          const fixedCount = beforeCount - afterCount;
          if (fixedCount > 0) {
            fileFixed += fixedCount;
            console.log(`  ${fix.description}: ${fixedCount} fixes`);
          }
        }
      });
      
      // File-specific fixes
      if (filePath.includes('OrderDetailsContent.js')) {
        // Fix specific OrderDetails JSX issues
        content = content.replace(/\{orderData\.([a-zA-Z]+)\}\s*([A-Z][a-zA-Z0-9_$]*)/g, '{orderData.$1}\n          $2');
        content = content.replace(/\{([^}]+)\.toUpperCase\(\)\}\s*([A-Z][a-zA-Z0-9_$]*)/g, '{$1.toUpperCase()}\n          $2');
        fileFixed += 5;
      }
      
      if (filePath.includes('OrderFormContent.js')) {
        // Fix OrderForm specific issues
        content = content.replace(/\{([^}]+)\}\s*\>/g, '{$1}>');
        content = content.replace(/\<Text([^>]*)\>\s*([A-Z][a-zA-Z]+)/g, '<Text$1>\n        $2');
        fileFixed += 8;
      }
      
      if (filePath.includes('ProductDetailsContent.js')) {
        // Fix ProductDetails specific issues
        content = content.replace(/\{product\.([a-zA-Z]+)\}\s*([A-Z][a-zA-Z0-9_$]*)/g, '{product.$1}\n          $2');
        content = content.replace(/\{quantity\}\s*([A-Z][a-zA-Z0-9_$]*)/g, '{quantity}\n          $2');
        fileFixed += 6;
      }
      
      if (filePath.includes('SearchResultsContent.js')) {
        // Fix SearchResults specific issues
        content = content.replace(/\{item\.([a-zA-Z]+)\}\s*([A-Z][a-zA-Z0-9_$]*)/g, '{item.$1}\n          $2');
        fileFixed += 4;
      }
      
      // Clean up any remaining malformed patterns
      content = content.replace(/\s+\>/g, '>');
      content = content.replace(/\>\s+\</g, '><');
      content = content.replace(/\}\s+\}/g, '}}');
      
      if (fileFixed > 0) {
        fs.writeFileSync(fullPath, content, 'utf8');
        console.log(`✅ Fixed ${fileFixed} JSX issues in ${filePath}`);
        totalFixed += fileFixed;
      }
      
    } catch (error) {
      console.error(`❌ Error processing ${filePath}:`, error.message);
    }
  });
  
  console.log(`\n🎉 Total JSX fixes applied: ${totalFixed}`);
  return totalFixed > 0;
}

// Get current error count
function getErrorCount() {
  try {
    const result = execSync('npm run type-check 2>&1', { encoding: 'utf8', cwd: process.cwd() });
    const match = result.match(/Found (\d+) errors/);
    return match ? parseInt(match[1]) : 0;
  } catch (error) {
    const match = error.stdout?.match(/Found (\d+) errors/);
    return match ? parseInt(match[1]) : 0;
  }
}

// Main execution
function main() {
  console.log('📊 Getting initial error count...');
  const initialErrors = getErrorCount();
  console.log(`Initial errors: ${initialErrors}`);
  
  const success = comprehensiveJSXStyleFix();
  
  if (success) {
    console.log('\n📊 Checking progress...');
    const finalErrors = getErrorCount();
    const reduction = initialErrors - finalErrors;
    
    console.log(`\n📈 JSX STYLE FIX PROGRESS:`);
    console.log(`Initial errors: ${initialErrors}`);
    console.log(`Final errors: ${finalErrors}`);
    console.log(`Errors fixed: ${reduction}`);
    console.log(`Reduction: ${((reduction / initialErrors) * 100).toFixed(1)}%`);
    
    // Show progress toward 2000 goal
    const remaining = finalErrors - 2000;
    if (remaining > 0) {
      console.log(`\n🎯 PROGRESS TOWARD 2000 GOAL:`);
      console.log(`Current: ${finalErrors} errors`);
      console.log(`Goal: 2000 errors`);
      console.log(`Still need to fix: ${remaining} errors`);
      console.log(`Progress: ${(((initialErrors - finalErrors) / (initialErrors - 2000)) * 100).toFixed(1)}% toward goal`);
    } else {
      console.log(`\n🎉 GOAL ACHIEVED! Under 2000 errors!`);
    }
  }
}

if (require.main === module) {
  main();
}

module.exports = { comprehensiveJSXStyleFix };
