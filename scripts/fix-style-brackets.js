#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Function to recursively find all JS/JSX/TS/TSX files
function findFiles(dir, extensions = ['.js', '.jsx', '.ts', '.tsx']) {
  let results = [];
  const list = fs.readdirSync(dir);
  
  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat && stat.isDirectory()) {
      if (!['node_modules', 'build', 'dist', '.git', 'android', 'ios'].includes(file)) {
        results = results.concat(findFiles(filePath, extensions));
      }
    } else {
      const ext = path.extname(file);
      if (extensions.includes(ext)) {
        results.push(filePath);
      }
    }
  });
  
  return results;
}

// Function to fix style bracket errors
function fixStyleBrackets(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Fix missing closing brackets in style arrays with theme colors
    // Pattern: style={[styles.something, { color: theme.colors.something }> 
    // Should be: style={[styles.something, { color: theme.colors.something }]}>
    const themeStyleBracketRegex = /style=\{(\[styles\.[^,]+,\s*\{[^}]*theme\.colors\.[^}]*\})\s*>/g;
    if (themeStyleBracketRegex.test(content)) {
      content = content.replace(themeStyleBracketRegex, 'style={$1]}>');
      modified = true;
    }
    
    // Fix missing closing brackets in Surface style arrays
    // Pattern: style={[styles.something, { backgroundColor: theme.colors.something }} 
    // Should be: style={[styles.something, { backgroundColor: theme.colors.something }]}
    const surfaceStyleRegex = /style=\{(\[styles\.[^,]+,\s*\{[^}]*backgroundColor:\s*theme\.colors\.[^}]*\})\s*\}/g;
    if (surfaceStyleRegex.test(content)) {
      content = content.replace(surfaceStyleRegex, 'style={$1]}');
      modified = true;
    }
    
    // Fix missing closing brackets in Text style arrays
    // Pattern: style={[styles.something, { color: theme.colors.something }>
    // Should be: style={[styles.something, { color: theme.colors.something }]}>
    const textStyleRegex = /style=\{(\[styles\.[^,]+,\s*\{[^}]*color:\s*theme\.colors\.[^}]*\})\s*>/g;
    if (textStyleRegex.test(content)) {
      content = content.replace(textStyleRegex, 'style={$1]}>');
      modified = true;
    }
    
    // Fix missing closing brackets in View style arrays
    // Pattern: style={[styles.container, style>
    // Should be: style={[styles.container, style]}>
    const viewStyleRegex = /style=\{(\[styles\.[^,]+,\s*\w+)\s*>/g;
    if (viewStyleRegex.test(content)) {
      content = content.replace(viewStyleRegex, 'style={$1]}>');
      modified = true;
    }
    
    // Fix missing closing brackets in elevation props
    // Pattern: }} elevation={0}>
    // Should be: }]} elevation={0}>
    const elevationRegex = /\}\}\s+elevation=/g;
    if (elevationRegex.test(content)) {
      content = content.replace(elevationRegex, '}]} elevation=');
      modified = true;
    }
    
    // Fix missing closing brackets in complex style arrays
    // Pattern: style={[styles.something, { backgroundColor: getColor() + '20' }}
    // Should be: style={[styles.something, { backgroundColor: getColor() + '20' }]}
    const complexStyleRegex = /style=\{(\[styles\.[^,]+,\s*\{[^}]*\w+\([^)]*\)\s*\+\s*'[^']*'\s*\})\s*\}/g;
    if (complexStyleRegex.test(content)) {
      content = content.replace(complexStyleRegex, 'style={$1]}');
      modified = true;
    }
    
    // Fix missing closing brackets in numberOfLines props
    // Pattern: }} numberOfLines={1}>
    // Should be: }]} numberOfLines={1}>
    const numberOfLinesRegex = /\}\}\s+numberOfLines=/g;
    if (numberOfLinesRegex.test(content)) {
      content = content.replace(numberOfLinesRegex, '}]} numberOfLines=');
      modified = true;
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`Fixed style brackets in: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Main function
function main() {
  const srcDir = path.join(process.cwd(), 'src');
  
  if (!fs.existsSync(srcDir)) {
    console.error('src directory not found');
    process.exit(1);
  }
  
  console.log('Finding files to fix style brackets...');
  const files = findFiles(srcDir);
  
  console.log(`Found ${files.length} files to check`);
  
  let fixedCount = 0;
  files.forEach(file => {
    if (fixStyleBrackets(file)) {
      fixedCount++;
    }
  });
  
  console.log(`\nFixed style brackets in ${fixedCount} files`);
  
  if (fixedCount > 0) {
    console.log('\nRunning type check to verify fixes...');
    const { execSync } = require('child_process');
    try {
      execSync('npm run type-check 2>&1 | head -10', { stdio: 'inherit' });
    } catch (error) {
      console.log('Continuing with fixes...');
    }
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixStyleBrackets, findFiles };
