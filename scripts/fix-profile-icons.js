#!/usr/bin/env node

/**
 * Quick fix script to replace all icon references in ProfileScreen with FeatherIcon
 * This is a temporary solution to resolve import conflicts
 */

const fs = require('fs');
const path = require('path');

const profileScreenPath = path.join(__dirname, '..', 'src', 'screens', 'ProfileScreen.js');

console.log('🔧 Fixing ProfileScreen icon references...');

try {
  let content = fs.readFileSync(profileScreenPath, 'utf8');
  
  // Replace all optimized icon references with FeatherIcon
  const replacements = [
    // Replace all OptimizedUnifiedIcon with FeatherIcon
    {
      from: /<OptimizedUnifiedIcon name="chevron-right" size=\{?(\d+)\}? colorContext="surfaceVariant" \/>/g,
      to: '<FeatherIcon name="chevron-right" size={$1} color={theme.colors.onSurfaceVariant} />'
    },
    
    // Replace all SettingsOptimizedIcon with FeatherIcon
    {
      from: /<SettingsOptimizedIcon name="([^"]+)" size=\{?(\d+)\}? color="white" \/>/g,
      to: '<FeatherIcon name="$1" size={$2} color="white" />'
    },
    
    // Replace all BusinessOptimizedIcon with FeatherIcon
    {
      from: /<BusinessOptimizedIcon name="([^"]+)" size=\{?(\d+)\}? color="white" \/>/g,
      to: '<FeatherIcon name="$1" size={$2} color="white" />'
    },
    
    // Replace all ActionOptimizedIcon with FeatherIcon
    {
      from: /<ActionOptimizedIcon name="([^"]+)" size=\{?(\d+)\}? color="white" \/>/g,
      to: '<FeatherIcon name="$1" size={$2} color="white" />'
    },
    
    // Replace all SettingsUnifiedIcon with FeatherIcon
    {
      from: /<SettingsUnifiedIcon name="([^"]+)" size=\{?(\d+)\}? color="white" \/>/g,
      to: '<FeatherIcon name="$1" size={$2} color="white" />'
    },
    
    // Replace specific icon names that don't exist in Feather
    {
      from: /name="crown"/g,
      to: 'name="award"'
    },
    {
      from: /name="notifications"/g,
      to: 'name="bell"'
    },
    {
      from: /name="log-out"/g,
      to: 'name="log-out"'
    }
  ];
  
  let modified = false;
  
  for (const { from, to } of replacements) {
    if (from.test(content)) {
      content = content.replace(from, to);
      modified = true;
      console.log(`✅ Replaced pattern: ${from.source}`);
    }
  }
  
  if (modified) {
    fs.writeFileSync(profileScreenPath, content, 'utf8');
    console.log('✅ ProfileScreen icons fixed successfully!');
  } else {
    console.log('ℹ️  No changes needed');
  }
  
} catch (error) {
  console.error('❌ Error fixing ProfileScreen icons:', error.message);
}

console.log('🎉 ProfileScreen icon fix complete!');
