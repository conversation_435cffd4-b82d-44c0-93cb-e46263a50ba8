#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

function fixPrintService() {
  const filePath = path.join(process.cwd(), 'src/services/PrintService.js');
  
  if (!fs.existsSync(filePath)) {
    console.error('PrintService.js not found');
    return;
  }
  
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  
  // Fix method parameter syntax - replace $3 with proper parameters
  const methodFixes = [
    { pattern: /async shareInvoice\(\$3\)/g, replacement: 'async shareInvoice(invoiceData)' },
    { pattern: /async generateReceipt\(\$3\)/g, replacement: 'async generateReceipt(order, paymentAmount, paymentMethod)' },
    { pattern: /async generateReceiptPDF\(\$3\)/g, replacement: 'async generateReceiptPDF(receiptData)' },
    { pattern: /async shareReceipt\(\$3\)/g, replacement: 'async shareReceipt(receiptData)' }
  ];
  
  methodFixes.forEach(fix => {
    if (fix.pattern.test(content)) {
      content = content.replace(fix.pattern, fix.replacement);
      modified = true;
    }
  });
  
  // Fix template literal issues
  const templateLiteralFixes = [
    // Fix unterminated template literals
    { pattern: /invoiceNumber: `INV-\$\{order\.id\.toString\(\)\.padStart\(4, '0'\)\} ,/g, replacement: "invoiceNumber: `INV-${order.id.toString().padStart(4, '0')}`," },
    { pattern: /`Invoice \$\{invoiceData\.invoiceNumber\} is ready\.\\n\\nOrder: \$\{order\.garment_type\}\\nCustomer: \$\{order\.customer_name\}\\nAmount: ₹\$\{order\.amount\.toLocaleString\(\)\} ,/g, replacement: "`Invoice ${invoiceData.invoiceNumber} is ready.\\n\\nOrder: ${order.garment_type}\\nCustomer: ${order.customer_name}\\nAmount: ₹${order.amount.toLocaleString()}`," },
    { pattern: /`Printing not available\. Invoice \$\{invoiceData\.invoiceNumber\} can be shared instead\.\\n\\nOrder: \$\{order\.garment_type\}\\nCustomer: \$\{order\.customer_name\}\\nAmount: ₹\$\{order\.amount\.toLocaleString\(\)\} ,`/g, replacement: "`Printing not available. Invoice ${invoiceData.invoiceNumber} can be shared instead.\\n\\nOrder: ${order.garment_type}\\nCustomer: ${order.customer_name}\\nAmount: ₹${order.amount.toLocaleString()}`," },
    { pattern: /dialogTitle: `Invoice \$\{invoiceData\.invoiceNumber\} ,/g, replacement: "dialogTitle: `Invoice ${invoiceData.invoiceNumber}`," },
    { pattern: /title: `Invoice \$\{invoiceData\.invoiceNumber\} ,/g, replacement: "title: `Invoice ${invoiceData.invoiceNumber}`," },
    { pattern: /receiptNumber: `RCP-\$\{Date\.now\(\)\} ,`/g, replacement: "receiptNumber: `RCP-${Date.now()}`," },
    { pattern: /`Receipt: \$\{receiptData\.receiptNumber\}\\nDate: \$\{receiptData\.date\}\\n\\nOrder #\$\{order\.id\}\\nPayment: ₹\$\{paymentAmount\.toLocaleString\(\)\}\\nMethod: \$\{paymentMethod\}\\n\\nThank you for your payment! ,/g, replacement: "`Receipt: ${receiptData.receiptNumber}\\nDate: ${receiptData.date}\\n\\nOrder #${order.id}\\nPayment: ₹${paymentAmount.toLocaleString()}\\nMethod: ${paymentMethod}\\n\\nThank you for your payment!`," },
    { pattern: /dialogTitle: `Receipt \$\{receiptData\.receiptNumber\} ,`/g, replacement: "dialogTitle: `Receipt ${receiptData.receiptNumber}`," },
    { pattern: /title: `Receipt \$\{receiptData\.receiptNumber\} ,`/g, replacement: "title: `Receipt ${receiptData.receiptNumber}`," }
  ];
  
  templateLiteralFixes.forEach(fix => {
    if (fix.pattern.test(content)) {
      content = content.replace(fix.pattern, fix.replacement);
      modified = true;
    }
  });
  
  // Fix specific template literal structure issues
  const structureFixes = [
    // Fix the conditional template literal in invoice HTML
    { pattern: /\$\{invoiceData\.order\.notes \? `/g, replacement: "${invoiceData.order.notes ? `" },
    { pattern: /                : ''\}/g, replacement: "` : ''}" },
    // Fix the trim() method call
    { pattern: /       \.trim\(\);/g, replacement: "      `.trim();" }
  ];
  
  structureFixes.forEach(fix => {
    if (fix.pattern.test(content)) {
      content = content.replace(fix.pattern, fix.replacement);
      modified = true;
    }
  });
  
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log('Fixed PrintService.js method parameters and template literals');
    return true;
  }
  
  return false;
}

// Main function
function main() {
  console.log('Fixing PrintService.js...');
  
  if (fixPrintService()) {
    console.log('PrintService.js fixed successfully');
  } else {
    console.log('No changes needed for PrintService.js');
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixPrintService };
