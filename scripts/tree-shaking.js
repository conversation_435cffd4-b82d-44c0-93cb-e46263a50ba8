#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const glob = require('glob');

/**
 * Tree Shaking Optimization Script
 * Converts barrel exports to specific imports for better bundle optimization
 */

class TreeShaking {
  constructor() {
    this.srcDir = path.join(__dirname, '..', 'src');
    this.stats = {
      filesProcessed: 0,
      importsOptimized: 0,
      barrelExportsConverted: 0
    };
  }

  async optimizeTreeShaking() {
    console.log('🌳 Optimizing tree shaking...');
    
    // Find all JS/TS/JSX/TSX files
    const files = glob.sync('**/*.{js,jsx,ts,tsx}', {
      cwd: this.srcDir,
      absolute: true
    });

    for (const file of files) {
      await this.processFile(file);
    }

    this.printStats();
  }

  async processFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      let modifiedContent = content;
      let hasChanges = false;

      // Optimize React Native Paper imports
      const paperResult = this.optimizePaperImports(modifiedContent);
      if (paperResult.modified) {
        modifiedContent = paperResult.content;
        hasChanges = true;
        this.stats.importsOptimized++;
      }

      // Optimize React Native Vector Icons imports
      const iconsResult = this.optimizeIconImports(modifiedContent);
      if (iconsResult.modified) {
        modifiedContent = iconsResult.content;
        hasChanges = true;
        this.stats.importsOptimized++;
      }

      // Optimize Lodash imports
      const lodashResult = this.optimizeLodashImports(modifiedContent);
      if (lodashResult.modified) {
        modifiedContent = lodashResult.content;
        hasChanges = true;
        this.stats.importsOptimized++;
      }

      // Optimize React imports
      const reactResult = this.optimizeReactImports(modifiedContent);
      if (reactResult.modified) {
        modifiedContent = reactResult.content;
        hasChanges = true;
        this.stats.importsOptimized++;
      }

      // Convert barrel exports to specific imports
      const barrelResult = this.convertBarrelExports(modifiedContent);
      if (barrelResult.modified) {
        modifiedContent = barrelResult.content;
        hasChanges = true;
        this.stats.barrelExportsConverted++;
      }

      if (hasChanges) {
        fs.writeFileSync(filePath, modifiedContent, 'utf8');
        console.log(`✅ Optimized: ${path.relative(this.srcDir, filePath)}`);
      }

      this.stats.filesProcessed++;
    } catch (error) {
      console.error(`❌ Error processing ${filePath}:`, error.message);
    }
  }

  optimizePaperImports(content) {
    // Convert: import { Button, Text, Card } from 'react-native-paper';
    // To: import Button from 'react-native-paper/lib/module/components/Button';
    
    const paperImportRegex = /import\s*{([^}]+)}\s*from\s*['"]react-native-paper['"]/g;
    let modified = false;
    
    const newContent = content.replace(paperImportRegex, (match, imports) => {
      const importList = imports.split(',').map(imp => imp.trim());
      const specificImports = importList.map(imp => {
        const componentName = imp.trim();
        return `import ${componentName} from 'react-native-paper/lib/module/components/${componentName}';`;
      }).join('\n');
      
      modified = true;
      return specificImports;
    });

    return { content: newContent, modified };
  }

  optimizeIconImports(content) {
    // Convert: import { MaterialIcons, FontAwesome } from '@expo/vector-icons';
    // To: import MaterialIcons from '@expo/vector-icons/MaterialIcons';
    
    const iconImportRegex = /import\s*{([^}]+)}\s*from\s*['"]@expo\/vector-icons['"]/g;
    let modified = false;
    
    const newContent = content.replace(iconImportRegex, (match, imports) => {
      const importList = imports.split(',').map(imp => imp.trim());
      const specificImports = importList.map(imp => {
        const iconFamily = imp.trim();
        return `import ${iconFamily} from '@expo/vector-icons/${iconFamily}';`;
      }).join('\n');
      
      modified = true;
      return specificImports;
    });

    return { content: newContent, modified };
  }

  optimizeLodashImports(content) {
    // Convert: import { debounce, throttle } from 'lodash';
    // To: import debounce from 'lodash/debounce';
    
    const lodashImportRegex = /import\s*{([^}]+)}\s*from\s*['"]lodash['"]/g;
    let modified = false;
    
    const newContent = content.replace(lodashImportRegex, (match, imports) => {
      const importList = imports.split(',').map(imp => imp.trim());
      const specificImports = importList.map(imp => {
        const functionName = imp.trim();
        return `import ${functionName} from 'lodash/${functionName}';`;
      }).join('\n');
      
      modified = true;
      return specificImports;
    });

    return { content: newContent, modified };
  }

  optimizeReactImports(content) {
    // Ensure React imports are optimized
    const reactImportRegex = /import\s+React(?:,\s*{([^}]+)})?\s*from\s*['"]react['"]/;
    const match = content.match(reactImportRegex);
    
    if (match && match[1]) {
      const namedImports = match[1].split(',').map(imp => imp.trim());
      const coreImports = ['useState', 'useEffect', 'useCallback', 'useMemo', 'useRef'];
      const hasCore = namedImports.some(imp => coreImports.includes(imp));
      
      if (hasCore) {
        // Already optimized
        return { content, modified: false };
      }
    }

    return { content, modified: false };
  }

  convertBarrelExports(content) {
    // Convert barrel exports in local components
    const barrelImportRegex = /import\s*{([^}]+)}\s*from\s*['"]\.\.?\/[^'"]*['"]/g;
    let modified = false;
    
    const newContent = content.replace(barrelImportRegex, (match, imports, offset) => {
      // Check if this is likely a barrel export (multiple imports from same path)
      const importList = imports.split(',').map(imp => imp.trim());
      
      if (importList.length > 3) {
        // This might be a barrel export, but we'll keep it for now
        // as converting requires knowledge of the actual file structure
        return match;
      }
      
      return match;
    });

    return { content: newContent, modified };
  }

  printStats() {
    console.log('\n📊 Tree Shaking Optimization Results:');
    console.log(`Files processed: ${this.stats.filesProcessed}`);
    console.log(`Import statements optimized: ${this.stats.importsOptimized}`);
    console.log(`Barrel exports converted: ${this.stats.barrelExportsConverted}`);
    console.log('\n🌳 Tree shaking optimization completed!');
  }
}

// Run the tree shaking optimization
if (require.main === module) {
  const optimizer = new TreeShaking();
  optimizer.optimizeTreeShaking().catch(console.error);
}

module.exports = TreeShaking;