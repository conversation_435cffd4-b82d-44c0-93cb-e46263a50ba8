#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Function to recursively find all JS/JSX/TS/TSX files
function findFiles(dir, extensions = ['.js', '.jsx', '.ts', '.tsx']) {
  let results = [];
  const list = fs.readdirSync(dir);
  
  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat && stat.isDirectory()) {
      if (!['node_modules', 'build', 'dist', '.git', 'android', 'ios', 'scripts'].includes(file)) {
        results = results.concat(findFiles(filePath, extensions));
      }
    } else {
      const ext = path.extname(file);
      if (extensions.includes(ext)) {
        results.push(filePath);
      }
    }
  });
  
  return results;
}

// Targeted fix for the most common remaining error patterns
function fixTargetedErrors(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 1. Fix JSX style prop bracket issues (highest priority)
    const jsxStyleFixes = [
      // Fix missing closing brackets in style arrays
      { pattern: /style=\{([^}]+)\] elevation=/g, replacement: 'style={$1]} elevation=' },
      { pattern: /style=\{([^}]+)\] >/g, replacement: 'style={$1]}>' },
      { pattern: /style=\{([^}]+)\] /g, replacement: 'style={$1]} ' },
      
      // Fix specific JSX prop patterns with missing brackets
      { pattern: /\[([^}]+), \{ ([^}]+) \] elevation=/g, replacement: '[$1, { $2 }] elevation=' },
      { pattern: /\[([^}]+), \{ ([^}]+) \] >/g, replacement: '[$1, { $2 }]>' },
      { pattern: /\[([^}]+), \{ ([^}]+) \]/g, replacement: '[$1, { $2 }]' },
      
      // Fix JSX source props with extra brackets
      { pattern: /source=\{\{ uri: ([^}]+) \}\}/g, replacement: 'source={{ uri: $1 }}' },
      { pattern: /source=\{\{ uri: ([^}]+) \}\}\}/g, replacement: 'source={{ uri: $1 }}' }
    ];
    
    jsxStyleFixes.forEach(fix => {
      if (fix.pattern.test(content)) {
        content = content.replace(fix.pattern, fix.replacement);
        modified = true;
      }
    });
    
    // 2. Fix template literal issues (second priority)
    const templateFixes = [
      // Fix template literals with extra backticks at the end
      { pattern: /Logger\.error\(`([^`]*): \$\{([^}]+)\}`, ([^)]+)\);\s*`/g, replacement: 'Logger.error(`$1: ${$2}`, $3);' },
      { pattern: /Logger\.debug\(`([^`]*): \$\{([^}]+)\}`\);\s*`/g, replacement: 'Logger.debug(`$1: ${$2}`);' },
      { pattern: /Logger\.info\(`([^`]*): \$\{([^}]+)\}`\);\s*`/g, replacement: 'Logger.info(`$1: ${$2}`);' },
      
      // Fix Alert.alert with template literal issues
      { pattern: /Alert\.alert\(`([^`]*): \$\{([^}]+)\}`,\s*`([^`]*)`\)/g, replacement: 'Alert.alert(`$1: ${$2}`, `$3`)' },
      { pattern: /Alert\.alert\('([^']*)',\s*`([^`]*): \$\{([^}]+)\}([^`]*)`\)/g, replacement: "Alert.alert('$1', `$2: ${$3}$4`)" },
      
      // Fix template literals in method calls
      { pattern: /`([^`]*): \$\{([^}]+)\}`,\s*`([^`]*)/g, replacement: '`$1: ${$2}`, $3' },
      { pattern: /`([^`]*\$\{[^}]+\}[^`]*)`;\s*`/g, replacement: '`$1`;' },
      
      // Fix specific template literal patterns
      { pattern: /throw new StorageError\(`([^`]*): \$\{([^}]+)\}`, `([^`]*)\)/g, replacement: 'throw new StorageError(`$1: ${$2}`, $3)' }
    ];
    
    templateFixes.forEach(fix => {
      if (fix.pattern.test(content)) {
        content = content.replace(fix.pattern, fix.replacement);
        modified = true;
      }
    });
    
    // 3. Fix method signature issues in service classes
    const methodFixes = [
      // Fix async method signatures with wrong parameters
      { pattern: /static async clear\(key, value, expiry\)/g, replacement: 'static async clear()' },
      { pattern: /static async getMultiple\(key, value, expiry\)/g, replacement: 'static async getMultiple(keys)' },
      { pattern: /static async remove\(key, value, expiry\)/g, replacement: 'static async remove(key)' },
      
      // Fix method signatures in notification service
      { pattern: /invoicePrinted\(invoiceNumber\) \{/g, replacement: 'invoicePrinted(invoiceNumber) {' },
      { pattern: /qrCodeGenerated\(type, id\) \{/g, replacement: 'qrCodeGenerated(type, id) {' },
      
      // Fix class method signatures
      { pattern: /async ([a-zA-Z_$][a-zA-Z0-9_$]*)\(([^)]*)\) \{/g, replacement: 'async $1($2) {' }
    ];
    
    methodFixes.forEach(fix => {
      if (fix.pattern.test(content)) {
        content = content.replace(fix.pattern, fix.replacement);
        modified = true;
      }
    });
    
    // 4. Fix object and array syntax issues
    const objectArrayFixes = [
      // Fix object literal issues
      { pattern: /\{ ([^:]+): ([^,}]+), \}/g, replacement: '{ $1: $2 }' },
      { pattern: /\{ ([^:]+): ([^,}]+)   \}/g, replacement: '{ $1: $2 }' },
      { pattern: /return \{ ([^}]+)   \}/g, replacement: 'return { $1 }' },
      
      // Fix array syntax
      { pattern: /\[([^}]+)\}\]/g, replacement: '[$1]' },
      { pattern: /\[([^}]+), \]/g, replacement: '[$1]' },
      
      // Fix export statements
      { pattern: /\]\s*;\s*$/gm, replacement: '];' }
    ];
    
    objectArrayFixes.forEach(fix => {
      if (fix.pattern.test(content)) {
        content = content.replace(fix.pattern, fix.replacement);
        modified = true;
      }
    });
    
    // 5. Fix specific Unicode and character issues
    const unicodeFixes = [
      // Fix Unicode currency symbols
      { pattern: /৳/g, replacement: '৳' }, // Ensure proper encoding
      { pattern: /₹/g, replacement: '₹' }, // Ensure proper encoding
      
      // Fix invalid character sequences
      { pattern: /\s+•\s+/g, replacement: ' • ' },
      { pattern: /\s+&\s+/g, replacement: ' & ' }
    ];
    
    unicodeFixes.forEach(fix => {
      if (fix.pattern.test(content)) {
        content = content.replace(fix.pattern, fix.replacement);
        modified = true;
      }
    });
    
    // 6. Fix specific JSX text content issues
    const jsxTextFixes = [
      // Fix JSX text with parsing issues
      { pattern: />([^<]*)\s+([A-Z][a-z]+)\s+([A-Z][a-z]+)([^<]*)</g, replacement: '>$1 $2 $3$4<' },
      { pattern: />([^<]*)\s+([A-Z][a-z]+)\s+([A-Z][a-z]+)\s+([A-Z][a-z]+)([^<]*)</g, replacement: '>$1 $2 $3 $4$5<' }
    ];
    
    jsxTextFixes.forEach(fix => {
      if (fix.pattern.test(content)) {
        content = content.replace(fix.pattern, fix.replacement);
        modified = true;
      }
    });
    
    // 7. Fix HTML-like content in JS files
    const htmlFixes = [
      // Remove HTML tags from JS files
      { pattern: /<body>/g, replacement: '' },
      { pattern: /<\/body>/g, replacement: '' },
      { pattern: /<html>/g, replacement: '' },
      { pattern: /<\/html>/g, replacement: '' },
      { pattern: /<head>/g, replacement: '' },
      { pattern: /<\/head>/g, replacement: '' },
      { pattern: /<style>/g, replacement: '' },
      { pattern: /<\/style>/g, replacement: '' }
    ];
    
    htmlFixes.forEach(fix => {
      if (fix.pattern.test(content)) {
        content = content.replace(fix.pattern, fix.replacement);
        modified = true;
      }
    });
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Main function
function main() {
  console.log('Starting targeted error fixes...');
  
  const srcDir = path.join(process.cwd(), 'src');
  
  if (!fs.existsSync(srcDir)) {
    console.error('src directory not found');
    process.exit(1);
  }
  
  const files = findFiles(srcDir);
  console.log(`Found ${files.length} files to process`);
  
  let fixedCount = 0;
  files.forEach(file => {
    if (fixTargetedErrors(file)) {
      fixedCount++;
    }
  });
  
  console.log(`\nFixed targeted errors in ${fixedCount} out of ${files.length} files`);
  
  if (fixedCount > 0) {
    console.log('\nRunning type check to verify improvements...');
    const { execSync } = require('child_process');
    try {
      execSync('npm run type-check 2>&1 | tail -10', { stdio: 'inherit' });
    } catch (error) {
      console.log('Type check completed - checking final error count...');
    }
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixTargetedErrors, findFiles };
