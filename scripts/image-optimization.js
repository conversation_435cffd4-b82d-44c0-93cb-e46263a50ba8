#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const glob = require('glob');

/**
 * Image Optimization Script
 * Analyzes and optimizes image usage in the React Native app
 */

class ImageOptimization {
  constructor() {
    this.projectDir = path.join(__dirname, '..');
    this.srcDir = path.join(this.projectDir, 'src');
    this.assetsDir = path.join(this.projectDir, 'assets');
    this.stats = {
      imagesFound: 0,
      largeImages: 0,
      unoptimizedImages: 0,
      unusedImages: 0,
      optimizationSuggestions: []
    };
  }

  async optimizeImages() {
    console.log('🖼️ Analyzing image usage and optimization...');
    
    // Find all images in assets directory
    const images = await this.findAllImages();
    
    // Analyze image usage in code
    const imageUsage = await this.analyzeImageUsage();
    
    // Check image sizes and formats
    await this.analyzeImageProperties(images);
    
    // Find unused images
    await this.findUnusedImages(images, imageUsage);
    
    // Generate optimization recommendations
    await this.generateOptimizationRecommendations();
    
    this.printStats();
  }

  async findAllImages() {
    console.log('📁 Finding all images...');
    
    const imageExtensions = ['**/*.{png,jpg,jpeg,gif,webp,svg}'];
    const images = [];
    
    // Search in assets directory
    if (fs.existsSync(this.assetsDir)) {
      const assetImages = glob.sync(imageExtensions[0], {
        cwd: this.assetsDir,
        absolute: true
      });
      images.push(...assetImages);
    }
    
    // Search in src directory for any embedded images
    const srcImages = glob.sync(imageExtensions[0], {
      cwd: this.srcDir,
      absolute: true
    });
    images.push(...srcImages);
    
    this.stats.imagesFound = images.length;
    console.log(`Found ${images.length} images`);
    
    return images;
  }

  async analyzeImageUsage() {
    console.log('🔍 Analyzing image usage in code...');
    
    const imageUsage = new Set();
    
    // Find all JS/TS/JSX/TSX files
    const codeFiles = glob.sync('**/*.{js,jsx,ts,tsx}', {
      cwd: this.srcDir,
      absolute: true
    });

    for (const file of codeFiles) {
      const content = fs.readFileSync(file, 'utf8');
      
      // Find require() statements for images
      const requireMatches = content.matchAll(/require\(['"]([^'"]*\.(png|jpg|jpeg|gif|webp|svg))['"]/g);
      for (const match of requireMatches) {
        imageUsage.add(match[1]);
      }
      
      // Find import statements for images
      const importMatches = content.matchAll(/import\s+\w+\s+from\s+['"]([^'"]*\.(png|jpg|jpeg|gif|webp|svg))['"]/g);
      for (const match of importMatches) {
        imageUsage.add(match[1]);
      }
      
      // Find Image source props
      const sourceMatches = content.matchAll(/source\s*=\s*{\s*require\(['"]([^'"]*\.(png|jpg|jpeg|gif|webp|svg))['"]/g);
      for (const match of sourceMatches) {
        imageUsage.add(match[1]);
      }
    }
    
    console.log(`Found ${imageUsage.size} image references in code`);
    return imageUsage;
  }

  async analyzeImageProperties(images) {
    console.log('📏 Analyzing image properties...');
    
    for (const imagePath of images) {
      try {
        const stats = fs.statSync(imagePath);
        const sizeInMB = stats.size / (1024 * 1024);
        const extension = path.extname(imagePath).toLowerCase();
        
        // Check for large images (>500KB)
        if (sizeInMB > 0.5) {
          this.stats.largeImages++;
          this.stats.optimizationSuggestions.push({
            type: 'large_image',
            file: path.relative(this.projectDir, imagePath),
            size: `${sizeInMB.toFixed(2)}MB`,
            suggestion: 'Consider compressing or resizing this image'
          });
        }
        
        // Check for unoptimized formats
        if (['.png', '.jpg', '.jpeg'].includes(extension)) {
          // Suggest WebP conversion for better compression
          this.stats.optimizationSuggestions.push({
            type: 'format_optimization',
            file: path.relative(this.projectDir, imagePath),
            currentFormat: extension,
            suggestion: 'Consider converting to WebP format for better compression'
          });
        }
        
      } catch (error) {
        console.warn(`Could not analyze ${imagePath}: ${error.message}`);
      }
    }
  }

  async findUnusedImages(images, imageUsage) {
    console.log('🗑️ Finding unused images...');
    
    for (const imagePath of images) {
      const relativePath = path.relative(this.projectDir, imagePath);
      const fileName = path.basename(imagePath);
      
      // Check if image is referenced in code
      let isUsed = false;
      for (const usedImage of imageUsage) {
        if (usedImage.includes(fileName) || usedImage.includes(relativePath)) {
          isUsed = true;
          break;
        }
      }
      
      if (!isUsed) {
        this.stats.unusedImages++;
        this.stats.optimizationSuggestions.push({
          type: 'unused_image',
          file: relativePath,
          suggestion: 'This image appears to be unused and can be removed'
        });
      }
    }
  }

  async generateOptimizationRecommendations() {
    console.log('💡 Generating optimization recommendations...');
    
    // Create image optimization guide
    const optimizationGuide = this.createOptimizationGuide();
    const guidePath = path.join(this.projectDir, 'docs', 'IMAGE_OPTIMIZATION_GUIDE.md');
    
    // Ensure docs directory exists
    const docsDir = path.dirname(guidePath);
    if (!fs.existsSync(docsDir)) {
      fs.mkdirSync(docsDir, { recursive: true });
    }
    
    fs.writeFileSync(guidePath, optimizationGuide, 'utf8');
    console.log('✅ Created image optimization guide');
    
    // Create image optimization script
    const optimizationScript = this.createOptimizationScript();
    const scriptPath = path.join(this.projectDir, 'scripts', 'optimize-images.sh');
    
    fs.writeFileSync(scriptPath, optimizationScript, 'utf8');
    fs.chmodSync(scriptPath, '755'); // Make executable
    console.log('✅ Created image optimization script');
  }

  createOptimizationGuide() {
    return `# Image Optimization Guide

## Overview
This guide provides recommendations for optimizing images in the Tailora React Native app.

## Current Analysis Results
- **Total Images Found:** ${this.stats.imagesFound}
- **Large Images (>500KB):** ${this.stats.largeImages}
- **Unused Images:** ${this.stats.unusedImages}

## Optimization Recommendations

### 1. Image Formats
- **WebP**: Use for photographs and complex images (70-90% smaller than JPEG)
- **PNG**: Use for images with transparency or simple graphics
- **SVG**: Use for icons and simple vector graphics

### 2. Image Sizes
- **Mobile Screens**: Optimize for 1x, 2x, and 3x densities
- **Maximum Width**: 1080px for most use cases
- **File Size**: Keep under 500KB per image

### 3. React Native Best Practices
\`\`\`javascript
// Good: Using require for static images
const logo = require('../assets/images/logo.png');

// Good: Providing multiple densities
// logo.png, <EMAIL>, <EMAIL>

// Good: Using resizeMode
<Image 
  source={logo} 
  style={{width: 100, height: 100}}
  resizeMode="contain"
/>
\`\`\`

### 4. Optimization Tools
- **ImageOptim** (macOS): Lossless compression
- **TinyPNG**: Online PNG/JPEG compression
- **Squoosh**: Google's image optimization tool
- **react-native-image-resizer**: Runtime image optimization

## Specific Issues Found

${this.stats.optimizationSuggestions.map(suggestion => 
  `### ${suggestion.type.replace('_', ' ').toUpperCase()}
- **File:** \`${suggestion.file}\`
- **Issue:** ${suggestion.suggestion}
${suggestion.size ? `- **Size:** ${suggestion.size}` : ''}
${suggestion.currentFormat ? `- **Format:** ${suggestion.currentFormat}` : ''}
`
).join('\n')}

## Action Items

1. **Compress Large Images**: Use tools like ImageOptim or TinyPNG
2. **Convert to WebP**: For better compression ratios
3. **Remove Unused Images**: Clean up unused assets
4. **Implement Lazy Loading**: For images not immediately visible
5. **Use Appropriate Densities**: Provide @2x and @3x variants

## Automated Optimization

Run the optimization script:
\`\`\`bash
./scripts/optimize-images.sh
\`\`\`

This will:
- Compress existing images
- Convert suitable images to WebP
- Generate multiple density variants
- Remove unused images (with confirmation)
`;
  }

  createOptimizationScript() {
    return `#!/bin/bash

# Image Optimization Script for Tailora React Native App
# This script optimizes images for better performance

echo "🖼️ Starting image optimization..."

# Check if ImageMagick is installed
if ! command -v convert &> /dev/null; then
    echo "❌ ImageMagick not found. Please install it first:"
    echo "   macOS: brew install imagemagick"
    echo "   Ubuntu: sudo apt-get install imagemagick"
    exit 1
fi

# Create backup directory
BACKUP_DIR="./assets/images/backup"
mkdir -p "$BACKUP_DIR"

# Function to optimize PNG images
optimize_png() {
    local file="$1"
    local backup="$BACKUP_DIR/$(basename "$file")"
    
    echo "📸 Optimizing PNG: $(basename "$file")"
    
    # Create backup
    cp "$file" "$backup"
    
    # Optimize PNG
    convert "$file" -strip -interlace Plane -quality 85 "$file"
}

# Function to optimize JPEG images
optimize_jpeg() {
    local file="$1"
    local backup="$BACKUP_DIR/$(basename "$file")"
    
    echo "📸 Optimizing JPEG: $(basename "$file")"
    
    # Create backup
    cp "$file" "$backup"
    
    # Optimize JPEG
    convert "$file" -strip -interlace Plane -quality 80 "$file"
}

# Function to convert to WebP
convert_to_webp() {
    local file="$1"
    local webp_file="\${file%.*}.webp"
    
    echo "🔄 Converting to WebP: $(basename "$file")"
    
    # Convert to WebP
    convert "$file" -quality 80 "$webp_file"
    
    echo "✅ Created: $(basename "$webp_file")"
}

# Find and optimize images
find ./assets -name "*.png" -type f | while read file; do
    optimize_png "$file"
done

find ./assets -name "*.jpg" -o -name "*.jpeg" -type f | while read file; do
    optimize_jpeg "$file"
done

# Convert large images to WebP
echo "\n🔄 Converting large images to WebP..."
find ./assets -name "*.png" -o -name "*.jpg" -o -name "*.jpeg" -type f | while read file; do
    # Check file size (greater than 100KB)
    if [ $(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null) -gt 102400 ]; then
        convert_to_webp "$file"
    fi
done

echo "\n✨ Image optimization completed!"
echo "📁 Backups saved in: $BACKUP_DIR"
echo "\n💡 Next steps:"
echo "   1. Test your app to ensure images display correctly"
echo "   2. Update image references to use WebP where appropriate"
echo "   3. Remove backup files once you're satisfied with results"
`;
  }

  printStats() {
    console.log('\n📊 Image Optimization Analysis Results:');
    console.log(`Images found: ${this.stats.imagesFound}`);
    console.log(`Large images (>500KB): ${this.stats.largeImages}`);
    console.log(`Unused images: ${this.stats.unusedImages}`);
    console.log(`Optimization suggestions: ${this.stats.optimizationSuggestions.length}`);
    
    if (this.stats.optimizationSuggestions.length > 0) {
      console.log('\n💡 Top Recommendations:');
      this.stats.optimizationSuggestions.slice(0, 5).forEach((suggestion, index) => {
        console.log(`${index + 1}. ${suggestion.suggestion} (${suggestion.file})`);
      });
    }
    
    console.log('\n🖼️ Image optimization analysis completed!');
    console.log('📖 Check docs/IMAGE_OPTIMIZATION_GUIDE.md for detailed recommendations');
    console.log('🔧 Run ./scripts/optimize-images.sh to apply optimizations');
  }
}

// Run the image optimization analysis
if (require.main === module) {
  const optimizer = new ImageOptimization();
  optimizer.optimizeImages().catch(console.error);
}

module.exports = ImageOptimization;