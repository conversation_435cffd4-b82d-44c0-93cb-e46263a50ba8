#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Function to recursively find all JS/JSX/TS/TSX files
function findFiles(dir, extensions = ['.js', '.jsx', '.ts', '.tsx']) {
  let results = [];
  const list = fs.readdirSync(dir);
  
  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat && stat.isDirectory()) {
      if (!['node_modules', 'build', 'dist', '.git', 'android', 'ios'].includes(file)) {
        results = results.concat(findFiles(filePath, extensions));
      }
    } else {
      const ext = path.extname(file);
      if (extensions.includes(ext)) {
        results.push(filePath);
      }
    }
  });
  
  return results;
}

// Function to fix JSX structure issues
function fixJSXStructure(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Fix missing closing brackets in style arrays with simple patterns
    // Pattern: style={[styles.container, style> -> style={[styles.container, style]}>
    const simpleStyleRegex = /style=\{(\[styles\.\w+,\s*\w+)\s*>/g;
    if (simpleStyleRegex.test(content)) {
      content = content.replace(simpleStyleRegex, 'style={$1]}>');
      modified = true;
    }
    
    // Fix missing closing brackets in contentContainerStyle
    // Pattern: contentContainerStyle={[styles.content, contentStyle} -> contentContainerStyle={[styles.content, contentStyle]}
    const contentContainerRegex = /contentContainerStyle=\{(\[styles\.\w+,\s*\w+)\s*\}/g;
    if (contentContainerRegex.test(content)) {
      content = content.replace(contentContainerRegex, 'contentContainerStyle={$1]}');
      modified = true;
    }
    
    // Fix malformed JSX props with }}>
    // Pattern: prop={value}}> -> prop={value}>
    const malformedPropRegex = /(\w+)=\{([^}]+)\}\}>/g;
    if (malformedPropRegex.test(content)) {
      content = content.replace(malformedPropRegex, '$1={$2}>');
      modified = true;
    }
    
    // Fix missing closing brackets in flex style props
    // Pattern: style={[styles.actionButton, { flex: 2 }} -> style={[styles.actionButton, { flex: 2 }]}
    const flexStyleRegex = /style=\{(\[styles\.\w+,\s*\{\s*flex:\s*\d+\s*\})\s*\}/g;
    if (flexStyleRegex.test(content)) {
      content = content.replace(flexStyleRegex, 'style={$1]}');
      modified = true;
    }
    
    // Fix missing closing brackets in backgroundColor style props
    // Pattern: style={[styles.chip, { backgroundColor: getColor() + '20' }} -> style={[styles.chip, { backgroundColor: getColor() + '20' }]}
    const backgroundColorRegex = /style=\{(\[styles\.\w+,\s*\{\s*backgroundColor:\s*[^}]+\})\s*\}/g;
    if (backgroundColorRegex.test(content)) {
      content = content.replace(backgroundColorRegex, 'style={$1]}');
      modified = true;
    }
    
    // Fix missing closing brackets in borderColor style props
    // Pattern: style={[styles.chip, { borderColor: getColor() }} -> style={[styles.chip, { borderColor: getColor() }]}
    const borderColorRegex = /style=\{(\[styles\.\w+,\s*\{\s*borderColor:\s*[^}]+\})\s*\}/g;
    if (borderColorRegex.test(content)) {
      content = content.replace(borderColorRegex, 'style={$1]}');
      modified = true;
    }
    
    // Fix array destructuring errors
    // Pattern: = [] = -> = [] } =
    const destructuringRegex1 = /=\s*\[\]\s*=/g;
    if (destructuringRegex1.test(content)) {
      content = content.replace(destructuringRegex1, '= [] } =');
      modified = true;
    }
    
    // Fix object property array errors
    // Pattern: tags: entity.tags || []; -> tags: entity.tags || []
    const arrayPropertyRegex = /(\w+:\s*[^;]+\|\|\s*\[\]);/g;
    if (arrayPropertyRegex.test(content)) {
      content = content.replace(arrayPropertyRegex, '$1');
      modified = true;
    }
    
    // Fix indexFields array closing brackets
    // Pattern: indexFields: ['name', 'email', 'phone'} -> indexFields: ['name', 'email', 'phone']
    const indexFieldsRegex = /(indexFields:\s*\[[^\]]*'[^']*')\}/g;
    if (indexFieldsRegex.test(content)) {
      content = content.replace(indexFieldsRegex, '$1]');
      modified = true;
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`Fixed JSX structure in: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Main function
function main() {
  const srcDir = path.join(process.cwd(), 'src');
  
  if (!fs.existsSync(srcDir)) {
    console.error('src directory not found');
    process.exit(1);
  }
  
  console.log('Finding files to fix JSX structure...');
  const files = findFiles(srcDir);
  
  console.log(`Found ${files.length} files to check`);
  
  let fixedCount = 0;
  files.forEach(file => {
    if (fixJSXStructure(file)) {
      fixedCount++;
    }
  });
  
  console.log(`\nFixed JSX structure in ${fixedCount} files`);
  
  if (fixedCount > 0) {
    console.log('\nRunning type check to verify fixes...');
    const { execSync } = require('child_process');
    try {
      execSync('npm run type-check 2>&1 | head -15', { stdio: 'inherit' });
    } catch (error) {
      console.log('Continuing with fixes...');
    }
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixJSXStructure, findFiles };
