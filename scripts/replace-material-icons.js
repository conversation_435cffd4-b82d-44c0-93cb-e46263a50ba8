#!/usr/bin/env node

/**
 * Replace MaterialCommunityIcons Script
 * Replaces all react-native-vector-icons/MaterialCommunityIcons with LocalIcon
 * This will complete the icon library migration and reduce bundle size
 */

const fs = require('fs');
const path = require('path');

class MaterialIconReplacer {
  constructor() {
    this.projectRoot = process.cwd();
    this.replacedFiles = [];
    this.errors = [];
  }

  // Get all source files
  getAllFiles(dir, extensions) {
    const files = [];
    
    const scan = (currentDir) => {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          scan(fullPath);
        } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
          files.push(fullPath);
        }
      }
    };
    
    scan(dir);
    return files;
  }

  // Replace MaterialCommunityIcons in a single file
  replaceInFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      let newContent = content;
      let hasChanges = false;

      // Replace import statement
      const importPattern = /import Icon from 'react-native-vector-icons\/MaterialCommunityIcons';/g;
      if (importPattern.test(content)) {
        newContent = newContent.replace(
          importPattern,
          "import LocalIcon from '../components/LocalIcon';"
        );
        hasChanges = true;
      }

      // Replace Icon usage with LocalIcon
      const iconUsagePattern = /<Icon\s+/g;
      if (iconUsagePattern.test(newContent)) {
        newContent = newContent.replace(iconUsagePattern, '<LocalIcon ');
        hasChanges = true;
      }

      // Replace Icon component references
      const iconRefPattern = /\bIcon\b(?=\s*name=)/g;
      if (iconRefPattern.test(newContent)) {
        newContent = newContent.replace(iconRefPattern, 'LocalIcon');
        hasChanges = true;
      }

      // Handle specific prop mappings
      const propMappings = [
        // size prop mapping
        { pattern: /size=\{(\d+)\}/g, replacement: 'size={$1}' },
        // color prop mapping (already compatible)
        // name prop mapping (already compatible)
      ];

      for (const { pattern, replacement } of propMappings) {
        if (pattern.test(newContent)) {
          newContent = newContent.replace(pattern, replacement);
          hasChanges = true;
        }
      }

      if (hasChanges) {
        fs.writeFileSync(filePath, newContent);
        this.replacedFiles.push(filePath);
        console.log(`✅ Replaced: ${path.relative(this.projectRoot, filePath)}`);
      }

    } catch (error) {
      this.errors.push({ file: filePath, error: error.message });
      console.error(`❌ Error processing ${filePath}:`, error.message);
    }
  }

  // Process all files
  replaceAll() {
    console.log('🔄 Starting MaterialCommunityIcons replacement...');
    
    const sourceDir = path.join(this.projectRoot, 'src');
    const files = this.getAllFiles(sourceDir, ['.js', '.jsx', '.ts', '.tsx']);
    
    console.log(`📁 Found ${files.length} source files to process`);
    
    for (const file of files) {
      this.replaceInFile(file);
    }
    
    console.log('\n🎉 MaterialCommunityIcons replacement completed!');
    console.log(`✅ Replaced ${this.replacedFiles.length} files`);
    console.log(`❌ ${this.errors.length} errors`);
    
    if (this.errors.length > 0) {
      console.log('\n❌ Errors:');
      this.errors.forEach(error => {
        console.log(`   - ${path.relative(this.projectRoot, error.file)}: ${error.error}`);
      });
    }
  }

  // Generate report
  generateReport() {
    const reportPath = path.join(this.projectRoot, 'material-icons-replacement-report.md');
    
    const report = `# MaterialCommunityIcons Replacement Report

## Summary
- **Files processed**: ${this.replacedFiles.length}
- **Errors**: ${this.errors.length}
- **Generated**: ${new Date().toISOString()}

## Replaced Files
${this.replacedFiles.map(file => `- ${path.relative(this.projectRoot, file)}`).join('\n')}

## Errors
${this.errors.map(error => `- ${path.relative(this.projectRoot, error.file)}: ${error.error}`).join('\n')}

## Changes Made
1. **Import statements**: \`import Icon from 'react-native-vector-icons/MaterialCommunityIcons'\` → \`import LocalIcon from '../components/LocalIcon'\`
2. **Component usage**: \`<Icon\` → \`<LocalIcon\`
3. **Component references**: \`Icon\` → \`LocalIcon\`

## Next Steps
1. **Test the app**: Verify all icons display correctly
2. **Remove dependencies**: Consider removing react-native-vector-icons if no longer needed
3. **Bundle analysis**: Check bundle size reduction

## Expected Benefits
- **Smaller bundle size**: No heavy MaterialCommunityIcons library
- **Faster loading**: Inline SVGs render faster
- **Better performance**: Reduced memory usage
- **Consistent styling**: All icons use the same LocalIcon system
`;

    fs.writeFileSync(reportPath, report);
    console.log(`📊 Generated replacement report: ${reportPath}`);
  }
}

// Run the replacement
if (require.main === module) {
  const replacer = new MaterialIconReplacer();
  replacer.replaceAll();
  replacer.generateReport();
}

module.exports = MaterialIconReplacer;
