#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const glob = require('glob');

/**
 * Lazy Loading Implementation Script
 * Implements lazy loading for non-critical components
 */

class LazyLoadingImplementation {
  constructor() {
    this.projectDir = path.join(__dirname, '..');
    this.srcDir = path.join(this.projectDir, 'src');
    this.stats = {
      componentsProcessed: 0,
      lazyComponentsCreated: 0,
      suspenseWrappersAdded: 0,
      importsUpdated: 0
    };
    
    // Components that should be lazy loaded (non-critical)
    this.lazyLoadCandidates = [
      'ProfileScreen',
      'SettingsScreen',
      'HelpScreen',
      'AboutScreen',
      'NotificationsScreen',
      'BackupScreen',
      'ExportScreen',
      'ImportScreen',
      'ThemeSelector',
      'LanguageSelector',
      'CurrencySelector',
      'PrintPreview',
      'EmailComposer',
      'ShareDialog',
      'FeedbackForm',
      'RatingDialog',
      'UpdateDialog',
      'MaintenanceScreen',
      'OfflineScreen',
      'ErrorBoundary',
      'LoadingSpinner',
      'EmptyState',
      'NoDataView',
      'SearchResults',
      'FilterPanel',
      'SortPanel',
      'CalendarPicker',
      'DateRangePicker',
      'ColorPicker',
      'ImageViewer',
      'DocumentViewer',
      'VideoPlayer',
      'AudioPlayer',
      'MapView',
      'ChartContainer',
      'GraphContainer',
      'TableView',
      'GridView',
      'ListView',
      'CardView',
      'DetailView',
      'PreviewPane',
      'SidePanel',
      'Modal',
      'Popup',
      'Tooltip',
      'Dropdown',
      'Accordion',
      'Carousel',
      'Gallery',
      'Slideshow',
      'Wizard',
      'Stepper',
      'ProgressIndicator',
      'StatusBar',
      'Toolbar',
      'Sidebar',
      'Footer',
      'Header',
      'Navigation',
      'Breadcrumb',
      'Pagination',
      'InfiniteScroll',
      'VirtualList',
      'LazyImage',
      'LazyVideo',
      'LazyComponent'
    ];
  }

  async implementLazyLoading() {
    console.log('⚡ Implementing lazy loading for non-critical components...');
    
    // Find all React components
    const components = await this.findReactComponents();
    
    // Identify components that should be lazy loaded
    const lazyComponents = this.identifyLazyComponents(components);
    
    // Create lazy loading wrappers
    await this.createLazyWrappers(lazyComponents);
    
    // Update imports to use lazy components
    await this.updateImportsToLazy(lazyComponents);
    
    // Add Suspense wrappers where needed
    await this.addSuspenseWrappers();
    
    // Create lazy loading utilities
    await this.createLazyLoadingUtils();
    
    this.printStats();
  }

  async findReactComponents() {
    console.log('🔍 Finding React components...');
    
    const componentFiles = glob.sync('**/*.{js,jsx,ts,tsx}', {
      cwd: this.srcDir,
      absolute: true
    });

    const components = [];

    for (const file of componentFiles) {
      const content = fs.readFileSync(file, 'utf8');
      
      // Skip if not a React component
      if (!this.isReactComponent(content)) continue;
      
      const componentName = this.extractComponentName(file, content);
      if (componentName) {
        components.push({
          name: componentName,
          file: file,
          relativePath: path.relative(this.srcDir, file),
          content: content
        });
      }
    }
    
    console.log(`Found ${components.length} React components`);
    return components;
  }

  isReactComponent(content) {
    // Check for React imports and component patterns
    const reactImportPattern = /import.*React.*from.*['"]react['"]/;
    const componentPattern = /(export\s+default\s+function|export\s+function|const\s+\w+\s*=.*=>|function\s+\w+.*\{)/;
    const jsxPattern = /<[A-Z][\w\s="'{}.,;:()\[\]\-]*>/;
    
    return (reactImportPattern.test(content) || jsxPattern.test(content)) && componentPattern.test(content);
  }

  extractComponentName(file, content) {
    const fileName = path.basename(file, path.extname(file));
    
    // Try to extract from export default
    const exportDefaultMatch = content.match(/export\s+default\s+(?:function\s+)?(\w+)/);
    if (exportDefaultMatch) {
      return exportDefaultMatch[1];
    }
    
    // Try to extract from const declaration
    const constMatch = content.match(/const\s+(\w+)\s*=.*=>/);
    if (constMatch) {
      return constMatch[1];
    }
    
    // Use file name as fallback
    return fileName;
  }

  identifyLazyComponents(components) {
    console.log('🎯 Identifying components for lazy loading...');
    
    const lazyComponents = components.filter(component => {
      // Check if component name matches lazy load candidates
      const isCandidate = this.lazyLoadCandidates.some(candidate => 
        component.name.toLowerCase().includes(candidate.toLowerCase()) ||
        candidate.toLowerCase().includes(component.name.toLowerCase())
      );
      
      // Check if component is in non-critical directories
      const isNonCritical = [
        'screens/settings',
        'screens/profile',
        'screens/help',
        'screens/about',
        'components/modals',
        'components/dialogs',
        'components/overlays',
        'components/charts',
        'components/reports',
        'components/export',
        'components/import',
        'components/backup',
        'utils/components'
      ].some(dir => component.relativePath.includes(dir));
      
      // Check if component has heavy dependencies
      const hasHeavyDeps = [
        'react-native-chart',
        'react-native-svg',
        'react-native-camera',
        'react-native-image',
        'react-native-video',
        'react-native-maps',
        'react-native-pdf'
      ].some(dep => component.content.includes(dep));
      
      return isCandidate || isNonCritical || hasHeavyDeps;
    });
    
    console.log(`Identified ${lazyComponents.length} components for lazy loading`);
    return lazyComponents;
  }

  async createLazyWrappers(lazyComponents) {
    console.log('🔄 Creating lazy loading wrappers...');
    
    const lazyDir = path.join(this.srcDir, 'components', 'lazy');
    if (!fs.existsSync(lazyDir)) {
      fs.mkdirSync(lazyDir, { recursive: true });
    }
    
    for (const component of lazyComponents) {
      const lazyWrapperPath = path.join(lazyDir, `${component.name}Lazy.js`);
      const relativePath = path.relative(lazyDir, component.file).replace(/\\/g, '/');
      
      const lazyWrapper = this.generateLazyWrapper(component.name, relativePath);
      
      fs.writeFileSync(lazyWrapperPath, lazyWrapper, 'utf8');
      this.stats.lazyComponentsCreated++;
    }
    
    // Create index file for lazy components
    const indexContent = this.generateLazyIndex(lazyComponents);
    fs.writeFileSync(path.join(lazyDir, 'index.js'), indexContent, 'utf8');
    
    console.log(`Created ${lazyComponents.length} lazy wrappers`);
  }

  generateLazyWrapper(componentName, relativePath) {
    return `import React, { lazy, Suspense } from 'react';
import { View, ActivityIndicator, StyleSheet } from 'react-native';

// Lazy load the ${componentName} component
const ${componentName}Component = lazy(() => import('${relativePath}'));

// Loading fallback component
const ${componentName}Loading = () => (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" color="#007AFF" />
  </View>
);

// Lazy wrapper component
const ${componentName}Lazy = (props) => {
  return (
    <Suspense fallback={<${componentName}Loading />}>
      <${componentName}Component {...props} />
    </Suspense>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
});

export default ${componentName}Lazy;
`;
  }

  generateLazyIndex(lazyComponents) {
    const imports = lazyComponents.map(component => 
      `export { default as ${component.name}Lazy } from './${component.name}Lazy';`
    ).join('\n');
    
    return `// Lazy loaded components index\n// Auto-generated by lazy-loading.js\n\n${imports}\n`;
  }

  async updateImportsToLazy(lazyComponents) {
    console.log('📝 Updating imports to use lazy components...');
    
    // Find all files that import the lazy components
    const allFiles = glob.sync('**/*.{js,jsx,ts,tsx}', {
      cwd: this.srcDir,
      absolute: true
    });

    for (const file of allFiles) {
      let content = fs.readFileSync(file, 'utf8');
      let modified = false;
      
      for (const component of lazyComponents) {
        const componentPath = path.relative(path.dirname(file), component.file).replace(/\\/g, '/');
        const lazyPath = path.relative(path.dirname(file), path.join(this.srcDir, 'components', 'lazy', `${component.name}Lazy.js`)).replace(/\\/g, '/');
        
        // Replace direct imports
        const importPattern = new RegExp(`import\s+${component.name}\s+from\s+['"]${componentPath.replace(/\./g, '\\.')}['"](;?)`, 'g');
        if (importPattern.test(content)) {
          content = content.replace(importPattern, `import ${component.name}Lazy from '${lazyPath}'$1`);
          modified = true;
        }
        
        // Replace component usage
        const usagePattern = new RegExp(`<${component.name}([\s>])`, 'g');
        if (usagePattern.test(content)) {
          content = content.replace(usagePattern, `<${component.name}Lazy$1`);
          modified = true;
        }
      }
      
      if (modified) {
        fs.writeFileSync(file, content, 'utf8');
        this.stats.importsUpdated++;
      }
    }
    
    console.log(`Updated imports in ${this.stats.importsUpdated} files`);
  }

  async addSuspenseWrappers() {
    console.log('🎭 Adding Suspense wrappers where needed...');
    
    // Find navigation files and main app files
    const navigationFiles = glob.sync('**/navigation/**/*.{js,jsx,ts,tsx}', {
      cwd: this.srcDir,
      absolute: true
    });
    
    const appFiles = glob.sync('**/App.{js,jsx,ts,tsx}', {
      cwd: this.srcDir,
      absolute: true
    });
    
    const targetFiles = [...navigationFiles, ...appFiles];
    
    for (const file of targetFiles) {
      let content = fs.readFileSync(file, 'utf8');
      let modified = false;
      
      // Check if Suspense is already imported
      if (!content.includes('Suspense')) {
        // Add Suspense import
        const reactImportMatch = content.match(/(import\s+.*React.*from\s+['"]react['"]);?/);
        if (reactImportMatch) {
          const newImport = reactImportMatch[1].replace('React', 'React, { Suspense }');
          content = content.replace(reactImportMatch[1], newImport);
          modified = true;
        }
      }
      
      // Add global Suspense wrapper if not present
      if (!content.includes('<Suspense')) {
        // Find the main return statement
        const returnMatch = content.match(/(return\s*\(?)([\s\S]*?)(\)?;?\s*}\s*$)/);
        if (returnMatch) {
          const suspenseWrapper = `$1
    <Suspense fallback={<LoadingScreen />}>
      $2
    </Suspense>
  $3`;
          content = content.replace(returnMatch[0], suspenseWrapper);
          modified = true;
        }
      }
      
      if (modified) {
        fs.writeFileSync(file, content, 'utf8');
        this.stats.suspenseWrappersAdded++;
      }
    }
    
    console.log(`Added Suspense wrappers to ${this.stats.suspenseWrappersAdded} files`);
  }

  async createLazyLoadingUtils() {
    console.log('🛠️ Creating lazy loading utilities...');
    
    // Create LoadingScreen component
    const loadingScreenPath = path.join(this.srcDir, 'components', 'common', 'LoadingScreen.js');
    const loadingScreenDir = path.dirname(loadingScreenPath);
    
    if (!fs.existsSync(loadingScreenDir)) {
      fs.mkdirSync(loadingScreenDir, { recursive: true });
    }
    
    const loadingScreenContent = this.generateLoadingScreen();
    fs.writeFileSync(loadingScreenPath, loadingScreenContent, 'utf8');
    
    // Create lazy loading hook
    const lazyHookPath = path.join(this.srcDir, 'hooks', 'useLazyLoading.js');
    const lazyHookDir = path.dirname(lazyHookPath);
    
    if (!fs.existsSync(lazyHookDir)) {
      fs.mkdirSync(lazyHookDir, { recursive: true });
    }
    
    const lazyHookContent = this.generateLazyHook();
    fs.writeFileSync(lazyHookPath, lazyHookContent, 'utf8');
    
    // Create lazy loading configuration
    const configPath = path.join(this.srcDir, 'config', 'lazyLoading.js');
    const configDir = path.dirname(configPath);
    
    if (!fs.existsSync(configDir)) {
      fs.mkdirSync(configDir, { recursive: true });
    }
    
    const configContent = this.generateLazyConfig();
    fs.writeFileSync(configPath, configContent, 'utf8');
    
    console.log('✅ Created lazy loading utilities');
  }

  generateLoadingScreen() {
    return `import React from 'react';
import { View, ActivityIndicator, Text, StyleSheet } from 'react-native';

const LoadingScreen = ({ message = 'Loading...', size = 'large', color = '#007AFF' }) => {
  return (
    <View style={styles.container}>
      <ActivityIndicator size={size} color={color} />
      <Text style={styles.message}>{message}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    padding: 20,
  },
  message: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
});

export default LoadingScreen;
`;
  }

  generateLazyHook() {
    return `import { useState, useEffect } from 'react';

/**
 * Custom hook for lazy loading components with loading states
 * @param {Function} importFunction - Dynamic import function
 * @param {Object} options - Configuration options
 * @returns {Object} - { Component, loading, error }
 */
export const useLazyLoading = (importFunction, options = {}) => {
  const [Component, setComponent] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  const {
    delay = 0,
    timeout = 10000,
    retries = 3,
    onLoad,
    onError
  } = options;
  
  useEffect(() => {
    let timeoutId;
    let retryCount = 0;
    
    const loadComponent = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Add artificial delay if specified
        if (delay > 0) {
          await new Promise(resolve => setTimeout(resolve, delay));
        }
        
        // Set timeout for loading
        const timeoutPromise = new Promise((_, reject) => {
          timeoutId = setTimeout(() => reject(new Error('Loading timeout')), timeout);
        });
        
        const componentModule = await Promise.race([
          importFunction(),
          timeoutPromise
        ]);
        
        clearTimeout(timeoutId);
        
        const LoadedComponent = componentModule.default || componentModule;
        setComponent(() => LoadedComponent);
        setLoading(false);
        
        if (onLoad) onLoad(LoadedComponent);
        
      } catch (err) {
        clearTimeout(timeoutId);
        
        if (retryCount < retries) {
          retryCount++;
          console.warn('Lazy loading failed, retrying (' + retryCount + '/' + retries + '):', err);
          setTimeout(loadComponent, 1000 * retryCount); // Exponential backoff
        } else {
          setError(err);
          setLoading(false);
          if (onError) onError(err);
        }
      }
    };
    
    loadComponent();
    
    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [importFunction, delay, timeout, retries, onLoad, onError]);
  
  return { Component, loading, error };
};

/**
 * Higher-order component for lazy loading
 * @param {Function} importFunction - Dynamic import function
 * @param {Object} options - Configuration options
 * @returns {React.Component} - Lazy loaded component
 */
export const withLazyLoading = (importFunction, options = {}) => {
  return (props) => {
    const { Component, loading, error } = useLazyLoading(importFunction, options);
    
    if (error) {
      const ErrorComponent = options.ErrorComponent || DefaultErrorComponent;
      return <ErrorComponent error={error} retry={() => window.location.reload()} />;
    }
    
    if (loading || !Component) {
      const LoadingComponent = options.LoadingComponent || DefaultLoadingComponent;
      return <LoadingComponent />;
    }
    
    return <Component {...props} />;
  };
};

// Default components
const DefaultLoadingComponent = () => {
  const { View, ActivityIndicator } = require('react-native');
  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
      <ActivityIndicator size="large" color="#007AFF" />
    </View>
  );
};

const DefaultErrorComponent = ({ error, retry }) => {
  const { View, Text, TouchableOpacity } = require('react-native');
  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 }}>
      <Text style={{ fontSize: 18, marginBottom: 10, textAlign: 'center' }}>Failed to load component</Text>
      <Text style={{ fontSize: 14, color: '#666', marginBottom: 20, textAlign: 'center' }}>{error.message}</Text>
      <TouchableOpacity onPress={retry} style={{ backgroundColor: '#007AFF', padding: 10, borderRadius: 5 }}>
        <Text style={{ color: 'white' }}>Retry</Text>
      </TouchableOpacity>
    </View>
  );
};
`;
  }

  generateLazyConfig() {
    return `/**
 * Lazy Loading Configuration
 * Centralized configuration for lazy loading behavior
 */

export const LAZY_LOADING_CONFIG = {
  // Global settings
  enabled: true,
  
  // Loading timeouts (in milliseconds)
  timeouts: {
    default: 10000,
    heavy: 15000,
    network: 30000
  },
  
  // Retry settings
  retries: {
    default: 3,
    critical: 5,
    optional: 1
  },
  
  // Preloading settings
  preload: {
    enabled: true,
    delay: 2000, // Preload after 2 seconds of idle time
    priority: ['ProfileScreen', 'SettingsScreen'] // High priority components to preload
  },
  
  // Component categories
  categories: {
    critical: [
      'HomeScreen',
      'LoginScreen',
      'DashboardScreen'
    ],
    heavy: [
      'ReportsScreen',
      'ChartsContainer',
      'DataVisualization'
    ],
    optional: [
      'HelpScreen',
      'AboutScreen',
      'FeedbackForm'
    ]
  },
  
  // Loading strategies
  strategies: {
    immediate: {
      delay: 0,
      timeout: 5000,
      retries: 1
    },
    lazy: {
      delay: 100,
      timeout: 10000,
      retries: 3
    },
    background: {
      delay: 2000,
      timeout: 30000,
      retries: 5
    }
  },
  
  // Error handling
  errorHandling: {
    showRetryButton: true,
    logErrors: true,
    fallbackComponent: 'ErrorBoundary'
  }
};

/**
 * Get configuration for a specific component
 * @param {string} componentName - Name of the component
 * @returns {Object} - Configuration object
 */
export const getComponentConfig = (componentName) => {
  const { categories, strategies } = LAZY_LOADING_CONFIG;
  
  if (categories.critical.includes(componentName)) {
    return { ...strategies.immediate, priority: 'critical' };
  }
  
  if (categories.heavy.includes(componentName)) {
    return { ...strategies.lazy, priority: 'heavy' };
  }
  
  if (categories.optional.includes(componentName)) {
    return { ...strategies.background, priority: 'optional' };
  }
  
  return { ...strategies.lazy, priority: 'default' };
};

/**
 * Check if lazy loading is enabled for a component
 * @param {string} componentName - Name of the component
 * @returns {boolean} - Whether lazy loading is enabled
 */
export const isLazyLoadingEnabled = (componentName) => {
  return LAZY_LOADING_CONFIG.enabled && 
         !LAZY_LOADING_CONFIG.categories.critical.includes(componentName);
};

export default LAZY_LOADING_CONFIG;
`;
  }

  printStats() {
    console.log('\n📊 Lazy Loading Implementation Results:');
    console.log(`Components processed: ${this.stats.componentsProcessed}`);
    console.log(`Lazy components created: ${this.stats.lazyComponentsCreated}`);
    console.log(`Suspense wrappers added: ${this.stats.suspenseWrappersAdded}`);
    console.log(`Imports updated: ${this.stats.importsUpdated}`);
    
    console.log('\n⚡ Lazy loading implementation completed!');
    console.log('🎯 Non-critical components are now lazy loaded');
    console.log('🔄 Suspense boundaries added for better UX');
    console.log('🛠️ Lazy loading utilities created for future use');
  }
}

// Run the lazy loading implementation
if (require.main === module) {
  const implementation = new LazyLoadingImplementation();
  implementation.implementLazyLoading().catch(console.error);
}

module.exports = LazyLoadingImplementation;