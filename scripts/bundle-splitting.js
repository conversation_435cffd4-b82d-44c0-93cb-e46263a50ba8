#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * Bundle Splitting Script
 * Implements React.lazy() for heavy components to improve initial load time
 */

class BundleSplitting {
  constructor() {
    this.srcDir = path.join(__dirname, '..', 'src');
    this.heavyComponents = [
      // Chart components
      { name: 'ReportsScreen', path: 'screens/ReportsScreen.js' },
      { name: 'FinancialReportsScreen', path: 'screens/FinancialReportsScreen.js' },
      { name: 'ProfitLossScreen', path: 'screens/ProfitLossScreen.js' },
      { name: 'TaxSummaryScreen', path: 'screens/TaxSummaryScreen.js' },
      
      // Camera/QR components
      { name: 'QRScannerScreen', path: 'screens/QRScannerScreen.js' },
      { name: 'QRScanner', path: 'components/QRScanner.js' },
      { name: 'ImagePicker', path: 'components/ImagePicker.js' },
      
      // Large form components
      { name: 'CreateOrderScreen', path: 'screens/CreateOrderScreen.tsx' },
      { name: 'UnifiedInventoryScreen', path: 'screens/UnifiedInventoryScreen.js' },
      { name: 'DataManagementScreen', path: 'screens/DataManagementScreen.js' }
    ];
  }

  async implementLazyLoading() {
    console.log('🚀 Implementing bundle splitting with React.lazy()...');
    
    // Create lazy loading wrapper components
    await this.createLazyComponents();
    
    // Update navigation to use lazy components
    await this.updateNavigation();
    
    console.log('✨ Bundle splitting implementation completed!');
  }

  async createLazyComponents() {
    const lazyDir = path.join(this.srcDir, 'components', 'lazy');
    
    // Create lazy components directory
    if (!fs.existsSync(lazyDir)) {
      fs.mkdirSync(lazyDir, { recursive: true });
    }

    for (const component of this.heavyComponents) {
      const lazyComponentContent = this.generateLazyComponent(component);
      const lazyFilePath = path.join(lazyDir, `Lazy${component.name}.js`);
      
      fs.writeFileSync(lazyFilePath, lazyComponentContent, 'utf8');
      console.log(`✅ Created lazy component: Lazy${component.name}.js`);
    }
  }

  generateLazyComponent(component) {
    return `import React, { Suspense } from 'react';
import { View, ActivityIndicator } from 'react-native';
import { Text } from 'react-native-paper';

// Lazy load the heavy component
const ${component.name} = React.lazy(() => import('../../${component.path.replace('.js', '').replace('.tsx', '')}'));

// Loading fallback component
const LoadingFallback = () => (
  <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
    <ActivityIndicator size="large" />
    <Text style={{ marginTop: 16 }}>Loading ${component.name}...</Text>
  </View>
);

// Lazy wrapper component
const Lazy${component.name} = (props) => {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <${component.name} {...props} />
    </Suspense>
  );
};

export default Lazy${component.name};
`;
  }

  async updateNavigation() {
    const navigatorPath = path.join(this.srcDir, 'navigation', 'AppNavigator.tsx');
    
    if (!fs.existsSync(navigatorPath)) {
      console.log('⚠️ AppNavigator.tsx not found, skipping navigation update');
      return;
    }

    let content = fs.readFileSync(navigatorPath, 'utf8');
    
    // Add lazy component imports
    const lazyImports = this.heavyComponents
      .map(comp => `import Lazy${comp.name} from '../components/lazy/Lazy${comp.name}';`)
      .join('\n');
    
    // Find the import section and add lazy imports
    const importRegex = /(import.*from.*;\s*\n)+/;
    if (importRegex.test(content)) {
      content = content.replace(importRegex, (match) => match + lazyImports + '\n\n');
    }

    // Replace heavy component references with lazy versions
    this.heavyComponents.forEach(comp => {
      const componentRegex = new RegExp(`component={${comp.name}}`, 'g');
      content = content.replace(componentRegex, `component={Lazy${comp.name}}`);
    });

    fs.writeFileSync(navigatorPath, content, 'utf8');
    console.log('✅ Updated AppNavigator.tsx with lazy components');
  }
}

// Create index file for easy imports
function createLazyIndex() {
  const lazyDir = path.join(__dirname, '..', 'src', 'components', 'lazy');
  const indexContent = `// Lazy loaded components for bundle splitting
// Auto-generated by bundle-splitting.js

${[
  'ReportsScreen', 'FinancialReportsScreen', 'ProfitLossScreen', 'TaxSummaryScreen',
  'QRScannerScreen', 'QRScanner', 'ImagePicker', 'CreateOrderScreen', 
  'UnifiedInventoryScreen', 'DataManagementScreen'
].map(name => `export { default as Lazy${name} } from './Lazy${name}';`).join('\n')}
`;
  
  fs.writeFileSync(path.join(lazyDir, 'index.js'), indexContent, 'utf8');
  console.log('✅ Created lazy components index file');
}

// Run the bundle splitting
if (require.main === module) {
  const bundleSplitter = new BundleSplitting();
  bundleSplitter.implementLazyLoading()
    .then(() => createLazyIndex())
    .catch(console.error);
}

module.exports = BundleSplitting;