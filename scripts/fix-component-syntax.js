#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const glob = require('glob');

/**
 * Fix Component Syntax Issues
 * Specifically targets the remaining syntax issues in split components
 */

class ComponentSyntaxFixer {
  constructor() {
    this.projectDir = path.join(__dirname, '..');
    this.srcDir = path.join(this.projectDir, 'src');
    this.stats = {
      filesFixed: 0,
      syntaxErrorsFixed: 0,
      importsFixed: 0
    };
  }

  async fixAllComponents() {
    console.log('🔧 Fixing component syntax issues...');
    
    // Target specific component directories
    const componentDirs = [
      'createorderscreen',
      'reportsscreen', 
      'financialreportsscreen',
      'unifiedinventoryscreen',
      'datamanagementscreen'
    ];
    
    for (const dir of componentDirs) {
      const dirPath = path.join(this.srcDir, 'components', dir);
      
      if (fs.existsSync(dirPath)) {
        await this.fixComponentDirectory(dirPath);
      }
    }
    
    this.printStats();
  }

  async fixComponentDirectory(dirPath) {
    const files = fs.readdirSync(dirPath)
      .filter(file => file.endsWith('.js') && file !== 'index.js');
    
    console.log(`Fixing ${files.length} files in ${path.basename(dirPath)}`);
    
    for (const file of files) {
      const filePath = path.join(dirPath, file);
      await this.fixComponentFile(filePath);
    }
  }

  async fixComponentFile(filePath) {
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      const originalContent = content;
      let modified = false;
      
      // Fix import consolidation
      content = this.fixImports(content);
      if (content !== originalContent) {
        this.stats.importsFixed++;
        modified = true;
      }
      
      // Fix style references
      content = this.fixStyleReferences(content);
      
      // Fix StyleSheet syntax errors
      content = this.fixStyleSheetSyntax(content);
      if (content !== originalContent) {
        this.stats.syntaxErrorsFixed++;
        modified = true;
      }
      
      if (modified) {
        fs.writeFileSync(filePath, content, 'utf8');
        this.stats.filesFixed++;
        console.log(`✅ Fixed ${path.basename(filePath)}`);
      }
    } catch (error) {
      console.warn(`⚠️  Could not fix ${filePath}: ${error.message}`);
    }
  }

  fixImports(content) {
    let newContent = content;
    
    // Consolidate react-native-paper imports
    const paperImportRegex = /import\s+{\s*([^}]+)\s*}\s+from\s+['"]react-native-paper['"]/g;
    const paperImports = [];
    let match;
    
    while ((match = paperImportRegex.exec(newContent)) !== null) {
      const imports = match[1].split(',').map(imp => imp.trim());
      paperImports.push(...imports);
    }
    
    // Also check for separate paper imports
    const separatePaperRegex = /import\s+{\s*(\w+)\s*}\s+from\s+['"]react-native-paper['"]/g;
    while ((match = separatePaperRegex.exec(newContent)) !== null) {
      paperImports.push(match[1]);
    }
    
    if (paperImports.length > 0) {
      // Remove all existing paper imports
      newContent = newContent.replace(/import\s+{[^}]*}\s+from\s+['"]react-native-paper['"];?\n?/g, '');
      
      // Add consolidated import
      const uniqueImports = [...new Set(paperImports)];
      const consolidatedImport = `import { ${uniqueImports.join(', ')} } from 'react-native-paper';\n`;
      
      // Insert after React import
      if (newContent.includes("import React")) {
        newContent = newContent.replace(
          /(import React[^\n]*;\n)/,
          `$1import { View, StyleSheet } from 'react-native';\n${consolidatedImport}`
        );
      } else {
        newContent = consolidatedImport + newContent;
      }
    }
    
    // Ensure react-native imports include StyleSheet
    if (newContent.includes('StyleSheet.create') && !newContent.includes('StyleSheet')) {
      if (newContent.includes("from 'react-native'")) {
        newContent = newContent.replace(
          /(import\s+{[^}]*)}\s+from\s+['"]react-native['"]/,
          (match, imports) => {
            if (!imports.includes('StyleSheet')) {
              return imports + ', StyleSheet} from \'react-native\'';
            }
            return match;
          }
        );
      } else {
        newContent = "import { StyleSheet } from 'react-native';\n" + newContent;
      }
    }
    
    return newContent;
  }

  fixStyleReferences(content) {
    // Fix style1 references to card
    return content.replace(/styles\.style1/g, 'styles.card');
  }

  fixStyleSheetSyntax(content) {
    let newContent = content;
    
    // Fix syntax errors in StyleSheet.create
    newContent = newContent.replace(
      /const styles = StyleSheet\.create\(\{[\s\S]*?\}\);/g,
      (match) => {
        let fixed = match;
        
        // Fix trailing commas in property names
        fixed = fixed.replace(/(\w+):\s*\{,/g, '$1: {');
        
        // Remove empty style objects
        fixed = fixed.replace(/\w+:\s*\{\s*\},?\s*/g, '');
        
        // Ensure proper card style
        if (!fixed.includes('card:') && fixed.includes('StyleSheet.create')) {
          fixed = fixed.replace(
            /(StyleSheet\.create\(\{)/,
            '$1\n  card: {\n    margin: 16,\n    elevation: 2,\n  },'
          );
        }
        
        // Clean up extra commas and spacing
        fixed = fixed.replace(/,\s*\}/g, '\n}');
        fixed = fixed.replace(/\{\s*,/g, '{');
        
        return fixed;
      }
    );
    
    return newContent;
  }

  printStats() {
    console.log('\n📊 Component Syntax Fix Results:');
    console.log(`Files fixed: ${this.stats.filesFixed}`);
    console.log(`Syntax errors fixed: ${this.stats.syntaxErrorsFixed}`);
    console.log(`Imports fixed: ${this.stats.importsFixed}`);
    
    console.log('\n✅ All component syntax issues have been fixed!');
  }
}

// Run the fix script
if (require.main === module) {
  const fixer = new ComponentSyntaxFixer();
  fixer.fixAllComponents().catch(console.error);
}

module.exports = ComponentSyntaxFixer;