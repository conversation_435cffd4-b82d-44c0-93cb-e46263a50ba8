#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Fix JSX style prop bracket issues in ProductDetailsContent.js
function fixProductDetailsJSX() {
  const filePath = path.join(process.cwd(), 'src/components/content/ProductDetailsContent.js');
  
  if (!fs.existsSync(filePath)) {
    console.error('ProductDetailsContent.js not found');
    return false;
  }
  
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    console.log('Fixing JSX style prop bracket issues in ProductDetailsContent.js...');
    
    // Fix missing closing brackets in style props - most common pattern
    const styleFixes = [
      // Fix style={[styles.X, { color: theme.colors.Y } patterns
      { 
        pattern: /style=\{(\[styles\.[^,]+, \{ color: theme\.colors\.[^}]+ \})\>/g, 
        replacement: 'style={$1]}>' 
      },
      { 
        pattern: /style=\{(\[styles\.[^,]+, \{ color: theme\.colors\.[^}]+ \})\s*numberOfLines/g, 
        replacement: 'style={$1]} numberOfLines' 
      },
      { 
        pattern: /style=\{(\[styles\.[^,]+, \{ color: theme\.colors\.[^}]+ \})\s*textStyle/g, 
        replacement: 'style={$1]} textStyle' 
      },
      
      // Fix more complex style patterns
      { 
        pattern: /style=\{(\[styles\.[^,]+, \{ [^}]+ \})\>/g, 
        replacement: 'style={$1]}>' 
      },
      { 
        pattern: /style=\{(\[styles\.[^,]+, \{ [^}]+ \})\s*elevation/g, 
        replacement: 'style={$1]} elevation' 
      },
      { 
        pattern: /style=\{(\[styles\.[^,]+, \{ [^}]+ \})\s*mode/g, 
        replacement: 'style={$1]} mode' 
      },
      { 
        pattern: /style=\{(\[styles\.[^,]+, \{ [^}]+ \})\s*compact/g, 
        replacement: 'style={$1]} compact' 
      },
      
      // Fix textStyle patterns
      { 
        pattern: /textStyle=\{(\{ color: [^}]+ \})\>/g, 
        replacement: 'textStyle={$1}>' 
      },
      
      // Fix specific backgroundColor patterns
      { 
        pattern: /style=\{(\[styles\.[^,]+, \{ backgroundColor: [^}]+ \+ '20' \})\}/g, 
        replacement: 'style={$1]}' 
      },
      
      // Fix button label patterns
      { 
        pattern: /label: ([^}]+)\}\)\}/g, 
        replacement: 'label: $1 }))}' 
      },
      
      // Fix JSX text content issues
      { 
        pattern: />\s*([A-Z][a-z]+)\s+([A-Z][a-z]+)\s*</g, 
        replacement: '>$1 $2<' 
      },
      { 
        pattern: />\s*([A-Z][a-z]+)\s+([A-Z][a-z]+)\s+([A-Z][a-z]+)\s*</g, 
        replacement: '>$1 $2 $3<' 
      },
      
      // Fix specific patterns found in the file
      { 
        pattern: /\{product\.stock > 0 \? 'In Stock' : 'Out of Stock'\}/g, 
        replacement: '{product.stock > 0 ? "In Stock" : "Out of Stock"}' 
      },
      { 
        pattern: /\{product\.rating \|\| 'N\/A'\}/g, 
        replacement: '{product.rating || "N/A"}' 
      },
      { 
        pattern: /\{product\.deliveryTime \|\| '2-3 days'\}/g, 
        replacement: '{product.deliveryTime || "2-3 days"}' 
      },
      
      // Fix function call patterns
      { 
        pattern: /onValueChange=\{([^}]+)\}\s*buttons/g, 
        replacement: 'onValueChange={$1} buttons' 
      },
      
      // Fix array mapping patterns
      { 
        pattern: /\.map\(([^)]+)\) => \(\{/g, 
        replacement: '.map($1) => ({' 
      },
      
      // Fix object literal patterns in JSX
      { 
        pattern: /value: ([^,]+),\s*label: ([^}]+)\}\)\)/g, 
        replacement: 'value: $1, label: $2 }))' 
      }
    ];
    
    // Apply all fixes
    styleFixes.forEach((fix, index) => {
      const beforeCount = (content.match(fix.pattern) || []).length;
      if (beforeCount > 0) {
        content = content.replace(fix.pattern, fix.replacement);
        const afterCount = (content.match(fix.pattern) || []).length;
        const fixedCount = beforeCount - afterCount;
        if (fixedCount > 0) {
          console.log(`Fix ${index + 1}: Fixed ${fixedCount} occurrences`);
          modified = true;
        }
      }
    });
    
    // Additional specific fixes for this file
    const specificFixes = [
      // Fix incomplete JSX tags
      { 
        pattern: /<Text([^>]*)\s+numberOfLines=\{1\}\s*>/g, 
        replacement: '<Text$1 numberOfLines={1}>' 
      },
      { 
        pattern: /<Button([^>]*)\s+mode="contained"\s*>/g, 
        replacement: '<Button$1 mode="contained">' 
      },
      { 
        pattern: /<Button([^>]*)\s+mode="outlined"\s*>/g, 
        replacement: '<Button$1 mode="outlined">' 
      },
      
      // Fix function parameter issues
      { 
        pattern: /const renderPeekContent = \(([^)]+)\) => \(/g, 
        replacement: 'const renderPeekContent = ($1) => (' 
      },
      { 
        pattern: /const renderHalfContent = \(([^)]+)\) => \(/g, 
        replacement: 'const renderHalfContent = ($1) => (' 
      },
      { 
        pattern: /const renderFullContent = \(([^)]+)\) => \(/g, 
        replacement: 'const renderFullContent = ($1) => (' 
      }
    ];
    
    specificFixes.forEach((fix, index) => {
      const beforeCount = (content.match(fix.pattern) || []).length;
      if (beforeCount > 0) {
        content = content.replace(fix.pattern, fix.replacement);
        const afterCount = (content.match(fix.pattern) || []).length;
        const fixedCount = beforeCount - afterCount;
        if (fixedCount > 0) {
          console.log(`Specific fix ${index + 1}: Fixed ${fixedCount} occurrences`);
          modified = true;
        }
      }
    });
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log('✅ ProductDetailsContent.js fixed successfully!');
      return true;
    } else {
      console.log('No changes needed in ProductDetailsContent.js');
      return false;
    }
    
  } catch (error) {
    console.error('Error fixing ProductDetailsContent.js:', error.message);
    return false;
  }
}

// Main function
function main() {
  console.log('Starting ProductDetailsContent.js JSX fixes...');
  
  const success = fixProductDetailsJSX();
  
  if (success) {
    console.log('\n🎉 ProductDetailsContent.js has been fixed!');
    console.log('Running type check to verify improvements...');
    
    const { execSync } = require('child_process');
    try {
      const result = execSync('npm run type-check 2>&1 | grep -E "(ProductDetailsContent|Found.*errors)" | tail -5', { 
        encoding: 'utf8',
        cwd: process.cwd()
      });
      console.log('\nType check results:');
      console.log(result);
    } catch (error) {
      console.log('Type check completed - checking results...');
    }
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixProductDetailsJSX };
