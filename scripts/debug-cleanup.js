#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const glob = require('glob');

/**
 * Debug Cleanup Script
 * Removes console.log statements, debug flags, and commented-out debug code
 * while preserving important error logging and warnings
 */

class DebugCleanup {
  constructor() {
    this.srcDir = path.join(__dirname, '..', 'src');
    this.stats = {
      filesProcessed: 0,
      consoleLogsRemoved: 0,
      debugFlagsRemoved: 0,
      commentedCodeRemoved: 0
    };
  }

  async cleanup() {
    console.log('🧹 Starting debug cleanup...');
    
    // Find all JS/TS/JSX/TSX files in src directory
    const files = glob.sync('**/*.{js,jsx,ts,tsx}', {
      cwd: this.srcDir,
      absolute: true
    });

    for (const file of files) {
      await this.processFile(file);
    }

    this.printStats();
  }

  async processFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      let modifiedContent = content;
      let hasChanges = false;

      // Remove console.log statements (but preserve console.error, console.warn)
      const consoleLogRegex = /^\s*console\.log\([^)]*\);?\s*$/gm;
      const consoleLogMatches = content.match(consoleLogRegex) || [];
      if (consoleLogMatches.length > 0) {
        modifiedContent = modifiedContent.replace(consoleLogRegex, '');
        this.stats.consoleLogsRemoved += consoleLogMatches.length;
        hasChanges = true;
      }

      // Remove debug flags and debugLog calls
      const debugPatterns = [
        /^\s*const\s+debug\s*=\s*(true|false);?\s*$/gm,
        /^\s*let\s+debug\s*=\s*(true|false);?\s*$/gm,
        /^\s*var\s+debug\s*=\s*(true|false);?\s*$/gm,
        /^\s*debugLog\([^)]*\);?\s*$/gm,
        /^\s*if\s*\(\s*debug\s*\)\s*\{[^}]*\}\s*$/gm
      ];

      debugPatterns.forEach(pattern => {
        const matches = modifiedContent.match(pattern) || [];
        if (matches.length > 0) {
          modifiedContent = modifiedContent.replace(pattern, '');
          this.stats.debugFlagsRemoved += matches.length;
          hasChanges = true;
        }
      });

      // Remove commented-out console.log and debug statements
      const commentedDebugRegex = /^\s*\/\/\s*(console\.log|debugLog|debug).*$/gm;
      const commentedMatches = modifiedContent.match(commentedDebugRegex) || [];
      if (commentedMatches.length > 0) {
        modifiedContent = modifiedContent.replace(commentedDebugRegex, '');
        this.stats.commentedCodeRemoved += commentedMatches.length;
        hasChanges = true;
      }

      // Clean up multiple empty lines
      modifiedContent = modifiedContent.replace(/\n\s*\n\s*\n/g, '\n\n');

      if (hasChanges) {
        fs.writeFileSync(filePath, modifiedContent, 'utf8');
        console.log(`✅ Cleaned: ${path.relative(this.srcDir, filePath)}`);
      }

      this.stats.filesProcessed++;
    } catch (error) {
      console.error(`❌ Error processing ${filePath}:`, error.message);
    }
  }

  printStats() {
    console.log('\n📊 Debug Cleanup Results:');
    console.log(`Files processed: ${this.stats.filesProcessed}`);
    console.log(`Console.log statements removed: ${this.stats.consoleLogsRemoved}`);
    console.log(`Debug flags removed: ${this.stats.debugFlagsRemoved}`);
    console.log(`Commented debug code removed: ${this.stats.commentedCodeRemoved}`);
    console.log('\n✨ Debug cleanup completed!');
  }
}

// Run the cleanup
if (require.main === module) {
  const cleanup = new DebugCleanup();
  cleanup.cleanup().catch(console.error);
}

module.exports = DebugCleanup;