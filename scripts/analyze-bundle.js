
// Bundle analyzer script
const { execSync } = require('child_process');

console.log('📊 Analyzing bundle size...');
try {
  execSync('npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output android-release.bundle --assets-dest android-release/', { stdio: 'inherit' });
  console.log('✅ Bundle analysis complete');
} catch (error) {
  console.error('❌ Bundle analysis failed:', error.message);
}
