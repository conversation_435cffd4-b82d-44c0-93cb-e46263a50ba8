#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const glob = require('glob');

/**
 * Performance Hooks Script
 * Adds useCallback to event handlers and React.memo to pure components
 */

class PerformanceHooks {
  constructor() {
    this.srcDir = path.join(__dirname, '..', 'src');
    this.stats = {
      filesProcessed: 0,
      useCallbackAdded: 0,
      reactMemoAdded: 0,
      importsUpdated: 0
    };
  }

  async optimizePerformance() {
    console.log('⚡ Adding performance hooks...');
    
    // Find all component files
    const files = glob.sync('**/*.{js,jsx,ts,tsx}', {
      cwd: this.srcDir,
      absolute: true
    }).filter(file => {
      const content = fs.readFileSync(file, 'utf8');
      return content.includes('export default') && 
             (content.includes('function') || content.includes('const') || content.includes('=>'))
             && !file.includes('lazy/');
    });

    for (const file of files) {
      await this.processFile(file);
    }

    this.printStats();
  }

  async processFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      let modifiedContent = content;
      let hasChanges = false;

      // Check if it's a React component
      if (!this.isReactComponent(content)) {
        return;
      }

      // Add useCallback import if needed
      if (this.needsUseCallback(content) && !content.includes('useCallback')) {
        modifiedContent = this.addUseCallbackImport(modifiedContent);
        hasChanges = true;
        this.stats.importsUpdated++;
      }

      // Add useCallback to event handlers
      const callbackResult = this.addUseCallbackToHandlers(modifiedContent);
      if (callbackResult.modified) {
        modifiedContent = callbackResult.content;
        hasChanges = true;
        this.stats.useCallbackAdded += callbackResult.count;
      }

      // Add React.memo to pure components
      const memoResult = this.addReactMemo(modifiedContent);
      if (memoResult.modified) {
        modifiedContent = memoResult.content;
        hasChanges = true;
        this.stats.reactMemoAdded++;
      }

      if (hasChanges) {
        fs.writeFileSync(filePath, modifiedContent, 'utf8');
        console.log(`✅ Optimized: ${path.relative(this.srcDir, filePath)}`);
      }

      this.stats.filesProcessed++;
    } catch (error) {
      console.error(`❌ Error processing ${filePath}:`, error.message);
    }
  }

  isReactComponent(content) {
    return content.includes('import React') || 
           content.includes('from \'react\'') ||
           content.includes('from "react"') ||
           content.includes('JSX.Element') ||
           content.includes('<') && content.includes('/>');
  }

  needsUseCallback(content) {
    const handlerPatterns = [
      /const\s+\w*[Hh]andler\s*=/,
      /const\s+on\w+\s*=/,
      /const\s+handle\w+\s*=/,
      /\s+on\w+\s*=\s*{/,
      /\s+handle\w+\s*=\s*{/
    ];
    
    return handlerPatterns.some(pattern => pattern.test(content));
  }

  addUseCallbackImport(content) {
    // Find React import and add useCallback
    const reactImportRegex = /import React(?:,\s*{([^}]*)})?\s+from\s+['"]react['"]/;
    const match = content.match(reactImportRegex);
    
    if (match) {
      if (match[1]) {
        // Already has named imports, add useCallback
        const existingImports = match[1].trim();
        if (!existingImports.includes('useCallback')) {
          const newImports = existingImports ? `${existingImports}, useCallback` : 'useCallback';
          return content.replace(reactImportRegex, `import React, { ${newImports} } from 'react'`);
        }
      } else {
        // No named imports, add useCallback
        return content.replace(reactImportRegex, `import React, { useCallback } from 'react'`);
      }
    }
    
    return content;
  }

  addUseCallbackToHandlers(content) {
    let modifiedContent = content;
    let count = 0;
    let modified = false;

    // Pattern for event handlers that should use useCallback
    const handlerPatterns = [
      {
        regex: /(const\s+(\w*[Hh]andler|on\w+|handle\w+)\s*=\s*)([^;]+;)/g,
        replacement: (match, declaration, name, implementation) => {
          if (implementation.includes('useCallback')) return match;
          count++;
          modified = true;
          return `${declaration}useCallback(${implementation.replace(';', '')}, []);`;
        }
      }
    ];

    handlerPatterns.forEach(({ regex, replacement }) => {
      modifiedContent = modifiedContent.replace(regex, replacement);
    });

    return { content: modifiedContent, modified, count };
  }

  addReactMemo(content) {
    // Check if component is already memoized or is a class component
    if (content.includes('React.memo') || 
        content.includes('class ') || 
        content.includes('Component') ||
        content.includes('useState') || 
        content.includes('useEffect')) {
      return { content, modified: false };
    }

    // Find functional component export
    const exportRegex = /export\s+default\s+(\w+)/;
    const match = content.match(exportRegex);
    
    if (match) {
      const componentName = match[1];
      const newExport = `export default React.memo(${componentName})`;
      const modifiedContent = content.replace(exportRegex, newExport);
      return { content: modifiedContent, modified: true };
    }

    return { content, modified: false };
  }

  printStats() {
    console.log('\n📊 Performance Optimization Results:');
    console.log(`Files processed: ${this.stats.filesProcessed}`);
    console.log(`useCallback hooks added: ${this.stats.useCallbackAdded}`);
    console.log(`React.memo optimizations: ${this.stats.reactMemoAdded}`);
    console.log(`Import statements updated: ${this.stats.importsUpdated}`);
    console.log('\n⚡ Performance optimization completed!');
  }
}

// Run the performance optimization
if (require.main === module) {
  const optimizer = new PerformanceHooks();
  optimizer.optimizePerformance().catch(console.error);
}

module.exports = PerformanceHooks;