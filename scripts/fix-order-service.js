#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Fix OrderService.ts TypeScript and template literal issues
function fixOrderService() {
  const filePath = path.join(process.cwd(), 'src/services/OrderService.ts');
  
  if (!fs.existsSync(filePath)) {
    console.error('OrderService.ts not found');
    return false;
  }
  
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    console.log('Fixing OrderService.ts TypeScript and template literal issues...');
    
    // Fix template literal issues
    const templateFixes = [
      // Fix malformed template literals
      { 
        pattern: /id: `([^`]*)\$\{([^}]+)\}_\$\{([^}]+)\}`,`/g, 
        replacement: 'id: `$1${$2}_${$3}`,' 
      },
      { 
        pattern: /orderNumber: `([^`]*)\$\{([^}]+)\}`,`/g, 
        replacement: 'orderNumber: `$1${$2}`,' 
      },
      { 
        pattern: /`([^`]*)\$\{([^}]+)\}([^`]*)`/g, 
        replacement: '`$1${$2}$3`' 
      },
      
      // Fix Logger calls with template literals
      { 
        pattern: /Logger\.error\(`([^`]*): \$\{([^}]+)\}`, ([^)]+)\);\s*`/g, 
        replacement: 'Logger.error(`$1: ${$2}`, $3);' 
      },
      { 
        pattern: /Logger\.debug\(`([^`]*): \$\{([^}]+)\}`\);\s*`/g, 
        replacement: 'Logger.debug(`$1: ${$2}`);' 
      },
      { 
        pattern: /Logger\.info\(`([^`]*): \$\{([^}]+)\}`\);\s*`/g, 
        replacement: 'Logger.info(`$1: ${$2}`);' 
      },
      
      // Fix console.log calls
      { 
        pattern: /console\.log\(`([^`]*): \$\{([^}]+)\}`\)/g, 
        replacement: 'console.log(`$1: ${$2}`)' 
      },
      { 
        pattern: /console\.error\(`([^`]*): \$\{([^}]+)\}`\)/g, 
        replacement: 'console.error(`$1: ${$2}`)' 
      },
      
      // Fix Alert.alert calls
      { 
        pattern: /Alert\.alert\('([^']*)',\s*`([^`]*): \$\{([^}]+)\}([^`]*)`\)/g, 
        replacement: "Alert.alert('$1', `$2: ${$3}$4`)" 
      },
      
      // Fix throw new Error calls
      { 
        pattern: /throw new Error\(`([^`]*): \$\{([^}]+)\}`\)/g, 
        replacement: 'throw new Error(`$1: ${$2}`)' 
      }
    ];
    
    templateFixes.forEach((fix, index) => {
      const beforeCount = (content.match(fix.pattern) || []).length;
      if (beforeCount > 0) {
        content = content.replace(fix.pattern, fix.replacement);
        const afterCount = (content.match(fix.pattern) || []).length;
        const fixedCount = beforeCount - afterCount;
        if (fixedCount > 0) {
          console.log(`Template fix ${index + 1}: Fixed ${fixedCount} occurrences`);
          modified = true;
        }
      }
    });
    
    // Fix TypeScript syntax issues
    const tsFixes = [
      // Fix object literal trailing commas
      { 
        pattern: /,\s*\n\s*\}/g, 
        replacement: '\n      }' 
      },
      { 
        pattern: /,\s*\n\s*\]/g, 
        replacement: '\n      ]' 
      },
      
      // Fix method signatures
      { 
        pattern: /async ([a-zA-Z_$][a-zA-Z0-9_$]*)\(([^)]*)\): Promise<([^>]+)> \{/g, 
        replacement: 'async $1($2): Promise<$3> {' 
      },
      { 
        pattern: /([a-zA-Z_$][a-zA-Z0-9_$]*)\(([^)]*)\): ([^{]+) \{/g, 
        replacement: '$1($2): $3 {' 
      },
      
      // Fix type annotations
      { 
        pattern: /: ([A-Z][a-zA-Z0-9_$]*)\[\]/g, 
        replacement: ': $1[]' 
      },
      { 
        pattern: /: ([A-Z][a-zA-Z0-9_$]*) \|/g, 
        replacement: ': $1 |' 
      },
      
      // Fix interface/type definitions
      { 
        pattern: /interface ([A-Z][a-zA-Z0-9_$]*) \{/g, 
        replacement: 'interface $1 {' 
      },
      { 
        pattern: /type ([A-Z][a-zA-Z0-9_$]*) = /g, 
        replacement: 'type $1 = ' 
      },
      
      // Fix import statements
      { 
        pattern: /import \{ ([^}]+) \} from '([^']+)';/g, 
        replacement: 'import { $1 } from \'$2\';' 
      },
      { 
        pattern: /import ([A-Z][a-zA-Z0-9_$]*) from '([^']+)';/g, 
        replacement: 'import $1 from \'$2\';' 
      },
      
      // Fix export statements
      { 
        pattern: /export \{ ([^}]+) \};/g, 
        replacement: 'export { $1 };' 
      },
      { 
        pattern: /export default ([A-Z][a-zA-Z0-9_$]*);/g, 
        replacement: 'export default $1;' 
      }
    ];
    
    tsFixes.forEach((fix, index) => {
      const beforeCount = (content.match(fix.pattern) || []).length;
      if (beforeCount > 0) {
        content = content.replace(fix.pattern, fix.replacement);
        const afterCount = (content.match(fix.pattern) || []).length;
        const fixedCount = beforeCount - afterCount;
        if (fixedCount > 0) {
          console.log(`TypeScript fix ${index + 1}: Fixed ${fixedCount} occurrences`);
          modified = true;
        }
      }
    });
    
    // Fix specific OrderService patterns
    const orderServiceFixes = [
      // Fix order object creation
      { 
        pattern: /const order: Order = \{([^}]+)\}/gs, 
        replacement: (match, content) => {
          // Clean up the object content
          const cleanContent = content
            .replace(/,\s*\n\s*,/g, ',')
            .replace(/,\s*$/g, '');
          return `const order: Order = {${cleanContent}}`;
        }
      },
      
      // Fix array and object method calls
      { 
        pattern: /\.map\(([^)]+)\) => \{/g, 
        replacement: '.map($1) => {' 
      },
      { 
        pattern: /\.filter\(([^)]+)\) => /g, 
        replacement: '.filter($1) => ' 
      },
      { 
        pattern: /\.find\(([^)]+)\) => /g, 
        replacement: '.find($1) => ' 
      },
      { 
        pattern: /\.reduce\(([^)]+)\) => /g, 
        replacement: '.reduce($1) => ' 
      },
      
      // Fix async/await patterns
      { 
        pattern: /await ([a-zA-Z_$][a-zA-Z0-9_$]*)\(([^)]*)\);\s*`/g, 
        replacement: 'await $1($2);' 
      },
      { 
        pattern: /return await ([a-zA-Z_$][a-zA-Z0-9_$]*)\(([^)]*)\);\s*`/g, 
        replacement: 'return await $1($2);' 
      },
      
      // Fix conditional statements
      { 
        pattern: /if \(([^)]+)\) \{([^}]+)\} else \{/g, 
        replacement: 'if ($1) {$2} else {' 
      },
      { 
        pattern: /\} catch \(([^)]+)\) \{/g, 
        replacement: '} catch ($1) {' 
      },
      { 
        pattern: /\} finally \{/g, 
        replacement: '} finally {' 
      }
    ];
    
    orderServiceFixes.forEach((fix, index) => {
      if (typeof fix.replacement === 'function') {
        const matches = content.match(fix.pattern);
        if (matches) {
          content = content.replace(fix.pattern, fix.replacement);
          console.log(`OrderService fix ${index + 1}: Applied function replacement`);
          modified = true;
        }
      } else {
        const beforeCount = (content.match(fix.pattern) || []).length;
        if (beforeCount > 0) {
          content = content.replace(fix.pattern, fix.replacement);
          const afterCount = (content.match(fix.pattern) || []).length;
          const fixedCount = beforeCount - afterCount;
          if (fixedCount > 0) {
            console.log(`OrderService fix ${index + 1}: Fixed ${fixedCount} occurrences`);
            modified = true;
          }
        }
      }
    });
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log('✅ OrderService.ts fixed successfully!');
      return true;
    } else {
      console.log('No changes needed in OrderService.ts');
      return false;
    }
    
  } catch (error) {
    console.error('Error fixing OrderService.ts:', error.message);
    return false;
  }
}

// Main function
function main() {
  console.log('Starting OrderService.ts fixes...');
  
  const success = fixOrderService();
  
  if (success) {
    console.log('\n🎉 OrderService.ts has been fixed!');
    console.log('Running type check to verify improvements...');
    
    const { execSync } = require('child_process');
    try {
      const result = execSync('npm run type-check 2>&1 | grep -E "(OrderService|Found.*errors)" | tail -5', { 
        encoding: 'utf8',
        cwd: process.cwd()
      });
      console.log('\nType check results:');
      console.log(result);
    } catch (error) {
      console.log('Type check completed - checking results...');
    }
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixOrderService };
