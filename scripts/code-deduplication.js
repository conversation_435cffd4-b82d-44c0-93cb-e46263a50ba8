#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const glob = require('glob');
const crypto = require('crypto');

/**
 * Code Deduplication Script
 * Identifies and consolidates duplicate functions and code blocks
 */

class CodeDeduplication {
  constructor() {
    this.projectDir = path.join(__dirname, '..');
    this.srcDir = path.join(this.projectDir, 'src');
    this.stats = {
      filesAnalyzed: 0,
      duplicateFunctions: 0,
      duplicateBlocks: 0,
      utilsCreated: 0,
      importsUpdated: 0
    };
    
    this.functionHashes = new Map();
    this.codeBlocks = new Map();
    this.duplicates = [];
  }

  async deduplicateCode() {
    console.log('🔍 Analyzing code for duplicates...');
    
    // Find all JavaScript/TypeScript files
    const files = await this.findSourceFiles();
    
    // Extract and analyze functions
    await this.analyzeFunctions(files);
    
    // Extract and analyze code blocks
    await this.analyzeCodeBlocks(files);
    
    // Identify duplicates
    await this.identifyDuplicates();
    
    // Create utility functions
    await this.createUtilityFunctions();
    
    // Update imports and references
    await this.updateReferences();
    
    // Generate deduplication report
    await this.generateReport();
    
    this.printStats();
  }

  async findSourceFiles() {
    console.log('📁 Finding source files...');
    
    const files = glob.sync('**/*.{js,jsx,ts,tsx}', {
      cwd: this.srcDir,
      absolute: true,
      ignore: ['**/node_modules/**', '**/dist/**', '**/build/**']
    });
    
    this.stats.filesAnalyzed = files.length;
    console.log(`Found ${files.length} source files`);
    
    return files;
  }

  async analyzeFunctions(files) {
    console.log('🔧 Analyzing functions...');
    
    for (const file of files) {
      const content = fs.readFileSync(file, 'utf8');
      const functions = this.extractFunctions(content, file);
      
      for (const func of functions) {
        const hash = this.generateHash(func.body);
        
        if (!this.functionHashes.has(hash)) {
          this.functionHashes.set(hash, []);
        }
        
        this.functionHashes.get(hash).push({
          ...func,
          file: file,
          hash: hash
        });
      }
    }
    
    console.log(`Analyzed ${this.functionHashes.size} unique function signatures`);
  }

  extractFunctions(content, file) {
    const functions = [];
    
    // Regular function declarations
    const functionRegex = /(?:export\s+)?(?:async\s+)?function\s+(\w+)\s*\([^)]*\)\s*{([^{}]*(?:{[^{}]*}[^{}]*)*)}/g;
    let match;
    
    while ((match = functionRegex.exec(content)) !== null) {
      functions.push({
        name: match[1],
        body: match[2].trim(),
        fullMatch: match[0],
        type: 'function',
        line: this.getLineNumber(content, match.index)
      });
    }
    
    // Arrow functions
    const arrowRegex = /(?:const|let|var)\s+(\w+)\s*=\s*(?:\([^)]*\)|\w+)\s*=>\s*{([^{}]*(?:{[^{}]*}[^{}]*)*)}/g;
    
    while ((match = arrowRegex.exec(content)) !== null) {
      functions.push({
        name: match[1],
        body: match[2].trim(),
        fullMatch: match[0],
        type: 'arrow',
        line: this.getLineNumber(content, match.index)
      });
    }
    
    // Method definitions in classes/objects
    const methodRegex = /(\w+)\s*\([^)]*\)\s*{([^{}]*(?:{[^{}]*}[^{}]*)*)}/g;
    
    while ((match = methodRegex.exec(content)) !== null) {
      // Skip if it's already captured as a function
      if (!functions.some(f => f.fullMatch.includes(match[0]))) {
        functions.push({
          name: match[1],
          body: match[2].trim(),
          fullMatch: match[0],
          type: 'method',
          line: this.getLineNumber(content, match.index)
        });
      }
    }
    
    return functions;
  }

  async analyzeCodeBlocks(files) {
    console.log('📦 Analyzing code blocks...');
    
    for (const file of files) {
      const content = fs.readFileSync(file, 'utf8');
      const blocks = this.extractCodeBlocks(content, file);
      
      for (const block of blocks) {
        const hash = this.generateHash(block.code);
        
        if (!this.codeBlocks.has(hash)) {
          this.codeBlocks.set(hash, []);
        }
        
        this.codeBlocks.get(hash).push({
          ...block,
          file: file,
          hash: hash
        });
      }
    }
    
    console.log(`Analyzed ${this.codeBlocks.size} unique code blocks`);
  }

  extractCodeBlocks(content, file) {
    const blocks = [];
    
    // Extract common patterns
    const patterns = [
      // API call patterns
      {
        name: 'api_call',
        regex: /(?:fetch|axios|api)\s*\([^)]+\)[\s\S]*?(?:then|catch|await)[\s\S]*?;/g
      },
      // Validation patterns
      {
        name: 'validation',
        regex: /if\s*\([^)]*(?:validate|check|verify)[^)]*\)[\s\S]*?{[^{}]*}/g
      },
      // Error handling patterns
      {
        name: 'error_handling',
        regex: /(?:try|catch)\s*{[\s\S]*?}(?:\s*catch\s*{[\s\S]*?})?/g
      },
      // State management patterns
      {
        name: 'state_management',
        regex: /(?:useState|useEffect|useReducer)\s*\([^)]*\)[\s\S]*?;/g
      },
      // Navigation patterns
      {
        name: 'navigation',
        regex: /navigation\.(?:navigate|push|replace|goBack)\s*\([^)]*\)/g
      },
      // Style patterns
      {
        name: 'styles',
        regex: /StyleSheet\.create\s*\({[\s\S]*?}\)/g
      }
    ];
    
    for (const pattern of patterns) {
      let match;
      while ((match = pattern.regex.exec(content)) !== null) {
        if (match[0].length > 50) { // Only consider substantial blocks
          blocks.push({
            type: pattern.name,
            code: match[0].trim(),
            line: this.getLineNumber(content, match.index)
          });
        }
      }
    }
    
    return blocks;
  }

  generateHash(code) {
    // Normalize code by removing whitespace and comments
    const normalized = code
      .replace(/\/\*[\s\S]*?\*\//g, '') // Remove block comments
      .replace(/\/\/.*$/gm, '') // Remove line comments
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();
    
    return crypto.createHash('md5').update(normalized).digest('hex');
  }

  getLineNumber(content, index) {
    return content.substring(0, index).split('\n').length;
  }

  async identifyDuplicates() {
    console.log('🔍 Identifying duplicates...');
    
    // Find duplicate functions
    for (const [hash, functions] of this.functionHashes) {
      if (functions.length > 1) {
        this.duplicates.push({
          type: 'function',
          hash: hash,
          count: functions.length,
          items: functions,
          name: functions[0].name
        });
        this.stats.duplicateFunctions++;
      }
    }
    
    // Find duplicate code blocks
    for (const [hash, blocks] of this.codeBlocks) {
      if (blocks.length > 1) {
        this.duplicates.push({
          type: 'block',
          hash: hash,
          count: blocks.length,
          items: blocks,
          name: blocks[0].type
        });
        this.stats.duplicateBlocks++;
      }
    }
    
    console.log(`Found ${this.stats.duplicateFunctions} duplicate functions`);
    console.log(`Found ${this.stats.duplicateBlocks} duplicate code blocks`);
  }

  async createUtilityFunctions() {
    console.log('🛠️ Creating utility functions...');
    
    // Create utils directory
    const utilsDir = path.join(this.srcDir, 'utils', 'common');
    if (!fs.existsSync(utilsDir)) {
      fs.mkdirSync(utilsDir, { recursive: true });
    }
    
    // Group duplicates by category
    const categories = {
      validation: [],
      api: [],
      navigation: [],
      formatting: [],
      helpers: []
    };
    
    for (const duplicate of this.duplicates) {
      if (duplicate.type === 'function') {
        const category = this.categorizeFunction(duplicate);
        categories[category].push(duplicate);
      }
    }
    
    // Create utility files for each category
    for (const [category, duplicates] of Object.entries(categories)) {
      if (duplicates.length > 0) {
        await this.createUtilityFile(category, duplicates, utilsDir);
        this.stats.utilsCreated++;
      }
    }
    
    // Create index file
    await this.createUtilsIndex(utilsDir);
  }

  categorizeFunction(duplicate) {
    const name = duplicate.name.toLowerCase();
    const body = duplicate.items[0].body.toLowerCase();
    
    if (name.includes('validate') || name.includes('check') || body.includes('validate')) {
      return 'validation';
    }
    
    if (name.includes('api') || name.includes('fetch') || body.includes('fetch') || body.includes('axios')) {
      return 'api';
    }
    
    if (name.includes('navigate') || body.includes('navigation')) {
      return 'navigation';
    }
    
    if (name.includes('format') || name.includes('parse') || name.includes('convert')) {
      return 'formatting';
    }
    
    return 'helpers';
  }

  async createUtilityFile(category, duplicates, utilsDir) {
    const fileName = `${category}Utils.js`;
    const filePath = path.join(utilsDir, fileName);
    
    let content = `/**\n * ${category.charAt(0).toUpperCase() + category.slice(1)} Utilities\n * Auto-generated by code-deduplication.js\n */\n\n`;
    
    for (const duplicate of duplicates) {
      const func = duplicate.items[0]; // Use the first occurrence as template
      const utilName = this.generateUtilName(func.name, category);
      
      content += `/**\n * ${func.name} - Extracted from ${duplicate.count} duplicate occurrences\n */\n`;
      content += `export const ${utilName} = ${func.fullMatch.replace(/^(?:export\s+)?/, '')}\n\n`;
    }
    
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Created ${fileName} with ${duplicates.length} utilities`);
  }

  generateUtilName(originalName, category) {
    // Generate a more descriptive utility name
    const prefix = category === 'helpers' ? '' : category;
    const cleanName = originalName.replace(/^(handle|on|do|get|set|is|has|can)/, '');
    
    return prefix ? `${prefix}${cleanName.charAt(0).toUpperCase() + cleanName.slice(1)}` : cleanName;
  }

  async createUtilsIndex(utilsDir) {
    const indexPath = path.join(utilsDir, 'index.js');
    
    const utilFiles = fs.readdirSync(utilsDir)
      .filter(file => file.endsWith('Utils.js'))
      .map(file => file.replace('.js', ''));
    
    let content = '// Common utilities index\n// Auto-generated by code-deduplication.js\n\n';
    
    for (const utilFile of utilFiles) {
      content += `export * from './${utilFile}';\n`;
    }
    
    fs.writeFileSync(indexPath, content, 'utf8');
  }

  async updateReferences() {
    console.log('📝 Updating references to use utility functions...');
    
    // This would be a complex operation requiring AST parsing
    // For now, we'll generate a migration guide
    await this.generateMigrationGuide();
  }

  async generateMigrationGuide() {
    const guidePath = path.join(this.projectDir, 'docs', 'DEDUPLICATION_MIGRATION.md');
    const docsDir = path.dirname(guidePath);
    
    if (!fs.existsSync(docsDir)) {
      fs.mkdirSync(docsDir, { recursive: true });
    }
    
    let content = `# Code Deduplication Migration Guide\n\n`;
    content += `## Overview\n`;
    content += `This guide helps you migrate duplicate code to use the newly created utility functions.\n\n`;
    content += `## Statistics\n`;
    content += `- **Duplicate Functions Found:** ${this.stats.duplicateFunctions}\n`;
    content += `- **Duplicate Code Blocks Found:** ${this.stats.duplicateBlocks}\n`;
    content += `- **Utility Files Created:** ${this.stats.utilsCreated}\n\n`;
    
    content += `## Duplicates Found\n\n`;
    
    for (const duplicate of this.duplicates.slice(0, 10)) { // Show top 10
      content += `### ${duplicate.name} (${duplicate.count} occurrences)\n`;
      content += `**Type:** ${duplicate.type}\n\n`;
      content += `**Files:**\n`;
      for (const item of duplicate.items) {
        const relativePath = path.relative(this.projectDir, item.file);
        content += `- \`${relativePath}\` (line ${item.line})\n`;
      }
      content += `\n`;
    }
    
    content += `## Migration Steps\n\n`;
    content += `1. **Review Generated Utilities:** Check \`src/utils/common/\` for new utility functions\n`;
    content += `2. **Update Imports:** Replace duplicate functions with utility imports\n`;
    content += `3. **Test Thoroughly:** Ensure all functionality works after migration\n`;
    content += `4. **Remove Duplicates:** Delete the original duplicate functions\n\n`;
    
    content += `## Example Migration\n\n`;
    content += `\`\`\`javascript\n`;
    content += `// Before\n`;
    content += `const validateEmail = (email) => {\n`;
    content += `  return /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email);\n`;
    content += `};\n\n`;
    content += `// After\n`;
    content += `import { validationValidateEmail } from '../utils/common';\n\n`;
    content += `// Use validationValidateEmail instead\n`;
    content += `\`\`\`\n`;
    
    fs.writeFileSync(guidePath, content, 'utf8');
    console.log('✅ Created migration guide');
  }

  async generateReport() {
    console.log('📊 Generating deduplication report...');
    
    const reportPath = path.join(this.projectDir, 'docs', 'DEDUPLICATION_REPORT.md');
    const docsDir = path.dirname(reportPath);
    
    if (!fs.existsSync(docsDir)) {
      fs.mkdirSync(docsDir, { recursive: true });
    }
    
    let content = `# Code Deduplication Report\n\n`;
    content += `Generated on: ${new Date().toISOString()}\n\n`;
    
    content += `## Summary\n`;
    content += `- **Files Analyzed:** ${this.stats.filesAnalyzed}\n`;
    content += `- **Duplicate Functions:** ${this.stats.duplicateFunctions}\n`;
    content += `- **Duplicate Code Blocks:** ${this.stats.duplicateBlocks}\n`;
    content += `- **Utility Files Created:** ${this.stats.utilsCreated}\n\n`;
    
    content += `## Top Duplicates\n\n`;
    
    // Sort by occurrence count
    const sortedDuplicates = this.duplicates.sort((a, b) => b.count - a.count);
    
    for (const duplicate of sortedDuplicates.slice(0, 20)) {
      content += `### ${duplicate.name}\n`;
      content += `- **Type:** ${duplicate.type}\n`;
      content += `- **Occurrences:** ${duplicate.count}\n`;
      content += `- **Hash:** \`${duplicate.hash}\`\n\n`;
    }
    
    content += `## Recommendations\n\n`;
    content += `1. **High Priority:** Functions with 3+ duplicates\n`;
    content += `2. **Medium Priority:** Code blocks with 2+ duplicates\n`;
    content += `3. **Low Priority:** Similar patterns that could be abstracted\n\n`;
    
    content += `## Next Steps\n\n`;
    content += `1. Review the migration guide: \`docs/DEDUPLICATION_MIGRATION.md\`\n`;
    content += `2. Test the generated utility functions\n`;
    content += `3. Gradually migrate duplicate code to use utilities\n`;
    content += `4. Set up linting rules to prevent future duplication\n`;
    
    fs.writeFileSync(reportPath, content, 'utf8');
    console.log('✅ Created deduplication report');
  }

  printStats() {
    console.log('\n📊 Code Deduplication Results:');
    console.log(`Files analyzed: ${this.stats.filesAnalyzed}`);
    console.log(`Duplicate functions: ${this.stats.duplicateFunctions}`);
    console.log(`Duplicate code blocks: ${this.stats.duplicateBlocks}`);
    console.log(`Utility files created: ${this.stats.utilsCreated}`);
    
    if (this.duplicates.length > 0) {
      console.log('\n🔍 Top Duplicates:');
      const topDuplicates = this.duplicates
        .sort((a, b) => b.count - a.count)
        .slice(0, 5);
      
      topDuplicates.forEach((duplicate, index) => {
        console.log(`${index + 1}. ${duplicate.name} (${duplicate.count} occurrences)`);
      });
    }
    
    console.log('\n🛠️ Code deduplication analysis completed!');
    console.log('📖 Check docs/DEDUPLICATION_REPORT.md for detailed analysis');
    console.log('📋 Check docs/DEDUPLICATION_MIGRATION.md for migration steps');
  }
}

// Run the code deduplication analysis
if (require.main === module) {
  const deduplication = new CodeDeduplication();
  deduplication.deduplicateCode().catch(console.error);
}

module.exports = CodeDeduplication;