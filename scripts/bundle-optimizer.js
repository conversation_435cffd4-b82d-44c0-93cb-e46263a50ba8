#!/usr/bin/env node

/**
 * Bundle Optimizer <PERSON>ript
 * Analyzes and optimizes bundle size for Elite Tailoring Management
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class BundleOptimizer {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
    this.packageJsonPath = path.join(this.projectRoot, 'package.json');
    this.nodeModulesPath = path.join(this.projectRoot, 'node_modules');
    this.results = {
      unusedDependencies: [],
      heavyPackages: [],
      duplicates: [],
      recommendations: []
    };
  }

  async analyze() {
    console.log('🔍 Starting bundle analysis...\n');
    
    await this.analyzePackageJson();
    await this.findHeavyPackages();
    await this.findUnusedDependencies();
    await this.generateRecommendations();
    
    this.printResults();
    await this.generateOptimizationScript();
  }

  async analyzePackageJson() {
    console.log('📦 Analyzing package.json...');
    
    const packageJson = JSON.parse(fs.readFileSync(this.packageJsonPath, 'utf8'));
    const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };
    
    console.log(`Total dependencies: ${Object.keys(dependencies).length}`);
    console.log(`Production dependencies: ${Object.keys(packageJson.dependencies || {}).length}`);
    console.log(`Dev dependencies: ${Object.keys(packageJson.devDependencies || {}).length}\n`);
  }

  async findHeavyPackages() {
    console.log('⚖️  Finding heavy packages...');
    
    try {
      const heavyPackages = [
        'react-native-chart-kit',
        '@gorhom/bottom-sheet',
        'react-native-reanimated',
        'react-native-gesture-handler',
        'react-native-svg',
        'react-native-vector-icons'
      ];

      for (const pkg of heavyPackages) {
        const pkgPath = path.join(this.nodeModulesPath, pkg);
        if (fs.existsSync(pkgPath)) {
          const size = this.getDirectorySize(pkgPath);
          this.results.heavyPackages.push({ name: pkg, size });
        }
      }

      this.results.heavyPackages.sort((a, b) => b.size - a.size);
      console.log(`Found ${this.results.heavyPackages.length} heavy packages\n`);
    } catch (error) {
      console.log('Could not analyze package sizes\n');
    }
  }

  async findUnusedDependencies() {
    console.log('🔍 Scanning for unused dependencies...');
    
    const packageJson = JSON.parse(fs.readFileSync(this.packageJsonPath, 'utf8'));
    const dependencies = Object.keys(packageJson.dependencies || {});
    
    // Known potentially unused packages
    const suspiciousPackages = [
      'lodash',
      'moment',
      'axios',
      'underscore',
      'jquery'
    ];

    for (const dep of dependencies) {
      if (suspiciousPackages.includes(dep)) {
        const isUsed = await this.checkIfPackageIsUsed(dep);
        if (!isUsed) {
          this.results.unusedDependencies.push(dep);
        }
      }
    }

    console.log(`Found ${this.results.unusedDependencies.length} potentially unused dependencies\n`);
  }

  async checkIfPackageIsUsed(packageName) {
    try {
      const srcPath = path.join(this.projectRoot, 'src');
      const result = execSync(`grep -r "from '${packageName}'" ${srcPath} || grep -r "require('${packageName}')" ${srcPath}`, 
        { encoding: 'utf8', stdio: 'pipe' });
      return result.length > 0;
    } catch {
      return false;
    }
  }

  getDirectorySize(dirPath) {
    let totalSize = 0;
    
    try {
      const files = fs.readdirSync(dirPath);
      
      for (const file of files) {
        const filePath = path.join(dirPath, file);
        const stats = fs.statSync(filePath);
        
        if (stats.isDirectory()) {
          totalSize += this.getDirectorySize(filePath);
        } else {
          totalSize += stats.size;
        }
      }
    } catch (error) {
      // Directory might not exist or be accessible
    }
    
    return totalSize;
  }

  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  generateRecommendations() {
    console.log('💡 Generating optimization recommendations...');

    // Bundle splitting recommendations
    this.results.recommendations.push({
      type: 'Bundle Splitting',
      description: 'Implement dynamic imports for heavy components',
      impact: 'High',
      implementation: 'Use React.lazy() for charts, bottom sheets, and camera components'
    });

    // Tree shaking recommendations
    this.results.recommendations.push({
      type: 'Tree Shaking',
      description: 'Optimize imports to reduce bundle size',
      impact: 'Medium',
      implementation: 'Use specific imports instead of barrel exports'
    });

    // Icon optimization
    this.results.recommendations.push({
      type: 'Icon Optimization',
      description: 'Use selective icon imports',
      impact: 'Medium',
      implementation: 'Import only needed icons from @expo/vector-icons'
    });

    // Remove unused dependencies
    if (this.results.unusedDependencies.length > 0) {
      this.results.recommendations.push({
        type: 'Dependency Cleanup',
        description: `Remove ${this.results.unusedDependencies.length} unused dependencies`,
        impact: 'Low',
        implementation: `npm uninstall ${this.results.unusedDependencies.join(' ')}`
      });
    }

    console.log(`Generated ${this.results.recommendations.length} recommendations\n`);
  }

  printResults() {
    console.log('📊 BUNDLE OPTIMIZATION RESULTS');
    console.log('================================\n');

    // Heavy packages
    if (this.results.heavyPackages.length > 0) {
      console.log('🏋️  HEAVY PACKAGES:');
      this.results.heavyPackages.forEach(pkg => {
        console.log(`  ${pkg.name}: ${this.formatBytes(pkg.size)}`);
      });
      console.log();
    }

    // Unused dependencies
    if (this.results.unusedDependencies.length > 0) {
      console.log('🗑️  POTENTIALLY UNUSED DEPENDENCIES:');
      this.results.unusedDependencies.forEach(dep => {
        console.log(`  ${dep}`);
      });
      console.log();
    }

    // Recommendations
    console.log('💡 OPTIMIZATION RECOMMENDATIONS:');
    this.results.recommendations.forEach((rec, index) => {
      console.log(`  ${index + 1}. ${rec.type} (${rec.impact} Impact)`);
      console.log(`     ${rec.description}`);
      console.log(`     Implementation: ${rec.implementation}\n`);
    });
  }

  async generateOptimizationScript() {
    const scriptContent = `#!/bin/bash

# Bundle Optimization Script for Elite Tailoring Management
# Generated on ${new Date().toISOString()}

echo "🚀 Starting bundle optimization..."

# Remove unused dependencies
${this.results.unusedDependencies.length > 0 ? 
  `echo "Removing unused dependencies..."
npm uninstall ${this.results.unusedDependencies.join(' ')}` : 
  '# No unused dependencies found'}

# Clean node_modules and reinstall
echo "Cleaning node_modules..."
rm -rf node_modules
npm install

# Run bundle analysis
echo "Running bundle analysis..."
npx react-native-bundle-visualizer

echo "✅ Optimization complete!"
`;

    fs.writeFileSync(path.join(this.projectRoot, 'scripts', 'optimize-bundle.sh'), scriptContent);
    console.log('📝 Generated optimization script: scripts/optimize-bundle.sh');
  }
}

// Run the optimizer
if (require.main === module) {
  const optimizer = new BundleOptimizer();
  optimizer.analyze().catch(console.error);
}

module.exports = BundleOptimizer;
