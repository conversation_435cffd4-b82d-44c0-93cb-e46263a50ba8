#!/usr/bin/env node

/**
 * Fix All Syntax Errors Script
 * Comprehensive fix for all LocalIcon import issues and path problems
 */

const fs = require('fs');
const path = require('path');

class SyntaxErrorFixer {
  constructor() {
    this.projectRoot = process.cwd();
    this.fixedFiles = [];
    this.errors = [];
  }

  // Get all source files
  getAllFiles(dir, extensions) {
    const files = [];
    
    const scan = (currentDir) => {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          scan(fullPath);
        } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
          files.push(fullPath);
        }
      }
    };
    
    scan(dir);
    return files;
  }

  // Fix all syntax errors in a single file
  fixSyntaxErrors(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      let newContent = content;
      let hasChanges = false;

      // 1. Fix duplicate LocalIcon imports
      const importPattern = /import\s+(?:LocalIcon(?:\s*,\s*\{[^}]*\})?|\{[^}]*\})\s+from\s+['"][^'"]*LocalIcon['"];?\n?/g;
      const imports = content.match(importPattern) || [];
      
      if (imports.length > 1) {
        console.log(`🔧 Fixing ${imports.length} duplicate imports in: ${path.relative(this.projectRoot, filePath)}`);
        
        // Extract all imported items
        let defaultImport = false;
        const namedImports = new Set();
        
        for (const importStatement of imports) {
          // Check for default import
          if (/import\s+LocalIcon/.test(importStatement)) {
            defaultImport = true;
          }
          
          // Extract named imports
          const namedMatch = importStatement.match(/\{\s*([^}]+)\s*\}/);
          if (namedMatch) {
            const names = namedMatch[1].split(',').map(name => name.trim());
            names.forEach(name => namedImports.add(name));
          }
        }
        
        // Create consolidated import with correct path
        let consolidatedImport = 'import ';
        if (defaultImport) {
          consolidatedImport += 'LocalIcon';
          if (namedImports.size > 0) {
            consolidatedImport += ', ';
          }
        }
        if (namedImports.size > 0) {
          consolidatedImport += `{ ${Array.from(namedImports).join(', ')} }`;
        }
        
        // Determine correct path based on file location
        const relativePath = path.relative(this.projectRoot, filePath);
        let importPath;
        
        if (relativePath.startsWith('src/components/')) {
          importPath = './LocalIcon';
        } else if (relativePath.startsWith('src/screens/')) {
          importPath = '../components/LocalIcon';
        } else if (relativePath.startsWith('src/')) {
          importPath = './components/LocalIcon';
        } else {
          importPath = '../components/LocalIcon';
        }
        
        consolidatedImport += ` from '${importPath}';\n`;
        
        // Remove all existing imports
        newContent = newContent.replace(importPattern, '');
        
        // Find the right place to insert the consolidated import
        const lastImportMatch = newContent.match(/^import\s+.*from\s+['"][^'"]*['"];?\s*$/gm);
        if (lastImportMatch) {
          const lastImport = lastImportMatch[lastImportMatch.length - 1];
          const lastImportIndex = newContent.lastIndexOf(lastImport);
          const insertIndex = lastImportIndex + lastImport.length;
          
          newContent = newContent.slice(0, insertIndex) + '\n' + consolidatedImport + newContent.slice(insertIndex);
        } else {
          // If no other imports found, add at the beginning
          newContent = consolidatedImport + '\n' + newContent;
        }
        
        hasChanges = true;
      }

      // 2. Fix incorrect LocalIcon import paths
      const incorrectPathPattern = /import\s+.*from\s+['"]\.\.\/components\/LocalIcon['"];?/g;
      if (incorrectPathPattern.test(newContent)) {
        const relativePath = path.relative(this.projectRoot, filePath);
        let correctPath;
        
        if (relativePath.startsWith('src/components/orders/') || 
            relativePath.startsWith('src/components/') && !relativePath.includes('LocalIcon')) {
          correctPath = '../LocalIcon';
        } else if (relativePath.startsWith('src/screens/')) {
          correctPath = '../components/LocalIcon';
        } else {
          correctPath = './components/LocalIcon';
        }
        
        newContent = newContent.replace(
          /import\s+(.*)\s+from\s+['"]\.\.\/components\/LocalIcon['"];?/g,
          `import $1 from '${correctPath}';`
        );
        hasChanges = true;
      }

      // 3. Fix double dot paths for components in subdirectories
      if (filePath.includes('src/components/orders/')) {
        newContent = newContent.replace(
          /import\s+(.*)\s+from\s+['"]\.\.\/components\/LocalIcon['"];?/g,
          "import $1 from '../LocalIcon';"
        );
        hasChanges = true;
      }

      if (hasChanges) {
        fs.writeFileSync(filePath, newContent);
        this.fixedFiles.push(filePath);
        console.log(`✅ Fixed: ${path.relative(this.projectRoot, filePath)}`);
      }

    } catch (error) {
      this.errors.push({ file: filePath, error: error.message });
      console.error(`❌ Error processing ${filePath}:`, error.message);
    }
  }

  // Process all files
  fixAll() {
    console.log('🔧 Starting comprehensive syntax error fix...');
    
    const sourceDir = path.join(this.projectRoot, 'src');
    const files = this.getAllFiles(sourceDir, ['.js', '.jsx', '.ts', '.tsx']);
    
    console.log(`📁 Found ${files.length} source files to process`);
    
    for (const file of files) {
      this.fixSyntaxErrors(file);
    }
    
    console.log('\n🎉 Syntax error fix completed!');
    console.log(`✅ Fixed ${this.fixedFiles.length} files`);
    console.log(`❌ ${this.errors.length} errors`);
    
    if (this.errors.length > 0) {
      console.log('\n❌ Errors:');
      this.errors.forEach(error => {
        console.log(`   - ${path.relative(this.projectRoot, error.file)}: ${error.error}`);
      });
    }
  }
}

// Run the fix
if (require.main === module) {
  const fixer = new SyntaxErrorFixer();
  fixer.fixAll();
}

module.exports = SyntaxErrorFixer;
