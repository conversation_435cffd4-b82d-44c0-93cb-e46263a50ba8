#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Function to recursively find all JS/JSX/TS/TSX files
function findFiles(dir, extensions = ['.js', '.jsx', '.ts', '.tsx']) {
  let results = [];
  const list = fs.readdirSync(dir);
  
  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat && stat.isDirectory()) {
      // Skip node_modules and other build directories
      if (!['node_modules', 'build', 'dist', '.git', 'android', 'ios'].includes(file)) {
        results = results.concat(findFiles(filePath, extensions));
      }
    } else {
      const ext = path.extname(file);
      if (extensions.includes(ext)) {
        results.push(filePath);
      }
    }
  });
  
  return results;
}

// Function to fix the final remaining errors
function fixFinalErrors(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Fix missing closing brackets in style arrays - most common error
    // Pattern: style={[styles.something, { ... }> -> style={[styles.something, { ... }]}>
    const styleArrayRegex = /style=\{(\[styles\.[^,]+,\s*\{[^}]*\})\s*>/g;
    if (styleArrayRegex.test(content)) {
      content = content.replace(styleArrayRegex, 'style={$1]}>');
      modified = true;
    }
    
    // Fix missing closing brackets in style arrays with theme
    // Pattern: { color: theme.colors.something }> -> { color: theme.colors.something }]}>
    const themeStyleArrayRegex = /(\[styles\.[^,]+,\s*\{[^}]*theme\.colors\.[^}]*\})\s*>/g;
    if (themeStyleArrayRegex.test(content)) {
      content = content.replace(themeStyleArrayRegex, '$1]}>');
      modified = true;
    }
    
    // Fix missing closing brackets in simple style arrays
    // Pattern: style={[styles.container, style> -> style={[styles.container, style]}>
    const simpleStyleArrayRegex = /style=\{(\[styles\.[^,]+,\s*\w+)\s*>/g;
    if (simpleStyleArrayRegex.test(content)) {
      content = content.replace(simpleStyleArrayRegex, 'style={$1]}>');
      modified = true;
    }
    
    // Fix contentContainerStyle arrays
    // Pattern: contentContainerStyle={[styles.content, contentStyle} -> contentContainerStyle={[styles.content, contentStyle]}
    const contentStyleRegex = /contentContainerStyle=\{(\[styles\.[^,]+,\s*\w+)\s*\}/g;
    if (contentStyleRegex.test(content)) {
      content = content.replace(contentStyleRegex, 'contentContainerStyle={$1]}');
      modified = true;
    }
    
    // Fix array closing bracket errors in data arrays
    // Pattern: [85000, 65000, 35000, 25000, 15000]] -> [85000, 65000, 35000, 25000, 15000]
    const doubleArrayClosingRegex = /(\[[^\]]*\d+)\]\]/g;
    if (doubleArrayClosingRegex.test(content)) {
      content = content.replace(doubleArrayClosingRegex, '$1]');
      modified = true;
    }
    
    // Fix object closing bracket errors
    // Pattern: }}} -> }}
    const tripleClosingRegex = /\}\}\}/g;
    if (tripleClosingRegex.test(content)) {
      content = content.replace(tripleClosingRegex, '}}');
      modified = true;
    }
    
    // Fix array access bracket errors
    // Pattern: paths[screen} -> paths[screen]
    const arrayAccessRegex = /(\w+\[\w+)\}/g;
    if (arrayAccessRegex.test(content)) {
      content = content.replace(arrayAccessRegex, '$1]');
      modified = true;
    }
    
    // Fix array closing bracket in object properties
    // Pattern: indexFields: ['name', 'email', 'phone'} -> indexFields: ['name', 'email', 'phone']
    const objectArrayRegex = /(\w+:\s*\[[^\]]*'[^']*')\}/g;
    if (objectArrayRegex.test(content)) {
      content = content.replace(objectArrayRegex, '$1]');
      modified = true;
    }
    
    // Fix timeline array closing bracket
    // Pattern: timeline: [...order.timeline, timelineEntry} -> timeline: [...order.timeline, timelineEntry]
    const timelineRegex = /(timeline:\s*\[[^\]]*\w+)\}/g;
    if (timelineRegex.test(content)) {
      content = content.replace(timelineRegex, '$1]');
      modified = true;
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`Fixed final errors in: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Main function
function main() {
  const srcDir = path.join(process.cwd(), 'src');
  
  if (!fs.existsSync(srcDir)) {
    console.error('src directory not found');
    process.exit(1);
  }
  
  console.log('Finding files to fix final errors...');
  const files = findFiles(srcDir);
  
  console.log(`Found ${files.length} files to check`);
  
  let fixedCount = 0;
  files.forEach(file => {
    if (fixFinalErrors(file)) {
      fixedCount++;
    }
  });
  
  console.log(`\nFixed final errors in ${fixedCount} files`);
  
  if (fixedCount > 0) {
    console.log('\nRunning type check to verify fixes...');
    const { execSync } = require('child_process');
    try {
      execSync('npm run type-check', { stdio: 'inherit' });
      console.log('Type check passed!');
    } catch (error) {
      console.log('Some errors remain, but many have been fixed.');
    }
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixFinalErrors, findFiles };
