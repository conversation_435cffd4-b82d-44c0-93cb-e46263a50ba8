#!/usr/bin/env node

/**
 * Fix Duplicate Imports Script
 * Fixes all duplicate LocalIcon imports that were created during migration
 */

const fs = require('fs');
const path = require('path');

class DuplicateImportFixer {
  constructor() {
    this.projectRoot = process.cwd();
    this.fixedFiles = [];
    this.errors = [];
  }

  // Get all source files
  getAllFiles(dir, extensions) {
    const files = [];
    
    const scan = (currentDir) => {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          scan(fullPath);
        } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
          files.push(fullPath);
        }
      }
    };
    
    scan(dir);
    return files;
  }

  // Fix duplicate imports in a single file
  fixDuplicateImports(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      let newContent = content;
      let hasChanges = false;

      // Pattern to find all LocalIcon imports
      const importPattern = /import\s+(?:LocalIcon(?:\s*,\s*\{[^}]*\})?|\{[^}]*\})\s+from\s+['"][^'"]*LocalIcon['"];?\n?/g;
      
      const imports = content.match(importPattern) || [];
      
      if (imports.length > 1) {
        console.log(`📁 Fixing ${imports.length} duplicate imports in: ${path.relative(this.projectRoot, filePath)}`);
        
        // Extract all imported items
        let defaultImport = false;
        const namedImports = new Set();
        
        for (const importStatement of imports) {
          // Check for default import
          if (/import\s+LocalIcon/.test(importStatement)) {
            defaultImport = true;
          }
          
          // Extract named imports
          const namedMatch = importStatement.match(/\{\s*([^}]+)\s*\}/);
          if (namedMatch) {
            const names = namedMatch[1].split(',').map(name => name.trim());
            names.forEach(name => namedImports.add(name));
          }
        }
        
        // Create consolidated import
        let consolidatedImport = 'import ';
        if (defaultImport) {
          consolidatedImport += 'LocalIcon';
          if (namedImports.size > 0) {
            consolidatedImport += ', ';
          }
        }
        if (namedImports.size > 0) {
          consolidatedImport += `{ ${Array.from(namedImports).join(', ')} }`;
        }
        consolidatedImport += " from '../components/LocalIcon';\n";
        
        // Remove all existing imports
        newContent = newContent.replace(importPattern, '');
        
        // Find the right place to insert the consolidated import
        const lastImportMatch = newContent.match(/^import\s+.*from\s+['"][^'"]*['"];?\s*$/gm);
        if (lastImportMatch) {
          const lastImport = lastImportMatch[lastImportMatch.length - 1];
          const lastImportIndex = newContent.lastIndexOf(lastImport);
          const insertIndex = lastImportIndex + lastImport.length;
          
          newContent = newContent.slice(0, insertIndex) + '\n' + consolidatedImport + newContent.slice(insertIndex);
        } else {
          // If no other imports found, add at the beginning after the first comment block
          const firstLineAfterComments = newContent.search(/^(?!\/\*|\/\/|\s*\*|\s*$)/m);
          if (firstLineAfterComments !== -1) {
            newContent = newContent.slice(0, firstLineAfterComments) + consolidatedImport + '\n' + newContent.slice(firstLineAfterComments);
          } else {
            newContent = consolidatedImport + '\n' + newContent;
          }
        }
        
        hasChanges = true;
      }

      if (hasChanges) {
        fs.writeFileSync(filePath, newContent);
        this.fixedFiles.push(filePath);
        console.log(`✅ Fixed: ${path.relative(this.projectRoot, filePath)}`);
      }

    } catch (error) {
      this.errors.push({ file: filePath, error: error.message });
      console.error(`❌ Error processing ${filePath}:`, error.message);
    }
  }

  // Process all files
  fixAll() {
    console.log('🔧 Starting duplicate import fix...');
    
    const sourceDir = path.join(this.projectRoot, 'src');
    const files = this.getAllFiles(sourceDir, ['.js', '.jsx', '.ts', '.tsx']);
    
    console.log(`📁 Found ${files.length} source files to process`);
    
    for (const file of files) {
      this.fixDuplicateImports(file);
    }
    
    console.log('\n🎉 Duplicate import fix completed!');
    console.log(`✅ Fixed ${this.fixedFiles.length} files`);
    console.log(`❌ ${this.errors.length} errors`);
    
    if (this.errors.length > 0) {
      console.log('\n❌ Errors:');
      this.errors.forEach(error => {
        console.log(`   - ${path.relative(this.projectRoot, error.file)}: ${error.error}`);
      });
    }
  }
}

// Run the fix
if (require.main === module) {
  const fixer = new DuplicateImportFixer();
  fixer.fixAll();
}

module.exports = DuplicateImportFixer;
