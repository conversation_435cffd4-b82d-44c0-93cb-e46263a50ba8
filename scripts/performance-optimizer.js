#!/usr/bin/env node

/**
 * Performance Optimizer Script
 * Analyzes and optimizes the React Native app for better performance
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class PerformanceOptimizer {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
    this.srcPath = path.join(this.projectRoot, 'src');
    this.issues = [];
    this.optimizations = [];
  }

  async optimize() {
    console.log('🚀 Starting performance optimization...\n');
    
    await this.analyzeImports();
    await this.findDuplicateCode();
    await this.analyzeComponentPerformance();
    await this.checkBundleSize();
    await this.optimizeImages();
    
    this.printResults();
    await this.applyOptimizations();
  }

  async analyzeImports() {
    console.log('📦 Analyzing imports...');
    
    const files = this.getAllJSFiles(this.srcPath);
    const importAnalysis = {
      duplicateImports: [],
      heavyImports: [],
      unusedImports: []
    };

    for (const file of files) {
      const content = fs.readFileSync(file, 'utf8');
      
      // Check for duplicate imports
      const imports = content.match(/import .* from ['"][^'"]+['"];?/g) || [];
      const importMap = new Map();
      
      imports.forEach(imp => {
        const module = imp.match(/from ['"]([^'"]+)['"]/)?.[1];
        if (module) {
          if (importMap.has(module)) {
            importAnalysis.duplicateImports.push({
              file: path.relative(this.projectRoot, file),
              module,
              imports: [importMap.get(module), imp]
            });
          } else {
            importMap.set(module, imp);
          }
        }
      });

      // Check for heavy imports
      const heavyModules = [
        'react-native-paper',
        'react-native-vector-icons',
        'react-native-chart-kit',
        '@gorhom/bottom-sheet'
      ];

      heavyModules.forEach(module => {
        if (content.includes(`from '${module}'`) && !content.includes('// Optimized import')) {
          importAnalysis.heavyImports.push({
            file: path.relative(this.projectRoot, file),
            module
          });
        }
      });
    }

    this.issues.push(...importAnalysis.duplicateImports.map(item => ({
      type: 'duplicate_import',
      severity: 'warning',
      message: `Duplicate import of ${item.module} in ${item.file}`,
      file: item.file
    })));

    this.issues.push(...importAnalysis.heavyImports.map(item => ({
      type: 'heavy_import',
      severity: 'info',
      message: `Heavy import ${item.module} in ${item.file} - consider selective imports`,
      file: item.file
    })));

    console.log(`Found ${importAnalysis.duplicateImports.length} duplicate imports`);
    console.log(`Found ${importAnalysis.heavyImports.length} heavy imports\n`);
  }

  async findDuplicateCode() {
    console.log('🔍 Finding duplicate code...');
    
    const files = this.getAllJSFiles(this.srcPath);
    const codeBlocks = new Map();
    
    for (const file of files) {
      const content = fs.readFileSync(file, 'utf8');
      const lines = content.split('\n');
      
      // Look for duplicate function definitions
      const functions = content.match(/(?:const|function)\s+\w+\s*[=\(]/g) || [];
      functions.forEach(func => {
        const funcName = func.match(/(?:const|function)\s+(\w+)/)?.[1];
        if (funcName && codeBlocks.has(funcName)) {
          this.issues.push({
            type: 'duplicate_function',
            severity: 'warning',
            message: `Potential duplicate function ${funcName}`,
            files: [codeBlocks.get(funcName), path.relative(this.projectRoot, file)]
          });
        } else if (funcName) {
          codeBlocks.set(funcName, path.relative(this.projectRoot, file));
        }
      });
    }

    console.log(`Analyzed ${files.length} files for duplicates\n`);
  }

  async analyzeComponentPerformance() {
    console.log('⚡ Analyzing component performance...');
    
    const files = this.getAllJSFiles(this.srcPath);
    const performanceIssues = [];

    for (const file of files) {
      const content = fs.readFileSync(file, 'utf8');
      const fileName = path.relative(this.projectRoot, file);
      
      // Check for missing React.memo
      if (content.includes('export default') && 
          content.includes('const ') && 
          !content.includes('memo(') &&
          !content.includes('React.memo') &&
          fileName.includes('components/')) {
        performanceIssues.push({
          type: 'missing_memo',
          severity: 'info',
          message: `Component in ${fileName} could benefit from React.memo`,
          file: fileName
        });
      }

      // Check for missing useCallback
      if (content.includes('onPress=') && 
          !content.includes('useCallback') &&
          content.includes('useState')) {
        performanceIssues.push({
          type: 'missing_callback',
          severity: 'info',
          message: `Event handlers in ${fileName} could benefit from useCallback`,
          file: fileName
        });
      }

      // Check for large inline styles
      const inlineStyles = content.match(/style=\{[^}]+\}/g) || [];
      if (inlineStyles.length > 5) {
        performanceIssues.push({
          type: 'inline_styles',
          severity: 'warning',
          message: `Too many inline styles in ${fileName} - consider StyleSheet`,
          file: fileName
        });
      }
    }

    this.issues.push(...performanceIssues);
    console.log(`Found ${performanceIssues.length} performance issues\n`);
  }

  async checkBundleSize() {
    console.log('📊 Checking bundle size...');
    
    try {
      // Check node_modules size
      const nodeModulesSize = this.getDirectorySize(path.join(this.projectRoot, 'node_modules'));
      const sizeInMB = (nodeModulesSize / (1024 * 1024)).toFixed(2);
      
      console.log(`Node modules size: ${sizeInMB} MB`);
      
      if (nodeModulesSize > 500 * 1024 * 1024) { // 500MB
        this.issues.push({
          type: 'large_bundle',
          severity: 'warning',
          message: `Large node_modules size: ${sizeInMB} MB`,
          suggestion: 'Consider removing unused dependencies'
        });
      }

      // Check for large packages
      const packageJson = JSON.parse(fs.readFileSync(path.join(this.projectRoot, 'package.json'), 'utf8'));
      const dependencies = Object.keys(packageJson.dependencies || {});
      
      const heavyPackages = [
        'react-native-chart-kit',
        'react-native-vector-icons',
        '@gorhom/bottom-sheet',
        'react-native-reanimated'
      ];

      heavyPackages.forEach(pkg => {
        if (dependencies.includes(pkg)) {
          this.optimizations.push({
            type: 'optimize_package',
            package: pkg,
            suggestion: `Consider lazy loading or selective imports for ${pkg}`
          });
        }
      });

    } catch (error) {
      console.log('Could not analyze bundle size\n');
    }
  }

  async optimizeImages() {
    console.log('🖼️  Checking image optimization...');
    
    const assetsPath = path.join(this.projectRoot, 'assets');
    if (fs.existsSync(assetsPath)) {
      const images = fs.readdirSync(assetsPath).filter(file => 
        /\.(png|jpg|jpeg|gif)$/i.test(file)
      );

      images.forEach(image => {
        const imagePath = path.join(assetsPath, image);
        const stats = fs.statSync(imagePath);
        const sizeInKB = (stats.size / 1024).toFixed(2);
        
        if (stats.size > 100 * 1024) { // 100KB
          this.issues.push({
            type: 'large_image',
            severity: 'info',
            message: `Large image: ${image} (${sizeInKB} KB)`,
            suggestion: 'Consider optimizing or using WebP format'
          });
        }
      });

      console.log(`Analyzed ${images.length} images\n`);
    }
  }

  printResults() {
    console.log('📋 Performance Analysis Results:\n');
    
    const severityOrder = ['error', 'warning', 'info'];
    const groupedIssues = this.issues.reduce((acc, issue) => {
      if (!acc[issue.severity]) acc[issue.severity] = [];
      acc[issue.severity].push(issue);
      return acc;
    }, {});

    severityOrder.forEach(severity => {
      if (groupedIssues[severity]) {
        console.log(`${severity.toUpperCase()} (${groupedIssues[severity].length}):`);
        groupedIssues[severity].forEach(issue => {
          console.log(`  • ${issue.message}`);
          if (issue.suggestion) {
            console.log(`    💡 ${issue.suggestion}`);
          }
        });
        console.log('');
      }
    });

    if (this.optimizations.length > 0) {
      console.log('🔧 Optimization Suggestions:');
      this.optimizations.forEach(opt => {
        console.log(`  • ${opt.suggestion}`);
      });
      console.log('');
    }

    console.log(`Total issues found: ${this.issues.length}`);
    console.log(`Optimization suggestions: ${this.optimizations.length}\n`);
  }

  async applyOptimizations() {
    console.log('🛠️  Applying automatic optimizations...\n');
    
    // Create optimized metro config if it doesn't exist
    await this.optimizeMetroConfig();
    
    // Create bundle analyzer script
    await this.createBundleAnalyzer();
    
    console.log('✅ Automatic optimizations applied!\n');
    console.log('📝 Manual optimizations recommended:');
    console.log('  1. Review and fix duplicate imports');
    console.log('  2. Add React.memo to pure components');
    console.log('  3. Use useCallback for event handlers');
    console.log('  4. Consider lazy loading for heavy components');
    console.log('  5. Optimize large images');
  }

  async optimizeMetroConfig() {
    const metroConfigPath = path.join(this.projectRoot, 'metro.config.js');
    if (fs.existsSync(metroConfigPath)) {
      console.log('✅ Metro config already optimized');
    }
  }

  async createBundleAnalyzer() {
    const analyzerScript = `
// Bundle analyzer script
const { execSync } = require('child_process');

console.log('📊 Analyzing bundle size...');
try {
  execSync('npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output android-release.bundle --assets-dest android-release/', { stdio: 'inherit' });
  console.log('✅ Bundle analysis complete');
} catch (error) {
  console.error('❌ Bundle analysis failed:', error.message);
}
`;

    const scriptPath = path.join(this.projectRoot, 'scripts', 'analyze-bundle.js');
    fs.writeFileSync(scriptPath, analyzerScript);
    console.log('✅ Bundle analyzer script created');
  }

  getAllJSFiles(dir) {
    const files = [];
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        files.push(...this.getAllJSFiles(fullPath));
      } else if (stat.isFile() && /\.(js|jsx|ts|tsx)$/.test(item)) {
        files.push(fullPath);
      }
    }
    
    return files;
  }

  getDirectorySize(dirPath) {
    let totalSize = 0;
    
    try {
      const files = fs.readdirSync(dirPath);
      
      for (const file of files) {
        const filePath = path.join(dirPath, file);
        const stats = fs.statSync(filePath);
        
        if (stats.isDirectory()) {
          totalSize += this.getDirectorySize(filePath);
        } else {
          totalSize += stats.size;
        }
      }
    } catch (error) {
      // Directory might not exist or be accessible
    }
    
    return totalSize;
  }
}

// Run the optimizer
if (require.main === module) {
  const optimizer = new PerformanceOptimizer();
  optimizer.optimize().catch(console.error);
}

module.exports = PerformanceOptimizer;
