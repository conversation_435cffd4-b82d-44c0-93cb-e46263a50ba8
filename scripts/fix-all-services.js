#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Generic function to fix template literals and method parameters
function fixServiceFile(filePath, methodFixes = [], templateFixes = []) {
  if (!fs.existsSync(filePath)) {
    console.error(`${filePath} not found`);
    return false;
  }
  
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  
  // Apply method fixes
  methodFixes.forEach(fix => {
    if (fix.pattern.test(content)) {
      content = content.replace(fix.pattern, fix.replacement);
      modified = true;
    }
  });
  
  // Apply template literal fixes
  templateFixes.forEach(fix => {
    if (fix.pattern.test(content)) {
      content = content.replace(fix.pattern, fix.replacement);
      modified = true;
    }
  });
  
  // Common template literal fixes that apply to all files
  const commonFixes = [
    { pattern: /return \{ success: true   \}/g, replacement: "return { success: true }" },
    { pattern: /return \{ success: false   \}/g, replacement: "return { success: false }" },
    { pattern: /return \{ \.\.\.([^,}]+), ([^}]+)   \}/g, replacement: "return { ...$1, $2 }" },
    { pattern: /`([^`]*)\$\{([^}]+)\} ,/g, replacement: "`$1${$2}`," },
    { pattern: /`([^`]*)\$\{([^}]+)\} ;/g, replacement: "`$1${$2}`;" },
    { pattern: /`([^`]*)\$\{([^}]+)\} \)/g, replacement: "`$1${$2}`)" },
    { pattern: /Logger\.error\(`([^`]*)\$\{([^}]+)\} ,`/g, replacement: "Logger.error(`$1${$2}`)," },
    { pattern: /Logger\.info\(`([^`]*)\$\{([^}]+)\} \)\;/g, replacement: "Logger.info(`$1${$2}`);" },
    { pattern: /console\.error\('([^']*): ', error\);/g, replacement: "console.error('$1:', error);" },
    { pattern: /console\.log\('([^']*): ', ([^)]+)\);/g, replacement: "console.log('$1:', $2);" }
  ];
  
  commonFixes.forEach(fix => {
    if (fix.pattern.test(content)) {
      content = content.replace(fix.pattern, fix.replacement);
      modified = true;
    }
  });
  
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    return true;
  }
  
  return false;
}

// Fix MigrationService.js
function fixMigrationService() {
  const filePath = path.join(process.cwd(), 'src/services/MigrationService.js');
  
  const methodFixes = [
    { pattern: /async migrateData\(\$3\)/g, replacement: 'async migrateData(fromVersion, toVersion)' },
    { pattern: /async backupData\(\$3\)/g, replacement: 'async backupData()' },
    { pattern: /async restoreData\(\$3\)/g, replacement: 'async restoreData(backupData)' }
  ];
  
  const templateFixes = [
    { pattern: /`Migration from \$\{fromVersion\} to \$\{toVersion\} completed ,/g, replacement: "`Migration from ${fromVersion} to ${toVersion} completed`," },
    { pattern: /`Backup created: \$\{backupKey\} ,/g, replacement: "`Backup created: ${backupKey}`," }
  ];
  
  return fixServiceFile(filePath, methodFixes, templateFixes);
}

// Fix NavigationService.ts
function fixNavigationService() {
  const filePath = path.join(process.cwd(), 'src/services/NavigationService.ts');
  
  const methodFixes = [
    { pattern: /navigate\(\$3\)/g, replacement: 'navigate(routeName: string, params?: any)' },
    { pattern: /goBack\(\$3\)/g, replacement: 'goBack()' },
    { pattern: /reset\(\$3\)/g, replacement: 'reset(routeName: string)' }
  ];
  
  return fixServiceFile(filePath, methodFixes, []);
}

// Fix OptimizedDataService.js
function fixOptimizedDataService() {
  const filePath = path.join(process.cwd(), 'src/services/OptimizedDataService.js');
  
  const methodFixes = [
    { pattern: /async loadData\(\$3\)/g, replacement: 'async loadData(key)' },
    { pattern: /async saveData\(\$3\)/g, replacement: 'async saveData(key, data)' },
    { pattern: /async clearCache\(\$3\)/g, replacement: 'async clearCache()' }
  ];
  
  return fixServiceFile(filePath, methodFixes, []);
}

// Fix OrderService.ts
function fixOrderService() {
  const filePath = path.join(process.cwd(), 'src/services/OrderService.ts');
  
  const methodFixes = [
    { pattern: /async createOrder\(\$3\)/g, replacement: 'async createOrder(orderData: any)' },
    { pattern: /async updateOrder\(\$3\)/g, replacement: 'async updateOrder(id: string, orderData: any)' },
    { pattern: /async deleteOrder\(\$3\)/g, replacement: 'async deleteOrder(id: string)' },
    { pattern: /async getOrders\(\$3\)/g, replacement: 'async getOrders(filters?: any)' }
  ];
  
  return fixServiceFile(filePath, methodFixes, []);
}

// Fix storageService.js
function fixStorageService() {
  const filePath = path.join(process.cwd(), 'src/services/storageService.js');
  
  const methodFixes = [
    { pattern: /async backup\(\$3\)/g, replacement: 'async backup(sourceKey, targetKey)' },
    { pattern: /async restore\(\$3\)/g, replacement: 'async restore(backupKey, targetKey)' }
  ];
  
  const templateFixes = [
    { pattern: /Logger\.error\(`Failed to backup \$\{sourceKey\} ,` error\);/g, replacement: "Logger.error(`Failed to backup ${sourceKey}:`, error);" },
    { pattern: /throw new StorageError\(`Failed to create backup`, error\);/g, replacement: "throw new StorageError(`Failed to create backup`, error);" },
    { pattern: /Logger\.info\(`Data restored: \$\{backupKey\} -> \$\{targetKey\} \)\;/g, replacement: "Logger.info(`Data restored: ${backupKey} -> ${targetKey}`);" },
    { pattern: /Logger\.error\(`Failed to restore from \$\{backupKey\} ,` error\);/g, replacement: "Logger.error(`Failed to restore from ${backupKey}:`, error);" }
  ];
  
  return fixServiceFile(filePath, methodFixes, templateFixes);
}

// Main function
function main() {
  console.log('Fixing multiple service files...');
  
  const services = [
    { name: 'MigrationService.js', fix: fixMigrationService },
    { name: 'NavigationService.ts', fix: fixNavigationService },
    { name: 'OptimizedDataService.js', fix: fixOptimizedDataService },
    { name: 'OrderService.ts', fix: fixOrderService },
    { name: 'storageService.js', fix: fixStorageService }
  ];
  
  let totalFixed = 0;
  
  services.forEach(service => {
    console.log(`Fixing ${service.name}...`);
    if (service.fix()) {
      console.log(`${service.name} fixed successfully`);
      totalFixed++;
    } else {
      console.log(`No changes needed for ${service.name}`);
    }
  });
  
  console.log(`\nFixed ${totalFixed} service files`);
}

if (require.main === module) {
  main();
}

module.exports = { 
  fixMigrationService, 
  fixNavigationService, 
  fixOptimizedDataService, 
  fixOrderService, 
  fixStorageService 
};
