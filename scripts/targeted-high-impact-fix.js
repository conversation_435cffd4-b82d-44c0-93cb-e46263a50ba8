#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Targeted high-impact fix for the most problematic files
function targetedHighImpactFix() {
  console.log('🎯 TARGETED HIGH-IMPACT ERROR REDUCTION');
  console.log('Focusing on files with 40+ errors each\n');
  
  let totalFixed = 0;
  
  // Phase 1: Fix the most broken JSX files (200+ errors combined)
  console.log('🔧 PHASE 1: Critical JSX Structure Fixes');
  
  const criticalJSXFixes = [
    {
      file: 'src/components/content/OrderDetailsContent.js',
      fixes: [
        // Fix the most common pattern: missing closing tags and brackets
        { pattern: /<\/Chip><\/View><\/View>/g, replacement: '</Chip>\n        </View>\n      </View>' },
        { pattern: /<\/Chip><\/View>/g, replacement: '</Chip>\n        </View>' },
        { pattern: /style=\{styles\.([a-zA-Z]+)\}>/g, replacement: 'style={styles.$1}>' },
        { pattern: /textStyle=\{\{ ([^}]+) \} ([a-zA-Z]+)=/g, replacement: 'textStyle={{ $1 }} $2=' },
        { pattern: /\{orderData\.([a-zA-Z]+)\.toUpperCase\(\)\}/g, replacement: '{orderData.$1.toUpperCase()}' }
      ]
    },
    {
      file: 'src/components/content/OrderFormContent.js', 
      fixes: [
        // Fix JSX structure issues
        { pattern: /<View style=\{styles\.([a-zA-Z]+)\}><Text/g, replacement: '<View style={styles.$1}>\n      <Text' },
        { pattern: /<\/Text><Text/g, replacement: '</Text>\n      <Text' },
        { pattern: /<\/Text><\/View>/g, replacement: '</Text>\n    </View>' },
        { pattern: /style=\{(\[styles\.[^,]+, \{ [^}]+ \})\}>/g, replacement: 'style={$1]}>' },
        { pattern: /<\/Button><\/View>/g, replacement: '</Button>\n    </View>' }
      ]
    },
    {
      file: 'src/components/content/ProductDetailsContent.js',
      fixes: [
        // Fix style prop and JSX structure
        { pattern: /style=\{(\[styles\.[^,]+, \{ flex: [12] \})\}/g, replacement: 'style={$1]}' },
        { pattern: /<\/Button><\/View><\/View>/g, replacement: '</Button>\n      </View>\n    </View>' },
        { pattern: /<\/Button><\/View><\/ScrollView>/g, replacement: '</Button>\n      </View>\n    </ScrollView>' },
        { pattern: /disabled=\{([^}]+)\}>/g, replacement: 'disabled={$1}>' },
        { pattern: /onPress=\{([^}]+)\}>/g, replacement: 'onPress={$1}>' }
      ]
    }
  ];
  
  criticalJSXFixes.forEach(({ file, fixes }) => {
    const fullPath = path.join(process.cwd(), file);
    if (!fs.existsSync(fullPath)) return;
    
    try {
      let content = fs.readFileSync(fullPath, 'utf8');
      let fileFixed = 0;
      
      fixes.forEach(fix => {
        const beforeCount = (content.match(fix.pattern) || []).length;
        if (beforeCount > 0) {
          content = content.replace(fix.pattern, fix.replacement);
          const afterCount = (content.match(fix.pattern) || []).length;
          fileFixed += (beforeCount - afterCount);
        }
      });
      
      if (fileFixed > 0) {
        fs.writeFileSync(fullPath, content, 'utf8');
        console.log(`  ✅ ${path.basename(file)}: ${fileFixed} fixes`);
        totalFixed += fileFixed;
      }
    } catch (error) {
      console.error(`  ❌ Error in ${file}:`, error.message);
    }
  });
  
  // Phase 2: Fix Service Method Signatures (300+ errors combined)
  console.log('\n🔧 PHASE 2: Service Method Signature Fixes');
  
  const serviceFixes = [
    {
      file: 'src/services/MigrationService.js',
      fixes: [
        { pattern: /async ([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\(\s*([^)]*)\s*\)\s*\{/g, replacement: 'async $1($2) {' },
        { pattern: /([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\(\s*([^)]*)\s*\)\s*\{/g, replacement: '$1($2) {' },
        { pattern: /export default new ([A-Z][a-zA-Z0-9_$]*)\(\)\s*;/g, replacement: 'export default new $1();' }
      ]
    },
    {
      file: 'src/services/SQLiteService.js',
      fixes: [
        { pattern: /async ([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\(\s*([^)]*)\s*\)\s*\{/g, replacement: 'async $1($2) {' },
        { pattern: /const wrappedItems = `\[\$\{row\.items\];/g, replacement: 'const wrappedItems = `[${row.items}]`;' },
        { pattern: /await this\.db\.runAsync\(`([^`]*)\$\{([^}]+)\}`\}/g, replacement: 'await this.db.runAsync(`$1${$2}`)' },
        { pattern: /tags: row\.tags \? JSON\.parse\(row\.tags\) : \[\]\)\);/g, replacement: 'tags: row.tags ? JSON.parse(row.tags) : []\n        }));' }
      ]
    },
    {
      file: 'src/services/QRCodeService.js',
      fixes: [
        { pattern: /displayText: `([^`]*)\$\{([^}]+)\}([^`]*)`,/g, replacement: 'displayText: `$1${$2}$3`,' },
        { pattern: /([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\(\s*([^)]*)\s*\)\s*\{/g, replacement: '$1($2) {' },
        { pattern: /export default new ([A-Z][a-zA-Z0-9_$]*)\(\)\s*;/g, replacement: 'export default new $1();' }
      ]
    }
  ];
  
  serviceFixes.forEach(({ file, fixes }) => {
    const fullPath = path.join(process.cwd(), file);
    if (!fs.existsSync(fullPath)) return;
    
    try {
      let content = fs.readFileSync(fullPath, 'utf8');
      let fileFixed = 0;
      
      fixes.forEach(fix => {
        const beforeCount = (content.match(fix.pattern) || []).length;
        if (beforeCount > 0) {
          content = content.replace(fix.pattern, fix.replacement);
          const afterCount = (content.match(fix.pattern) || []).length;
          fileFixed += (beforeCount - afterCount);
        }
      });
      
      if (fileFixed > 0) {
        fs.writeFileSync(fullPath, content, 'utf8');
        console.log(`  ✅ ${path.basename(file)}: ${fileFixed} fixes`);
        totalFixed += fileFixed;
      }
    } catch (error) {
      console.error(`  ❌ Error in ${file}:`, error.message);
    }
  });
  
  // Phase 3: Fix Navigation & TypeScript (150+ errors combined)
  console.log('\n🔧 PHASE 3: Navigation & TypeScript Fixes');
  
  const navigationFixes = [
    {
      file: 'src/navigation/AppNavigator.tsx',
      fixes: [
        { pattern: /Screen\s+name="([^"]+)"\s+component=\{([^}]+)\}/g, replacement: 'Screen name="$1" component={$2}' },
        { pattern: /<Navigator([^>]*)>/g, replacement: '<Navigator$1>' },
        { pattern: /<\/Navigator>/g, replacement: '</Navigator>' }
      ]
    },
    {
      file: 'src/utils/deepLinkUtils.ts',
      fixes: [
        { pattern: /return `\$\{baseUrl\} \$\{paths\[screen\];/g, replacement: 'return `${baseUrl}${paths[screen]}`;' },
        { pattern: /path = path\.replace\(`:([^`]*)\$\{([^}]+)\}`, String\(value\)\);/g, replacement: 'path = path.replace(`:${$2}`, String(value));' },
        { pattern: /queryParams\.push\(`([^`]*)\$\{([^}]+)\} =([^`]*)`\);/g, replacement: 'queryParams.push(`${$2}=${$3}`);' },
        { pattern: /return `\$\{baseUrl\} \$\{path\}\$\{queryString\};/g, replacement: 'return `${baseUrl}${path}${queryString}`;' }
      ]
    }
  ];
  
  navigationFixes.forEach(({ file, fixes }) => {
    const fullPath = path.join(process.cwd(), file);
    if (!fs.existsSync(fullPath)) return;
    
    try {
      let content = fs.readFileSync(fullPath, 'utf8');
      let fileFixed = 0;
      
      fixes.forEach(fix => {
        const beforeCount = (content.match(fix.pattern) || []).length;
        if (beforeCount > 0) {
          content = content.replace(fix.pattern, fix.replacement);
          const afterCount = (content.match(fix.pattern) || []).length;
          fileFixed += (beforeCount - afterCount);
        }
      });
      
      if (fileFixed > 0) {
        fs.writeFileSync(fullPath, content, 'utf8');
        console.log(`  ✅ ${path.basename(file)}: ${fileFixed} fixes`);
        totalFixed += fileFixed;
      }
    } catch (error) {
      console.error(`  ❌ Error in ${file}:`, error.message);
    }
  });
  
  // Phase 4: Fix Screen Components (200+ errors combined)
  console.log('\n🔧 PHASE 4: Screen Component Fixes');
  
  const screenFixes = [
    {
      file: 'src/screens/NotificationScreen.js',
      fixes: [
        { pattern: /<([A-Z][a-zA-Z0-9_$]*)\s+([^>]*)\s+>/g, replacement: '<$1 $2>' },
        { pattern: /\{([^}]+)\}\s*([A-Z][a-zA-Z0-9_$]*)/g, replacement: '{$1}\n      $2' },
        { pattern: /useState\(\s*([^)]+)\s*\)/g, replacement: 'useState($1)' }
      ]
    },
    {
      file: 'src/screens/AboutScreen.js',
      fixes: [
        { pattern: /style=\{(\[styles\.[^,]+, \{ [^}]+ \})\}/g, replacement: 'style={$1]}' },
        { pattern: /<\/([A-Z][a-zA-Z0-9_$]*)\>\s*([A-Z][a-zA-Z0-9_$]*)/g, replacement: '</$1>\n      $2' }
      ]
    }
  ];
  
  screenFixes.forEach(({ file, fixes }) => {
    const fullPath = path.join(process.cwd(), file);
    if (!fs.existsSync(fullPath)) return;
    
    try {
      let content = fs.readFileSync(fullPath, 'utf8');
      let fileFixed = 0;
      
      fixes.forEach(fix => {
        const beforeCount = (content.match(fix.pattern) || []).length;
        if (beforeCount > 0) {
          content = content.replace(fix.pattern, fix.replacement);
          const afterCount = (content.match(fix.pattern) || []).length;
          fileFixed += (beforeCount - afterCount);
        }
      });
      
      if (fileFixed > 0) {
        fs.writeFileSync(fullPath, content, 'utf8');
        console.log(`  ✅ ${path.basename(file)}: ${fileFixed} fixes`);
        totalFixed += fileFixed;
      }
    } catch (error) {
      console.error(`  ❌ Error in ${file}:`, error.message);
    }
  });
  
  console.log(`\n🎉 Total targeted fixes applied: ${totalFixed}`);
  return totalFixed;
}

// Get current error count
function getErrorCount() {
  try {
    const result = execSync('cd "/Volumes/Files/Tailora Stable" && npm run type-check 2>&1', { encoding: 'utf8' });
    const match = result.match(/Found (\d+) errors/);
    return match ? parseInt(match[1]) : 0;
  } catch (error) {
    const match = error.stdout?.match(/Found (\d+) errors/);
    return match ? parseInt(match[1]) : 0;
  }
}

// Main execution
function main() {
  console.log('📊 Getting initial error count...');
  const initialErrors = getErrorCount();
  console.log(`Initial errors: ${initialErrors}\n`);
  
  const fixesApplied = targetedHighImpactFix();
  
  if (fixesApplied > 0) {
    console.log('\n📊 Checking progress...');
    const finalErrors = getErrorCount();
    const reduction = initialErrors - finalErrors;
    
    console.log(`\n📈 TARGETED FIX RESULTS:`);
    console.log(`Initial errors: ${initialErrors}`);
    console.log(`Final errors: ${finalErrors}`);
    console.log(`Errors fixed: ${reduction}`);
    console.log(`Reduction: ${((reduction / initialErrors) * 100).toFixed(1)}%`);
    
    // Progress toward 2000 goal
    const remaining = finalErrors - 2000;
    if (remaining > 0) {
      console.log(`\n🎯 PROGRESS TOWARD 2000 GOAL:`);
      console.log(`Current: ${finalErrors} errors`);
      console.log(`Goal: 2000 errors`);
      console.log(`Still need to fix: ${remaining} errors`);
      console.log(`Progress: ${(((initialErrors - finalErrors) / (initialErrors - 2000)) * 100).toFixed(1)}% toward goal`);
      
      // Calculate total progress from original 3781
      const originalErrors = 3781;
      const totalReduction = originalErrors - finalErrors;
      console.log(`\n📊 OVERALL PROGRESS FROM START:`);
      console.log(`Original errors: ${originalErrors}`);
      console.log(`Current errors: ${finalErrors}`);
      console.log(`Total errors fixed: ${totalReduction}`);
      console.log(`Overall reduction: ${((totalReduction / originalErrors) * 100).toFixed(1)}%`);
    } else {
      console.log(`\n🎉 GOAL ACHIEVED! Under 2000 errors!`);
    }
  } else {
    console.log('\n⚠️  No fixes were applied. Files may already be fixed or patterns may need adjustment.');
  }
}

if (require.main === module) {
  main();
}

module.exports = { targetedHighImpactFix };
