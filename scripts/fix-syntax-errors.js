#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Function to recursively find all JS/JSX/TS/TSX files
function findFiles(dir, extensions = ['.js', '.jsx', '.ts', '.tsx']) {
  let results = [];
  const list = fs.readdirSync(dir);
  
  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat && stat.isDirectory()) {
      // Skip node_modules and other build directories
      if (!['node_modules', 'build', 'dist', '.git', 'android', 'ios'].includes(file)) {
        results = results.concat(findFiles(filePath, extensions));
      }
    } else {
      const ext = path.extname(file);
      if (extensions.includes(ext)) {
        results.push(filePath);
      }
    }
  });
  
  return results;
}

// Function to fix syntax errors in a file
function fixSyntaxErrors(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // Fix object syntax errors: {, -> {
    const objectSyntaxRegex = /\{\s*,/g;
    if (objectSyntaxRegex.test(content)) {
      content = content.replace(objectSyntaxRegex, '{');
      modified = true;
    }

    // Fix JSX syntax errors: ]}> -> }>
    const jsxSyntaxRegex = /\]\s*\}\s*>/g;
    if (jsxSyntaxRegex.test(content)) {
      content = content.replace(jsxSyntaxRegex, '}>');
      modified = true;
    }

    // Fix JSX syntax errors: ]} -> }
    const jsxSyntaxRegex2 = /\]\s*\}/g;
    if (jsxSyntaxRegex2.test(content)) {
      content = content.replace(jsxSyntaxRegex2, '}');
      modified = true;
    }

    // Fix array syntax errors: [} -> []
    const arraySyntaxRegex = /\[\s*\}/g;
    if (arraySyntaxRegex.test(content)) {
      content = content.replace(arraySyntaxRegex, '[]');
      modified = true;
    }

    // Fix missing closing brackets in style arrays: }> -> }>
    const styleBracketRegex = /(\w+)\s*}\s*>/g;
    if (styleBracketRegex.test(content)) {
      content = content.replace(styleBracketRegex, '$1}>');
      modified = true;
    }

    // Fix missing closing brackets in arrays: [0} -> [0]
    const arrayIndexRegex = /\[(\d+)\s*\}/g;
    if (arrayIndexRegex.test(content)) {
      content = content.replace(arrayIndexRegex, '[$1]');
      modified = true;
    }

    // Fix missing closing brackets in property access: .split('T')[0} -> .split('T')[0]
    const propertyAccessRegex = /\[(\d+)\s*\}/g;
    if (propertyAccessRegex.test(content)) {
      content = content.replace(propertyAccessRegex, '[$1]');
      modified = true;
    }

    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`Fixed syntax errors in: ${filePath}`);
      return true;
    }

    return false;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Main function
function main() {
  const srcDir = path.join(process.cwd(), 'src');
  
  if (!fs.existsSync(srcDir)) {
    console.error('src directory not found');
    process.exit(1);
  }
  
  console.log('Finding files to fix...');
  const files = findFiles(srcDir);
  
  console.log(`Found ${files.length} files to check`);
  
  let fixedCount = 0;
  files.forEach(file => {
    if (fixSyntaxErrors(file)) {
      fixedCount++;
    }
  });
  
  console.log(`\nFixed syntax errors in ${fixedCount} files`);
  
  if (fixedCount > 0) {
    console.log('\nRunning type check to verify fixes...');
    const { execSync } = require('child_process');
    try {
      execSync('npm run type-check', { stdio: 'inherit' });
      console.log('Type check passed!');
    } catch (error) {
      console.log('Some errors remain, but syntax errors have been fixed.');
    }
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixSyntaxErrors, findFiles };
