#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const glob = require('glob');

/**
 * StyleSheet Optimization Script
 * Converts inline styles to StyleSheet objects for better performance
 */

class StyleSheetOptimization {
  constructor() {
    this.projectDir = path.join(__dirname, '..');
    this.srcDir = path.join(this.projectDir, 'src');
    this.stats = {
      filesProcessed: 0,
      inlineStylesFound: 0,
      inlineStylesConverted: 0,
      styleSheetImportsAdded: 0,
      duplicateStylesRemoved: 0
    };
    
    this.commonStyles = new Map();
    this.styleHashes = new Map();
  }

  async optimizeStyleSheets() {
    console.log('🎨 Optimizing StyleSheets...');
    
    // Find all React Native files
    const files = await this.findReactNativeFiles();
    
    // Analyze existing styles
    await this.analyzeExistingStyles(files);
    
    // Convert inline styles to StyleSheet objects
    await this.convertInlineStyles(files);
    
    // Extract common styles
    await this.extractCommonStyles();
    
    // Create global style utilities
    await this.createStyleUtilities();
    
    // Generate optimization report
    await this.generateReport();
    
    this.printStats();
  }

  async findReactNativeFiles() {
    console.log('📁 Finding React Native files...');
    
    const files = glob.sync('**/*.{js,jsx,ts,tsx}', {
      cwd: this.srcDir,
      absolute: true,
      ignore: ['**/node_modules/**', '**/dist/**', '**/build/**']
    });
    
    // Filter files that likely contain React Native components
    const rnFiles = files.filter(file => {
      const content = fs.readFileSync(file, 'utf8');
      return content.includes('react-native') || 
             content.includes('StyleSheet') ||
             content.includes('style=') ||
             content.includes('View') ||
             content.includes('Text');
    });
    
    console.log(`Found ${rnFiles.length} React Native files`);
    return rnFiles;
  }

  async analyzeExistingStyles(files) {
    console.log('🔍 Analyzing existing styles...');
    
    for (const file of files) {
      const content = fs.readFileSync(file, 'utf8');
      
      // Find existing StyleSheet.create calls
      const styleSheetMatches = content.matchAll(/StyleSheet\.create\s*\(\s*{([\s\S]*?)}\s*\)/g);
      for (const match of styleSheetMatches) {
        this.parseStyleObject(match[1], file);
      }
      
      // Find inline styles
      const inlineMatches = content.matchAll(/style\s*=\s*{([^}]+)}/g);
      for (const match of inlineMatches) {
        this.stats.inlineStylesFound++;
        this.parseInlineStyle(match[1], file);
      }
    }
    
    console.log(`Found ${this.stats.inlineStylesFound} inline styles`);
  }

  parseStyleObject(styleContent, file) {
    // Parse individual style properties
    const properties = styleContent.split(',');
    
    for (const prop of properties) {
      const trimmed = prop.trim();
      if (trimmed) {
        const hash = this.generateStyleHash(trimmed);
        if (!this.commonStyles.has(hash)) {
          this.commonStyles.set(hash, {
            property: trimmed,
            count: 0,
            files: new Set()
          });
        }
        
        const style = this.commonStyles.get(hash);
        style.count++;
        style.files.add(file);
      }
    }
  }

  parseInlineStyle(styleContent, file) {
    // Similar to parseStyleObject but for inline styles
    this.parseStyleObject(styleContent, file);
  }

  generateStyleHash(styleProperty) {
    return styleProperty.replace(/\s+/g, '').toLowerCase();
  }

  async convertInlineStyles(files) {
    console.log('🔄 Converting inline styles to StyleSheet objects...');
    
    for (const file of files) {
      let content = fs.readFileSync(file, 'utf8');
      let modified = false;
      
      // Check if StyleSheet is already imported
      const hasStyleSheetImport = content.includes('StyleSheet');
      
      // Find all inline styles
      const inlineStyles = [];
      const inlineMatches = content.matchAll(/style\s*=\s*{([^}]+)}/g);
      
      for (const match of inlineMatches) {
        inlineStyles.push({
          full: match[0],
          content: match[1],
          index: match.index
        });
      }
      
      if (inlineStyles.length > 0) {
        // Add StyleSheet import if not present
        if (!hasStyleSheetImport) {
          content = this.addStyleSheetImport(content);
          this.stats.styleSheetImportsAdded++;
          modified = true;
        }
        
        // Convert inline styles to StyleSheet references
        const { newContent, styleObject } = this.convertToStyleSheet(content, inlineStyles);
        
        if (styleObject) {
          // Add StyleSheet.create at the end of the file
          const styleSheetCode = this.generateStyleSheetCode(styleObject);
          const finalContent = this.insertStyleSheet(newContent, styleSheetCode);
          
          content = finalContent;
          this.stats.inlineStylesConverted += inlineStyles.length;
          modified = true;
        }
      }
      
      if (modified) {
        fs.writeFileSync(file, content, 'utf8');
        this.stats.filesProcessed++;
      }
    }
    
    console.log(`Converted ${this.stats.inlineStylesConverted} inline styles`);
  }

  addStyleSheetImport(content) {
    // Find existing React Native import
    const rnImportMatch = content.match(/(import\s+{[^}]*})\s+from\s+['"]react-native['"]/); 
    
    if (rnImportMatch) {
      // Add StyleSheet to existing import
      const imports = rnImportMatch[1];
      if (!imports.includes('StyleSheet')) {
        const newImports = imports.replace('}', ', StyleSheet}');
        return content.replace(rnImportMatch[0], rnImportMatch[0].replace(imports, newImports));
      }
    } else {
      // Add new import at the top
      const importLine = "import { StyleSheet } from 'react-native';\n";
      return importLine + content;
    }
    
    return content;
  }

  convertToStyleSheet(content, inlineStyles) {
    let newContent = content;
    const styleObject = {};
    let styleCounter = 1;
    
    // Process inline styles from end to beginning to maintain indices
    const sortedStyles = inlineStyles.sort((a, b) => b.index - a.index);
    
    for (const style of sortedStyles) {
      const styleName = `style${styleCounter}`;
      const styleProps = this.parseStyleProperties(style.content);
      
      // Check if this style already exists
      const existingStyleName = this.findExistingStyle(styleObject, styleProps);
      
      if (existingStyleName) {
        // Use existing style
        newContent = newContent.substring(0, style.index) + 
                    `style={styles.${existingStyleName}}` + 
                    newContent.substring(style.index + style.full.length);
      } else {
        // Create new style
        styleObject[styleName] = styleProps;
        newContent = newContent.substring(0, style.index) + 
                    `style={styles.${styleName}}` + 
                    newContent.substring(style.index + style.full.length);
        styleCounter++;
      }
    }
    
    return { newContent, styleObject: Object.keys(styleObject).length > 0 ? styleObject : null };
  }

  parseStyleProperties(styleContent) {
    const properties = {};
    
    // Split by comma and parse each property
    const props = styleContent.split(',');
    
    for (const prop of props) {
      const trimmed = prop.trim();
      if (trimmed) {
        const colonIndex = trimmed.indexOf(':');
        if (colonIndex > 0) {
          const key = trimmed.substring(0, colonIndex).trim();
          const value = trimmed.substring(colonIndex + 1).trim();
          properties[key] = value;
        }
      }
    }
    
    return properties;
  }

  findExistingStyle(styleObject, newStyleProps) {
    for (const [styleName, existingProps] of Object.entries(styleObject)) {
      if (this.areStylesEqual(existingProps, newStyleProps)) {
        this.stats.duplicateStylesRemoved++;
        return styleName;
      }
    }
    return null;
  }

  areStylesEqual(style1, style2) {
    const keys1 = Object.keys(style1).sort();
    const keys2 = Object.keys(style2).sort();
    
    if (keys1.length !== keys2.length) return false;
    
    for (let i = 0; i < keys1.length; i++) {
      if (keys1[i] !== keys2[i] || style1[keys1[i]] !== style2[keys2[i]]) {
        return false;
      }
    }
    
    return true;
  }

  generateStyleSheetCode(styleObject) {
    let code = '\nconst styles = StyleSheet.create({\n';
    
    for (const [styleName, properties] of Object.entries(styleObject)) {
      code += `  ${styleName}: {\n`;
      
      for (const [key, value] of Object.entries(properties)) {
        code += `    ${key}: ${value},\n`;
      }
      
      code += '  },\n';
    }
    
    code += '});\n';
    return code;
  }

  insertStyleSheet(content, styleSheetCode) {
    // Find the best place to insert the StyleSheet
    // Try to insert before the last export or at the end
    
    const exportMatch = content.lastIndexOf('export default');
    if (exportMatch > 0) {
      return content.substring(0, exportMatch) + 
             styleSheetCode + '\n' + 
             content.substring(exportMatch);
    }
    
    // Insert at the end
    return content + '\n' + styleSheetCode;
  }

  async extractCommonStyles() {
    console.log('🎯 Extracting common styles...');
    
    // Find styles that appear in multiple files
    const commonStylesArray = Array.from(this.commonStyles.values())
      .filter(style => style.count >= 3 && style.files.size >= 2)
      .sort((a, b) => b.count - a.count);
    
    if (commonStylesArray.length > 0) {
      console.log(`Found ${commonStylesArray.length} common styles`);
    }
  }

  async createStyleUtilities() {
    console.log('🛠️ Creating style utilities...');
    
    // Create styles directory
    const stylesDir = path.join(this.srcDir, 'styles');
    if (!fs.existsSync(stylesDir)) {
      fs.mkdirSync(stylesDir, { recursive: true });
    }
    
    // Create common styles
    await this.createCommonStyles(stylesDir);
    
    // Create theme styles
    await this.createThemeStyles(stylesDir);
    
    // Create layout styles
    await this.createLayoutStyles(stylesDir);
    
    // Create typography styles
    await this.createTypographyStyles(stylesDir);
    
    // Create index file
    await this.createStylesIndex(stylesDir);
  }

  async createCommonStyles(stylesDir) {
    const commonStylesPath = path.join(stylesDir, 'commonStyles.js');
    
    const content = `import { StyleSheet } from 'react-native';

/**
 * Common styles used across the application
 * Auto-generated by stylesheet-optimization.js
 */

export const commonStyles = StyleSheet.create({
  // Layout
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  column: {
    flexDirection: 'column',
  },
  spaceBetween: {
    justifyContent: 'space-between',
  },
  spaceAround: {
    justifyContent: 'space-around',
  },
  
  // Spacing
  padding: {
    padding: 16,
  },
  paddingHorizontal: {
    paddingHorizontal: 16,
  },
  paddingVertical: {
    paddingVertical: 16,
  },
  margin: {
    margin: 16,
  },
  marginHorizontal: {
    marginHorizontal: 16,
  },
  marginVertical: {
    marginVertical: 16,
  },
  
  // Borders
  border: {
    borderWidth: 1,
    borderColor: '#ddd',
  },
  borderRadius: {
    borderRadius: 8,
  },
  
  // Shadows
  shadow: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  
  // Text
  textCenter: {
    textAlign: 'center',
  },
  textBold: {
    fontWeight: 'bold',
  },
  
  // Colors
  backgroundPrimary: {
    backgroundColor: '#007AFF',
  },
  backgroundSecondary: {
    backgroundColor: '#f8f9fa',
  },
  backgroundDanger: {
    backgroundColor: '#dc3545',
  },
  backgroundSuccess: {
    backgroundColor: '#28a745',
  },
});

export default commonStyles;
`;
    
    fs.writeFileSync(commonStylesPath, content, 'utf8');
    console.log('✅ Created commonStyles.js');
  }

  async createThemeStyles(stylesDir) {
    const themeStylesPath = path.join(stylesDir, 'theme.js');
    
    const content = `/**
 * Theme configuration and styles
 * Auto-generated by stylesheet-optimization.js
 */

export const colors = {
  // Primary colors
  primary: '#007AFF',
  primaryDark: '#0056CC',
  primaryLight: '#4DA6FF',
  
  // Secondary colors
  secondary: '#6c757d',
  secondaryDark: '#545b62',
  secondaryLight: '#868e96',
  
  // Status colors
  success: '#28a745',
  danger: '#dc3545',
  warning: '#ffc107',
  info: '#17a2b8',
  
  // Neutral colors
  white: '#ffffff',
  black: '#000000',
  gray: '#6c757d',
  lightGray: '#f8f9fa',
  darkGray: '#343a40',
  
  // Background colors
  background: '#ffffff',
  backgroundSecondary: '#f8f9fa',
  backgroundDark: '#343a40',
  
  // Text colors
  text: '#212529',
  textSecondary: '#6c757d',
  textLight: '#ffffff',
  
  // Border colors
  border: '#dee2e6',
  borderLight: '#e9ecef',
  borderDark: '#adb5bd',
};

export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

export const typography = {
  // Font sizes
  fontSize: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24,
    xxxl: 32,
  },
  
  // Font weights
  fontWeight: {
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
  },
  
  // Line heights
  lineHeight: {
    tight: 1.2,
    normal: 1.4,
    relaxed: 1.6,
  },
};

export const borderRadius = {
  none: 0,
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  full: 9999,
};

export const shadows = {
  sm: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.18,
    shadowRadius: 1.0,
    elevation: 1,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.30,
    shadowRadius: 4.65,
    elevation: 8,
  },
};

export default {
  colors,
  spacing,
  typography,
  borderRadius,
  shadows,
};
`;
    
    fs.writeFileSync(themeStylesPath, content, 'utf8');
    console.log('✅ Created theme.js');
  }

  async createLayoutStyles(stylesDir) {
    const layoutStylesPath = path.join(stylesDir, 'layout.js');
    
    const content = `import { StyleSheet } from 'react-native';
import { spacing } from './theme';

/**
 * Layout utility styles
 * Auto-generated by stylesheet-optimization.js
 */

export const layout = StyleSheet.create({
  // Flex
  flex1: { flex: 1 },
  flex2: { flex: 2 },
  flex3: { flex: 3 },
  flexGrow: { flexGrow: 1 },
  flexShrink: { flexShrink: 1 },
  
  // Direction
  row: { flexDirection: 'row' },
  column: { flexDirection: 'column' },
  rowReverse: { flexDirection: 'row-reverse' },
  columnReverse: { flexDirection: 'column-reverse' },
  
  // Justify Content
  justifyStart: { justifyContent: 'flex-start' },
  justifyEnd: { justifyContent: 'flex-end' },
  justifyCenter: { justifyContent: 'center' },
  justifyBetween: { justifyContent: 'space-between' },
  justifyAround: { justifyContent: 'space-around' },
  justifyEvenly: { justifyContent: 'space-evenly' },
  
  // Align Items
  alignStart: { alignItems: 'flex-start' },
  alignEnd: { alignItems: 'flex-end' },
  alignCenter: { alignItems: 'center' },
  alignStretch: { alignItems: 'stretch' },
  alignBaseline: { alignItems: 'baseline' },
  
  // Align Self
  alignSelfStart: { alignSelf: 'flex-start' },
  alignSelfEnd: { alignSelf: 'flex-end' },
  alignSelfCenter: { alignSelf: 'center' },
  alignSelfStretch: { alignSelf: 'stretch' },
  alignSelfBaseline: { alignSelf: 'baseline' },
  
  // Position
  absolute: { position: 'absolute' },
  relative: { position: 'relative' },
  
  // Common layouts
  container: {
    flex: 1,
    padding: spacing.md,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullScreen: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
});

export default layout;
`;
    
    fs.writeFileSync(layoutStylesPath, content, 'utf8');
    console.log('✅ Created layout.js');
  }

  async createTypographyStyles(stylesDir) {
    const typographyStylesPath = path.join(stylesDir, 'typography.js');
    
    const content = `import { StyleSheet } from 'react-native';
import { typography, colors } from './theme';

/**
 * Typography utility styles
 * Auto-generated by stylesheet-optimization.js
 */

export const textStyles = StyleSheet.create({
  // Headings
  h1: {
    fontSize: typography.fontSize.xxxl,
    fontWeight: typography.fontWeight.bold,
    lineHeight: typography.lineHeight.tight,
    color: colors.text,
  },
  h2: {
    fontSize: typography.fontSize.xxl,
    fontWeight: typography.fontWeight.bold,
    lineHeight: typography.lineHeight.tight,
    color: colors.text,
  },
  h3: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.semibold,
    lineHeight: typography.lineHeight.normal,
    color: colors.text,
  },
  h4: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    lineHeight: typography.lineHeight.normal,
    color: colors.text,
  },
  h5: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium,
    lineHeight: typography.lineHeight.normal,
    color: colors.text,
  },
  h6: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    lineHeight: typography.lineHeight.normal,
    color: colors.text,
  },
  
  // Body text
  body: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.normal,
    lineHeight: typography.lineHeight.normal,
    color: colors.text,
  },
  bodySmall: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.normal,
    lineHeight: typography.lineHeight.normal,
    color: colors.text,
  },
  bodyLarge: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.normal,
    lineHeight: typography.lineHeight.normal,
    color: colors.text,
  },
  
  // Text variants
  caption: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.normal,
    lineHeight: typography.lineHeight.normal,
    color: colors.textSecondary,
  },
  overline: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.medium,
    lineHeight: typography.lineHeight.normal,
    color: colors.textSecondary,
    textTransform: 'uppercase',
    letterSpacing: 1,
  },
  
  // Text alignment
  textLeft: { textAlign: 'left' },
  textCenter: { textAlign: 'center' },
  textRight: { textAlign: 'right' },
  textJustify: { textAlign: 'justify' },
  
  // Text colors
  textPrimary: { color: colors.primary },
  textSecondary: { color: colors.textSecondary },
  textSuccess: { color: colors.success },
  textDanger: { color: colors.danger },
  textWarning: { color: colors.warning },
  textInfo: { color: colors.info },
  textLight: { color: colors.textLight },
  
  // Text decoration
  underline: { textDecorationLine: 'underline' },
  lineThrough: { textDecorationLine: 'line-through' },
  noDecoration: { textDecorationLine: 'none' },
  
  // Text transform
  uppercase: { textTransform: 'uppercase' },
  lowercase: { textTransform: 'lowercase' },
  capitalize: { textTransform: 'capitalize' },
});

export default textStyles;
`;
    
    fs.writeFileSync(typographyStylesPath, content, 'utf8');
    console.log('✅ Created typography.js');
  }

  async createStylesIndex(stylesDir) {
    const indexPath = path.join(stylesDir, 'index.js');
    
    const content = `/**
 * Styles index
 * Auto-generated by stylesheet-optimization.js
 */

export { default as commonStyles } from './commonStyles';
export { default as theme, colors, spacing, typography, borderRadius, shadows } from './theme';
export { default as layout } from './layout';
export { default as textStyles } from './typography';

// Re-export everything for convenience
export * from './commonStyles';
export * from './theme';
export * from './layout';
export * from './typography';
`;
    
    fs.writeFileSync(indexPath, content, 'utf8');
    console.log('✅ Created styles index');
  }

  async generateReport() {
    console.log('📊 Generating stylesheet optimization report...');
    
    const reportPath = path.join(this.projectDir, 'docs', 'STYLESHEET_OPTIMIZATION_REPORT.md');
    const docsDir = path.dirname(reportPath);
    
    if (!fs.existsSync(docsDir)) {
      fs.mkdirSync(docsDir, { recursive: true });
    }
    
    let content = `# StyleSheet Optimization Report\n\n`;
    content += `Generated on: ${new Date().toISOString()}\n\n`;
    
    content += `## Summary\n`;
    content += `- **Files Processed:** ${this.stats.filesProcessed}\n`;
    content += `- **Inline Styles Found:** ${this.stats.inlineStylesFound}\n`;
    content += `- **Inline Styles Converted:** ${this.stats.inlineStylesConverted}\n`;
    content += `- **StyleSheet Imports Added:** ${this.stats.styleSheetImportsAdded}\n`;
    content += `- **Duplicate Styles Removed:** ${this.stats.duplicateStylesRemoved}\n\n`;
    
    content += `## Benefits\n\n`;
    content += `### Performance Improvements\n`;
    content += `- **Reduced Bridge Traffic:** StyleSheet objects are created once and referenced by ID\n`;
    content += `- **Better Memory Usage:** Styles are cached and reused\n`;
    content += `- **Faster Rendering:** No need to parse inline styles on each render\n\n`;
    
    content += `### Code Quality\n`;
    content += `- **Consistency:** Centralized style definitions\n`;
    content += `- **Maintainability:** Easier to update and modify styles\n`;
    content += `- **Reusability:** Common styles can be shared across components\n\n`;
    
    content += `## Style Utilities Created\n\n`;
    content += `- **\`src/styles/commonStyles.js\`:** Common layout and utility styles\n`;
    content += `- **\`src/styles/theme.js\`:** Theme configuration (colors, spacing, typography)\n`;
    content += `- **\`src/styles/layout.js\`:** Flexbox and layout utilities\n`;
    content += `- **\`src/styles/typography.js\`:** Text and typography styles\n`;
    content += `- **\`src/styles/index.js\`:** Centralized exports\n\n`;
    
    content += `## Usage Examples\n\n`;
    content += `### Before Optimization\n`;
    content += `\`\`\`javascript\n`;
    content += `<View style={{flex: 1, padding: 16, backgroundColor: '#fff'}}>\n`;
    content += `  <Text style={{fontSize: 18, fontWeight: 'bold', textAlign: 'center'}}>\n`;
    content += `    Hello World\n`;
    content += `  </Text>\n`;
    content += `</View>\n`;
    content += `\`\`\`\n\n`;
    
    content += `### After Optimization\n`;
    content += `\`\`\`javascript\n`;
    content += `import { commonStyles, textStyles } from '../styles';\n\n`;
    content += `<View style={[commonStyles.container, commonStyles.backgroundPrimary]}>\n`;
    content += `  <Text style={[textStyles.h3, textStyles.textCenter]}>\n`;
    content += `    Hello World\n`;
    content += `  </Text>\n`;
    content += `</View>\n`;
    content += `\`\`\`\n\n`;
    
    content += `## Best Practices\n\n`;
    content += `1. **Use StyleSheet.create():** Always wrap styles in StyleSheet.create()\n`;
    content += `2. **Avoid Inline Styles:** Use StyleSheet references instead\n`;
    content += `3. **Leverage Style Arrays:** Combine multiple styles using arrays\n`;
    content += `4. **Use Theme Variables:** Reference theme colors and spacing\n`;
    content += `5. **Create Reusable Styles:** Extract common patterns into utilities\n\n`;
    
    content += `## Next Steps\n\n`;
    content += `1. **Review Generated Styles:** Check the new style utilities\n`;
    content += `2. **Update Components:** Use the new style utilities in components\n`;
    content += `3. **Test Performance:** Measure the performance improvements\n`;
    content += `4. **Establish Guidelines:** Create style guidelines for the team\n`;
    
    fs.writeFileSync(reportPath, content, 'utf8');
    console.log('✅ Created stylesheet optimization report');
  }

  printStats() {
    console.log('\n📊 StyleSheet Optimization Results:');
    console.log(`Files processed: ${this.stats.filesProcessed}`);
    console.log(`Inline styles found: ${this.stats.inlineStylesFound}`);
    console.log(`Inline styles converted: ${this.stats.inlineStylesConverted}`);
    console.log(`StyleSheet imports added: ${this.stats.styleSheetImportsAdded}`);
    console.log(`Duplicate styles removed: ${this.stats.duplicateStylesRemoved}`);
    
    const conversionRate = this.stats.inlineStylesFound > 0 
      ? ((this.stats.inlineStylesConverted / this.stats.inlineStylesFound) * 100).toFixed(1)
      : 0;
    
    console.log(`\n📈 Conversion rate: ${conversionRate}%`);
    
    console.log('\n🎨 StyleSheet optimization completed!');
    console.log('📖 Check docs/STYLESHEET_OPTIMIZATION_REPORT.md for detailed analysis');
    console.log('🎯 Use the new style utilities in src/styles/ for better performance');
  }
}

// Run the stylesheet optimization
if (require.main === module) {
  const optimization = new StyleSheetOptimization();
  optimization.optimizeStyleSheets().catch(console.error);
}

module.exports = StyleSheetOptimization;