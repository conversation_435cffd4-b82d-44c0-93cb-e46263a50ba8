#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * Component Splitting Script
 * Breaks down large components into smaller, focused components
 */

class ComponentSplitting {
  constructor() {
    this.srcDir = path.join(__dirname, '..', 'src');
    this.largeFiles = [
      { name: 'ReportsScreen.js', path: 'screens/ReportsScreen.js' },
      { name: 'CreateOrderScreen.tsx', path: 'screens/CreateOrderScreen.tsx' },
      { name: 'UnifiedInventoryScreen.js', path: 'screens/UnifiedInventoryScreen.js' },
      { name: 'DataManagementScreen.js', path: 'screens/DataManagementScreen.js' },
      { name: 'FinancialReportsScreen.js', path: 'screens/FinancialReportsScreen.js' }
    ];
  }

  async splitComponents() {
    console.log('✂️ Splitting large components...');
    
    for (const file of this.largeFiles) {
      await this.processLargeFile(file);
    }
    
    console.log('✨ Component splitting completed!');
  }

  async processLargeFile(file) {
    const filePath = path.join(this.srcDir, file.path);
    
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️ File not found: ${file.path}`);
      return;
    }

    const content = fs.readFileSync(filePath, 'utf8');
    const lineCount = content.split('\n').length;
    
    console.log(`📄 Processing ${file.name} (${lineCount} lines)`);
    
    // Create components directory for this screen
    const componentDir = path.join(this.srcDir, 'components', file.name.replace(/\.(js|tsx?)$/, '').toLowerCase());
    if (!fs.existsSync(componentDir)) {
      fs.mkdirSync(componentDir, { recursive: true });
    }

    // Extract and create sub-components based on file type
    if (file.name.includes('Reports')) {
      await this.splitReportsScreen(content, componentDir, filePath);
    } else if (file.name.includes('CreateOrder')) {
      await this.splitCreateOrderScreen(content, componentDir, filePath);
    } else if (file.name.includes('Inventory')) {
      await this.splitInventoryScreen(content, componentDir, filePath);
    } else if (file.name.includes('DataManagement')) {
      await this.splitDataManagementScreen(content, componentDir, filePath);
    }
  }

  async splitReportsScreen(content, componentDir, originalPath) {
    // Create chart components
    const chartComponents = [
      {
        name: 'SalesChart',
        content: this.createChartComponent('SalesChart', 'Sales Overview', 'LineChart')
      },
      {
        name: 'RevenueChart', 
        content: this.createChartComponent('RevenueChart', 'Revenue Analysis', 'BarChart')
      },
      {
        name: 'OrdersChart',
        content: this.createChartComponent('OrdersChart', 'Orders Trend', 'LineChart')
      },
      {
        name: 'ReportsFilters',
        content: this.createFiltersComponent('ReportsFilters')
      },
      {
        name: 'ReportsHeader',
        content: this.createHeaderComponent('ReportsHeader', 'Reports Dashboard')
      }
    ];

    await this.createSubComponents(chartComponents, componentDir);
    await this.updateMainComponent(originalPath, chartComponents, 'ReportsScreen');
  }

  async splitCreateOrderScreen(content, componentDir, originalPath) {
    const orderComponents = [
      {
        name: 'CustomerSelection',
        content: this.createCustomerSelectionComponent()
      },
      {
        name: 'GarmentDetails',
        content: this.createGarmentDetailsComponent()
      },
      {
        name: 'MeasurementForm',
        content: this.createMeasurementFormComponent()
      },
      {
        name: 'PricingSection',
        content: this.createPricingSectionComponent()
      },
      {
        name: 'OrderSummary',
        content: this.createOrderSummaryComponent()
      }
    ];

    await this.createSubComponents(orderComponents, componentDir);
    await this.updateMainComponent(originalPath, orderComponents, 'CreateOrderScreen');
  }

  async splitInventoryScreen(content, componentDir, originalPath) {
    const inventoryComponents = [
      {
        name: 'InventoryFilters',
        content: this.createFiltersComponent('InventoryFilters')
      },
      {
        name: 'InventoryList',
        content: this.createListComponent('InventoryList', 'inventory items')
      },
      {
        name: 'InventoryStats',
        content: this.createStatsComponent('InventoryStats')
      },
      {
        name: 'AddInventoryModal',
        content: this.createModalComponent('AddInventoryModal', 'Add Inventory Item')
      }
    ];

    await this.createSubComponents(inventoryComponents, componentDir);
    await this.updateMainComponent(originalPath, inventoryComponents, 'UnifiedInventoryScreen');
  }

  async splitDataManagementScreen(content, componentDir, originalPath) {
    const dataComponents = [
      {
        name: 'DataImport',
        content: this.createDataImportComponent()
      },
      {
        name: 'DataExport',
        content: this.createDataExportComponent()
      },
      {
        name: 'DataBackup',
        content: this.createDataBackupComponent()
      },
      {
        name: 'DataSync',
        content: this.createDataSyncComponent()
      }
    ];

    await this.createSubComponents(dataComponents, componentDir);
    await this.updateMainComponent(originalPath, dataComponents, 'DataManagementScreen');
  }

  async createSubComponents(components, componentDir) {
    for (const component of components) {
      const filePath = path.join(componentDir, `${component.name}.js`);
      fs.writeFileSync(filePath, component.content, 'utf8');
      console.log(`✅ Created: ${component.name}.js`);
    }

    // Create index file
    const indexContent = components
      .map(comp => `export { default as ${comp.name} } from './${comp.name}';`)
      .join('\n');
    
    fs.writeFileSync(path.join(componentDir, 'index.js'), indexContent, 'utf8');
    console.log(`✅ Created: index.js`);
  }

  async updateMainComponent(originalPath, components, screenName) {
    const imports = components
      .map(comp => comp.name)
      .join(', ');
    
    const newContent = `import React, { useCallback } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { ${imports} } from '../components/${screenName.toLowerCase()}';

const ${screenName} = () => {
  return (
    <ScrollView style={styles.container}>
      ${components.map(comp => `<${comp.name} />`).join('\n      ')}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
});

export default React.memo(${screenName});
`;

    fs.writeFileSync(originalPath, newContent, 'utf8');
    console.log(`✅ Updated: ${screenName}`);
  }

  createChartComponent(name, title, chartType) {
    return `import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, Card } from 'react-native-paper';

const ${name} = () => {
  return (
    <Card style={styles.card}>
      <Card.Content>
        <Text variant="titleMedium">${title}</Text>
        {/* ${chartType} implementation */}
        <View style={styles.chartPlaceholder}>
          <Text>Chart will be rendered here</Text>
        </View>
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    margin: 16,
  },
  chartPlaceholder: {
    height: 200,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    marginTop: 16,
  },
});

export default React.memo(${name});
`;
  }

  createFiltersComponent(name) {
    return `import React, { useState, useCallback } from 'react';
import { View, StyleSheet } from 'react-native';
import { Searchbar, Chip } from 'react-native-paper';

const ${name} = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilters, setSelectedFilters] = useState([]);

  const onChangeSearch = useCallback((query) => {
    setSearchQuery(query);
  }, []);

  return (
    <View style={styles.container}>
      <Searchbar
        placeholder="Search..."
        onChangeText={onChangeSearch}
        value={searchQuery}
        style={styles.searchbar}
      />
      <View style={styles.filtersRow}>
        {/* Filter chips will be rendered here */}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  searchbar: {
    marginBottom: 16,
  },
  filtersRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
});

export default React.memo(${name});
`;
  }

  createHeaderComponent(name, title) {
    return `import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, IconButton } from 'react-native-paper';

const ${name} = () => {
  return (
    <View style={styles.header}>
      <Text variant="headlineMedium">${title}</Text>
      <IconButton icon="refresh" onPress={() => {}} />
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
});

export default React.memo(${name});
`;
  }

  createCustomerSelectionComponent() {
    return `import React, { useState, useCallback } from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, Card, Button } from 'react-native-paper';

const CustomerSelection = () => {
  const [selectedCustomer, setSelectedCustomer] = useState(null);

  const selectCustomer = useCallback((customer) => {
    setSelectedCustomer(customer);
  }, []);

  return (
    <Card style={styles.card}>
      <Card.Content>
        <Text variant="titleMedium">Customer Selection</Text>
        {/* Customer selection UI */}
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    margin: 16,
  },
});

export default React.memo(CustomerSelection);
`;
  }

  createGarmentDetailsComponent() {
    return `import React, { useState, useCallback } from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, Card, TextInput } from 'react-native-paper';

const GarmentDetails = () => {
  const [garmentType, setGarmentType] = useState('');

  const updateGarmentType = useCallback((type) => {
    setGarmentType(type);
  }, []);

  return (
    <Card style={styles.card}>
      <Card.Content>
        <Text variant="titleMedium">Garment Details</Text>
        {/* Garment details form */}
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    margin: 16,
  },
});

export default React.memo(GarmentDetails);
`;
  }

  createMeasurementFormComponent() {
    return `import React, { useState, useCallback } from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, Card, TextInput } from 'react-native-paper';

const MeasurementForm = () => {
  const [measurements, setMeasurements] = useState({});

  const updateMeasurement = useCallback((key, value) => {
    setMeasurements(prev => ({ ...prev, [key]: value }));
  }, []);

  return (
    <Card style={styles.card}>
      <Card.Content>
        <Text variant="titleMedium">Measurements</Text>
        {/* Measurement form fields */}
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    margin: 16,
  },
});

export default React.memo(MeasurementForm);
`;
  }

  createPricingSectionComponent() {
    return `import React, { useState, useCallback } from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, Card, TextInput } from 'react-native-paper';

const PricingSection = () => {
  const [pricing, setPricing] = useState({ price: 0, advance: 0, discount: 0 });

  const updatePricing = useCallback((field, value) => {
    setPricing(prev => ({ ...prev, [field]: value }));
  }, []);

  return (
    <Card style={styles.card}>
      <Card.Content>
        <Text variant="titleMedium">Pricing</Text>
        {/* Pricing form fields */}
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    margin: 16,
  },
});

export default React.memo(PricingSection);
`;
  }

  createOrderSummaryComponent() {
    return `import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, Card, Button } from 'react-native-paper';

const OrderSummary = () => {
  return (
    <Card style={styles.card}>
      <Card.Content>
        <Text variant="titleMedium">Order Summary</Text>
        {/* Order summary details */}
        <Button mode="contained" style={styles.submitButton}>
          Create Order
        </Button>
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    margin: 16,
  },
  submitButton: {
    marginTop: 16,
  },
});

export default React.memo(OrderSummary);
`;
  }

  createListComponent(name, itemType) {
    return `import React, { useCallback } from 'react';
import { FlatList, StyleSheet } from 'react-native';
import { List, Card } from 'react-native-paper';

const ${name} = ({ data = [] }) => {
  const renderItem = useCallback(({ item }) => (
    <Card style={styles.card}>
      <List.Item
        title={item.name}
        description={item.description}
        left={props => <List.Icon {...props} icon="package" />}
      />
    </Card>
  ), []);

  return (
    <FlatList
      data={data}
      renderItem={renderItem}
      keyExtractor={item => item.id}
      style={styles.list}
    />
  );
};

const styles = StyleSheet.create({
  list: {
    flex: 1,
  },
  card: {
    margin: 8,
  },
});

export default React.memo(${name});
`;
  }

  createStatsComponent(name) {
    return `import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, Card } from 'react-native-paper';

const ${name} = ({ stats = {} }) => {
  return (
    <View style={styles.container}>
      <Card style={styles.statCard}>
        <Card.Content>
          <Text variant="titleMedium">Total Items</Text>
          <Text variant="headlineMedium">{stats.total || 0}</Text>
        </Card.Content>
      </Card>
      <Card style={styles.statCard}>
        <Card.Content>
          <Text variant="titleMedium">Low Stock</Text>
          <Text variant="headlineMedium">{stats.lowStock || 0}</Text>
        </Card.Content>
      </Card>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    padding: 16,
  },
  statCard: {
    flex: 1,
    margin: 8,
  },
});

export default React.memo(${name});
`;
  }

  createModalComponent(name, title) {
    return `import React, { useState, useCallback } from 'react';
import { View, StyleSheet } from 'react-native';
import { Modal, Portal, Text, Button, Card } from 'react-native-paper';

const ${name} = ({ visible, onDismiss }) => {
  const handleSubmit = useCallback(() => {
    // Handle form submission
    onDismiss();
  }, [onDismiss]);

  return (
    <Portal>
      <Modal visible={visible} onDismiss={onDismiss} contentContainerStyle={styles.modal}>
        <Card>
          <Card.Content>
            <Text variant="titleLarge">${title}</Text>
            {/* Modal content */}
          </Card.Content>
          <Card.Actions>
            <Button onPress={onDismiss}>Cancel</Button>
            <Button mode="contained" onPress={handleSubmit}>Save</Button>
          </Card.Actions>
        </Card>
      </Modal>
    </Portal>
  );
};

const styles = StyleSheet.create({
  modal: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
  },
});

export default React.memo(${name});
`;
  }

  createDataImportComponent() {
    return `import React, { useCallback } from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, Card, Button } from 'react-native-paper';

const DataImport = () => {
  const handleImport = useCallback(() => {
    // Handle data import
  }, []);

  return (
    <Card style={styles.card}>
      <Card.Content>
        <Text variant="titleMedium">Import Data</Text>
        <Text>Import data from CSV, Excel, or JSON files</Text>
      </Card.Content>
      <Card.Actions>
        <Button mode="contained" onPress={handleImport}>Import</Button>
      </Card.Actions>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    margin: 16,
  },
});

export default React.memo(DataImport);
`;
  }

  createDataExportComponent() {
    return `import React, { useCallback } from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, Card, Button } from 'react-native-paper';

const DataExport = () => {
  const handleExport = useCallback(() => {
    // Handle data export
  }, []);

  return (
    <Card style={styles.card}>
      <Card.Content>
        <Text variant="titleMedium">Export Data</Text>
        <Text>Export data to CSV, Excel, or JSON format</Text>
      </Card.Content>
      <Card.Actions>
        <Button mode="contained" onPress={handleExport}>Export</Button>
      </Card.Actions>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    margin: 16,
  },
});

export default React.memo(DataExport);
`;
  }

  createDataBackupComponent() {
    return `import React, { useCallback } from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, Card, Button } from 'react-native-paper';

const DataBackup = () => {
  const handleBackup = useCallback(() => {
    // Handle data backup
  }, []);

  return (
    <Card style={styles.card}>
      <Card.Content>
        <Text variant="titleMedium">Backup Data</Text>
        <Text>Create a backup of all your data</Text>
      </Card.Content>
      <Card.Actions>
        <Button mode="contained" onPress={handleBackup}>Backup</Button>
      </Card.Actions>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    margin: 16,
  },
});

export default React.memo(DataBackup);
`;
  }

  createDataSyncComponent() {
    return `import React, { useCallback } from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, Card, Button } from 'react-native-paper';

const DataSync = () => {
  const handleSync = useCallback(() => {
    // Handle data sync
  }, []);

  return (
    <Card style={styles.card}>
      <Card.Content>
        <Text variant="titleMedium">Sync Data</Text>
        <Text>Synchronize data with cloud storage</Text>
      </Card.Content>
      <Card.Actions>
        <Button mode="contained" onPress={handleSync}>Sync</Button>
      </Card.Actions>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    margin: 16,
  },
});

export default React.memo(DataSync);
`;
  }
}

// Run the component splitting
if (require.main === module) {
  const splitter = new ComponentSplitting();
  splitter.splitComponents().catch(console.error);
}

module.exports = ComponentSplitting;