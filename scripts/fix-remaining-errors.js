#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Files that still have significant errors
const REMAINING_PROBLEM_FILES = [
  'src/components/content/AnalyticsContent.js',
  'src/components/content/ProductDetailsContent.js',
  'src/components/charts/AdvancedTailoringCharts.js',
  'src/components/charts/NativeCharts.js',
  'src/services/PrintService.js',
  'src/services/SQLiteService.js',
  'src/services/storageService.js',
  'src/utils/pdfInvoiceGenerator.js'
];

// Function to recursively find all JS/JSX/TS/TSX files
function findFiles(dir, extensions = ['.js', '.jsx', '.ts', '.tsx']) {
  let results = [];
  const list = fs.readdirSync(dir);
  
  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat && stat.isDirectory()) {
      if (!['node_modules', 'build', 'dist', '.git', 'android', 'ios', 'scripts'].includes(file)) {
        results = results.concat(findFiles(filePath, extensions));
      }
    } else {
      const ext = path.extname(file);
      if (extensions.includes(ext)) {
        results.push(filePath);
      }
    }
  });
  
  return results;
}

// Advanced fix for remaining syntax errors
function fixRemainingSyntaxErrors(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 1. Fix JSX style prop bracket issues (most critical remaining)
    const jsxStyleFixes = [
      // Fix missing closing brackets in style arrays
      { pattern: /style=\{([^}]+) elevation=/g, replacement: 'style={$1} elevation=' },
      { pattern: /style=\{([^}]+) >/g, replacement: 'style={$1}>' },
      { pattern: /\{ color: ([^}]+) >/g, replacement: '{ color: $1 }>' },
      { pattern: /\{ backgroundColor: ([^}]+) >/g, replacement: '{ backgroundColor: $1 }>' },
      
      // Fix specific JSX prop patterns
      { pattern: /\[([^}]+), \{ ([^}]+) \} elevation=/g, replacement: '[$1, { $2 }] elevation=' },
      { pattern: /\[([^}]+), \{ ([^}]+) \} >/g, replacement: '[$1, { $2 }]>' },
      
      // Fix template literal in JSX
      { pattern: />\{`([^`]*\$\{[^}]+\}[^`]*) %`\}</g, replacement: '>{`$1 %`}<' },
      { pattern: />\{`([^`]*\$\{[^}]+\}[^`]*)`\}</g, replacement: '>{`$1`}<' }
    ];
    
    jsxStyleFixes.forEach(fix => {
      if (fix.pattern.test(content)) {
        content = content.replace(fix.pattern, fix.replacement);
        modified = true;
      }
    });
    
    // 2. Fix template literal issues (second priority)
    const templateFixes = [
      // Fix unterminated template literals
      { pattern: /`([^`]*\$\{[^}]+\}[^`]*)`;\s*$/gm, replacement: '`$1`;' },
      { pattern: /`([^`]*\$\{[^}]+\}[^`]*)`\s*$/gm, replacement: '`$1`' },
      
      // Fix specific template literal patterns
      { pattern: /Logger\.debug\(`([^`]*): \$\{([^}]+)\}`\);\s*$/gm, replacement: 'Logger.debug(`$1: ${$2}`);' },
      { pattern: /Logger\.error\(`([^`]*): \$\{([^}]+)\}`, ([^)]+)\);\s*$/gm, replacement: 'Logger.error(`$1: ${$2}`, $3);' },
      { pattern: /throw new StorageError\(`([^`]*): \$\{([^}]+)\}`,\s*([^)]+)\);\s*$/gm, replacement: 'throw new StorageError(`$1: ${$2}`, $3);' },
      
      // Fix template literals with extra backticks
      { pattern: /`([^`]*\$\{[^}]+\}[^`]*)`\s*`/g, replacement: '`$1`' },
      { pattern: /`([^`]*)`;\s*`/g, replacement: '`$1`;' }
    ];
    
    templateFixes.forEach(fix => {
      if (fix.pattern.test(content)) {
        content = content.replace(fix.pattern, fix.replacement);
        modified = true;
      }
    });
    
    // 3. Fix invalid characters and Unicode issues
    const unicodeFixes = [
      // Replace problematic Unicode characters
      { pattern: /৳/g, replacement: '৳' }, // Ensure proper encoding
      { pattern: /₹/g, replacement: '₹' }, // Ensure proper encoding
      { pattern: /•/g, replacement: '•' }, // Bullet point
      
      // Fix invalid character sequences
      { pattern: /\s+•\s+/g, replacement: ' • ' },
      { pattern: /\s+&\s+/g, replacement: ' & ' }
    ];
    
    unicodeFixes.forEach(fix => {
      if (fix.pattern.test(content)) {
        content = content.replace(fix.pattern, fix.replacement);
        modified = true;
      }
    });
    
    // 4. Fix method and function syntax issues
    const functionFixes = [
      // Fix async method syntax
      { pattern: /async ([a-zA-Z_$][a-zA-Z0-9_$]*)\(([^)]*)\)\s*\{/g, replacement: 'async $1($2) {' },
      
      // Fix method parameter issues
      { pattern: /static async ([a-zA-Z_$][a-zA-Z0-9_$]*)\(\$3\)/g, replacement: 'static async $1(key, value, expiry)' },
      
      // Fix catch/try block issues
      { pattern: /\}\s*catch\s*\(([^)]+)\)\s*\{/g, replacement: '} catch ($1) {' },
      { pattern: /try\s*\{/g, replacement: 'try {' }
    ];
    
    functionFixes.forEach(fix => {
      if (fix.pattern.test(content)) {
        content = content.replace(fix.pattern, fix.replacement);
        modified = true;
      }
    });
    
    // 5. Fix HTML-like content in JS files
    const htmlFixes = [
      // Remove HTML tags from JS files
      { pattern: /<body>/g, replacement: '' },
      { pattern: /<\/body>/g, replacement: '' },
      { pattern: /<html>/g, replacement: '' },
      { pattern: /<\/html>/g, replacement: '' },
      { pattern: /<head>/g, replacement: '' },
      { pattern: /<\/head>/g, replacement: '' },
      { pattern: /<style>/g, replacement: '' },
      { pattern: /<\/style>/g, replacement: '' },
      
      // Fix self-closing tags
      { pattern: /<br>/g, replacement: '<br />' },
      { pattern: /<hr>/g, replacement: '<hr />' }
    ];
    
    htmlFixes.forEach(fix => {
      if (fix.pattern.test(content)) {
        content = content.replace(fix.pattern, fix.replacement);
        modified = true;
      }
    });
    
    // 6. Fix object and export syntax
    const objectFixes = [
      // Fix object literal syntax
      { pattern: /export const ([A-Z_]+) = \{/g, replacement: 'export const $1 = {' },
      { pattern: /\]\s*;\s*$/gm, replacement: '];' },
      
      // Fix comma issues
      { pattern: /,\s*\]/g, replacement: ']' },
      { pattern: /,\s*\}/g, replacement: '}' }
    ];
    
    objectFixes.forEach(fix => {
      if (fix.pattern.test(content)) {
        content = content.replace(fix.pattern, fix.replacement);
        modified = true;
      }
    });
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Main function
function main() {
  console.log('Starting remaining syntax error fixes...');
  
  // First, fix the specific problem files
  let fixedCount = 0;
  let totalFiles = 0;
  
  console.log('\n=== Fixing High-Priority Problem Files ===');
  REMAINING_PROBLEM_FILES.forEach(file => {
    const filePath = path.join(process.cwd(), file);
    
    if (fs.existsSync(filePath)) {
      totalFiles++;
      console.log(`Processing: ${file}`);
      
      if (fixRemainingSyntaxErrors(filePath)) {
        fixedCount++;
        console.log(`  ✓ Fixed remaining errors in ${file}`);
      } else {
        console.log(`  - No changes needed in ${file}`);
      }
    } else {
      console.log(`  ⚠ File not found: ${file}`);
    }
  });
  
  // Then, fix all other files in src/
  console.log('\n=== Fixing All Other Files ===');
  const srcDir = path.join(process.cwd(), 'src');
  
  if (fs.existsSync(srcDir)) {
    const allFiles = findFiles(srcDir);
    
    allFiles.forEach(file => {
      // Skip files we already processed
      const relativePath = path.relative(process.cwd(), file);
      if (!REMAINING_PROBLEM_FILES.includes(relativePath)) {
        totalFiles++;
        
        if (fixRemainingSyntaxErrors(file)) {
          fixedCount++;
        }
      }
    });
  }
  
  console.log(`\nCompleted: Fixed remaining errors in ${fixedCount} out of ${totalFiles} files`);
  
  if (fixedCount > 0) {
    console.log('\nRunning type check to verify improvements...');
    const { execSync } = require('child_process');
    try {
      execSync('npm run type-check 2>&1 | tail -10', { stdio: 'inherit' });
    } catch (error) {
      console.log('Type check completed - checking final error count...');
    }
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixRemainingSyntaxErrors, REMAINING_PROBLEM_FILES };
