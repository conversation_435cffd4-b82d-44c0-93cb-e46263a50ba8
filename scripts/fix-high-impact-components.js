#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Generic function to fix JSX and template literal issues
function fixComponentFile(filePath, specificFixes = []) {
  if (!fs.existsSync(filePath)) {
    console.error(`${filePath} not found`);
    return false;
  }
  
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  
  // Apply specific fixes first
  specificFixes.forEach(fix => {
    if (fix.pattern.test(content)) {
      content = content.replace(fix.pattern, fix.replacement);
      modified = true;
    }
  });
  
  // Common JSX and template literal fixes
  const commonFixes = [
    // Fix JSX style prop bracket issues
    { pattern: /style=\{([^}]+)\}\]/g, replacement: 'style={$1}' },
    { pattern: /style=\{([^}]+)\}\}/g, replacement: 'style={$1}' },
    { pattern: /(\w+)=\{([^}]+)\}\}/g, replacement: '$1={$2}' },
    
    // Fix template literal issues
    { pattern: /`([^`]*)\$\{([^}]+)\} ,/g, replacement: "`$1${$2}`," },
    { pattern: /`([^`]*)\$\{([^}]+)\} ;/g, replacement: "`$1${$2}`;" },
    { pattern: /`([^`]*)\$\{([^}]+)\} \)/g, replacement: "`$1${$2}`)" },
    
    // Fix JSX closing bracket issues
    { pattern: /source=\{\{ uri: ([^}]+) \} style=/g, replacement: 'source={{ uri: $1 }} style=' },
    { pattern: /source=\{\{ uri: ([^}]+) \}/g, replacement: 'source={{ uri: $1 }}' },
    
    // Fix array syntax issues
    { pattern: /tags: ([^}]+)\[\]/g, replacement: 'tags: $1' },
    { pattern: /([^:]+): \[\]/g, replacement: '$1: []' },
    
    // Fix object literal issues
    { pattern: /\{ ([^:]+): ([^,}]+), \}/g, replacement: '{ $1: $2 }' },
    { pattern: /\{ ([^:]+): ([^,}]+)   \}/g, replacement: '{ $1: $2 }' },
    
    // Fix console statements
    { pattern: /console\.error\('([^']*): ', ([^)]+)\);/g, replacement: "console.error('$1:', $2);" },
    { pattern: /console\.log\('([^']*): ', ([^)]+)\);/g, replacement: "console.log('$1:', $2);" }
  ];
  
  commonFixes.forEach(fix => {
    if (fix.pattern.test(content)) {
      content = content.replace(fix.pattern, fix.replacement);
      modified = true;
    }
  });
  
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    return true;
  }
  
  return false;
}

// Fix AppNavigator.tsx
function fixAppNavigator() {
  const filePath = path.join(process.cwd(), 'src/navigation/AppNavigator.tsx');
  
  const specificFixes = [
    // Fix navigation component props
    { pattern: /screenOptions=\{([^}]+)\}\}/g, replacement: 'screenOptions={$1}' },
    { pattern: /options=\{([^}]+)\}\}/g, replacement: 'options={$1}' }
  ];
  
  return fixComponentFile(filePath, specificFixes);
}

// Fix SubscriptionScreen.js
function fixSubscriptionScreen() {
  const filePath = path.join(process.cwd(), 'src/screens/SubscriptionScreen.js');
  
  const specificFixes = [
    // Fix subscription-specific template literals
    { pattern: /`Subscription \$\{([^}]+)\} ,/g, replacement: "`Subscription ${$1}`," },
    { pattern: /`Plan: \$\{([^}]+)\} ,/g, replacement: "`Plan: ${$1}`," }
  ];
  
  return fixComponentFile(filePath, specificFixes);
}

// Fix GarmentTemplatesScreen.tsx
function fixGarmentTemplatesScreen() {
  const filePath = path.join(process.cwd(), 'src/screens/GarmentTemplatesScreen.tsx');
  
  const specificFixes = [
    // Fix TypeScript-specific issues
    { pattern: /: ([^=]+)=>/g, replacement: ': ($1) =>' },
    { pattern: /\(\$3\)/g, replacement: '()' }
  ];
  
  return fixComponentFile(filePath, specificFixes);
}

// Fix NotificationScreen.js
function fixNotificationScreen() {
  const filePath = path.join(process.cwd(), 'src/screens/NotificationScreen.js');
  
  const specificFixes = [
    // Fix notification-specific template literals
    { pattern: /`Notification \$\{([^}]+)\} ,/g, replacement: "`Notification ${$1}`," },
    { pattern: /`Alert: \$\{([^}]+)\} ,/g, replacement: "`Alert: ${$1}`," }
  ];
  
  return fixComponentFile(filePath, specificFixes);
}

// Fix CustomerDetailsScreen.js
function fixCustomerDetailsScreen() {
  const filePath = path.join(process.cwd(), 'src/screens/CustomerDetailsScreen.js');
  
  const specificFixes = [
    // Fix customer-specific template literals
    { pattern: /`Customer: \$\{([^}]+)\} ,/g, replacement: "`Customer: ${$1}`," },
    { pattern: /`Phone: \$\{([^}]+)\} ,/g, replacement: "`Phone: ${$1}`," }
  ];
  
  return fixComponentFile(filePath, specificFixes);
}

// Fix FabricsScreen.js
function fixFabricsScreen() {
  const filePath = path.join(process.cwd(), 'src/screens/FabricsScreen.js');
  
  const specificFixes = [
    // Fix fabric-specific template literals
    { pattern: /`Fabric: \$\{([^}]+)\} ,/g, replacement: "`Fabric: ${$1}`," },
    { pattern: /`Stock: \$\{([^}]+)\} ,/g, replacement: "`Stock: ${$1}`," }
  ];
  
  return fixComponentFile(filePath, specificFixes);
}

// Fix PaymentsScreen.js
function fixPaymentsScreen() {
  const filePath = path.join(process.cwd(), 'src/screens/PaymentsScreen.js');
  
  const specificFixes = [
    // Fix payment-specific template literals
    { pattern: /`Payment: \$\{([^}]+)\} ,/g, replacement: "`Payment: ${$1}`," },
    { pattern: /`Amount: \$\{([^}]+)\} ,/g, replacement: "`Amount: ${$1}`," }
  ];
  
  return fixComponentFile(filePath, specificFixes);
}

// Main function
function main() {
  console.log('Fixing high-impact component files...');
  
  const components = [
    { name: 'AppNavigator.tsx', fix: fixAppNavigator },
    { name: 'SubscriptionScreen.js', fix: fixSubscriptionScreen },
    { name: 'GarmentTemplatesScreen.tsx', fix: fixGarmentTemplatesScreen },
    { name: 'NotificationScreen.js', fix: fixNotificationScreen },
    { name: 'CustomerDetailsScreen.js', fix: fixCustomerDetailsScreen },
    { name: 'FabricsScreen.js', fix: fixFabricsScreen },
    { name: 'PaymentsScreen.js', fix: fixPaymentsScreen }
  ];
  
  let totalFixed = 0;
  
  components.forEach(component => {
    console.log(`Fixing ${component.name}...`);
    if (component.fix()) {
      console.log(`${component.name} fixed successfully`);
      totalFixed++;
    } else {
      console.log(`No changes needed for ${component.name}`);
    }
  });
  
  console.log(`\nFixed ${totalFixed} component files`);
}

if (require.main === module) {
  main();
}

module.exports = { 
  fixAppNavigator,
  fixSubscriptionScreen,
  fixGarmentTemplatesScreen,
  fixNotificationScreen,
  fixCustomerDetailsScreen,
  fixFabricsScreen,
  fixPaymentsScreen
};
