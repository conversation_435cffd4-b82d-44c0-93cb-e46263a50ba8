#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const glob = require('glob');

/**
 * Icon Optimization Script
 * Implements selective imports from @expo/vector-icons and optimizes icon usage
 */

class IconOptimization {
  constructor() {
    this.srcDir = path.join(__dirname, '..', 'src');
    this.stats = {
      filesProcessed: 0,
      iconImportsOptimized: 0,
      unusedIconsRemoved: 0,
      iconFamiliesConsolidated: 0
    };
    
    // Common icon mappings for consolidation
    this.iconMappings = {
      'MaterialIcons': {
        'home': 'home',
        'menu': 'menu',
        'search': 'search',
        'settings': 'settings',
        'person': 'person',
        'add': 'add',
        'edit': 'edit',
        'delete': 'delete',
        'save': 'save',
        'close': 'close'
      },
      'Ionicons': {
        'home-outline': 'home',
        'menu-outline': 'menu',
        'search-outline': 'search',
        'settings-outline': 'settings',
        'person-outline': 'person',
        'add-outline': 'add',
        'create-outline': 'edit',
        'trash-outline': 'delete',
        'save-outline': 'save',
        'close-outline': 'close'
      }
    };
  }

  async optimizeIcons() {
    console.log('🎨 Optimizing icon usage...');
    
    // First, analyze icon usage across the app
    const iconUsage = await this.analyzeIconUsage();
    
    // Find all component files
    const files = glob.sync('**/*.{js,jsx,ts,tsx}', {
      cwd: this.srcDir,
      absolute: true
    });

    for (const file of files) {
      await this.processFile(file, iconUsage);
    }

    // Generate optimized icon components
    await this.generateOptimizedIconComponents(iconUsage);

    this.printStats();
  }

  async analyzeIconUsage() {
    console.log('📊 Analyzing icon usage...');
    
    const iconUsage = {
      families: new Set(),
      icons: new Map(),
      totalUsage: 0
    };

    const files = glob.sync('**/*.{js,jsx,ts,tsx}', {
      cwd: this.srcDir,
      absolute: true
    });

    for (const file of files) {
      const content = fs.readFileSync(file, 'utf8');
      
      // Find icon imports
      const importMatches = content.matchAll(/import\s+{([^}]+)}\s+from\s+['"]@expo\/vector-icons['"]/g);
      for (const match of importMatches) {
        const families = match[1].split(',').map(f => f.trim());
        families.forEach(family => iconUsage.families.add(family));
      }

      // Find icon usage in JSX
      const iconMatches = content.matchAll(/<(\w+)\s+name=['"]([^'"]+)['"]/g);
      for (const match of iconMatches) {
        const [, family, iconName] = match;
        if (iconUsage.families.has(family)) {
          const key = `${family}:${iconName}`;
          iconUsage.icons.set(key, (iconUsage.icons.get(key) || 0) + 1);
          iconUsage.totalUsage++;
        }
      }
    }

    console.log(`Found ${iconUsage.families.size} icon families and ${iconUsage.icons.size} unique icons`);
    return iconUsage;
  }

  async processFile(filePath, iconUsage) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      let modifiedContent = content;
      let hasChanges = false;

      // Optimize icon imports
      const importResult = this.optimizeIconImports(modifiedContent);
      if (importResult.modified) {
        modifiedContent = importResult.content;
        hasChanges = true;
        this.stats.iconImportsOptimized++;
      }

      // Consolidate icon families
      const consolidateResult = this.consolidateIconFamilies(modifiedContent);
      if (consolidateResult.modified) {
        modifiedContent = consolidateResult.content;
        hasChanges = true;
        this.stats.iconFamiliesConsolidated++;
      }

      // Remove unused icon imports
      const unusedResult = this.removeUnusedIconImports(modifiedContent);
      if (unusedResult.modified) {
        modifiedContent = unusedResult.content;
        hasChanges = true;
        this.stats.unusedIconsRemoved++;
      }

      if (hasChanges) {
        fs.writeFileSync(filePath, modifiedContent, 'utf8');
        console.log(`✅ Optimized: ${path.relative(this.srcDir, filePath)}`);
      }

      this.stats.filesProcessed++;
    } catch (error) {
      console.error(`❌ Error processing ${filePath}:`, error.message);
    }
  }

  optimizeIconImports(content) {
    // Convert to selective imports
    const iconImportRegex = /import\s*{([^}]+)}\s*from\s*['"]@expo\/vector-icons['"]/g;
    let modified = false;
    
    const newContent = content.replace(iconImportRegex, (match, imports) => {
      const importList = imports.split(',').map(imp => imp.trim());
      
      // Convert to individual imports for better tree shaking
      const specificImports = importList.map(iconFamily => {
        return `import ${iconFamily} from '@expo/vector-icons/${iconFamily}';`;
      }).join('\n');
      
      modified = true;
      return specificImports;
    });

    return { content: newContent, modified };
  }

  consolidateIconFamilies(content) {
    // Consolidate multiple icon families to a single preferred family
    let modified = false;
    let newContent = content;

    // Replace Ionicons with MaterialIcons where possible
    const ioniconsRegex = /<Ionicons\s+name=['"]([^'"]+)['"]/g;
    newContent = newContent.replace(ioniconsRegex, (match, iconName) => {
      const materialEquivalent = this.findMaterialEquivalent(iconName);
      if (materialEquivalent) {
        modified = true;
        return `<MaterialIcons name="${materialEquivalent}"`;
      }
      return match;
    });

    // Update imports if we made changes
    if (modified) {
      // Add MaterialIcons import if not present
      if (!newContent.includes('MaterialIcons') && newContent.includes('<MaterialIcons')) {
        const importRegex = /(import.*from\s*['"]@expo\/vector-icons\/\w+['"]);/;
        if (importRegex.test(newContent)) {
          newContent = newContent.replace(importRegex, '$1\nimport MaterialIcons from \'@expo/vector-icons/MaterialIcons\';');
        }
      }
    }

    return { content: newContent, modified };
  }

  findMaterialEquivalent(ioniconsName) {
    const mappings = {
      'home-outline': 'home',
      'menu-outline': 'menu',
      'search-outline': 'search',
      'settings-outline': 'settings',
      'person-outline': 'person',
      'add-outline': 'add',
      'create-outline': 'edit',
      'trash-outline': 'delete',
      'save-outline': 'save',
      'close-outline': 'close',
      'chevron-back': 'arrow_back',
      'chevron-forward': 'arrow_forward',
      'checkmark': 'check',
      'information-circle': 'info'
    };
    
    return mappings[ioniconsName] || null;
  }

  removeUnusedIconImports(content) {
    let modified = false;
    let newContent = content;

    // Find all imported icon families
    const importedFamilies = [];
    const importMatches = content.matchAll(/import\s+(\w+)\s+from\s+['"]@expo\/vector-icons\/(\w+)['"]/g);
    for (const match of importMatches) {
      importedFamilies.push(match[1]);
    }

    // Check which families are actually used
    const usedFamilies = new Set();
    for (const family of importedFamilies) {
      const usageRegex = new RegExp(`<${family}\\s`, 'g');
      if (usageRegex.test(content)) {
        usedFamilies.add(family);
      }
    }

    // Remove unused imports
    for (const family of importedFamilies) {
      if (!usedFamilies.has(family)) {
        const importRegex = new RegExp(`import\\s+${family}\\s+from\\s+['"]@expo\/vector-icons\/${family}['"'];?\\s*\\n?`, 'g');
        newContent = newContent.replace(importRegex, '');
        modified = true;
      }
    }

    return { content: newContent, modified };
  }

  async generateOptimizedIconComponents(iconUsage) {
    const iconComponentsDir = path.join(this.srcDir, 'components', 'icons');
    
    if (!fs.existsSync(iconComponentsDir)) {
      fs.mkdirSync(iconComponentsDir, { recursive: true });
    }

    // Generate a unified icon component
    const unifiedIconContent = this.generateUnifiedIconComponent(iconUsage);
    fs.writeFileSync(path.join(iconComponentsDir, 'UnifiedIcon.js'), unifiedIconContent, 'utf8');
    
    // Generate icon constants
    const iconConstantsContent = this.generateIconConstants(iconUsage);
    fs.writeFileSync(path.join(iconComponentsDir, 'IconConstants.js'), iconConstantsContent, 'utf8');
    
    // Generate index file
    const indexContent = `export { default as UnifiedIcon } from './UnifiedIcon';\nexport * from './IconConstants';\n`;
    fs.writeFileSync(path.join(iconComponentsDir, 'index.js'), indexContent, 'utf8');
    
    console.log('✅ Generated optimized icon components');
  }

  generateUnifiedIconComponent(iconUsage) {
    return `import React from 'react';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import Ionicons from '@expo/vector-icons/Ionicons';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import { ICON_MAPPINGS } from './IconConstants';

/**
 * Unified Icon Component
 * Provides a single interface for all icons with automatic family selection
 */
const UnifiedIcon = ({ name, size = 24, color = '#000', family = 'auto', ...props }) => {
  // Auto-select icon family based on icon name
  const getIconFamily = (iconName) => {
    if (family !== 'auto') {
      return family;
    }
    
    // Check if icon exists in MaterialIcons first (preferred)
    if (ICON_MAPPINGS.MaterialIcons.includes(iconName)) {
      return 'MaterialIcons';
    }
    
    // Fallback to Ionicons
    if (ICON_MAPPINGS.Ionicons.includes(iconName)) {
      return 'Ionicons';
    }
    
    // Default to MaterialIcons
    return 'MaterialIcons';
  };

  const iconFamily = getIconFamily(name);
  const iconProps = { name, size, color, ...props };

  switch (iconFamily) {
    case 'MaterialIcons':
      return <MaterialIcons {...iconProps} />;
    case 'Ionicons':
      return <Ionicons {...iconProps} />;
    case 'FontAwesome':
      return <FontAwesome {...iconProps} />;
    default:
      return <MaterialIcons {...iconProps} />;
  }
};

export default React.memo(UnifiedIcon);
`;
  }

  generateIconConstants(iconUsage) {
    const materialIcons = [];
    const ionicons = [];
    const fontAwesome = [];

    // Extract icons by family
    for (const [iconKey] of iconUsage.icons) {
      const [family, iconName] = iconKey.split(':');
      switch (family) {
        case 'MaterialIcons':
          materialIcons.push(iconName);
          break;
        case 'Ionicons':
          ionicons.push(iconName);
          break;
        case 'FontAwesome':
          fontAwesome.push(iconName);
          break;
      }
    }

    return `/**
 * Icon Constants
 * Defines available icons for each family
 */

export const ICON_MAPPINGS = {
  MaterialIcons: [
    ${materialIcons.map(icon => `'${icon}'`).join(',\n    ')}
  ],
  Ionicons: [
    ${ionicons.map(icon => `'${icon}'`).join(',\n    ')}
  ],
  FontAwesome: [
    ${fontAwesome.map(icon => `'${icon}'`).join(',\n    ')}
  ]
};

// Common icon names for easy reference
export const COMMON_ICONS = {
  HOME: 'home',
  MENU: 'menu',
  SEARCH: 'search',
  SETTINGS: 'settings',
  USER: 'person',
  ADD: 'add',
  EDIT: 'edit',
  DELETE: 'delete',
  SAVE: 'save',
  CLOSE: 'close',
  BACK: 'arrow_back',
  FORWARD: 'arrow_forward',
  CHECK: 'check',
  INFO: 'info'
};
`;
  }

  printStats() {
    console.log('\n📊 Icon Optimization Results:');
    console.log(`Files processed: ${this.stats.filesProcessed}`);
    console.log(`Icon imports optimized: ${this.stats.iconImportsOptimized}`);
    console.log(`Icon families consolidated: ${this.stats.iconFamiliesConsolidated}`);
    console.log(`Unused icons removed: ${this.stats.unusedIconsRemoved}`);
    console.log('\n🎨 Icon optimization completed!');
  }
}

// Run the icon optimization
if (require.main === module) {
  const optimizer = new IconOptimization();
  optimizer.optimizeIcons().catch(console.error);
}

module.exports = IconOptimization;