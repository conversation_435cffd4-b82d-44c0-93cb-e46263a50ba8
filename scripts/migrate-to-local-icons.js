#!/usr/bin/env node

/**
 * Migrate to Local Icons Script
 * Replaces heavy icon library imports with lightweight LocalIcon
 * This will dramatically reduce bundle size
 */

const fs = require('fs');
const path = require('path');

class IconMigrator {
  constructor() {
    this.projectRoot = process.cwd();
    this.migratedFiles = [];
    this.errors = [];
  }

  // Get all source files
  getAllFiles(dir, extensions) {
    const files = [];
    
    const scan = (currentDir) => {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          scan(fullPath);
        } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
          files.push(fullPath);
        }
      }
    };
    
    scan(dir);
    return files;
  }

  // Migration patterns
  getMigrationPatterns() {
    return [
      // Replace MinimalIcon imports
      {
        pattern: /import MinimalIcon.*from.*MinimalIcon.*;\n?/g,
        replacement: "import LocalIcon, { NavigationLocalIcon, ActionLocalIcon, BusinessLocalIcon, SettingsLocalIcon, StatusLocalIcon } from '../components/LocalIcon';\n"
      },
      
      // Replace LightweightUnifiedIcon imports
      {
        pattern: /import LightweightUnifiedIcon.*from.*LightweightUnifiedIcon.*;\n?/g,
        replacement: "import LocalIcon, { NavigationLocalIcon, ActionLocalIcon, BusinessLocalIcon, SettingsLocalIcon, StatusLocalIcon } from '../components/LocalIcon';\n"
      },
      
      // Replace OptimizedUnifiedIcon imports
      {
        pattern: /import OptimizedUnifiedIcon.*from.*OptimizedUnifiedIcon.*;\n?/g,
        replacement: "import LocalIcon, { NavigationLocalIcon, ActionLocalIcon, BusinessLocalIcon, SettingsLocalIcon, StatusLocalIcon } from '../components/LocalIcon';\n"
      },
      
      // Replace FeatherIcon imports
      {
        pattern: /import FeatherIcon.*from.*FeatherIcon.*;\n?/g,
        replacement: "import LocalIcon from '../components/LocalIcon';\n"
      },
      
      // Replace PhosphorIcon imports
      {
        pattern: /import.*PhosphorIcon.*from.*PhosphorIcon.*;\n?/g,
        replacement: "import LocalIcon from '../components/LocalIcon';\n"
      },
      
      // Replace UnifiedIcon imports
      {
        pattern: /import.*UnifiedIcon.*from.*UnifiedIcon.*;\n?/g,
        replacement: "import LocalIcon from '../components/LocalIcon';\n"
      },
      
      // Replace component usage
      {
        pattern: /MinimalIcon/g,
        replacement: "LocalIcon"
      },
      {
        pattern: /LightweightUnifiedIcon/g,
        replacement: "LocalIcon"
      },
      {
        pattern: /OptimizedUnifiedIcon/g,
        replacement: "LocalIcon"
      },
      {
        pattern: /FeatherIcon/g,
        replacement: "LocalIcon"
      },
      {
        pattern: /PhosphorIcon/g,
        replacement: "LocalIcon"
      },
      {
        pattern: /UnifiedIcon/g,
        replacement: "LocalIcon"
      },
      
      // Replace preset component usage
      {
        pattern: /NavigationMinimalIcon/g,
        replacement: "NavigationLocalIcon"
      },
      {
        pattern: /ActionMinimalIcon/g,
        replacement: "ActionLocalIcon"
      },
      {
        pattern: /BusinessMinimalIcon/g,
        replacement: "BusinessLocalIcon"
      },
      {
        pattern: /SettingsMinimalIcon/g,
        replacement: "SettingsLocalIcon"
      },
      {
        pattern: /StatusMinimalIcon/g,
        replacement: "StatusLocalIcon"
      },
      
      {
        pattern: /NavigationLightweightIcon/g,
        replacement: "NavigationLocalIcon"
      },
      {
        pattern: /ActionLightweightIcon/g,
        replacement: "ActionLocalIcon"
      },
      {
        pattern: /BusinessLightweightIcon/g,
        replacement: "BusinessLocalIcon"
      },
      {
        pattern: /SettingsLightweightIcon/g,
        replacement: "SettingsLocalIcon"
      },
      {
        pattern: /StatusLightweightIcon/g,
        replacement: "StatusLocalIcon"
      },
      
      {
        pattern: /NavigationOptimizedIcon/g,
        replacement: "NavigationLocalIcon"
      },
      {
        pattern: /ActionOptimizedIcon/g,
        replacement: "ActionLocalIcon"
      },
      {
        pattern: /BusinessOptimizedIcon/g,
        replacement: "BusinessLocalIcon"
      },
      {
        pattern: /SettingsOptimizedIcon/g,
        replacement: "SettingsLocalIcon"
      },
      {
        pattern: /StatusOptimizedIcon/g,
        replacement: "StatusLocalIcon"
      },
    ];
  }

  // Migrate a single file
  migrateFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      let newContent = content;
      let hasChanges = false;
      
      const patterns = this.getMigrationPatterns();
      
      for (const { pattern, replacement } of patterns) {
        const beforeLength = newContent.length;
        newContent = newContent.replace(pattern, replacement);
        if (newContent.length !== beforeLength) {
          hasChanges = true;
        }
      }
      
      if (hasChanges) {
        fs.writeFileSync(filePath, newContent);
        this.migratedFiles.push(filePath);
        console.log(`✅ Migrated: ${path.relative(this.projectRoot, filePath)}`);
      }
      
    } catch (error) {
      this.errors.push({ file: filePath, error: error.message });
      console.error(`❌ Error migrating ${filePath}:`, error.message);
    }
  }

  // Create package.json backup and update dependencies
  updatePackageJson() {
    const packagePath = path.join(this.projectRoot, 'package.json');
    
    if (!fs.existsSync(packagePath)) {
      console.warn('⚠️  package.json not found');
      return;
    }
    
    // Create backup
    const backupPath = path.join(this.projectRoot, 'package.json.backup');
    fs.copyFileSync(packagePath, backupPath);
    console.log(`📦 Created backup: package.json.backup`);
    
    // Read and update package.json
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    
    // Dependencies to remove (they'll be commented out for safety)
    const heavyDependencies = [
      'phosphor-react-native',
      'react-native-vector-icons',
      '@expo/vector-icons'
    ];
    
    let hasChanges = false;
    
    // Add react-native-svg if not present
    if (!packageJson.dependencies['react-native-svg']) {
      packageJson.dependencies['react-native-svg'] = '^13.4.0';
      hasChanges = true;
      console.log('➕ Added react-native-svg dependency');
    }
    
    // Comment out heavy dependencies (don't remove yet for safety)
    for (const dep of heavyDependencies) {
      if (packageJson.dependencies[dep]) {
        console.log(`📝 Found ${dep} - consider removing after testing`);
      }
    }
    
    if (hasChanges) {
      fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2));
      console.log('📦 Updated package.json');
    }
  }

  // Generate migration report
  generateReport() {
    const reportPath = path.join(this.projectRoot, 'icon-migration-report.md');
    
    const report = `# Icon Migration Report

## Summary
- **Files migrated**: ${this.migratedFiles.length}
- **Errors**: ${this.errors.length}
- **Generated**: ${new Date().toISOString()}

## Migrated Files
${this.migratedFiles.map(file => `- ${path.relative(this.projectRoot, file)}`).join('\n')}

## Errors
${this.errors.map(error => `- ${path.relative(this.projectRoot, error.file)}: ${error.error}`).join('\n')}

## Next Steps

### 1. Install Dependencies
\`\`\`bash
npm install react-native-svg
# For iOS
cd ios && pod install
\`\`\`

### 2. Test the App
Run the app and verify all icons display correctly:
\`\`\`bash
npx expo start
\`\`\`

### 3. Remove Heavy Dependencies (After Testing)
Once you've verified everything works, remove the heavy icon libraries:
\`\`\`bash
npm uninstall phosphor-react-native react-native-vector-icons @expo/vector-icons
\`\`\`

### 4. Bundle Size Analysis
Compare bundle sizes before and after:
\`\`\`bash
npx expo start --clear
\`\`\`

## Expected Benefits
- **Bundle size reduction**: 80-90% smaller icon-related bundle
- **Faster loading**: No heavy icon libraries to load
- **Better performance**: Inline SVGs render faster
- **Maintainability**: Only icons you actually use

## Icon Coverage
The LocalIcon component includes the most critical icons used in your app:
- Navigation icons (home, shopping-bag, users, camera)
- Dashboard icons (CurrencyDollar, TrendUp, ClipboardText, etc.)
- Toast icons (check-circle, alert-circle, alert-triangle, info, x)
- Common UI icons (Package, Star, Ruler, plus, trending-up/down)

If you need additional icons, add them to the INLINE_ICONS object in LocalIcon.js.
`;

    fs.writeFileSync(reportPath, report);
    console.log(`📊 Generated migration report: ${reportPath}`);
  }

  // Main migration process
  migrate() {
    console.log('🚀 Starting icon migration to LocalIcon...');
    
    const sourceDir = path.join(this.projectRoot, 'src');
    const files = this.getAllFiles(sourceDir, ['.js', '.jsx', '.ts', '.tsx']);
    
    console.log(`📁 Found ${files.length} source files to migrate`);
    
    // Migrate each file
    for (const file of files) {
      this.migrateFile(file);
    }
    
    // Update package.json
    this.updatePackageJson();
    
    // Generate report
    this.generateReport();
    
    console.log('\n🎉 Icon migration completed!');
    console.log(`✅ Migrated ${this.migratedFiles.length} files`);
    console.log(`❌ ${this.errors.length} errors`);
    console.log('\n📋 Next steps:');
    console.log('1. Install react-native-svg: npm install react-native-svg');
    console.log('2. Test the app: npx expo start');
    console.log('3. Check icon-migration-report.md for details');
    console.log('4. Remove heavy icon libraries after testing');
  }
}

// Run the migration
if (require.main === module) {
  const migrator = new IconMigrator();
  migrator.migrate();
}

module.exports = IconMigrator;
