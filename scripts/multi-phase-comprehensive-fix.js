#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Multi-phase comprehensive fix strategy
class ComprehensiveErrorFixer {
  constructor() {
    this.totalFixed = 0;
    this.phaseResults = [];
  }

  // Phase 1: JSX Style Props - Most common pattern (400+ errors)
  async fixJSXStyleProps() {
    console.log('\n🔧 PHASE 1: JSX Style Props Fix');
    
    const jsxFiles = [
      'src/components/content/OrderDetailsContent.js',
      'src/components/content/OrderFormContent.js', 
      'src/components/content/ProductDetailsContent.js',
      'src/components/content/SearchResultsContent.js',
      'src/components/content/SettingsContent.js',
      'src/components/content/MapFeatureContent.js',
      'src/components/BottomNavBar.js',
      'src/components/CustomerSelectionModal.js',
      'src/components/CustomerSelector.js',
      'src/components/LoadingSpinner.js',
      'src/components/MD3FormField.js',
      'src/components/ProtectedRoute.js',
      'src/components/QRCodeGenerator.js',
      'src/components/UnifiedSortMenu.js',
      'src/components/UniversalCard.js'
    ];
    
    const fixes = [
      { pattern: /style=\{(\[styles\.[^,]+, \{ [^}]+ \})\s*([a-zA-Z]+)/g, replacement: 'style={$1]} $2' },
      { pattern: /style=\{(\[styles\.[^,]+, \{ [^}]+ \})\>/g, replacement: 'style={$1]}>' },
      { pattern: /style=\{(\[styles\.[^,]+, \{ [^}]+ \})\s*numberOfLines/g, replacement: 'style={$1]} numberOfLines' },
      { pattern: /style=\{(\[styles\.[^,]+, \{ [^}]+ \})\s*elevation/g, replacement: 'style={$1]} elevation' },
      { pattern: /iconColor=\{([^}]+)\}\s*([a-zA-Z]+)/g, replacement: 'iconColor={$1} $2' }
    ];
    
    return this.applyFixesToFiles(jsxFiles, fixes, 'JSX Style Props');
  }

  // Phase 2: Template Literals - Second most common (300+ errors)
  async fixTemplateLiterals() {
    console.log('\n🔧 PHASE 2: Template Literals Fix');
    
    const templateFiles = [
      'src/services/QRCodeService.js',
      'src/services/SimpleNotificationService.js',
      'src/services/SQLiteService.js',
      'src/utils/deepLinkUtils.ts',
      'src/utils/errorHandler.js',
      'src/utils/pdfInvoiceGenerator.js'
    ];
    
    const fixes = [
      { pattern: /`([^`]*)\$\{([^}]+)\}([^`]*)`([^;,)\]}])/g, replacement: '`$1${$2}$3`$4' },
      { pattern: /`([^`]*): \$\{([^}]+)\}`/g, replacement: '`$1: ${$2}`' },
      { pattern: /displayText: `([^`]*)\$\{([^}]+)\}([^`]*)`,/g, replacement: 'displayText: `$1${$2}$3`,' },
      { pattern: /const ([a-zA-Z_$][a-zA-Z0-9_$]*) = `([^`]*)\$\{([^}]+)\}([^`]*);/g, replacement: 'const $1 = `$2${$3}$4`;' },
      { pattern: /Alert\.alert\('([^']*)', `([^`]*): \$\{([^}]+)\}([^`]*)`\)/g, replacement: "Alert.alert('$1', `$2: ${$3}$4`)" }
    ];
    
    return this.applyFixesToFiles(templateFiles, fixes, 'Template Literals');
  }

  // Phase 3: Service Method Signatures - Third priority (200+ errors)
  async fixServiceMethods() {
    console.log('\n🔧 PHASE 3: Service Method Signatures Fix');
    
    const serviceFiles = [
      'src/services/MigrationService.js',
      'src/services/SQLiteService.js',
      'src/services/QRCodeService.js',
      'src/services/SimpleNotificationService.js',
      'src/services/OptimizedDataService.js',
      'src/services/NativePDFService.js'
    ];
    
    const fixes = [
      { pattern: /async ([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\(\s*([^)]*)\s*\)\s*\{/g, replacement: 'async $1($2) {' },
      { pattern: /([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\(\s*([^)]*)\s*\)\s*\{/g, replacement: '$1($2) {' },
      { pattern: /export default new ([A-Z][a-zA-Z0-9_$]*)\(\)\s*;/g, replacement: 'export default new $1();' },
      { pattern: /class ([A-Z][a-zA-Z0-9_$]*)\s*\{/g, replacement: 'class $1 {' },
      { pattern: /\}\s*catch\s*\(\s*([^)]+)\s*\)\s*\{/g, replacement: '} catch ($1) {' }
    ];
    
    return this.applyFixesToFiles(serviceFiles, fixes, 'Service Methods');
  }

  // Phase 4: Navigation & TypeScript - Fourth priority (150+ errors)
  async fixNavigationTypeScript() {
    console.log('\n🔧 PHASE 4: Navigation & TypeScript Fix');
    
    const tsFiles = [
      'src/navigation/AppNavigator.tsx',
      'src/services/OrderValidationService.ts',
      'src/utils/deepLinkUtils.ts',
      'src/hooks/useTypedNavigation.ts',
      'src/components/DatePicker.tsx',
      'src/navigation/TabNavigator.tsx'
    ];
    
    const fixes = [
      { pattern: /Screen\s+name="([^"]+)"\s+component=\{([^}]+)\}/g, replacement: 'Screen name="$1" component={$2}' },
      { pattern: /interface\s+([A-Z][a-zA-Z0-9_$]*)\s*\{/g, replacement: 'interface $1 {' },
      { pattern: /type\s+([A-Z][a-zA-Z0-9_$]*)\s*=/g, replacement: 'type $1 =' },
      { pattern: /:\s*([A-Z][a-zA-Z0-9_$]*)\[\]/g, replacement: ': $1[]' },
      { pattern: /:\s*Promise<([^>]+)>\s*\{/g, replacement: ': Promise<$1> {' }
    ];
    
    return this.applyFixesToFiles(tsFiles, fixes, 'Navigation & TypeScript');
  }

  // Phase 5: Screen Components - Fifth priority (100+ errors)
  async fixScreenComponents() {
    console.log('\n🔧 PHASE 5: Screen Components Fix');
    
    const screenFiles = [
      'src/screens/NotificationScreen.js',
      'src/screens/AboutScreen.js',
      'src/screens/CustomerDetailsScreen.js',
      'src/screens/EditGarmentTemplateScreen.js',
      'src/screens/GarmentTemplatesScreen.tsx',
      'src/screens/AddCustomerScreen.js'
    ];
    
    const fixes = [
      { pattern: /\<([A-Z][a-zA-Z0-9_$]*)\s+([^>]*)\>/g, replacement: '<$1 $2>' },
      { pattern: /\<\/([A-Z][a-zA-Z0-9_$]*)\>/g, replacement: '</$1>' },
      { pattern: /\{([^}]+)\}\s*([A-Z][a-zA-Z0-9_$]*)/g, replacement: '{$1}\n      $2' },
      { pattern: /useState\(\s*([^)]+)\s*\)/g, replacement: 'useState($1)' },
      { pattern: /useEffect\(\s*([^,]+),\s*([^)]+)\s*\)/g, replacement: 'useEffect($1, $2)' }
    ];
    
    return this.applyFixesToFiles(screenFiles, fixes, 'Screen Components');
  }

  // Apply fixes to a set of files
  applyFixesToFiles(files, fixes, phaseName) {
    let phaseFixed = 0;
    
    files.forEach(filePath => {
      const fullPath = path.join(process.cwd(), filePath);
      if (!fs.existsSync(fullPath)) {
        return;
      }
      
      try {
        let content = fs.readFileSync(fullPath, 'utf8');
        let fileFixed = 0;
        
        fixes.forEach(fix => {
          const beforeCount = (content.match(fix.pattern) || []).length;
          if (beforeCount > 0) {
            content = content.replace(fix.pattern, fix.replacement);
            const afterCount = (content.match(fix.pattern) || []).length;
            const fixedCount = beforeCount - afterCount;
            fileFixed += fixedCount;
          }
        });
        
        if (fileFixed > 0) {
          fs.writeFileSync(fullPath, content, 'utf8');
          console.log(`  ✅ ${path.basename(filePath)}: ${fileFixed} fixes`);
          phaseFixed += fileFixed;
        }
        
      } catch (error) {
        console.error(`  ❌ Error in ${filePath}:`, error.message);
      }
    });
    
    this.phaseResults.push({ phase: phaseName, fixed: phaseFixed });
    this.totalFixed += phaseFixed;
    console.log(`  📊 ${phaseName} Total: ${phaseFixed} fixes`);
    return phaseFixed;
  }

  // Get current error count
  getErrorCount() {
    try {
      const result = execSync('npm run type-check 2>&1', { encoding: 'utf8', cwd: process.cwd() });
      const match = result.match(/Found (\d+) errors/);
      return match ? parseInt(match[1]) : 0;
    } catch (error) {
      const match = error.stdout?.match(/Found (\d+) errors/);
      return match ? parseInt(match[1]) : 0;
    }
  }

  // Run all phases
  async runAllPhases() {
    console.log('🚀 STARTING MULTI-PHASE COMPREHENSIVE ERROR REDUCTION');
    
    const initialErrors = this.getErrorCount();
    console.log(`📊 Initial errors: ${initialErrors}`);
    
    // Run all phases
    await this.fixJSXStyleProps();
    await this.fixTemplateLiterals();
    await this.fixServiceMethods();
    await this.fixNavigationTypeScript();
    await this.fixScreenComponents();
    
    const finalErrors = this.getErrorCount();
    const reduction = initialErrors - finalErrors;
    
    console.log('\n📈 COMPREHENSIVE RESULTS:');
    console.log(`Initial errors: ${initialErrors}`);
    console.log(`Final errors: ${finalErrors}`);
    console.log(`Total errors fixed: ${reduction}`);
    console.log(`Overall reduction: ${((reduction / initialErrors) * 100).toFixed(1)}%`);
    
    console.log('\n📋 PHASE BREAKDOWN:');
    this.phaseResults.forEach(result => {
      console.log(`  ${result.phase}: ${result.fixed} fixes`);
    });
    
    // Progress toward 2000 goal
    const remaining = finalErrors - 2000;
    if (remaining > 0) {
      console.log(`\n🎯 PROGRESS TOWARD 2000 GOAL:`);
      console.log(`Current: ${finalErrors} errors`);
      console.log(`Goal: 2000 errors`);
      console.log(`Still need to fix: ${remaining} errors`);
      console.log(`Progress: ${(((initialErrors - finalErrors) / (initialErrors - 2000)) * 100).toFixed(1)}% toward goal`);
    } else {
      console.log(`\n🎉 GOAL ACHIEVED! Under 2000 errors!`);
    }
    
    return { initialErrors, finalErrors, reduction, phaseResults: this.phaseResults };
  }
}

// Main execution
async function main() {
  const fixer = new ComprehensiveErrorFixer();
  await fixer.runAllPhases();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { ComprehensiveErrorFixer };
