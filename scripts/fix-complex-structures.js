#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Function to recursively find all JS/JSX/TS/TSX files
function findFiles(dir, extensions = ['.js', '.jsx', '.ts', '.tsx']) {
  let results = [];
  const list = fs.readdirSync(dir);
  
  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat && stat.isDirectory()) {
      if (!['node_modules', 'build', 'dist', '.git', 'android', 'ios'].includes(file)) {
        results = results.concat(findFiles(filePath, extensions));
      }
    } else {
      const ext = path.extname(file);
      if (extensions.includes(ext)) {
        results.push(filePath);
      }
    }
  });
  
  return results;
}

// Function to fix complex structural issues
function fixComplexStructures(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Fix malformed template literals
    // Pattern: `${variable}`} -> `${variable}`
    const malformedTemplateLiteralRegex = /`([^`]*)`\}/g;
    if (malformedTemplateLiteralRegex.test(content)) {
      content = content.replace(malformedTemplateLiteralRegex, '`$1`');
      modified = true;
    }
    
    // Fix JSX text content with malformed brackets
    // Pattern: >{text}< -> >{text}<
    const jsxTextRegex = />\{([^}]+)\}</g;
    if (jsxTextRegex.test(content)) {
      content = content.replace(jsxTextRegex, '>{$1}<');
      modified = true;
    }
    
    // Fix TypeScript parameter syntax
    // Pattern: method(param: type) { -> method(param: type) {
    const tsParamRegex = /(\w+)\s*\(\s*(\w+):\s*(\w+)\s*\)\s*\{/g;
    if (tsParamRegex.test(content)) {
      content = content.replace(tsParamRegex, '$1($2: $3) {');
      modified = true;
    }
    
    // Fix TypeScript return type syntax
    // Pattern: method(): Type { -> method(): Type {
    const tsReturnTypeRegex = /(\w+)\s*\(\s*\)\s*:\s*(\w+)\s*\{/g;
    if (tsReturnTypeRegex.test(content)) {
      content = content.replace(tsReturnTypeRegex, '$1(): $2 {');
      modified = true;
    }
    
    // Fix object destructuring with missing brackets
    // Pattern: const { prop } = object; -> const { prop } = object;
    const destructuringRegex = /const\s*\{\s*(\w+)\s*\}\s*=\s*(\w+);/g;
    if (destructuringRegex.test(content)) {
      content = content.replace(destructuringRegex, 'const { $1 } = $2;');
      modified = true;
    }
    
    // Fix array destructuring with missing brackets
    // Pattern: const [item] = array; -> const [item] = array;
    const arrayDestructuringRegex = /const\s*\[\s*(\w+)\s*\]\s*=\s*(\w+);/g;
    if (arrayDestructuringRegex.test(content)) {
      content = content.replace(arrayDestructuringRegex, 'const [$1] = $2;');
      modified = true;
    }
    
    // Fix switch statement syntax
    // Pattern: switch (value) { case 'x': ... } -> switch (value) { case 'x': ... }
    const switchRegex = /switch\s*\(\s*(\w+)\s*\)\s*\{/g;
    if (switchRegex.test(content)) {
      content = content.replace(switchRegex, 'switch ($1) {');
      modified = true;
    }
    
    // Fix try-catch syntax
    // Pattern: try { ... } catch (error) { ... } -> try { ... } catch (error) { ... }
    const tryCatchRegex = /try\s*\{([^}]*)\}\s*catch\s*\(\s*(\w+)\s*\)\s*\{/g;
    if (tryCatchRegex.test(content)) {
      content = content.replace(tryCatchRegex, 'try {$1} catch ($2) {');
      modified = true;
    }
    
    // Fix function parameter syntax
    // Pattern: function name(param1, param2) { -> function name(param1, param2) {
    const functionParamRegex = /function\s+(\w+)\s*\(\s*([^)]*)\s*\)\s*\{/g;
    if (functionParamRegex.test(content)) {
      content = content.replace(functionParamRegex, 'function $1($2) {');
      modified = true;
    }
    
    // Fix arrow function syntax
    // Pattern: const func = (param) => { -> const func = (param) => {
    const arrowFunctionRegex = /const\s+(\w+)\s*=\s*\(\s*([^)]*)\s*\)\s*=>\s*\{/g;
    if (arrowFunctionRegex.test(content)) {
      content = content.replace(arrowFunctionRegex, 'const $1 = ($2) => {');
      modified = true;
    }
    
    // Fix object method syntax
    // Pattern: methodName(params) { -> methodName(params) {
    const objectMethodRegex = /(\s+)(\w+)\s*\(\s*([^)]*)\s*\)\s*\{/g;
    if (objectMethodRegex.test(content)) {
      content = content.replace(objectMethodRegex, '$1$2($3) {');
      modified = true;
    }
    
    // Fix class method syntax
    // Pattern: async methodName(params) { -> async methodName(params) {
    const classMethodRegex = /(\s+)(async\s+)?(\w+)\s*\(\s*([^)]*)\s*\)\s*\{/g;
    if (classMethodRegex.test(content)) {
      content = content.replace(classMethodRegex, '$1$2$3($4) {');
      modified = true;
    }
    
    // Fix import statement syntax
    // Pattern: import { item } from 'module'; -> import { item } from 'module';
    const importRegex = /import\s*\{\s*([^}]+)\s*\}\s*from\s*['"]([^'"]+)['"];/g;
    if (importRegex.test(content)) {
      content = content.replace(importRegex, "import { $1 } from '$2';");
      modified = true;
    }
    
    // Fix export statement syntax
    // Pattern: export { item }; -> export { item };
    const exportRegex = /export\s*\{\s*([^}]+)\s*\};/g;
    if (exportRegex.test(content)) {
      content = content.replace(exportRegex, 'export { $1 };');
      modified = true;
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`Fixed complex structures in: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Main function
function main() {
  const srcDir = path.join(process.cwd(), 'src');
  
  if (!fs.existsSync(srcDir)) {
    console.error('src directory not found');
    process.exit(1);
  }
  
  console.log('Finding files to fix complex structures...');
  const files = findFiles(srcDir);
  
  console.log(`Found ${files.length} files to check`);
  
  let fixedCount = 0;
  files.forEach(file => {
    if (fixComplexStructures(file)) {
      fixedCount++;
    }
  });
  
  console.log(`\nFixed complex structures in ${fixedCount} files`);
  
  if (fixedCount > 0) {
    console.log('\nRunning final type check...');
    const { execSync } = require('child_process');
    try {
      execSync('npm run type-check 2>&1 | head -25', { stdio: 'inherit' });
    } catch (error) {
      console.log('Final check completed.');
    }
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixComplexStructures, findFiles };
