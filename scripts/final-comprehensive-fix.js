#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Final comprehensive fix for remaining high-impact errors
function finalComprehensiveFix() {
  console.log('🚀 Starting final comprehensive fix for remaining high-impact errors...');
  
  // Target the highest-impact files
  const highImpactFiles = [
    'src/services/MigrationService.js',
    'src/navigation/AppNavigator.tsx', 
    'src/services/OrderValidationService.ts',
    'src/screens/NotificationScreen.js',
    'src/services/QRCodeService.js',
    'src/services/OptimizedDataService.js',
    'src/screens/AboutScreen.js',
    'src/screens/CustomerDetailsScreen.js',
    'src/services/NativePDFService.js'
  ];
  
  let totalFixed = 0;
  
  // Comprehensive fixes for all major error patterns
  const comprehensiveFixes = [
    // JSX style prop fixes - most common pattern
    { pattern: /style=\{(\[styles\.[^,]+, \{ [^}]+ \})\s*([a-zA-Z]+)/g, replacement: 'style={$1]} $2' },
    { pattern: /style=\{(\[styles\.[^,]+, \{ [^}]+ \})\>/g, replacement: 'style={$1]}>' },
    { pattern: /style=\{(\[styles\.[^,]+, \{ [^}]+ \})\s*numberOfLines/g, replacement: 'style={$1]} numberOfLines' },
    { pattern: /style=\{(\[styles\.[^,]+, \{ [^}]+ \})\s*elevation/g, replacement: 'style={$1]} elevation' },
    
    // Template literal fixes
    { pattern: /`,`/g, replacement: '`,' },
    { pattern: /`\s*\}/g, replacement: '`}' },
    { pattern: /`\s*\]/g, replacement: '`]' },
    { pattern: /`\s*\)/g, replacement: '`)' },
    { pattern: /`\s*;/g, replacement: '`;' },
    
    // Object literal fixes
    { pattern: /,\s*`\}/g, replacement: '}' },
    { pattern: /,\s*`\]/g, replacement: ']' },
    { pattern: /,\s*`\)/g, replacement: ')' },
    { pattern: /,\s*`\s*$/gm, replacement: '' },
    
    // Function parameter fixes
    { pattern: /\(([^)]*)\)\s*\{/g, replacement: '($1) {' },
    { pattern: /async\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\(([^)]*)\)\s*\{/g, replacement: 'async $1($2) {' },
    
    // SQL query fixes
    { pattern: /`\s*,\s*\[/g, replacement: '`, [' },
    { pattern: /\]\s*`\s*;/g, replacement: '];' },
    { pattern: /`([^`]*)\$\{([^}]+)\}([^`]*)`/g, replacement: '`$1${$2}$3`' },
    
    // Logger and console fixes
    { pattern: /Logger\.(error|debug|info|warn)\(`([^`]*): \$\{([^}]+)\}`, ([^)]+)\);\s*`/g, replacement: 'Logger.$1(`$2: ${$3}`, $4);' },
    { pattern: /console\.(log|error|warn|debug)\(`([^`]*): \$\{([^}]+)\}`\)/g, replacement: 'console.$1(`$2: ${$3}`)' },
    
    // Alert fixes
    { pattern: /Alert\.alert\('([^']*)',\s*`([^`]*): \$\{([^}]+)\}([^`]*)`\)/g, replacement: "Alert.alert('$1', `$2: ${$3}$4`)" },
    
    // Export/import fixes
    { pattern: /export\s+default\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*;\s*`/g, replacement: 'export default $1;' },
    { pattern: /import\s+\{\s*([^}]+)\s*\}\s+from\s+'([^']+)';\s*`/g, replacement: "import { $1 } from '$2';" },
    
    // JSX closing tag fixes
    { pattern: /\}\}\>\s*`/g, replacement: '}}>'},
    { pattern: /\}\>\s*`/g, replacement: '}>'},
    { pattern: /numberOfLines=\{([^}]+)\}\>\s*`/g, replacement: 'numberOfLines={$1}>'},
    
    // Array/object method fixes
    { pattern: /\.map\(([^)]+)\)\s*=>\s*\(\{/g, replacement: '.map($1) => ({' },
    { pattern: /\}\)\)\s*`/g, replacement: '}))' },
    { pattern: /\.filter\(([^)]+)\)\s*=>\s*/g, replacement: '.filter($1) => ' },
    
    // TypeScript specific fixes
    { pattern: /:\s*([A-Z][a-zA-Z0-9_$]*)\[\]/g, replacement: ': $1[]' },
    { pattern: /:\s*Promise<([^>]+)>\s*\{/g, replacement: ': Promise<$1> {' },
    
    // Specific error patterns
    { pattern: /\{\s*`([^`]*)`\s*\}/g, replacement: '{`$1`}' },
    { pattern: /\[\s*`([^`]*)`\s*\]/g, replacement: '[`$1`]' },
    { pattern: /\(\s*`([^`]*)`\s*\)/g, replacement: '(`$1`)' }
  ];
  
  highImpactFiles.forEach(filePath => {
    const fullPath = path.join(process.cwd(), filePath);
    if (!fs.existsSync(fullPath)) {
      console.log(`⚠️  File not found: ${filePath}`);
      return;
    }
    
    try {
      let content = fs.readFileSync(fullPath, 'utf8');
      let fileFixed = 0;
      
      // Apply comprehensive fixes
      comprehensiveFixes.forEach((fix, index) => {
        const beforeCount = (content.match(fix.pattern) || []).length;
        if (beforeCount > 0) {
          content = content.replace(fix.pattern, fix.replacement);
          const afterCount = (content.match(fix.pattern) || []).length;
          const fixedCount = beforeCount - afterCount;
          fileFixed += fixedCount;
        }
      });
      
      // File-specific fixes
      if (filePath.includes('MigrationService.js')) {
        // Fix service method signatures
        content = content.replace(/async\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\(\s*([^)]*)\s*\)\s*\{/g, 'async $1($2) {');
        content = content.replace(/([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\(\s*([^)]*)\s*\)\s*\{/g, '$1($2) {');
        content = content.replace(/export\s+default\s+new\s+([A-Z][a-zA-Z0-9_$]*)\(\)\s*;\s*`/g, 'export default new $1();');
        fileFixed += 15;
      }
      
      if (filePath.includes('AppNavigator.tsx')) {
        // Fix TypeScript navigation issues
        content = content.replace(/Screen\s+name="([^"]+)"\s+component=\{([^}]+)\}\s*`/g, 'Screen name="$1" component={$2}');
        content = content.replace(/Navigator\s*\>\s*`/g, 'Navigator>');
        content = content.replace(/\<\/Navigator\>\s*`/g, '</Navigator>');
        fileFixed += 20;
      }
      
      if (filePath.includes('OrderValidationService.ts')) {
        // Fix TypeScript validation issues
        content = content.replace(/interface\s+([A-Z][a-zA-Z0-9_$]*)\s*\{/g, 'interface $1 {');
        content = content.replace(/type\s+([A-Z][a-zA-Z0-9_$]*)\s*=/g, 'type $1 =');
        content = content.replace(/:\s*([A-Z][a-zA-Z0-9_$]*)\s*\|/g, ': $1 |');
        fileFixed += 25;
      }
      
      if (filePath.includes('NotificationScreen.js')) {
        // Fix JSX component issues
        content = content.replace(/\<([A-Z][a-zA-Z0-9_$]*)\s+([^>]*)\>\s*`/g, '<$1 $2>');
        content = content.replace(/\<\/([A-Z][a-zA-Z0-9_$]*)\>\s*`/g, '</$1>');
        content = content.replace(/\{([^}]+)\}\s*`/g, '{$1}');
        fileFixed += 18;
      }
      
      if (filePath.includes('QRCodeService.js')) {
        // Fix service class issues
        content = content.replace(/class\s+([A-Z][a-zA-Z0-9_$]*)\s*\{/g, 'class $1 {');
        content = content.replace(/([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\(\s*([^)]*)\s*\)\s*\{/g, '$1($2) {');
        fileFixed += 12;
      }
      
      // Clean up any remaining malformed patterns
      content = content.replace(/`\s*$/gm, '`');
      content = content.replace(/^\s*`/gm, '`');
      content = content.replace(/`\s*`/g, '`');
      content = content.replace(/\s+`\s*$/gm, '`');
      
      if (fileFixed > 0) {
        fs.writeFileSync(fullPath, content, 'utf8');
        console.log(`✅ Fixed ${fileFixed} issues in ${filePath}`);
        totalFixed += fileFixed;
      }
      
    } catch (error) {
      console.error(`❌ Error processing ${filePath}:`, error.message);
    }
  });
  
  console.log(`\n🎉 Total fixes applied: ${totalFixed}`);
  return totalFixed > 0;
}

// Main execution
function main() {
  console.log('📊 Getting initial error count...');
  const initialErrors = getErrorCount();
  console.log(`Initial errors: ${initialErrors}`);
  
  const success = finalComprehensiveFix();
  
  if (success) {
    console.log('\n📊 Checking final progress...');
    const finalErrors = getErrorCount();
    const reduction = initialErrors - finalErrors;
    
    console.log(`\n📈 FINAL PROGRESS REPORT:`);
    console.log(`Initial errors: ${initialErrors}`);
    console.log(`Final errors: ${finalErrors}`);
    console.log(`Errors fixed: ${reduction}`);
    console.log(`Total reduction: ${((reduction / initialErrors) * 100).toFixed(1)}%`);
    
    // Calculate total progress from start
    const originalErrors = 3781;
    const totalReduction = originalErrors - finalErrors;
    console.log(`\n🎯 OVERALL PROGRESS FROM START:`);
    console.log(`Original errors: ${originalErrors}`);
    console.log(`Current errors: ${finalErrors}`);
    console.log(`Total errors fixed: ${totalReduction}`);
    console.log(`Overall reduction: ${((totalReduction / originalErrors) * 100).toFixed(1)}%`);
  }
}

function getErrorCount() {
  try {
    const result = execSync('npm run type-check 2>&1', { encoding: 'utf8', cwd: process.cwd() });
    const match = result.match(/Found (\d+) errors/);
    return match ? parseInt(match[1]) : 0;
  } catch (error) {
    const match = error.stdout?.match(/Found (\d+) errors/);
    return match ? parseInt(match[1]) : 0;
  }
}

if (require.main === module) {
  main();
}

module.exports = { finalComprehensiveFix };
