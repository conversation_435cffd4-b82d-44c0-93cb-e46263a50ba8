#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Function to recursively find all JS/JSX/TS/TSX files
function findFiles(dir, extensions = ['.js', '.jsx', '.ts', '.tsx']) {
  let results = [];
  const list = fs.readdirSync(dir);
  
  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat && stat.isDirectory()) {
      if (!['node_modules', 'build', 'dist', '.git', 'android', 'ios'].includes(file)) {
        results = results.concat(findFiles(filePath, extensions));
      }
    } else {
      const ext = path.extname(file);
      if (extensions.includes(ext)) {
        results.push(filePath);
      }
    }
  });
  
  return results;
}

// Function to fix service class syntax errors
function fixServiceClasses(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Fix switch case syntax errors
    // Pattern: case 'value': -> case 'value':
    const switchCaseRegex = /case\s+'([^']+)':\s*$/gm;
    if (switchCaseRegex.test(content)) {
      // This is already correct, but let's ensure proper formatting
      content = content.replace(switchCaseRegex, "case '$1':");
      modified = true;
    }
    
    // Fix async method declarations
    // Pattern: async methodName(params) { -> async methodName(params) {
    const asyncMethodRegex = /(\s+)async\s+(\w+)\s*\([^)]*\)\s*\{/g;
    if (asyncMethodRegex.test(content)) {
      // This should already be correct, but ensure proper spacing
      content = content.replace(asyncMethodRegex, '$1async $2($3) {');
      modified = true;
    }
    
    // Fix method parameter syntax
    // Pattern: methodName(param1, param2) { -> methodName(param1, param2) {
    const methodParamRegex = /(\w+)\s*\(([^)]*)\)\s*\{/g;
    if (methodParamRegex.test(content)) {
      // Ensure proper spacing
      content = content.replace(methodParamRegex, '$1($2) {');
      modified = true;
    }
    
    // Fix try-catch block syntax
    // Pattern: } catch (error) { -> } catch (error) {
    const tryCatchRegex = /\}\s*catch\s*\(\s*(\w+)\s*\)\s*\{/g;
    if (tryCatchRegex.test(content)) {
      content = content.replace(tryCatchRegex, '} catch ($1) {');
      modified = true;
    }
    
    // Fix object property syntax in classes
    // Pattern: property: value, -> property: value,
    const objectPropertyRegex = /(\w+):\s*([^,\n]+),/g;
    if (objectPropertyRegex.test(content)) {
      content = content.replace(objectPropertyRegex, '$1: $2,');
      modified = true;
    }
    
    // Fix array property syntax
    // Pattern: items: order.items || []; -> items: order.items || []
    const arrayPropertySemicolonRegex = /(\w+:\s*[^;]+\|\|\s*\[\]);/g;
    if (arrayPropertySemicolonRegex.test(content)) {
      content = content.replace(arrayPropertySemicolonRegex, '$1');
      modified = true;
    }
    
    // Fix return statement syntax
    // Pattern: return { ...object, property: value }; -> return { ...object, property: value };
    const returnStatementRegex = /return\s*\{\s*([^}]+)\s*\};/g;
    if (returnStatementRegex.test(content)) {
      content = content.replace(returnStatementRegex, 'return { $1 };');
      modified = true;
    }
    
    // Fix timeline array syntax
    // Pattern: timeline: [...order.timeline, timelineEntry} -> timeline: [...order.timeline, timelineEntry]
    const timelineArrayRegex = /(timeline:\s*\[[^\]]*\w+)\}/g;
    if (timelineArrayRegex.test(content)) {
      content = content.replace(timelineArrayRegex, '$1]');
      modified = true;
    }
    
    // Fix designReferences array syntax
    // Pattern: designReferences: row.designReferences ? JSON.parse(row.designReferences) : []; -> designReferences: row.designReferences ? JSON.parse(row.designReferences) : []
    const designReferencesRegex = /(designReferences:\s*[^;]+\:\s*\[\]);/g;
    if (designReferencesRegex.test(content)) {
      content = content.replace(designReferencesRegex, '$1');
      modified = true;
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`Fixed service class syntax in: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Main function
function main() {
  const srcDir = path.join(process.cwd(), 'src');
  
  if (!fs.existsSync(srcDir)) {
    console.error('src directory not found');
    process.exit(1);
  }
  
  console.log('Finding service files to fix...');
  const files = findFiles(path.join(srcDir, 'services'));
  
  console.log(`Found ${files.length} service files to check`);
  
  let fixedCount = 0;
  files.forEach(file => {
    if (fixServiceClasses(file)) {
      fixedCount++;
    }
  });
  
  console.log(`\nFixed service class syntax in ${fixedCount} files`);
  
  if (fixedCount > 0) {
    console.log('\nRunning type check to verify fixes...');
    const { execSync } = require('child_process');
    try {
      execSync('npm run type-check 2>&1 | head -15', { stdio: 'inherit' });
    } catch (error) {
      console.log('Continuing with fixes...');
    }
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixServiceClasses, findFiles };
