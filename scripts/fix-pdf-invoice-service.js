#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

function fixPDFInvoiceService() {
  const filePath = path.join(process.cwd(), 'src/services/PDFInvoiceService.js');
  
  if (!fs.existsSync(filePath)) {
    console.error('PDFInvoiceService.js not found');
    return;
  }
  
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  
  // Fix method parameter syntax - replace $3 with proper parameters
  const methodFixes = [
    { pattern: /async deleteInvoice\(\$3\)/g, replacement: 'async deleteInvoice(filename)' }
  ];
  
  methodFixes.forEach(fix => {
    if (fix.pattern.test(content)) {
      content = content.replace(fix.pattern, fix.replacement);
      modified = true;
    }
  });
  
  // Fix template literal issues
  const templateLiteralFixes = [
    // Fix unterminated template literals
    { pattern: /const invoiceNo = invoiceNumber \|\| `INV-\$\{order\.id\}-\$\{Date\.now\(\)\} ;/g, replacement: "const invoiceNo = invoiceNumber || `INV-${order.id}-${Date.now()}`;" },
    { pattern: /const finalUri = `\$\{documentsDir\} \$\{finalFilename\} ;`/g, replacement: "const finalUri = `${documentsDir}${finalFilename}`;" },
    { pattern: /uri: `\$\{documentsDir\} \$\{file\} ,`/g, replacement: "uri: `${documentsDir}${file}`," },
    { pattern: /const uri = `\$\{documentsDir\} \$\{filename\} ;`/g, replacement: "const uri = `${documentsDir}${filename}`;" },
    { pattern: /return \{ success: true   \}/g, replacement: "return { success: true }" }
  ];
  
  templateLiteralFixes.forEach(fix => {
    if (fix.pattern.test(content)) {
      content = content.replace(fix.pattern, fix.replacement);
      modified = true;
    }
  });
  
  // Fix specific template literal structure issues in HTML
  const htmlFixes = [
    // Fix conditional template literals in HTML
    { pattern: /\$\{this\.businessInfo\.website \? `Website: \$\{this\.businessInfo\.website\}<br>  : ''\}/g, replacement: "${this.businessInfo.website ? `Website: ${this.businessInfo.website}<br>` : ''}" },
    { pattern: /\$\{customer\.email \? `\$\{customer\.email\} <br>` : ''\}/g, replacement: "${customer.email ? `${customer.email}<br>` : ''}" },
    { pattern: /\$\{customer\.address \? `\$\{customer\.address\}`` : ''\}/g, replacement: "${customer.address ? `${customer.address}` : ''}" },
    { pattern: /\$\{order\.notes \? `<br><small style="color: #666;">\$\{order\.notes\}<\/small>  : ''\}/g, replacement: "${order.notes ? `<br><small style=\"color: #666;\">${order.notes}</small>` : ''}" }
  ];
  
  htmlFixes.forEach(fix => {
    if (fix.pattern.test(content)) {
      content = content.replace(fix.pattern, fix.replacement);
      modified = true;
    }
  });
  
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log('Fixed PDFInvoiceService.js method parameters and template literals');
    return true;
  }
  
  return false;
}

// Main function
function main() {
  console.log('Fixing PDFInvoiceService.js...');

  if (fixPDFInvoiceService()) {
    console.log('PDFInvoiceService.js fixed successfully');
  } else {
    console.log('No changes needed for PDFInvoiceService.js');
  }

  console.log('Fixing notificationService.js...');

  if (fixNotificationService()) {
    console.log('notificationService.js fixed successfully');
  } else {
    console.log('No changes needed for notificationService.js');
  }
}

if (require.main === module) {
  main();
}

function fixNotificationService() {
  const filePath = path.join(process.cwd(), 'src/services/notificationService.js');

  if (!fs.existsSync(filePath)) {
    console.error('notificationService.js not found');
    return;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // Fix method parameter syntax - replace $3 with proper parameters
  const methodFixes = [
    { pattern: /static async createOrderStatusNotification\(\$3\)/g, replacement: 'static async createOrderStatusNotification(order, oldStatus, newStatus)' },
    { pattern: /static async createFabricLowStockNotification\(\$3\)/g, replacement: 'static async createFabricLowStockNotification(fabric)' },
    { pattern: /static async createMeasurementNeededNotification\(\$3\)/g, replacement: 'static async createMeasurementNeededNotification(customer, orderType)' },
    { pattern: /static async createPaymentDueNotification\(\$3\)/g, replacement: 'static async createPaymentDueNotification(order, customer)' },
    { pattern: /static async createDeliveryDueNotification\(\$3\)/g, replacement: 'static async createDeliveryDueNotification(order, customer)' }
  ];

  methodFixes.forEach(fix => {
    if (fix.pattern.test(content)) {
      content = content.replace(fix.pattern, fix.replacement);
      modified = true;
    }
  });

  // Fix template literal issues
  const templateLiteralFixes = [
    // Fix unterminated template literals
    { pattern: /Logger\.info\(`Notifications \$\{enabled \? 'enabled' : 'disabled'\} \)\;/g, replacement: "Logger.info(`Notifications ${enabled ? 'enabled' : 'disabled'}`);" },
    { pattern: /Logger\.info\(`Notification created: \$\{title\} \)\;`/g, replacement: "Logger.info(`Notification created: ${title}`);" },
    { pattern: /message: `\$\{lowStockItems\.length\}  item\(s\) are running low on stock`,/g, replacement: "message: `${lowStockItems.length} item(s) are running low on stock`," },
    { pattern: /message: `Today: \$\{todayOrders\.length\} orders, \$\$\{totalSales\.toFixed\(2\)\} in sales ,/g, replacement: "message: `Today: ${todayOrders.length} orders, $${totalSales.toFixed(2)} in sales`," },
    { pattern: /const smsUrl = `sms:\$\{cleanPhone\}\?body=\$\{encodeURIComponent\(message\)\} ;`/g, replacement: "const smsUrl = `sms:${cleanPhone}?body=${encodeURIComponent(message)}`;" },
    { pattern: /Logger\.info\(`SMS sent to \$\{cleanPhone\} \)\;/g, replacement: "Logger.info(`SMS sent to ${cleanPhone}`);" },
    { pattern: /return \{ success: true, method: 'sms'   \}/g, replacement: "return { success: true, method: 'sms' }" },
    { pattern: /const whatsappUrl = `whatsapp:\/\/send\?phone=\$\{cleanPhone\}&text=\$\{encodeURIComponent\(message\)\} ;`/g, replacement: "const whatsappUrl = `whatsapp://send?phone=${cleanPhone}&text=${encodeURIComponent(message)}`;" },
    { pattern: /Logger\.info\(`WhatsApp message sent to \$\{cleanPhone\} \)\;/g, replacement: "Logger.info(`WhatsApp message sent to ${cleanPhone}`);" },
    { pattern: /return \{ success: true, method: 'whatsapp'   \}/g, replacement: "return { success: true, method: 'whatsapp' }" },
    { pattern: /const webWhatsappUrl = `https:\/\/wa\.me\/\$\{cleanPhone\}\?text=\$\{encodeURIComponent\(message\)\} ;`/g, replacement: "const webWhatsappUrl = `https://wa.me/${cleanPhone}?text=${encodeURIComponent(message)}`;" },
    { pattern: /Logger\.info\(`WhatsApp web message sent to \$\{cleanPhone\} \)\;/g, replacement: "Logger.info(`WhatsApp web message sent to ${cleanPhone}`);" },
    { pattern: /return \{ success: true, method: 'whatsapp_web'   \}/g, replacement: "return { success: true, method: 'whatsapp_web' }" },
    { pattern: /return \{ success: false, error: error\.message   \}/g, replacement: "return { success: false, error: error.message }" },
    { pattern: /throw new Error\(`Template \$\{templateKey\} not found`\);/g, replacement: "throw new Error(`Template ${templateKey} not found`);" },
    { pattern: /const placeholder = `\{\$\{key\}\} ;/g, replacement: "const placeholder = `{${key}}`;" },
    { pattern: /message: `Notified \$\{customer\.name\} that order #\$\{order\.id\} is ready`,/g, replacement: "message: `Notified ${customer.name} that order #${order.id} is ready`," },
    { pattern: /message: `Sent payment reminder to \$\{customer\.name\} for ৳\$\{amount\} ,/g, replacement: "message: `Sent payment reminder to ${customer.name} for ৳${amount}`," },
    { pattern: /message: `Appointment with \$\{customer\.name\} tomorrow at \$\{appointment\.time\} ,`/g, replacement: "message: `Appointment with ${customer.name} tomorrow at ${appointment.time}`," },
    { pattern: /message: `Order #\$\{order\.id\} changed from \$\{oldStatus\} to \$\{newStatus\} ,/g, replacement: "message: `Order #${order.id} changed from ${oldStatus} to ${newStatus}`," },
    { pattern: /message: `\$\{fabric\.name\}` is running low \(\$\{fabric\.stock\} meters remaining\) ,/g, replacement: "message: `${fabric.name} is running low (${fabric.stock} meters remaining)`," },
    { pattern: /message: `\$\{customer\.name\}` needs measurements for \$\{orderType\} ,/g, replacement: "message: `${customer.name} needs measurements for ${orderType}`," },
    { pattern: /message: `Payment pending for Order #\$\{order\.id\} from \$\{customer\.name\} ,`/g, replacement: "message: `Payment pending for Order #${order.id} from ${customer.name}`," },
    { pattern: /message: `Order #\$\{order\.id\} ready for delivery to \$\{customer\.name\} ,/g, replacement: "message: `Order #${order.id} ready for delivery to ${customer.name}`," },
    { pattern: /message: `\$\{todayOrders\.length\}` new order\(s\) received today`,/g, replacement: "message: `${todayOrders.length} new order(s) received today`," },
    { pattern: /message: `\$\{dueToday\.length\}  order\(s\) are due for delivery today`,/g, replacement: "message: `${dueToday.length} order(s) are due for delivery today`," },
    { pattern: /message: `\$\{overdueOrders\.length\}  order\(s\) are overdue`,/g, replacement: "message: `${overdueOrders.length} order(s) are overdue`," },
    { pattern: /message: `\$\{lowStockItems\.length\}  item\(s\) are running low on stock`,/g, replacement: "message: `${lowStockItems.length} item(s) are running low on stock`," }
  ];

  templateLiteralFixes.forEach(fix => {
    if (fix.pattern.test(content)) {
      content = content.replace(fix.pattern, fix.replacement);
      modified = true;
    }
  });

  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log('Fixed notificationService.js method parameters and template literals');
    return true;
  }

  return false;
}

module.exports = { fixPDFInvoiceService, fixNotificationService };
