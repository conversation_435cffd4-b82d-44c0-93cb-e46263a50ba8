#!/usr/bin/env node

/**
 * Dependency Cleanup Script
 * Removes unused dependencies and optimizes bundle size
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class DependencyCleanup {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
    this.packageJsonPath = path.join(this.projectRoot, 'package.json');
    this.nodeModulesPath = path.join(this.projectRoot, 'node_modules');
  }

  async cleanup() {
    console.log('🧹 Starting dependency cleanup...\n');
    
    await this.clearNodeModules();
    await this.optimizePackageJson();
    await this.reinstallDependencies();
    await this.clearCaches();
    
    console.log('✅ Dependency cleanup completed!\n');
  }

  async clearNodeModules() {
    console.log('🗑️  Clearing node_modules...');
    
    try {
      if (fs.existsSync(this.nodeModulesPath)) {
        execSync(`rm -rf "${this.nodeModulesPath}"`, { stdio: 'inherit' });
        console.log('✅ node_modules cleared');
      }
    } catch (error) {
      console.error('❌ Failed to clear node_modules:', error.message);
    }
  }

  async optimizePackageJson() {
    console.log('📦 Optimizing package.json...');
    
    try {
      const packageJson = JSON.parse(fs.readFileSync(this.packageJsonPath, 'utf8'));
      
      // Remove potentially unused dependencies
      const potentiallyUnused = [
        'lodash',
        'moment',
        'axios',
        'underscore',
        'jquery',
        'react-native-vector-icons' // We're using FeatherIcon instead
      ];

      let removed = 0;
      potentiallyUnused.forEach(dep => {
        if (packageJson.dependencies && packageJson.dependencies[dep]) {
          delete packageJson.dependencies[dep];
          removed++;
          console.log(`  - Removed ${dep}`);
        }
      });

      // Optimize scripts
      packageJson.scripts = {
        ...packageJson.scripts,
        "clean": "rm -rf node_modules && rm -rf .expo && rm -rf dist",
        "clean:cache": "expo r -c",
        "optimize": "node scripts/performance-optimizer.js",
        "bundle:analyze": "node scripts/analyze-bundle.js"
      };

      // Add bundle optimization config
      packageJson.bundleOptimization = {
        "treeshaking": true,
        "minification": true,
        "compression": true,
        "excludeFromBundle": [
          "**/__tests__/**",
          "**/*.test.js",
          "**/*.spec.js",
          "**/docs/**",
          "**/examples/**"
        ]
      };

      fs.writeFileSync(this.packageJsonPath, JSON.stringify(packageJson, null, 2));
      console.log(`✅ Optimized package.json (removed ${removed} dependencies)`);
    } catch (error) {
      console.error('❌ Failed to optimize package.json:', error.message);
    }
  }

  async reinstallDependencies() {
    console.log('📥 Reinstalling dependencies...');
    
    try {
      // Clear package-lock.json for fresh install
      const lockFile = path.join(this.projectRoot, 'package-lock.json');
      if (fs.existsSync(lockFile)) {
        fs.unlinkSync(lockFile);
      }

      // Install with optimization flags
      execSync('npm install --production=false --no-optional --no-audit --no-fund', {
        cwd: this.projectRoot,
        stdio: 'inherit'
      });
      
      console.log('✅ Dependencies reinstalled');
    } catch (error) {
      console.error('❌ Failed to reinstall dependencies:', error.message);
    }
  }

  async clearCaches() {
    console.log('🧽 Clearing caches...');
    
    try {
      // Clear Expo cache
      execSync('npx expo r -c', { cwd: this.projectRoot, stdio: 'inherit' });
      
      // Clear Metro cache
      const metroCacheDir = path.join(this.projectRoot, 'node_modules', '.cache', 'metro');
      if (fs.existsSync(metroCacheDir)) {
        execSync(`rm -rf "${metroCacheDir}"`, { stdio: 'inherit' });
      }

      // Clear React Native cache
      execSync('npx react-native start --reset-cache', { 
        cwd: this.projectRoot, 
        stdio: 'inherit',
        timeout: 5000 
      }).catch(() => {
        // Ignore timeout - cache is cleared
      });

      console.log('✅ Caches cleared');
    } catch (error) {
      console.log('⚠️  Some caches could not be cleared (this is normal)');
    }
  }

  async analyzeResults() {
    console.log('📊 Analyzing results...');
    
    try {
      const newSize = this.getDirectorySize(this.nodeModulesPath);
      const sizeInMB = (newSize / (1024 * 1024)).toFixed(2);
      
      console.log(`New node_modules size: ${sizeInMB} MB`);
      
      if (newSize < 1000 * 1024 * 1024) { // Less than 1GB
        console.log('✅ Bundle size optimized successfully!');
      } else {
        console.log('⚠️  Bundle size still large - consider further optimization');
      }
    } catch (error) {
      console.log('Could not analyze results');
    }
  }

  getDirectorySize(dirPath) {
    let totalSize = 0;
    
    try {
      const files = fs.readdirSync(dirPath);
      
      for (const file of files) {
        const filePath = path.join(dirPath, file);
        const stats = fs.statSync(filePath);
        
        if (stats.isDirectory()) {
          totalSize += this.getDirectorySize(filePath);
        } else {
          totalSize += stats.size;
        }
      }
    } catch (error) {
      // Directory might not exist or be accessible
    }
    
    return totalSize;
  }
}

// Run the cleanup
if (require.main === module) {
  const cleanup = new DependencyCleanup();
  cleanup.cleanup()
    .then(() => cleanup.analyzeResults())
    .catch(console.error);
}

module.exports = DependencyCleanup;
