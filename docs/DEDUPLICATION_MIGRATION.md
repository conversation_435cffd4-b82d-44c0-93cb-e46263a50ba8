# Code Deduplication Migration Guide

## Overview
This guide helps you migrate duplicate code to use the newly created utility functions.

## Statistics
- **Duplicate Functions Found:** 52
- **Duplicate Code Blocks Found:** 29
- **Utility Files Created:** 3

## Duplicates Found

### if (2 occurrences)
**Type:** function

**Files:**
- `src/utils/validation.js` (line 16)
- `src/services/ValidationService.js` (line 39)

### if (4 occurrences)
**Type:** function

**Files:**
- `src/utils/dataReset.js` (line 99)
- `src/utils/buildTimeReset.js` (line 130)
- `src/services/MigrationService.js` (line 322)
- `src/services/MigrationService.js` (line 893)

### if (2 occurrences)
**Type:** function

**Files:**
- `src/services/notificationService.js` (line 64)
- `src/services/notificationService.js` (line 81)

### schedulePeriodicChecks (13 occurrences)
**Type:** function

**Files:**
- `src/services/notificationService.js` (line 315)
- `src/services/SQLiteService.js` (line 71)
- `src/services/SQLiteService.js` (line 78)
- `src/services/OptimizedDataService.js` (line 436)
- `src/services/OfflineService.js` (line 68)
- `src/screens/LoginScreen.js` (line 57)
- `src/screens/LoginScreen.js` (line 83)
- `src/screens/EmployeeManagementScreen.js` (line 59)
- `src/screens/DashboardScreen.js` (line 48)
- `src/screens/DashboardScreen.js` (line 69)
- `src/screens/DashboardScreen.js` (line 381)
- `src/screens/DashboardScreen.js` (line 478)
- `src/screens/DashboardScreen.js` (line 481)

### if (2 occurrences)
**Type:** function

**Files:**
- `src/services/ValidationService.js` (line 147)
- `src/services/ValidationService.js` (line 234)

### if (6 occurrences)
**Type:** function

**Files:**
- `src/services/SQLiteService.js` (line 51)
- `src/services/OrderArchiveService.js` (line 247)
- `src/services/OfflineService.js` (line 93)
- `src/services/BackupService.js` (line 286)
- `src/hooks/useOptimizedData.js` (line 403)
- `src/context/DataContext.js` (line 535)

### if (2 occurrences)
**Type:** function

**Files:**
- `src/services/SQLiteService.js` (line 329)
- `src/services/OptimizedDataService.js` (line 444)

### catch (2 occurrences)
**Type:** function

**Files:**
- `src/services/SQLiteService.js` (line 592)
- `src/services/OrderService.ts` (line 284)

### constructor (2 occurrences)
**Type:** function

**Files:**
- `src/services/QRService.js` (line 5)
- `src/services/PrintService.js` (line 21)

### catch (5 occurrences)
**Type:** function

**Files:**
- `src/services/QRService.js` (line 103)
- `src/services/OrderService.ts` (line 458)
- `src/services/GoogleDriveService.js` (line 247)
- `src/services/BackupService.js` (line 147)
- `src/services/BackupService.js` (line 153)

## Migration Steps

1. **Review Generated Utilities:** Check `src/utils/common/` for new utility functions
2. **Update Imports:** Replace duplicate functions with utility imports
3. **Test Thoroughly:** Ensure all functionality works after migration
4. **Remove Duplicates:** Delete the original duplicate functions

## Example Migration

```javascript
// Before
const validateEmail = (email) => {
  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
};

// After
import { validationValidateEmail } from '../utils/common';

// Use validationValidateEmail instead
```
