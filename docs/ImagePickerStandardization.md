# Image Picker Standardization

## Overview
The image picker functionality has been standardized across the entire Tailora app using a centralized `ImagePickerService` and reusable `ImagePicker` component. This ensures consistent behavior, permissions handling, and image optimization throughout the application.

## Architecture

### 1. ImagePickerService (`src/services/ImagePickerService.js`)
Central service that handles all image picking functionality:

- **Permissions Management**: Automatic camera and gallery permission requests
- **Image Optimization**: Automatic resizing and compression for better performance
- **Error Handling**: Consistent error messages and fallback behavior
- **Multiple Selection**: Support for selecting multiple images when needed
- **Flexible Options**: Configurable aspect ratios, quality, and optimization settings

### 2. ImagePicker Component (`src/components/ImagePicker.js`)
Reusable UI component that provides a consistent interface:

- **Visual Consistency**: Standardized appearance across all screens
- **Loading States**: Shows processing indicators during image operations
- **Edit/Remove Actions**: Built-in edit and remove functionality
- **Customizable**: Configurable size, border radius, and placeholder text

## Implementation Across Screens

### ✅ **Standardized Screens**

#### 1. **QuickOrderScreen** - Reference Photos
```javascript
// Multiple photo upload for order references
const showPhotoOptions = () => {
  ImagePickerService.showImageSourceOptions(
    (imageUri) => {
      const newPhoto = ImagePickerService.createPhotoObject(imageUri, 'reference');
      setOrderPhotos(prev => [...prev, newPhoto]);
    },
    {
      title: 'Add Photo',
      message: 'Choose how to add a reference photo',
      aspect: [4, 3],
      quality: 0.8,
    }
  );
};
```

#### 2. **AddProductScreen** - Product Images
```javascript
// Single product image with standardized component
<ImagePicker
  onImageSelected={(imageUri) => handleInputChange('image', imageUri)}
  currentImage={productData.image}
  placeholder="Add image"
/>
```

#### 3. **AddEmployeeScreen** - Profile Photos
```javascript
// Employee avatar with standardized component
<ImagePicker
  onImageSelected={handleImageSelected}
  currentImage={formData.avatar}
  style={styles.imagePicker}
/>
```

#### 4. **ProfileScreen** - User Avatar
```javascript
// User profile image with custom sizing
<ImagePicker
  onImageSelected={handleProfileImageSelected}
  currentImage={user?.avatar || state.settings?.profileImage}
  showEditIcon={true}
  size={56}
  borderRadius={28}
/>
```

#### 5. **ProfileSettingsScreen** - Settings Avatar
```javascript
// Profile settings with same standardized approach
<ImagePicker
  onImageSelected={handleProfileImageSelected}
  currentImage={user?.avatar || state.settings?.profileImage}
  showEditIcon={true}
  size={56}
  borderRadius={28}
/>
```

#### 6. **AddFabricScreen** - Fabric Images
```javascript
// Fabric inventory images
<ImagePicker
  currentImage={fabricData.image}
  onImageSelected={handleImageSelect}
  placeholder="Add fabric image"
/>
```

### 🔄 **Potential Future Implementations**

#### AddCustomerScreen
Could benefit from customer profile photos:
```javascript
// Add to customer form
<ImagePicker
  onImageSelected={(imageUri) => handleInputChange('avatar', imageUri)}
  currentImage={customerData.avatar}
  placeholder="Add customer photo"
  size={80}
/>
```

## Key Features

### 1. **Automatic Permissions**
- Requests camera permissions when taking photos
- Requests gallery permissions when selecting from library
- Handles permission denials gracefully with user-friendly messages
- Provides guidance for manually enabling permissions in settings

### 2. **Image Optimization**
- Automatic resizing to 800px max width/height
- JPEG compression at 80% quality by default
- Maintains aspect ratio during optimization
- Reduces file sizes for better app performance

### 3. **Flexible Configuration**
```javascript
// Example of customizable options
const options = {
  allowsEditing: true,
  aspect: [1, 1], // Square aspect ratio
  quality: 0.8,   // 80% quality
  optimize: true, // Enable optimization
  allowsMultipleSelection: false, // Single selection
};
```

### 4. **Error Handling**
- Network error recovery
- Permission denial handling
- Image loading error fallbacks
- User-friendly error messages

### 5. **Performance Optimizations**
- Lazy loading of images
- Automatic compression
- Memory-efficient image handling
- Optimized for mobile devices

## Usage Guidelines

### For Single Images
```javascript
import ImagePickerService from '../services/ImagePickerService';

const handleImageSelection = async () => {
  const imageUri = await ImagePickerService.takePhoto({
    aspect: [1, 1],
    quality: 0.8,
  });
  
  if (imageUri) {
    setFormData(prev => ({ ...prev, image: imageUri }));
  }
};
```

### For Multiple Images
```javascript
const handleMultipleImages = async () => {
  const imageUris = await ImagePickerService.selectFromGallery({
    allowsMultipleSelection: true,
    quality: 0.7,
  });
  
  if (imageUris && imageUris.length > 0) {
    setImages(prev => [...prev, ...imageUris]);
  }
};
```

### Using the Component
```javascript
import ImagePicker from '../components/ImagePicker';

<ImagePicker
  onImageSelected={(uri) => setImage(uri)}
  currentImage={image}
  placeholder="Add photo"
  size={120}
  borderRadius={8}
/>
```

## Benefits

1. **Consistency**: Same behavior across all screens
2. **Maintainability**: Single source of truth for image picking logic
3. **Performance**: Optimized images reduce app size and improve loading
4. **User Experience**: Consistent UI and smooth interactions
5. **Error Handling**: Robust error recovery and user guidance
6. **Permissions**: Proper permission management with clear messaging

## Dependencies

- `expo-image-picker`: Core image picking functionality
- `expo-image-manipulator`: Image optimization and resizing
- `react-native-paper`: UI components and theming
- `react-native-vector-icons`: Icons for UI elements

The standardized image picker system ensures that all image-related functionality in the Tailora app works consistently and efficiently across all screens and use cases.
