# Phosphor Icons Integration Guide for Tailora App

## Overview

Phosphor Icons have been successfully integrated into your Tailora app as the preferred icon library. This guide explains how to use the new icon system and migrate from existing icons.

## 🎯 Why Phosphor Icons?

- **Modern Design**: Clean, minimal aesthetic that matches your app's design
- **Consistent Style**: All icons follow the same design principles
- **Multiple Weights**: thin, light, regular, bold, fill, duotone
- **Comprehensive Set**: 1,200+ icons covering all use cases
- **Better Performance**: Optimized SVG icons with smaller bundle size

## 📦 Installation

Phosphor Icons are already installed and configured:

```bash
npm install phosphor-react-native react-native-svg
```

## 🚀 Quick Start

### Basic Usage

```jsx
import PhosphorIcon from '../components/PhosphorIcon';

// Simple icon
<PhosphorIcon name="House" size="large" />

// Icon with custom color
<PhosphorIcon name="User" size="medium" color="#FF6B6B" />

// Icon with weight
<PhosphorIcon name="Heart" size="large" weight="fill" />
```

### Using Unified Icons (Recommended)

The UnifiedIcon component automatically migrates old icon names to Phosphor equivalents:

```jsx
import UnifiedIcon from '../components/UnifiedIcon';

// Automatically converts 'home' to 'House'
<UnifiedIcon name="home" size="large" />

// Semantic category-based icons
<NavigationUnifiedIcon name="settings" />
<ActionUnifiedIcon name="edit" />
<StatusUnifiedIcon name="success" />
```

## 🎨 Icon Components

### 1. PhosphorIcon (Direct)
```jsx
import PhosphorIcon, { 
  PrimaryPhosphorIcon, 
  FilledPhosphorIcon,
  CirclePhosphorIcon 
} from '../components/PhosphorIcon';

<PhosphorIcon name="Star" size="large" weight="regular" />
<PrimaryPhosphorIcon name="Heart" />
<FilledPhosphorIcon name="Bell" />
<CirclePhosphorIcon name="Check" />
```

### 2. UnifiedIcon (Migration-Friendly)
```jsx
import UnifiedIcon, {
  NavigationUnifiedIcon,
  ActionUnifiedIcon,
  BusinessUnifiedIcon,
  SettingsUnifiedIcon
} from '../components/UnifiedIcon';

<UnifiedIcon name="search" />  // Auto-migrates to MagnifyingGlass
<NavigationUnifiedIcon name="home" />  // Auto-migrates to House
<ActionUnifiedIcon name="edit" />  // Auto-migrates to PencilSimple
```

## 📋 Icon Categories & Mappings

### Navigation Icons
| Old Name | Phosphor Name | Usage |
|----------|---------------|-------|
| `home` | `House` | Home/Dashboard |
| `user` | `User` | Profile |
| `settings` | `Gear` | Settings |
| `search` | `MagnifyingGlass` | Search |

### Action Icons
| Old Name | Phosphor Name | Usage |
|----------|---------------|-------|
| `plus` | `Plus` | Add/Create |
| `edit` | `PencilSimple` | Edit |
| `trash` | `Trash` | Delete |
| `save` | `FloppyDisk` | Save |

### Business Icons
| Old Name | Phosphor Name | Usage |
|----------|---------------|-------|
| `package` | `Package` | Products |
| `users` | `Users` | Customers |
| `receipt` | `Receipt` | Orders |
| `ruler` | `Ruler` | Measurements |

## 🎛️ Icon Properties

### Sizes
```jsx
size="small"      // 16px
size="medium"     // 20px
size="large"      // 24px (default)
size="extraLarge" // 28px
size="huge"       // 32px
size="jumbo"      // 48px
size={32}         // Custom size
```

### Weights
```jsx
weight="thin"     // Thinnest stroke
weight="light"    // Light stroke
weight="regular"  // Default weight
weight="bold"     // Bold stroke
weight="fill"     // Filled version
weight="duotone"  // Two-tone version
```

### Colors
```jsx
// Direct color
color="#FF6B6B"

// Theme-based colors
colorContext="primary"
colorContext="secondary"
colorContext="surface"
colorContext="surfaceVariant"
colorContext="error"
colorContext="warning"
colorContext="success"
```

## 🔄 Migration Examples

### Before (Feather Icons)
```jsx
import FeatherIcon from 'react-native-vector-icons/Feather';

<FeatherIcon name="home" size={24} color="#333" />
<FeatherIcon name="edit" size={20} color="#666" />
<FeatherIcon name="bell" size={24} color="#999" />
```

### After (Phosphor Icons)
```jsx
import UnifiedIcon from '../components/UnifiedIcon';

<UnifiedIcon name="home" size="large" colorContext="surface" />
<UnifiedIcon name="edit" size="medium" colorContext="surfaceVariant" />
<UnifiedIcon name="bell" size="large" colorContext="surfaceVariant" />
```

## 🎯 Best Practices

### 1. Use UnifiedIcon for Migration
```jsx
// ✅ Good - Automatic migration
<UnifiedIcon name="search" />

// ❌ Avoid - Manual conversion needed
<PhosphorIcon name="MagnifyingGlass" />
```

### 2. Use Semantic Categories
```jsx
// ✅ Good - Clear intent
<NavigationUnifiedIcon name="home" />
<ActionUnifiedIcon name="edit" />
<StatusUnifiedIcon name="success" />

// ❌ Avoid - Generic usage
<UnifiedIcon name="home" />
<UnifiedIcon name="edit" />
<UnifiedIcon name="success" />
```

### 3. Consistent Sizing
```jsx
// ✅ Good - Consistent sizes
<UnifiedIcon name="home" size="large" />
<UnifiedIcon name="user" size="large" />

// ❌ Avoid - Mixed sizes without reason
<UnifiedIcon name="home" size="large" />
<UnifiedIcon name="user" size="medium" />
```

### 4. Use Theme Colors
```jsx
// ✅ Good - Theme-aware
<UnifiedIcon name="home" colorContext="primary" />

// ❌ Avoid - Hard-coded colors
<UnifiedIcon name="home" color="#FF6B6B" />
```

## 🛠️ Advanced Usage

### Custom Icon with Container
```jsx
<PhosphorIcon 
  name="Star" 
  size="large"
  containerSize={44}
  backgroundColor="#FF6B6B"
  borderRadius={22}
  colorContext="primary"
/>
```

### Interactive Icons
```jsx
<InteractiveUnifiedIcon 
  name="heart" 
  onPress={() => console.log('Liked!')}
  size="large"
/>
```

### Debug Mode
```jsx
<UnifiedIcon 
  name="home" 
  debug={true}  // Shows migration info in console
/>
```

## 📊 Migration Status

Current migration status in ProfileScreen:
- ✅ Subscription Management: `crown` (BusinessUnifiedIcon)
- ✅ Notifications: `notifications` (SettingsUnifiedIcon)
- ✅ Chevron arrows: `chevron-right` (UnifiedIcon)

## 🔧 Troubleshooting

### Icon Not Found
```jsx
// If icon doesn't exist, fallback is used
<UnifiedIcon name="nonexistent" fallbackIcon="Question" />
```

### Performance Issues
```jsx
// Use specific categories for better performance
<NavigationUnifiedIcon name="home" />  // ✅ Fast lookup
<UnifiedIcon name="home" />            // ❌ Slower lookup
```

## 📚 Resources

- [Phosphor Icons Website](https://phosphoricons.com/)
- [Icon Search & Browse](https://phosphoricons.com/)
- [React Native Implementation](https://github.com/duongdev/phosphor-react-native)

## 🎉 Next Steps

1. **Gradual Migration**: Replace Feather icons with UnifiedIcon components
2. **Consistency Check**: Ensure all icons use the same size/weight patterns
3. **Performance Optimization**: Use category-specific components
4. **Custom Icons**: Add app-specific icons to the mapping system

The Phosphor Icons system is now ready for use throughout your Tailora app!
