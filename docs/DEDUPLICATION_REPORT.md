# Code Deduplication Report

Generated on: 2025-07-11T11:48:34.714Z

## Summary
- **Files Analyzed:** 263
- **Duplicate Functions:** 52
- **Duplicate Code Blocks:** 29
- **Utility Files Created:** 3

## Top Duplicates

### styles
- **Type:** block
- **Occurrences:** 22
- **Hash:** `d074bb1b3fab9c7000e477c3a7ebba6c`

### schedulePeriodicChecks
- **Type:** function
- **Occurrences:** 13
- **Hash:** `d41d8cd98f00b204e9800998ecf8427e`

### if
- **Type:** function
- **Occurrences:** 9
- **Hash:** `5d1f22f1f78c1cdc3f6d2876f0b633f9`

### styles
- **Type:** block
- **Occurrences:** 8
- **Hash:** `5f40cfad864cf1a6e4fe1d99b8a3b212`

### checkCameraPermissions
- **Type:** function
- **Occurrences:** 7
- **Hash:** `2e913be2539392ddf88e8ee8b320b20e`

### error_handling
- **Type:** block
- **Occurrences:** 7
- **Hash:** `5fd85883e520bcaa88c4eb85b259ffab`

### if
- **Type:** function
- **Occurrences:** 6
- **Hash:** `505b97969baa28c3f607a38ee02f4f2d`

### if
- **Type:** function
- **Occurrences:** 6
- **Hash:** `bbef19786c08a56d47025fc80a748f53`

### styles
- **Type:** block
- **Occurrences:** 6
- **Hash:** `e31f6f65dab751dd4ff16537001401e7`

### catch
- **Type:** function
- **Occurrences:** 5
- **Hash:** `d0fb1625c2453fc80769deb4b7780c87`

### styles
- **Type:** block
- **Occurrences:** 5
- **Hash:** `9bbda0763d487dd0efef439461a4243a`

### if
- **Type:** function
- **Occurrences:** 4
- **Hash:** `c5a639f2f0b551ac72b4f1f69e93f0f6`

### getEmployeeInitials
- **Type:** function
- **Occurrences:** 4
- **Hash:** `d86a8edb19bced1b8f84f0af10a52077`

### error_handling
- **Type:** block
- **Occurrences:** 4
- **Hash:** `8f484062b2d3b212bd9868d034ca28ff`

### error_handling
- **Type:** block
- **Occurrences:** 4
- **Hash:** `31f974b64a663f4b5a03a8a8b2e15da6`

### error_handling
- **Type:** block
- **Occurrences:** 4
- **Hash:** `49071ec7e90fe19b3af139bc3b7e793e`

### if
- **Type:** function
- **Occurrences:** 3
- **Hash:** `9aca84c45a49b746da4527e96eed4f1a`

### if
- **Type:** function
- **Occurrences:** 3
- **Hash:** `331be8df524efc3bfc842b772b27c46a`

### if
- **Type:** function
- **Occurrences:** 3
- **Hash:** `3644202570241f31d58278339c9670ed`

### if
- **Type:** function
- **Occurrences:** 3
- **Hash:** `f82e9a95b80ab5030b64ca0ff3f495cd`

## Recommendations

1. **High Priority:** Functions with 3+ duplicates
2. **Medium Priority:** Code blocks with 2+ duplicates
3. **Low Priority:** Similar patterns that could be abstracted

## Next Steps

1. Review the migration guide: `docs/DEDUPLICATION_MIGRATION.md`
2. Test the generated utility functions
3. Gradually migrate duplicate code to use utilities
4. Set up linting rules to prevent future duplication
