# Navigation Migration Guide

This guide explains how to migrate from the old navigation system to the new typed navigation system with deep linking support.

## Overview of Changes

### ✅ What's New
- **TypeScript Support**: Full type safety for navigation
- **Deep Linking**: Comprehensive URL scheme support
- **Typed Hooks**: Custom hooks for better developer experience
- **Centralized Configuration**: All navigation config in one place
- **Better Error Handling**: Compile-time error detection

### 📁 New File Structure
```
src/
├── types/
│   └── navigation.ts          # TypeScript type definitions
├── hooks/
│   └── useTypedNavigation.ts  # Typed navigation hooks
├── config/
│   └── deepLinking.ts         # Deep linking configuration
└── examples/
    └── TypedNavigationExample.tsx # Usage examples
```

## Migration Steps

### 1. Update Navigation Calls

**Before:**
```javascript
// Old way - no type safety
import { useNavigation } from '@react-navigation/native';

const navigation = useNavigation();
navigation.navigate('OrderDetails', { orderId: 123 }); // Could pass wrong type
```

**After:**
```typescript
// New way - fully typed
import { useTypedNavigation } from '../hooks/useTypedNavigation';

const navigation = useTypedNavigation<'Orders'>();
navigation.navigate('OrderDetails', { orderId: '123' }); // TypeScript ensures correct type
```

### 2. Update Route Parameter Access

**Before:**
```javascript
// Old way - no type checking
import { useRoute } from '@react-navigation/native';

const route = useRoute();
const { orderId } = route.params; // Could be undefined or wrong type
```

**After:**
```typescript
// New way - typed parameters
import { useTypedRoute } from '../hooks/useTypedNavigation';

const route = useTypedRoute<'OrderDetails'>();
const { orderId } = route.params; // TypeScript knows this is a string
```

### 3. Use Deep Link Navigation

**New Feature:**
```typescript
import { useDeepLinkNavigation } from '../hooks/useTypedNavigation';

const { navigateToOrder, navigateToCustomer } = useDeepLinkNavigation();

// Navigate with type safety
navigateToOrder('order-123');
navigateToCustomer('customer-456');
```

### 4. Generate Deep Links

**New Feature:**
```typescript
import { generateDeepLink } from '../config/deepLinking';

// Generate shareable links
const orderLink = generateDeepLink.order('order-123');
const customerLink = generateDeepLink.customer('customer-456');

// Share or copy to clipboard
Share.share({ message: orderLink });
```

## Screen Component Updates

### Before (JavaScript)
```javascript
const OrderDetailsScreen = ({ navigation, route }) => {
  const { orderId } = route.params;
  
  const handleEdit = () => {
    navigation.navigate('EditOrder', { orderId });
  };
  
  // Component implementation...
};
```

### After (TypeScript)
```typescript
import type { RootStackScreenProps } from '../types/navigation';

type Props = RootStackScreenProps<'OrderDetails'>;

const OrderDetailsScreen: React.FC<Props> = ({ navigation, route }) => {
  const { orderId } = route.params; // TypeScript knows this is a string
  
  const handleEdit = () => {
    // TypeScript ensures correct parameters
    navigation.navigate('EditOrder', { orderId });
  };
  
  // Component implementation...
};
```

## Deep Linking Setup

### URL Schemes Supported
- `tailora://` - Production app
- `tailora-dev://` - Development app
- `https://tailora.app` - Universal links
- `https://app.tailora.com` - Web app links

### Example Deep Links
```
# Navigate to specific order
tailora://orders/order-123

# Navigate to customer details
tailora://customers/customer-456

# Navigate to dashboard
tailora://dashboard

# Navigate to QR scanner
tailora://scan
```

### Registering Custom Deep Link Handlers
```typescript
import NavigationService from '../services/NavigationService';

// Register custom handler
NavigationService.registerDeepLinkHandler(
  '/custom/:id',
  (params) => {
    // Handle custom deep link
    console.log('Custom ID:', params.id);
  }
);
```

## Best Practices

### 1. Always Use Typed Hooks
```typescript
// ✅ Good
const navigation = useTypedNavigation<'CurrentScreen'>();

// ❌ Avoid
const navigation = useNavigation();
```

### 2. Define Screen Props Types
```typescript
// ✅ Good
type Props = RootStackScreenProps<'OrderDetails'>;
const OrderDetailsScreen: React.FC<Props> = ({ navigation, route }) => {
  // Implementation
};

// ❌ Avoid
const OrderDetailsScreen = ({ navigation, route }) => {
  // Implementation
};
```

### 3. Use Deep Link Navigation for Common Actions
```typescript
// ✅ Good - semantic and reusable
const { navigateToOrder } = useDeepLinkNavigation();
navigateToOrder(orderId);

// ❌ Less ideal - manual navigation
navigation.navigate('OrderDetails', { orderId });
```

### 4. Generate Deep Links for Sharing
```typescript
// ✅ Good - shareable links
const orderLink = generateDeepLink.order(orderId);
Share.share({ message: `Check out this order: ${orderLink}` });
```

## Common Migration Issues

### Issue 1: Type Errors
**Problem:** TypeScript errors when navigating
**Solution:** Ensure parameter types match the navigation type definitions

```typescript
// ❌ Error - orderId should be string
navigation.navigate('OrderDetails', { orderId: 123 });

// ✅ Fixed
navigation.navigate('OrderDetails', { orderId: '123' });
```

### Issue 2: Missing Screen Types
**Problem:** Screen not found in RootStackParamList
**Solution:** Add the screen to the type definition in `src/types/navigation.ts`

### Issue 3: Deep Links Not Working
**Problem:** Deep links don't navigate correctly
**Solution:** Ensure the URL scheme is registered in the app configuration

## Testing Deep Links

### iOS Simulator
```bash
# Test deep link in iOS simulator
xcrun simctl openurl booted "tailora://orders/test-order-123"
```

### Android Emulator
```bash
# Test deep link in Android emulator
adb shell am start -W -a android.intent.action.VIEW -d "tailora://orders/test-order-123" com.tailora.app
```

### Development Server
```bash
# Test with development server
curl "http://localhost:8081/orders/test-order-123"
```

## Performance Benefits

1. **Compile-time Error Detection**: Catch navigation errors before runtime
2. **Better IntelliSense**: Auto-completion for screen names and parameters
3. **Reduced Bundle Size**: Tree-shaking removes unused navigation code
4. **Improved Developer Experience**: Less debugging, more productive development

## Next Steps

1. **Migrate Existing Screens**: Update one screen at a time to use typed navigation
2. **Add Deep Link Handlers**: Register handlers for important app flows
3. **Test Deep Links**: Verify all deep links work correctly
4. **Update Documentation**: Document new navigation patterns for your team

## Support

If you encounter issues during migration:
1. Check the TypeScript errors carefully
2. Refer to the example file: `src/examples/TypedNavigationExample.tsx`
3. Ensure all screen types are defined in `src/types/navigation.ts`
4. Test deep links thoroughly on both platforms

---

**Migration Checklist:**
- [ ] Update navigation calls to use typed hooks
- [ ] Add TypeScript types to screen components
- [ ] Test deep linking functionality
- [ ] Update existing screens gradually
- [ ] Add custom deep link handlers as needed
- [ ] Test on both iOS and Android
- [ ] Update team documentation