{"name": "tailora", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "type-check": "tsc --noEmit", "bundle-analyze": "npx react-native-bundle-visualizer", "bundle-size": "npx bundlesize"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.4.1", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.9", "@react-navigation/stack": "^7.3.2", "expo": "53.0.19", "expo-asset": "^11.1.5", "expo-camera": "~16.1.6", "expo-document-picker": "^13.1.5", "expo-file-system": "~18.1.10", "expo-font": "^13.3.1", "expo-image-manipulator": "~13.1.7", "expo-image-picker": "~16.1.4", "expo-linking": "~7.1.7", "expo-local-authentication": "^16.0.4", "expo-media-library": "~17.1.6", "expo-modules-core": "^2.3.13", "expo-print": "~14.1.4", "expo-sharing": "~13.1.5", "expo-sqlite": "~15.2.14", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-paper": "^5.14.5", "react-native-paper-dates": "^0.22.43", "react-native-qrcode-svg": "^6.3.15", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "wa-sqlite": "^1.0.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@expo/metro-config": "^0.20.14", "@types/jest": "^29.5.14", "@types/node": "^20.17.55", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true, "expo": {"doctor": {"reactNativeDirectoryCheck": {"exclude": ["react-native-vector-icons", "react-native-screens"], "listUnknownPackages": false}}}, "eas": {"cli": {"appVersionSource": "remote"}}}