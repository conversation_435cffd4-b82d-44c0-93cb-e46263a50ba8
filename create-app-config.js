#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Get arguments
const projectRoot = process.argv[2] || process.cwd();
const destinationDir = process.argv[3];

if (!destinationDir) {
  console.error('Usage: node create-app-config.js <projectRoot> <destinationDir>');
  process.exit(1);
}

// Create destination directory if it doesn't exist
if (!fs.existsSync(destinationDir)) {
  fs.mkdirSync(destinationDir, { recursive: true });
}

// Read app.json or create a minimal config
let appConfig;
const appJsonPath = path.join(projectRoot, 'app.json');

if (fs.existsSync(appJsonPath)) {
  try {
    const appJson = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'));
    appConfig = appJson.expo || appJson;
  } catch (error) {
    console.error('Error reading app.json:', error);
    appConfig = {};
  }
} else {
  // Create minimal config
  appConfig = {
    name: "<PERSON><PERSON>",
    slug: "tailora",
    version: "1.0.0",
    orientation: "portrait",
    icon: "./assets/icon.png",
    userInterfaceStyle: "light",
    splash: {
      image: "./assets/splash.png",
      resizeMode: "contain",
      backgroundColor: "#ffffff"
    },
    assetBundlePatterns: [
      "**/*"
    ],
    ios: {
      supportsTablet: true
    },
    android: {
      adaptiveIcon: {
        foregroundImage: "./assets/adaptive-icon.png",
        backgroundColor: "#ffffff"
      }
    },
    web: {
      favicon: "./assets/favicon.png"
    }
  };
}

// Write the config file
const configPath = path.join(destinationDir, 'app.config');
fs.writeFileSync(configPath, JSON.stringify(appConfig));

console.log(`App config written to: ${configPath}`);
