const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// Add TypeScript, WASM, and Web Worker support
config.resolver.sourceExts.push('ts', 'tsx');
config.resolver.assetExts.push('wasm', 'worker.js');

// Web-specific configuration
if (process.env.EXPO_PLATFORM === 'web') {
  // Ensure web workers are handled correctly
  config.resolver.platforms = ['web', 'native', 'ios', 'android'];
  
  // Add public directory for web assets
  config.watchFolders = [__dirname + '/public'];
}

// Bundle size optimization
config.transformer = {
  ...config.transformer,
  minifierConfig: {
    mangle: {
      keep_fnames: true,
    },
    output: {
      ascii_only: true,
      quote_keys: false,
      wrap_iife: true,
    },
    sourceMap: false,
    toplevel: false,
    warnings: false,
    parse: {
      ecma: 8,
    },
    compress: {
      ecma: 5,
      warnings: false,
      comparisons: false,
      inline: 2,
      drop_console: process.env.NODE_ENV === 'production',
      drop_debugger: true,
      pure_getters: true,
      unsafe: true,
      unsafe_comps: true,
      unsafe_math: true,
      unsafe_methods: true,
      passes: 2,
    },
  },
  unstable_allowRequireContext: true,
};

// Resolver optimization
config.resolver = {
  ...config.resolver,
  sourceExts: [...config.resolver.sourceExts, 'jsx', 'ts', 'tsx'],
  blacklistRE: /node_modules\/.*\/(test|__tests__|spec|__spec__|example|examples|demo|demos|docs|documentation|\.stories\.js|\.test\.js|\.spec\.js)$/,

  // Phosphor Icons optimization - block unused icons to reduce bundle size
  blockList: [
    // Block unused vector icon libraries
    /node_modules\/react-native-vector-icons\/(?!MaterialCommunityIcons).*/,

    // Block unused Expo vector icons
    /node_modules\/@expo\/vector-icons\/(?!MaterialCommunityIcons).*/,

    // Block test and example files
    /node_modules\/.*\/(test|__tests__|spec|__spec__|example|examples|demo|demos|docs|documentation)$/,
  ],

  alias: {
    '@': './src',
    '@components': './src/components',
    '@screens': './src/screens',
    '@services': './src/services',
    '@utils': './src/utils',
    '@context': './src/context',
    '@hooks': './src/hooks',
    '@theme': './src/theme',
    '@assets': './assets',
  },
};

// Performance optimizations
config.maxWorkers = Math.max(1, Math.floor(require('os').cpus().length * 0.75));
config.resetCache = false;

module.exports = config;
